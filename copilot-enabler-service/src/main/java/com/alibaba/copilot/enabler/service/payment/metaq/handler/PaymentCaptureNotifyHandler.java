package com.alibaba.copilot.enabler.service.payment.metaq.handler;

import com.alibaba.aepay.fund.business.api.payment.dto.notify.CaptureNotifyDTO;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.constant.StatusFlowReasonEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageStatusEnum;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageTypeEnum;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionCompleteReason;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.domain.payment.model.MessageInfo;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentInfo;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.service.OrderDomainService;
import com.alibaba.copilot.enabler.domain.subscription.event.SubscriptionPlanChangedEventProducer;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.infra.base.utils.MoneyUtils;
import com.alibaba.copilot.enabler.service.payment.schedule.handler.InitiateDeductionHandler;
import com.alibaba.fastjson.JSON;
import com.alibaba.global.money.Money;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 支付请款通知handler
 */
@Slf4j
@Component
public class PaymentCaptureNotifyHandler {

    @Autowired
    private OrderDomainService orderDomainService;
    @Autowired
    private InitiateDeductionHandler initiateDeductionHandler;
    @Autowired
    private SubscriptionPlanChangedEventProducer subscriptionPlanChangedEventProducer;

    /**
     * 支付请款处理
     */
    @Transactional(rollbackFor = Exception.class)
    public void handle(String tradeNo, String msgId, String notifyContentStr, CaptureNotifyDTO captureNotifyDTO) {

        TradeRecord payRecord = orderDomainService.getTradeRecord(tradeNo);
        if (payRecord == null) {
            return;
        }

        if (payRecord.getStatus() != TradeRecordStatus.TODO) {
            return;
        }

        BigDecimal tradeAmount = payRecord.getTradeAmount();
        Money captureAmount = captureNotifyDTO.getCaptureAmount();

        Assertor.asserts(captureAmount != null
                        && captureAmount.getCurrency() != null
                        && Objects.equals(captureAmount.getCurrency().getCurrencyCode(), AEPaymentConstants.CURRENCY_CODE)
                        && MoneyUtils.isEqualBigDecimal(captureAmount.getAmount(), tradeAmount),
                "captureAmount incorrect"
        );

        MessageInfo messageInfo = MessageInfo.builder()
                .msgId(msgId)
                .notifyContentOrRequestParam(notifyContentStr)
                .response(ConsumeConcurrentlyStatus.CONSUME_SUCCESS.name())
                .typeEnum(MessageTypeEnum.PAYMENT_CAPTURE_NOTIFY)
                .statusEnum(MessageStatusEnum.SUCCESS)
                .entityId(tradeNo)
                .build();

        PaymentInfo paymentInfo = PaymentInfo.builder()
                .outTradeNo(captureNotifyDTO.getPaymentId())
                .tradeTime(captureNotifyDTO.getCaptureTime())
                .build();
        orderDomainService.paymentSuccess(payRecord, messageInfo, paymentInfo);
    }

    /**
     * 支付请款失败处理
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleFail(String tradeNo, String msgId, String notifyContentStr, CaptureNotifyDTO captureNotifyDTO) {

        TradeRecord payRecord = orderDomainService.getTradeRecord(tradeNo);
        if (payRecord == null) {
            return;
        }

        // 告警通知
        log.error("payAlarm-支付请款通知失败,tradeNo:{},msgId:{},appCode:{},userId:{},notifyContent:{}",
                tradeNo, msgId, payRecord.getAppCode(), payRecord.getUserId(), notifyContentStr);

        MessageInfo messageInfo = MessageInfo.builder()
                .msgId(msgId)
                .notifyContentOrRequestParam(notifyContentStr)
                .response(ConsumeConcurrentlyStatus.CONSUME_SUCCESS.name())
                .typeEnum(MessageTypeEnum.PAYMENT_CAPTURE_NOTIFY)
                .statusEnum(MessageStatusEnum.FAIL)
                .entityId(tradeNo)
                .build();

        PaymentInfo paymentInfo = PaymentInfo.builder()
                .outTradeNo(captureNotifyDTO.getPaymentId())
                .tradeTime(captureNotifyDTO.getCaptureTime())
                .build();

        orderDomainService.cancelPendingPaymentOrder(payRecord, messageInfo, paymentInfo, StatusFlowReasonEnum.PERIODIC_DEDUCT_FAIL);

        if (PaymentTypeEnum.PERIODIC_DEDUCT.equals(payRecord.getPaymentType())) {
            log.info("periodic_deduct fail, payRecord={}", JSON.toJSONString(payRecord));

            // 完结当前生效的订单
            SubscriptionOrder oldEffectOrder = initiateDeductionHandler.completeCurrentEffectOrderIfNeed(payRecord.getAppCode(), payRecord.getUserId());

            // 发送老套餐完结事件
            if (oldEffectOrder != null) {
                subscriptionPlanChangedEventProducer.sendCompleteEvent(oldEffectOrder, SubscriptionCompleteReason.FAIL_TO_RENEW);
            }

            // 发送「扣费失败」的邮件
            initiateDeductionHandler.sendDeductFailureEmail(payRecord);
        }
    }
}
