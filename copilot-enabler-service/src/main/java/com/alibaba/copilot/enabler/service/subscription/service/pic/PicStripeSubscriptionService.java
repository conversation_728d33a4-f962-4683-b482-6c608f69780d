package com.alibaba.copilot.enabler.service.subscription.service.pic;

import cn.hutool.core.date.DateUtil;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.constant.StatusFlowReasonEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.payment.constant.dispute.DisputeResult;
import com.alibaba.copilot.enabler.client.payment.constant.dispute.DisputeStatus;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageStatusEnum;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageTypeEnum;
import com.alibaba.copilot.enabler.client.payment.dto.GoodsDTO;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionCompleteReason;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanResultDTO;
import com.alibaba.copilot.enabler.domain.base.exception.BizException;
import com.alibaba.copilot.enabler.domain.base.utils.PaymentUtils;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.domain.payment.constant.StripeCheckoutSessionMode;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.StripeGateway;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeCreateSessionRequest;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeCreateSessionResponse;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEvent;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEventMetadata;
import com.alibaba.copilot.enabler.domain.payment.model.MessageInfo;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentInfo;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecordAttributes;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.payment.service.OrderDomainService;
import com.alibaba.copilot.enabler.domain.payment.service.PaymentDomainService;
import com.alibaba.copilot.enabler.domain.subscription.event.SubscriptionPlanChangedEventProducer;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategy;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategyFactory;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.CreateOrderAndTradeResultDTO;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.SubscribeContext;
import com.alibaba.copilot.enabler.service.payment.metaq.trade.TradeStartProducer;
import com.alibaba.copilot.enabler.service.payment.schedule.handler.InitiateDeductionHandler;
import com.alibaba.copilot.enabler.service.stripe.StripeService;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeExecuteContext;
import com.alibaba.copilot.enabler.service.subscription.factory.SubscribeContextBuilder;
import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Pic 业务使用 Stripe 支付订阅的处理器
 *
 * <AUTHOR>
 * @date 2024/11/08
 */
@Service
@Slf4j
public class PicStripeSubscriptionService {

    @Resource
    private OrderDomainService orderDomainService;
    @Resource
    private SubscribeContextBuilder subscribeContextBuilder;
    @Resource
    private TradeStartProducer tradeStartProducer;
    @Resource
    private StripeGateway stripeGateway;
    @Resource
    private StripeService stripeService;
    @Resource
    private TradeRecordRepository tradeRecordRepository;
    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;
    @Resource
    private InitiateDeductionHandler initiateDeductionHandler;
    @Resource
    private PaymentDomainService paymentDomainService;
    @Resource
    private SubscriptionPlanChangedEventProducer subscriptionPlanChangedEventProducer;


    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public SubscribePlanResultDTO subscribePlanByStripe(SubscribePlanDTO subscribePlanDTO) {
        // 构建订阅请求的上下文信息
        SubscribeContext subscribeContext = subscribeContextBuilder.build(subscribePlanDTO);

        // get or create stripe customer
        String customerId = stripeService.getOrCreateStripeCustomer(subscribePlanDTO.getUserId());

        // 创建订单和流水
        CreateOrderAndTradeResultDTO handleResult = createOrderAndTrade(subscribePlanDTO.getAppCode(), subscribeContext);

        TradeRecord tradeRecord = handleResult.getPayRecord();
        if (tradeRecord == null) {
            // 存在不需要支付的场景 (如PicCopilot中的高套餐切换低套餐)
            return new SubscribePlanResultDTO().setNeedPay(false);
        }
        String tradeNo = tradeRecord.getTradeNo();

        // 组装创建 Stripe Checkout Session 请求
        StripeCreateSessionRequest request = buildStripeCreateSessionRequest(subscribePlanDTO, tradeNo, customerId, handleResult);
        // 执行CreateSession
        StripeCreateSessionResponse checkoutSession = stripeGateway.createCheckoutSession(request);

        // 保存消息记录
        saveMessageRecord(subscribePlanDTO, request, checkoutSession, handleResult);

        return new SubscribePlanResultDTO()
                .setNeedPay(true)
                .setPaymentRequestId(tradeNo)
                .setOrderId(tradeRecord.getSubscriptionOrderId())
                .setSubscriptionPayTypeName(SubscriptionPayType.STRIPE.name())
                .setClientSecret(checkoutSession.getClientSecret())
                ;
    }

    @NotNull
    private StripeCreateSessionRequest buildStripeCreateSessionRequest(SubscribePlanDTO subscribePlanDTO,
                                                                       String tradeNo,
                                                                       String customerId,
                                                                       CreateOrderAndTradeResultDTO handleResult) {

        // 获取订阅价格
        SubscriptionOrder newOrder = handleResult.getNewOrder();
        BigDecimal planPrice = newOrder.getPlanPrice();
        long cent = planPrice.multiply(new BigDecimal(100)).longValue();
        // 设置商品信息
        List<GoodsDTO> goodsList = new ArrayList<>();
        GoodsDTO goods = new GoodsDTO();
        goods.setQuantity(1L);
        goods.setUnitAmount(cent);
        goods.setGoodsName(newOrder.getSubscriptionPlanName());
        goodsList.add(goods);

        // 构造 request
        StripeCreateSessionRequest request = new StripeCreateSessionRequest();
        request.setMode(StripeCheckoutSessionMode.SETUP);
        String finalUrl = appendBizParamToUrl(subscribePlanDTO.getRedirectUrl(), subscribePlanDTO.getPlanId(), tradeNo);
        request.setReturnUrl(finalUrl);
        request.setStripeCustomerId(customerId);
        // meta data
        StripeEventMetadata metadata = StripeEventMetadata.of(
                StripeCheckoutSessionMode.SETUP,
                subscribePlanDTO.getAppCode(),
                tradeNo);
        metadata.setUserId(subscribePlanDTO.getUserId().toString());
        metadata.setSubscriptionOrderId(newOrder.getId().toString());
        request.setMetadata(metadata);
        request.setGoodsList(goodsList);
        request.setTimeout(3600L);
        request.setClientReferenceId(subscribePlanDTO.getClientReferenceId());
        return request;
    }

    private String appendBizParamToUrl(String url, Long planId, String paymentRequestId) {
        return UriComponentsBuilder.fromHttpUrl(url)
                .queryParam(AEPaymentConstants.KEY_PLAN_ID, planId)
                .queryParam(AEPaymentConstants.KEY_PAYMENT_REQUEST_ID, paymentRequestId)
                .toUriString();
    }

    private CreateOrderAndTradeResultDTO createOrderAndTrade(String appCode, SubscribeContext subscribeContext) {
        SubscriptionStrategy subscriptionStrategy = SubscriptionStrategyFactory.getStrategy(appCode, subscribeContext.getSubscriptionPayType());
        // pic stripe ok
        CreateOrderAndTradeResultDTO handleResult = subscriptionStrategy.createOrderAndTrade(subscribeContext);
        log.info("subscribePlan handleResult:{}", JSON.toJSONString(handleResult));
        return handleResult;
    }


    private void saveMessageRecord(SubscribePlanDTO subscribePlanDTO,
                                   StripeCreateSessionRequest request,
                                   StripeCreateSessionResponse checkoutSession,
                                   CreateOrderAndTradeResultDTO result) {
        MessageInfo messageInfo = MessageInfo.builder()
                .notifyContentOrRequestParam(JSON.toJSONString(request))
                .response(JSON.toJSONString(checkoutSession))
                .typeEnum(MessageTypeEnum.PAYMENT_AUTH_NOTIFY)
                .statusEnum(MessageStatusEnum.SUCCESS)
                .entityId(result.getNewOrder().getId().toString())
                .build();
        orderDomainService.saveMessageRecord(messageInfo, subscribePlanDTO.getAppCode(), subscribePlanDTO.getUserId());
    }


    // ============= charge ==============

    @Transactional(rollbackFor = Exception.class)
    public void chargeForNewSubscriptionByStripe(TradeRecord tradeRecord) {
        // 查询订阅订单
        SubscriptionOrder order = orderDomainService.querySubscriptionOrder(tradeRecord,
                SubscriptionOrderStatus.PENDING_PAYMENT);
        if (order == null) {
            return;
        }
        // 发起扣款
        stripeService.createPaymentIntentForSubInSetupMode(order, tradeRecord);
        // 更新已发起支付
        TradeRecord toUpdateTradeRecord = new TradeRecord();
        toUpdateTradeRecord.setId(tradeRecord.getId());
        toUpdateTradeRecord.setHadInitiatePay(true);
        tradeRecordRepository.createOrUpdateTradeRecord(toUpdateTradeRecord);
    }


    // ========== payment intent succeed =============

    public void handlePaymentIntentSucceed4SetupMode(StripeExecuteContext context) {
        checkStripeEvent(context);

        // 流程参考：
        // com.alibaba.copilot.enabler.service.payment.metaq.handler.PaymentCaptureNotifyHandler.handle
        StripeEvent event = context.getEvent();
        StripeEventMetadata metadata = context.getMetaData();
        String tradeNo = metadata.getTradeNo();
        TradeRecord payRecord = orderDomainService.getTradeRecord(tradeNo);
        if (payRecord == null || payRecord.getStatus() != TradeRecordStatus.TODO) {
            return;
        }
        MessageInfo messageInfo = MessageInfo.builder()
                .msgId(event.fetchEventId())
                .notifyContentOrRequestParam("")
                .response(ConsumeConcurrentlyStatus.CONSUME_SUCCESS.name())
                .typeEnum(MessageTypeEnum.PAYMENT_CAPTURE_NOTIFY)
                .statusEnum(MessageStatusEnum.SUCCESS)
                .entityId(tradeNo)
                .build();
        PaymentInfo paymentInfo = PaymentInfo.builder()
                .outTradeNo(tradeNo)
                .tradeTime(event.fetchEventCreatedTimeMillis())
                .build();
        orderDomainService.paymentSuccess(payRecord, messageInfo, paymentInfo);
    }

    private void checkStripeEvent(StripeExecuteContext context) {
        Assertor.assertNonNull(context, "context is null");
        StripeEvent event = context.getEvent();
        Assertor.assertNonNull(event, "event is null");
        StripeEventMetadata metadata = context.getMetaData();
        Assertor.assertNonNull(metadata, "event is null");
        String sessionMode = metadata.getSessionMode();
        if (!metadata.whetherSetupMode()) {
            throw new BizException("metadata#sessionMode is not SETUP, but " + sessionMode);
        }
    }

    // ========== payment intent failed =============

    public void handlePaymentIntentFailed4SetupMode(StripeExecuteContext context) {
        checkStripeEvent(context);

        // 流程参考：
        // com.alibaba.copilot.enabler.service.payment.metaq.handler.PaymentCaptureNotifyHandler.handleFail
        StripeEvent event = context.getEvent();
        StripeEventMetadata metadata = context.getMetaData();
        String tradeNo = metadata.getTradeNo();
        TradeRecord payRecord = orderDomainService.getTradeRecord(tradeNo);
        if (payRecord == null) {
            return;
        }

        if (TradeRecordStatus.SUCC.equals(payRecord.getStatus())) {
            log.error("handlePaymentIntentFailed4SetupMode, tradeRecord status is succ, metaData={}",
                    JSON.toJSONString(metadata));
            return;
        }


        // 告警通知
        log.error("payAlarm stripe payment intent failed, tradeNo:{}, msgId:{}, appCode:{}, userId:{}",
                tradeNo, event.fetchEventId(), payRecord.getAppCode(), payRecord.getUserId());

        MessageInfo messageInfo = MessageInfo.builder()
                .msgId(event.fetchEventId())
                .notifyContentOrRequestParam("")
                .response(ConsumeConcurrentlyStatus.CONSUME_SUCCESS.name())
                .typeEnum(MessageTypeEnum.PAYMENT_CAPTURE_NOTIFY)
                .statusEnum(MessageStatusEnum.FAIL)
                .entityId(tradeNo)
                .build();

        PaymentInfo paymentInfo = PaymentInfo.builder()
                .outTradeNo(tradeNo)
                .tradeTime(event.fetchEventCreatedTimeMillis())
                .build();

        orderDomainService.cancelPendingPaymentOrder(payRecord, messageInfo, paymentInfo, StatusFlowReasonEnum.PERIODIC_DEDUCT_FAIL);

        if (PaymentTypeEnum.PERIODIC_DEDUCT.equals(payRecord.getPaymentType())) {
            log.info("periodic_deduct fail, payRecord={}", JSON.toJSONString(payRecord));
            // 完结当前生效的订单
            SubscriptionOrder oldEffectOrder = initiateDeductionHandler.completeCurrentEffectOrderIfNeed(payRecord.getAppCode(), payRecord.getUserId());
            // 发送老套餐完结事件
            if (oldEffectOrder != null) {
                subscriptionPlanChangedEventProducer.sendCompleteEvent(oldEffectOrder, SubscriptionCompleteReason.FAIL_TO_RENEW);
            }
            // 发送「扣费失败」的邮件
            initiateDeductionHandler.sendDeductFailureEmail(payRecord);
        }

    }


    // ========== refund succeeded =============

    public void handleRefundSucceed4SetupMode(StripeExecuteContext context) {
        checkStripeEvent(context);

        // 流程参考
        // com.alibaba.copilot.enabler.service.subscription.strategy.BaseSubscriptionStrategy.doRefund

        StripeEvent event = context.getEvent();
        StripeEventMetadata metadata = context.getMetaData();

        String refundTradeId = metadata.getRefundTradeId();
        TradeRecord refundRecord;
        if (StringUtils.isNotBlank(refundTradeId)) {
            refundRecord = tradeRecordRepository.queryById(Long.valueOf(refundTradeId));
        } else {
            Long orderId = Long.valueOf(metadata.getSubscriptionOrderId());
            refundRecord = tradeRecordRepository.queryByOrderId(orderId, TradeDirection.REFUND);
        }
        Assertor.assertNonNull(refundRecord, "refundRecord is null");

        SubscriptionOrder order = subscriptionOrderRepository.getByOrderId(refundRecord.getSubscriptionOrderId());

        PaymentInfo paymentInfo = PaymentInfo.builder()
                .tradeTime(event.fetchEventCreatedTimeMillis())
                .outTradeNo(order.getAttributes().getPaymentTokenId())
                .build();
        StatusFlowReasonEnum flowReasonEnum = StatusFlowReasonEnum.USER_HAD_DISPUTE;
        orderDomainService.refundSuccess(refundRecord, null, paymentInfo, flowReasonEnum);
    }


    @Transactional(rollbackFor = Exception.class)
    public void dealWithDisputeRefund(StripeEventMetadata metaData, TradeRecord tradeRecord, DisputeResult disputeResult) {
        String appCode = metaData.getAppCode();
        Long userId = Long.valueOf(metaData.getUserId());

        if (DisputeResult.LOST.equals(disputeResult)) {
            // com.alibaba.copilot.enabler.domain.payment.service.OrderDomainService.cancelCurrentEffectOrder
            // 取消生效中的订单
            SubscriptionOrder effectOrder = subscriptionOrderRepository.queryEffectOrder(appCode, userId);
            log.info("cancelCurrentEffectOrder, queryEffectOrder finished, result={}", JSON.toJSONString(effectOrder));
            if (Objects.nonNull(effectOrder)) {
                // 更改订单状态
                effectOrder.setStatus(SubscriptionOrderStatus.UNSUBSCRIBE);
                effectOrder.setPerformEndTime(DateUtil.date());
                effectOrder.setAutoRenew(false);
                effectOrder.getAttributes().addStatusFlowReason(StatusFlowReasonEnum.USER_HAD_DISPUTE.name());
                subscriptionOrderRepository.saveSubscriptionOrder(effectOrder);
                // 清除下一笔订单的信息
                subscriptionOrderRepository.clearNextPlan(effectOrder.getId());
                // 创建退款流水
                createDisputeRefundTradeRecord(tradeRecord);
            }
        }
        // 发送MQ
        paymentDomainService.publishDisputeEvent(tradeRecord, DisputeStatus.CLOSED.name(), disputeResult.name());
    }


    private void createDisputeRefundTradeRecord(TradeRecord tradeRecord) {
        TradeRecord refundRecord = new TradeRecord();
        refundRecord.setTradeNo(PaymentUtils.generateTradeNo(tradeRecord.getUserId()));
        refundRecord.setOutTradeNo(tradeRecord.getOutTradeNo());
        refundRecord.setGmtCreate(new Date());
        refundRecord.setGmtModified(new Date());
        refundRecord.setUserId(tradeRecord.getUserId());
        refundRecord.setAppCode(tradeRecord.getAppCode());
        refundRecord.setPaymentType(tradeRecord.getPaymentType());
        refundRecord.setTradeAmount(tradeRecord.getTradeAmount());
        refundRecord.setTradeCurrency(tradeRecord.getTradeCurrency());
        refundRecord.setTradeDirection(TradeDirection.REFUND);
        refundRecord.setPaymentMethod(tradeRecord.getPaymentMethod());
        refundRecord.setTradeTime(new Date());
        refundRecord.setStatus(TradeRecordStatus.SUCC);
        refundRecord.setHadInitiatePay(true);
        refundRecord.setDeleted(false);

        TradeRecordAttributes attributes = tradeRecord.getAttributes();
        if (attributes == null) {
            attributes = new TradeRecordAttributes(null);
        }
        attributes.setStatusFlowReason(StatusFlowReasonEnum.USER_HAD_DISPUTE.name());
        refundRecord.setAttributes(attributes);
        tradeRecordRepository.createOrUpdateTradeRecord(refundRecord);
    }


}