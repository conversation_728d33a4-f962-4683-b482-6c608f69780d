package com.alibaba.copilot.enabler.service.subscription.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2023/10/7
 */
@ApiModel("退款请求体")
@Data
@Accessors(chain = true)
public class RefundRequestDTO implements Serializable {

    /**
     * 支付机构在支付时生成的唯一id，可用于这一笔支付的退款
     */
    @ApiModelProperty(value = "支付机构在支付时生成的唯一id，可用于这一笔支付的退款", example = "202309271940108001001889F0271342571")
    private String paymentId;

    /**
     * 退款请求id
     */
    @ApiModelProperty(value = "退款请求id", example = "refundRequestId1687697151100")
    private String refundRequestId;

    /**
     * 退款金额
     */
    @ApiModelProperty(value = "退款金额", example = "1")
    private BigDecimal refundAmount;
}
