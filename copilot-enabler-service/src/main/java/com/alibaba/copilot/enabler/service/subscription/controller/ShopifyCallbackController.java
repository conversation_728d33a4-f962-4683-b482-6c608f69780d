package com.alibaba.copilot.enabler.service.subscription.controller;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 2023/10/30
 */
@Slf4j
@RestController
@RequestMapping("shopify/callback")
public class ShopifyCallbackController {

    @PostMapping("onSubscriptionEvent")
    public SingleResult<Void> onSubscriptionEvent(@RequestBody Map<String, Object> params) {
        log.info("onSubscriptionEvent, params={}", JSON.toJSONString(params));
        return SingleResult.buildSuccess(null);
    }
}
