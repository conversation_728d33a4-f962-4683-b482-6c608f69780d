package com.alibaba.copilot.enabler.service.subscription.log.helper;

import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.service.subscription.log.LogSceneCodeEnum;
import com.alibaba.copilot.enabler.service.subscription.log.SubsciptionBizLogger;
import com.alibaba.copilot.enabler.service.subscription.log.UnifyLog;
import com.alibaba.copilot.enabler.service.subscription.log.unifybizlog.SubscriptionEventBizLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * 套餐切换的辅助类, 为了内聚套餐切换的日志埋点逻辑
 *
 * <AUTHOR>
 * @version 2023/11/23
 */
@Slf4j
@Component
public class PlanSwitchHelper {

    @Resource
    private SubscriptionPlanRepository subscriptionPlanRepository;

    /**
     * 套餐切换
     *
     * @param appCode  应用标识
     * @param userId   用户ID
     * @param oldOrder 老套餐对应的订单
     * @param newOrder 新套餐对应的订单
     */
    public void onSwitchPlan(String appCode, Long userId, SubscriptionOrder oldOrder, SubscriptionOrder newOrder) {
        if (oldOrder == null && newOrder == null) {
            log.warn("PlanSwitchHelper#onSwitchPlan, both oldOrder and newOrder is null");
            return;
        }

        String durationUnit = "";
        BigDecimal actualFee = null;
        // 查询当前应用的免费套餐
        SubscriptionPlan freePlan = subscriptionPlanRepository.queryFreePlan(appCode, false);
        if (newOrder != null) {
            //如果本次切换订阅了新套餐，则 durationUnit 和  actualFee是新套餐信息
            SubscriptionPlan newPlan = subscriptionPlanRepository.queryByPlanId(newOrder.getSubscriptionPlanId(), false);
            durationUnit = newPlan != null ? newPlan.getDurationUnit().name() : "";
            actualFee = newOrder.getActualFee() != null ? newOrder.getActualFee() : BigDecimal.ZERO;
        } else {
            //如果本次切换是取消订阅没有新套餐，则 durationUnit 和  actualFee是老套餐信息
            SubscriptionPlan oldPlan = subscriptionPlanRepository.queryByPlanId(oldOrder.getSubscriptionPlanId(), false);
            durationUnit = oldPlan != null ? oldPlan.getDurationUnit().name() : "";
            actualFee = oldOrder.getActualFee() != null ? oldOrder.getActualFee() : BigDecimal.ZERO;
        }

        // 解析出新老状态对应的套餐名称
        String oldPlanName = getPlanName(oldOrder, freePlan);
        String newPlanName = getPlanName(newOrder, freePlan);

        // 构建日志埋点信息
        UnifyLog bizLog = UnifyLog.builder()
                .shopifyShopId(userId)
                .sceneCode(LogSceneCodeEnum.SUBSCRIPTION_EVETN.getSceneCode())
                .unifyBizLog(new SubscriptionEventBizLog()
                        .setOldPlanName(oldPlanName)
                        .setNewPlanName(newPlanName)
                        .setAppCode(appCode)
                        .setDurationUnit(durationUnit)//取消订阅时是被取消套餐的信息
                        .setNewPlanPrice(actualFee)//取消订阅时是被取消套餐的付款价格
                )
                .build();

        // 执行埋点操作
        SubsciptionBizLogger.unifyBizLog(bizLog);
    }

    /**
     * 获取订单对应的套餐名称 (优先取关联的套餐名称, 取不到时取免费套餐的信息)
     */
    private static String getPlanName(@Nullable SubscriptionOrder order, @Nullable SubscriptionPlan freePlan) {
        if (order == null) {
            // 没有订单时, 取免费套餐的信息
            return Optional.ofNullable(freePlan)
                    .map(SubscriptionPlan::getName)
                    .orElse(null);
        } else {
            // 有订单时, 取订单对应套餐的信息
            return order.getSubscriptionPlanName();
        }
    }
}
