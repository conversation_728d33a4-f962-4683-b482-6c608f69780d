package com.alibaba.copilot.enabler.service.payment.schedule;

import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.service.OrderDomainService;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionOrderQuery;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.google.common.collect.Sets;
import com.taobao.eagleeye.EagleEye;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.alibaba.copilot.enabler.service.subscription.utils.BizUtils.isAePayForSubscription;
import static com.alibaba.copilot.enabler.service.subscription.utils.BizUtils.isPicOrderByStripe;

/**
 * 生成定期扣款账单的定时任务
 */
@Component
public class GeneratePeriodicDeductBillTask extends MapJobProcessor {
    public static final Logger TASK_LOG = LoggerFactory.getLogger("taskLogger");

    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;
    @Resource
    private OrderDomainService orderDomainService;

    @Override
    @Monitor(name = "生成定期扣款账单的定时任务", level = Monitor.Level.P1, layer = Monitor.Layer.OTHER)
    public ProcessResult process(JobContext context) {

        try {
            EagleEye.startTrace(null, this.getClass().getName(), EagleEye.TYPE_CUSTOM_MESSAGE_SUB);
            TASK_LOG.info("GeneratePeriodicDeductBillTask start……");

            // 扫描需要扣款的订单（扫描范围为自动续费时间到期前30分钟~到期后30分钟之间）
            long currentTimeMillis = System.currentTimeMillis();

            SubscriptionOrderQuery subscriptionOrderQuery = SubscriptionOrderQuery.builder()
                    .status(Collections.singletonList(SubscriptionOrderStatus.IN_EFFECT))
                    .hadNextRenew(false)
                    .nextRenewalStartTime(new Date(currentTimeMillis - SwitchConfig.scheduledDeductionAdvanceSeconds * 1000))
                    .nextRenewalEndTime(new Date(currentTimeMillis + SwitchConfig.scheduledDeductionAdvanceSeconds * 1000))
                    .build();

            List<SubscriptionOrder> subscriptionOrders = subscriptionOrderRepository.scanSubscriptionOrders(subscriptionOrderQuery);
            Set<Long> orderIdSet = Sets.newHashSet();
            Set<Long> excludeOrderIdSet = Sets.newHashSet();

            for (SubscriptionOrder oldOrder : subscriptionOrders) {
                if (isAePayForSubscription(oldOrder) || isPicOrderByStripe(oldOrder)) {
                    execute(oldOrder);
                    orderIdSet.add(oldOrder.getId());
                } else {
                    Optional.ofNullable(oldOrder).ifPresent(order -> excludeOrderIdSet.add(order.getId()));
                }
            }

            TASK_LOG.info("GeneratePeriodicDeductBillTask completed-ordersCount:{},orderIds:{},excludeOrderIdSet:{}",
                    orderIdSet.size(), JSON.toJSONString(orderIdSet), JSON.toJSONString(excludeOrderIdSet));
            return new ProcessResult(true);
        } catch (Throwable e) {
            TASK_LOG.error("payAlarm-生成定期扣款账单的定时任务失败 error,msg:{}", e.getMessage(), e);
            return new ProcessResult(false);
        } finally {
            EagleEye.endTrace(null);
        }
    }

    /**
     * 处理已到期的订单
     * 自动续约：生成新支付流水
     * 不再续约：更新订单状态
     */
    private void execute(SubscriptionOrder oldOrder) {
        Long orderId = oldOrder.getId();
        String appCode = oldOrder.getAppCode();
        Long userId = oldOrder.getUserId();

        try {
            // 自动续约
            if (oldOrder.getAutoRenew()) {

                // 处理每笔需要代扣的订单
                TradeRecord payRecord = orderDomainService.createPreparePayOrder(oldOrder, appCode, userId);
                TASK_LOG.info("GeneratePeriodicDeductBillTask autoRenew,appCode:{},userId:{},orderId:{},tradeNo:{}",
                        appCode, userId, orderId, payRecord == null ? null : payRecord.getTradeNo());
            }
            // 不续约
            else {
                orderDomainService.handlePerformDueOrder(oldOrder);
                TASK_LOG.info("GeneratePeriodicDeductBillTask endRenew appCode:{},userId:{},orderId:{}",
                        appCode, userId, orderId);
            }
        } catch (Exception e) {
            TASK_LOG.error("payAlarm-生成定期扣款账单的定时任务失败,msg:{},appCode:{},userId:{},orderId:{}",
                    e.getMessage(), appCode, userId, orderId, e);
        }
    }
}
