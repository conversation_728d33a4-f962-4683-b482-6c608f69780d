package com.alibaba.copilot.enabler.service.stripe.processor.charge;

import com.alibaba.copilot.enabler.client.payment.constant.dispute.DisputeStatus;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEventMetadata;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.service.PaymentDomainService;
import com.alibaba.copilot.enabler.service.stripe.StripeConsts;
import com.alibaba.copilot.enabler.service.stripe.processor.AbstractStripeEventProcessor;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeExecuteContext;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 处理 charge.dispute.created webhook 事件
 */

@Slf4j
@Component
public class ChargeDisputeCreatedProcessor4<PERSON>idge extends AbstractStripeEventProcessor {

    @Resource
    protected PaymentDomainService paymentDomainService;

    @Override
    public String getEventType() {
        return StripeConsts.CHARGE_DISPUTE_CREATED;
    }

    @Override
    public String getAppCode() {
        return AppEnum.AIDGE.getCode();
    }

    @Override
    protected void doProcess(StripeExecuteContext context) {
        log.info("ChargeDisputeCreatedProcessor4Aidge context={}", JSON.toJSONString(context));

        StripeEventMetadata metaData = context.getMetaData();
        if (metaData.whetherPaymentMode()) {
            // PAYMENT 模式
            TradeRecord tradeRecord = paymentDomainService.queryByTradeNo(metaData.getTradeNo());
            if (tradeRecord == null) {
                log.error("ChargeDisputeCreatedProcessor4Aidge tradeRecord is null, metaData={}", JSON.toJSONString(metaData));
                return;
            }
            paymentDomainService.publishDisputeEvent(tradeRecord, DisputeStatus.CREATED.name(), null);
        }
    }

}