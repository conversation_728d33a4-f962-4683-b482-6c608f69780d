package com.alibaba.copilot.enabler.service.subscription.strategy.basic;

import com.alibaba.copilot.enabler.client.subscription.dto.NeedPayToSubscribePlanDTO;
import com.alibaba.copilot.enabler.domain.subscription.dto.FinalDiscountDTO;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeNewPlanPriceContext;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeNewPlanPriceResultDTO;
import com.alibaba.copilot.enabler.service.subscription.strategy.BaseSubscriptionStrategy;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

/**
 * 基建应用的订阅处理策略<hr/>
 * 注: 该类与{@link BaseSubscriptionStrategy}的区别是, 前者承载基建订阅的公用逻辑, 后者承载所有订阅的公用逻辑
 *
 * <AUTHOR>
 * @version 2023/10/12
 */
@Slf4j
public abstract class BasicSubscriptionStrategy extends BaseSubscriptionStrategy {

    @Override
    protected Date computeOrderEndTimeForTrial(Date now, long planDays, long remainTrialDays) {
        // 试用期订单, 结束时间 = 当前时间 + 试用期剩余时间 + 套餐持续时间
        return DateUtils.addDays(now, (int) (remainTrialDays + planDays));
    }

    @Override
    public ComputeNewPlanPriceResultDTO computeNewPlanPrice(ComputeNewPlanPriceContext context) {
        // 1. 判断是否需要支付
        Boolean needPay = adjustNeedPayByComputePriceContext(context);

        // 2. 套餐金额
        BigDecimal planAmount = Optional.ofNullable(context.getNewPlan())
                .filter(plan -> !plan.isFree())
                .map(SubscriptionPlan::getPrice)
                .orElse(BigDecimal.ZERO);

        // 3. 折扣金额
        BigDecimal discountAmount = Optional.ofNullable(context.getFinalDiscountDTO())
                .filter(FinalDiscountDTO::getIsDiscount)
                .map(FinalDiscountDTO::getDiscountPrice)
                .orElse(null);

        // 4. 支付金额
        BigDecimal payAmount = Optional.ofNullable(discountAmount).orElse(planAmount);
        boolean finalNeedPay = needPay && payAmount.compareTo(BigDecimal.ZERO) > 0;
        BigDecimal finalPayAmount = finalNeedPay ? payAmount : BigDecimal.ZERO;

        ComputeNewPlanPriceResultDTO result = new ComputeNewPlanPriceResultDTO()
                .setNeedPay(finalNeedPay)
                .setPlanAmount(planAmount)
                .setDiscountAmount(discountAmount)
                .setDeductedAmountOfLastPlan(BigDecimal.ZERO)
                .setPayAmount(finalPayAmount);
        log.info("computeNewPlanPrice, result={}", JSON.toJSONString(result));
        return result;
    }

    @Override
    protected boolean needPayToSubscribePlanWhenPlanNeedCost(NeedPayToSubscribePlanDTO dto, SubscriptionPlan targetPlan) {
        // 基建场景下, 只要套餐需要付钱, 最终的订单都需要支付
        return true;
    }
}
