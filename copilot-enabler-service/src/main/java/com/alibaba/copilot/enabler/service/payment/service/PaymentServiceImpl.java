package com.alibaba.copilot.enabler.service.payment.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.copilot.boot.basic.result.PageResult;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.base.dto.PageDTO;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.payment.dto.*;
import com.alibaba.copilot.enabler.client.payment.request.CashierPayRequest;
import com.alibaba.copilot.enabler.client.payment.request.TradeRecordPageQuery;
import com.alibaba.copilot.enabler.client.payment.request.TradeRecordsQuery;
import com.alibaba.copilot.enabler.client.payment.service.PaymentService;
import com.alibaba.copilot.enabler.client.subscription.dto.QueryTradeRecordDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.QueryTradeRecordResultDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.dto.PaymentSyncResultDTO;
import com.alibaba.copilot.enabler.domain.payment.factory.IPaymentChannelStrategy;
import com.alibaba.copilot.enabler.domain.payment.factory.PaymentChannelFactory;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.payment.service.PaymentDomainService;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.request.InquiryPaymentRequest;
import com.alibaba.copilot.enabler.infra.base.utils.ModelConvertUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 支付服务
 */
@Service
@Slf4j
public class PaymentServiceImpl implements PaymentService {

    @Autowired
    private TradeRecordRepository tradeRecordRepository;
    @Autowired
    private SubscriptionOrderRepository orderRepository;
    @Autowired
    private ConsumerPayBillService consumerPayBillService;
    @Resource
    private PaymentDomainService paymentDomainService;

    @Override
    public SingleResult<CashierPayResultDTO> cashierPay(CashierPayRequest request) {
        log.info("cashierPay, request={}", JSON.toJSONString(request));

        // 0. validate request parameter
        validateCashierPayRequest(request);

        // 1. 发起收银台支付请求
        TradeRecord tradeRecord = paymentDomainService.createCashierPay(request);

        // 2. 处理返回结果
        CashierPayResultDTO resultDTO = buildCashierPayResult(tradeRecord);

        return SingleResult.buildSuccess(resultDTO);
    }


    @Override
    public List<PaymentRecordDTO> queryRecordsByOrderId(Long orderId) {
        TradeRecordsQuery paymentRecordsQuery = TradeRecordsQuery.builder().orderId(orderId).build();
        List<TradeRecord> tradeRecords = tradeRecordRepository.queryTradeRecords(paymentRecordsQuery);
        if (CollectionUtils.isEmpty(tradeRecords)) {
            return new ArrayList<>();
        }
        return tradeRecords.stream().map(record -> PaymentRecordDTO.builder()
                .subscriptionOrderId(record.getSubscriptionOrderId())
                .tradeAmount(record.getTradeAmount())
                .tradeDirection(record.getTradeDirection().name())
                .outTradeNo(record.getOutTradeNo())
                .tradeTime(record.getTradeTime())
                .build()).collect(Collectors.toList());
    }

    @Monitor(name = "[Subscription Service] 支付域-支付结果主动查询", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    @Override
    public SingleResult<InquiryPaymentResultDTO> inquiryPayment(InquiryPaymentDTO paymentDTO) {
        TradeRecord tradeRecord = tradeRecordRepository.queryByTradeNo(paymentDTO.getPaymentRequestId());
        Assertor.asserts(tradeRecord != null, "tradeRecord not exists");

        InquiryPaymentRequest inquiryPaymentRequest = ModelConvertUtils.copyByReflect(paymentDTO, InquiryPaymentRequest::new);
        inquiryPaymentRequest.setAppEnum(AppEnum.getAppByCode(tradeRecord.getAppCode()));

        IPaymentChannelStrategy paymentChannelStrategy = PaymentChannelFactory.getPaymentByApp(tradeRecord.getAppCode());
        SingleResult<PaymentSyncResultDTO> singleResult = paymentChannelStrategy.inquiryPayment(inquiryPaymentRequest);
        return ModelConvertUtils.convertSingResult(singleResult,
                dto -> ModelConvertUtils.copyByReflect(dto, InquiryPaymentResultDTO::new)
        );
    }


    @Override
    public SingleResult<RefundResultDTO> refund(RefundDTO refundDTO) {
        IPaymentChannelStrategy paymentChannelStrategy = PaymentChannelFactory.getPaymentByApp(refundDTO.getAppCode());
        return paymentChannelStrategy.refund(refundDTO);
    }

    @Monitor(name = "[Subscription Service] 支付域-查询交易流水分页信息", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    @Override
    public PageResult<TradeRecordDetailDTO> queryTradeRecordsByPage(TradeRecordPageQuery query) {
        log.info("queryTradeRecordsPage, query={}", JSON.toJSONString(query));

        PageDTO<TradeRecord> recordsPage = tradeRecordRepository.queryTradeRecordsPage(query);
        log.info("queryTradeRecordsPage, query finished, result={}", JSON.toJSONString(recordsPage));

        // 数据模型转换
        Function<TradeRecord, TradeRecordDetailDTO> dtoConverter = record -> {
            Long firstMonthDiscountRuleId = record.getAttributes().getFirstMonthDiscountRuleId();
            TradeRecordForPicCopilotDTO dto = new TradeRecordForPicCopilotDTO();
            dto.setPaymentProductCode(record.getAttributes().getProductCode());
            dto.setOrder(record.getAttributes().getOrder());
            dto.setUseFirstMonthDiscount(firstMonthDiscountRuleId != null);
            return dto;
        };
        List<TradeRecordDetailDTO> recordList = Optional.ofNullable(recordsPage.getList())
                .map(records -> ModelConvertUtils.copyListByReflect(records, dtoConverter))
                .orElseGet(Lists::newArrayList);

        // 注入套餐名称
        injectPlanName(recordList);

        return PageResult.buildSuccess(recordList)
                .setPageNum(recordsPage.getPageNum())
                .setPageSize(recordsPage.getPageSize())
                .setTotal(recordsPage.getTotal())
                .setTotalPage(recordsPage.getTotalPage());
    }

    /**
     * 查询交易流水分页信息
     *
     * @param query 查询参数
     * @return 分页信息
     */
    @Override
    public PageResult<TradeRecordDetailDTO> queryTradeRecordsByPageWithStripe(TradeRecordPageQuery query) {
        log.info("queryTradeRecordsByPageWithStripe, query={}", JSON.toJSONString(query));

        PageDTO<TradeRecord> recordsPage = tradeRecordRepository.queryTradeRecordsPageWithStripe(query);
        log.info("queryTradeRecordsByPageWithStripe, query finished, result={}", JSON.toJSONString(recordsPage));

        // 数据模型转换
        Function<TradeRecord, TradeRecordDetailDTO> dtoConverter = record -> {
            Long firstMonthDiscountRuleId = record.getAttributes().getFirstMonthDiscountRuleId();
            TradeRecordForPicCopilotDTO dto = new TradeRecordForPicCopilotDTO();
            dto.setUseFirstMonthDiscount(firstMonthDiscountRuleId != null);
            dto.setPlanId(record.getAttributes().getSubscriptionPlanId());
            dto.setPlanName(record.getAttributes().getSubscriptionPlanName());
            return dto;
        };
        List<TradeRecordDetailDTO> recordList = Optional.ofNullable(recordsPage.getList())
                .map(records -> ModelConvertUtils.copyListByReflect(records, dtoConverter))
                .orElseGet(Lists::newArrayList);

        return PageResult.buildSuccess(recordList)
                .setPageNum(recordsPage.getPageNum())
                .setPageSize(recordsPage.getPageSize())
                .setTotal(recordsPage.getTotal())
                .setTotalPage(recordsPage.getTotalPage());
    }

    @Override
    public SingleResult<QueryTradeRecordResultDTO> queryTradeRecord(QueryTradeRecordDTO dto) {
        log.info("getTradeRecord, dto={}", JSON.toJSONString(dto));
        String appCode = dto.getAppCode();
        String tradeNo = dto.getTradeNo();
        Long userId = dto.getUserId();

        TradeRecord tradeRecord = tradeRecordRepository.queryByTradeNo(tradeNo);
        Assertor.assertNonNull(tradeRecord, "No trade record found");
        Assertor.asserts(Objects.equals(appCode, tradeRecord.getAppCode()), "appCode not match");
        Assertor.asserts(Objects.equals(userId, tradeRecord.getUserId()), "userId not match");

        QueryTradeRecordResultDTO resultDTO = new QueryTradeRecordResultDTO()
                .setStatus(tradeRecord.getStatus());
        return SingleResult.buildSuccess(resultDTO);
    }

    @Override
    public SingleResult<String> queryBillInvoice(QueryTradeRecordDTO dto) throws IOException {
        String url = consumerPayBillService.queryBillInvoice(AppEnum.getAppByCode(dto.getAppCode()), dto.getUserId(), dto.getTradeNo());
        return SingleResult.buildSuccess(url);
    }

    private void injectPlanName(List<TradeRecordDetailDTO> recordList) {
        if (CollectionUtil.isEmpty(recordList)) {
            return;
        }

        // 查询 orderId -> order 的映射关系
        List<Long> orderIds = recordList.stream()
                .map(TradeRecordDetailDTO::getSubscriptionOrderId)
                .collect(Collectors.toList());
        List<SubscriptionOrder> orders = orderRepository.queryByOrderIds(orderIds);
        Map<Long, SubscriptionOrder> orderId2Order = orders.stream()
                .collect(Collectors.toMap(SubscriptionOrder::getId, o -> o, (k1, k2) -> k1));

        // 设置套餐名称
        recordList.forEach(record -> Optional.ofNullable(record.getSubscriptionOrderId())
                .map(orderId2Order::get)
                .ifPresent(order -> {
                    record.setPlanId(order.getSubscriptionPlanId());
                    record.setPlanName(order.getSubscriptionPlanName());
                })
        );
    }

    private static CashierPayResultDTO buildCashierPayResult(TradeRecord tradeRecord) {
        CashierPayResultDTO resultDTO = new CashierPayResultDTO();

        resultDTO.setTradeNo(tradeRecord.getTradeNo());
        resultDTO.setOutTradeNo(tradeRecord.getOutTradeNo());
        resultDTO.setPaymentAmount(tradeRecord.getTradeAmount());
        resultDTO.setPaymentCreateTime(tradeRecord.getGmtCreate().getTime());
        resultDTO.setRedirectUrl(tradeRecord.getAttributes().getCashierPayRedirectUrl());
        resultDTO.setNormalUrl(tradeRecord.getAttributes().getCashierPayNormalUrl());
        resultDTO.setPaymentSessionData(tradeRecord.getAttributes().getPaymentSessionData());
        resultDTO.setPaymentSessionExpiryTime(tradeRecord.getAttributes().getPaymentSessionExpiryTime());
        resultDTO.setPaymentSessionId(tradeRecord.getAttributes().getPaymentSessionId());
        resultDTO.setEnvironment(tradeRecord.getAttributes().getEnv());
        resultDTO.setClientSecret(tradeRecord.getAttributes().getStripeSessionClientSecret());

        return resultDTO;
    }

    private void validateCashierPayRequest(CashierPayRequest request) {
        Assertor.assertNonNull(request, "request is null");
        Assertor.assertNonNull(AppEnum.getAppByCode(request.getAppCode()), "appCode is null");
        Assertor.assertNonNull(PaymentMethodEnum.ofName(request.getPaymentMethod()), "paymentMethod is null");
        Assertor.assertNonNull(request.getClientType(), "clientType is null");

        request.validateOrder();
    }
}
