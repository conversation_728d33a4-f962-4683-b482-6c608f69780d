package com.alibaba.copilot.enabler.service.payment.schedule.handler;

import cn.hutool.core.date.DateUtil;
import com.alibaba.aepay.fund.business.api.payment.dto.Buyer;
import com.alibaba.aepay.fund.business.api.payment.dto.Goods;
import com.alibaba.aepay.fund.business.api.payment.dto.Order;
import com.alibaba.copilot.boot.basic.enums.IEnum;
import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.tools.verify.AssertionError;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.email.dto.DeductFailureEmailDTO;
import com.alibaba.copilot.enabler.client.email.dto.SeoDeductFailureEmailDTO;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.constant.StatusFlowReasonEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageDirectionEnum;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageStatusEnum;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageTypeEnum;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionCompleteReason;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.base.email.EdmService;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.domain.payment.dto.PaymentAsyncResultDTO;
import com.alibaba.copilot.enabler.domain.payment.factory.IPaymentChannelStrategy;
import com.alibaba.copilot.enabler.domain.payment.factory.PaymentChannelFactory;
import com.alibaba.copilot.enabler.domain.payment.model.MessageInfo;
import com.alibaba.copilot.enabler.domain.payment.model.MessageRecord;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentInfo;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentToken;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.repository.MessageRecordRepository;
import com.alibaba.copilot.enabler.domain.payment.repository.PaymentTokenRepository;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.payment.service.OrderDomainService;
import com.alibaba.copilot.enabler.domain.subscription.event.SubscriptionPlanChangedEventProducer;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.request.InitiatePaymentRequest;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategyFactory;
import com.alibaba.copilot.enabler.domain.user.model.AEPayInfo;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import com.alibaba.copilot.enabler.service.subscription.service.pic.PicStripeSubscriptionService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.money.Money;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.alibaba.copilot.enabler.service.subscription.utils.BizUtils.isPicOrderByStripe;

/**
 * 发起代扣支付handler
 */
@Slf4j
@Component
public class InitiateDeductionHandler {

    public static final Long DEDUCT_FAILURE_EMAIL_ID = 288L;
    @Autowired
    private MessageRecordRepository messageRecordRepository;
    @Autowired
    private TradeRecordRepository tradeRecordRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private OrderDomainService orderDomainService;
    @Autowired
    private SubscriptionOrderRepository subscriptionOrderRepository;
    @Autowired
    private SubscriptionPlanChangedEventProducer subscriptionPlanChangedEventProducer;
    @Autowired
    private PaymentTokenRepository paymentTokenRepository;
    @Autowired
    private EdmService edmService;
    @Resource
    private PicStripeSubscriptionService picStripeSubscriptionService;

    /**
     * 支付后跳转地址
     */
    private final static String PAYMENT_REDIRECT_URL = "https://www.dscopilot.ai/";

    /**
     * 发起支付逻辑
     */
    @Transactional(rollbackFor = Exception.class)
    public void initiateDeduction(TradeRecord tradeRecord) {
        // 查询订阅订单
        SubscriptionOrder order = orderDomainService.querySubscriptionOrder(tradeRecord,
                SubscriptionOrderStatus.PENDING_PAYMENT);
        if (order == null) {
            return;
        }

        if (isPicOrderByStripe(order)) {
            picStripeSubscriptionService.chargeForNewSubscriptionByStripe(tradeRecord);
            return;
        }

        // 构建发起支付request
        InitiatePaymentRequest request = buildInitiatePaymentRequest(tradeRecord);
        if (request == null) {
            return;
        }

        // 保存本地消息
        MessageInfo messageInfo = MessageInfo.builder()
                .directionEnum(MessageDirectionEnum.OUT)
                .entityId(tradeRecord.getTradeNo())
                .typeEnum(MessageTypeEnum.PERIODIC_INITIATE_DEDUCTION)
                .notifyContentOrRequestParam(JSON.toJSONString(request))
                .response(ConsumeConcurrentlyStatus.CONSUME_SUCCESS.name())
                .statusEnum(MessageStatusEnum.WAITING)
                .build();
        MessageRecord messageRecord = orderDomainService.saveMessageRecord(messageInfo, tradeRecord.getAppCode(), tradeRecord.getUserId());

        TradeRecord toUpdateTradeRecord = new TradeRecord();
        toUpdateTradeRecord.setId(tradeRecord.getId());
        // 发起支付
        String outTradeNo = initiatePay(request, messageRecord);
        toUpdateTradeRecord.setOutTradeNo(outTradeNo);

        // 发起支付失败
        if (messageRecord.getStatus() == MessageStatusEnum.FAIL) {
            toUpdateTradeRecord.setStatus(TradeRecordStatus.FAIL);
        }

        // 更新已发起支付
        toUpdateTradeRecord.setHadInitiatePay(true);
        tradeRecordRepository.createOrUpdateTradeRecord(toUpdateTradeRecord);
    }

    /**
     * 发起支付失败
     */
    @Transactional(rollbackFor = Exception.class)
    public void initiateDeductionFail(TradeRecord payRecord) {
        String appCode = payRecord.getAppCode();
        Long userId = payRecord.getUserId();

        // 1. 取消待支付的流水
        PaymentInfo paymentInfo = PaymentInfo.builder()
                .outTradeNo(payRecord.getTradeNo())
                .tradeTime(System.currentTimeMillis())
                .build();
        orderDomainService.cancelPendingPaymentOrder(payRecord, null, paymentInfo, StatusFlowReasonEnum.PERIODIC_DEDUCT_FAIL);

        // 2. 完结当前生效的订单
        SubscriptionOrder oldEffectOrder = completeCurrentEffectOrderIfNeed(appCode, userId);

        // 3. 发送老套餐完结事件
        if (oldEffectOrder != null) {
            subscriptionPlanChangedEventProducer.sendCompleteEvent(oldEffectOrder, SubscriptionCompleteReason.FAIL_TO_RENEW);
        }

        // 4. 发送「扣费失败」的邮件
        sendDeductFailureEmail(payRecord);
    }

    /**
     * 完结当前生效的订单
     */
    public SubscriptionOrder completeCurrentEffectOrderIfNeed(String appCode, Long userId) {
        SubscriptionOrder oldEffectOrder = subscriptionOrderRepository.queryEffectOrder(appCode, userId);
        if (oldEffectOrder != null) {
            oldEffectOrder.setStatus(SubscriptionOrderStatus.COMPLETED);
            oldEffectOrder.setAutoRenew(false);
            oldEffectOrder.setPerformEndTime(DateUtil.date());
            oldEffectOrder.setGmtModified(DateUtil.date());
            subscriptionOrderRepository.saveSubscriptionOrder(oldEffectOrder);
        }
        return oldEffectOrder;
    }

    /**
     * 发送「扣费失败」的邮件
     */
    public void sendDeductFailureEmail(TradeRecord payRecord) {
        if (payRecord.getPaymentType() == PaymentTypeEnum.PERIODIC_DEDUCT) {
            // 仅对定期代扣做处理
            String appCode = payRecord.getAppCode();
            Long userId = payRecord.getUserId();
            AppEnum appEnum = AppEnum.getAppByCode(appCode);
            SubscriptionOrder order = subscriptionOrderRepository.getByOrderId(payRecord.getSubscriptionOrderId());
            if (appEnum == AppEnum.DS_COPILOT) {
                // 如果是DS_COPILOT业务，创建特定的邮件DTO并调用对应的邮件发送策略
//                DsCopilotDeductFailureEmailDTO emailDTO = new DsCopilotDeductFailureEmailDTO()
//                    .setUserName(order.getEmail())
//                    .setAppName(appEnum.getName())
//                    .setPlanName(order.getSubscriptionPlanName());
//                SubscriptionStrategyFactory.getStrategy(appEnum).sendEmail(userId, emailDTO);
                User user = userRepository.getUser(userId);
                Map<String, Object> params = new HashMap<>();
                params.put(EdmService.USER_NAME_KEY, user.getEmail());
                edmService.triggerEmail(DEDUCT_FAILURE_EMAIL_ID, user.getEmail(), params);
            } else if (Objects.equals(appCode, AppEnum.SEO_COPILOT_SITE.getCode())) {
                //SEO_COPILOT_SITE
                SeoDeductFailureEmailDTO emailDTO = new SeoDeductFailureEmailDTO()
                        .setOrderNo(payRecord.getTradeNo())
                        .setAppName(appEnum.getName())
                        .setPlanName(order.getSubscriptionPlanName());
                SubscriptionStrategyFactory.getStrategy(appEnum, order.getSubscriptionPayType()).sendEmail(userId, emailDTO);
            } else {
                // 如果是其他业务，创建通用的邮件DTO并调用对应的邮件发送策略
                DeductFailureEmailDTO emailDTO = new DeductFailureEmailDTO()
                        .setOrderNo(payRecord.getTradeNo())
                        .setAppName(appEnum.getName())
                        .setPlanName(order.getSubscriptionPlanName());
//                SubscriptionStrategyFactory.getStrategy(appEnum).sendEmail(userId, emailDTO);
                SubscriptionStrategyFactory.getStrategy(appEnum, order.getSubscriptionPayType()).sendEmail(userId, emailDTO);
            }
        }
    }


    @Retryable(value = {RuntimeException.class, BizException.class, AssertionError.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000, multiplier = 2))
    public String initiatePay(InitiatePaymentRequest request, MessageRecord messageRecord) {
        IPaymentChannelStrategy paymentChannelStrategy = PaymentChannelFactory.getPaymentByApp(messageRecord.getAppCode());
        SingleResult<PaymentAsyncResultDTO> paymentResult = paymentChannelStrategy.initiatePay(request);
        Assertor.assertResult(paymentResult, "initiatePay error", Collections.singletonList(request));

        PaymentAsyncResultDTO paymentResultDTO = paymentResult.getData();

        messageRecord.setRetryCount(messageRecord.getRetryCount() + 1);

        if (paymentResultDTO != null && Objects.equals(paymentResultDTO.getResultStatus(), AEPaymentConstants.RESULT_STATUS_UNKNOWN)) {
            messageRecord.setStatus(MessageStatusEnum.WAITING);
        } else {
            messageRecord.setStatus(paymentResultDTO != null
                    && Objects.equals(paymentResultDTO.getResultStatus(), AEPaymentConstants.RESULT_STATUS_SUCCESS)
                    ? MessageStatusEnum.SUCCESS : MessageStatusEnum.FAIL);
        }

        messageRecord.setCallResponse(JSON.toJSONString(paymentResultDTO, SerializerFeature.IgnoreNonFieldGetter));
        messageRecordRepository.createOrUpdateMessageRecord(messageRecord);

        if (messageRecord.getStatus() == MessageStatusEnum.FAIL) {
            throw new AssertionError(ErrorCode.SYS_ERROR, "aepayGateway initiatePay fail", "aepayGateway initiatePay fail");
        }

        return paymentResultDTO.getPaymentId();
    }

    /**
     * 构建发起支付request
     */
    @Nullable
    private InitiatePaymentRequest buildInitiatePaymentRequest(TradeRecord tradeRecord) {
        String appCode = tradeRecord.getAppCode();
        Long userId = tradeRecord.getUserId();
        Long subscriptionOrderId = tradeRecord.getSubscriptionOrderId();
        PaymentMethodEnum paymentMethod = PaymentMethodEnum.ofName(tradeRecord.getPaymentMethod());

        InitiatePaymentRequest request = new InitiatePaymentRequest();
        request.setAppEnum(AppEnum.getAppByCode(appCode));
        request.setPaymentMethod(paymentMethod);

        User user = userRepository.getUser(userId);
        if (PaymentMethodEnum.isAlipayPaymentMethod(paymentMethod)) {
            // Alipay支付
            Map<PaymentMethodEnum, PaymentToken> pythonMethod2Token = paymentTokenRepository.queryAuthedTokensByUser(appCode, userId);
            PaymentToken paymentToken = pythonMethod2Token.get(paymentMethod);
            if (paymentToken == null) {
                // 告警通知
                log.error("payAlarm-定期代扣失败 no alipayToken,orderId:{},appCode:{},userId:{}",
                        subscriptionOrderId, appCode, userId);
                initiateDeductionFail(tradeRecord);
                return null;
            }

            // 填充Alipay token信息
            request.setAlipayToken(paymentToken.getToken());
        } else if (paymentMethod == PaymentMethodEnum.CREDIT_CARD) {
            // 信用卡支付
            AEPayInfo aePayInfo = user.getLatestPayCardInfo();
            // 没有卡token信息订单状态改为失败
            if (aePayInfo == null) {
                // 告警通知
                log.error("payAlarm-定期代扣失败 no cardToken,orderId:{},appCode:{},userId:{}",
                        subscriptionOrderId, appCode, userId);
                initiateDeductionFail(tradeRecord);
                return null;
            }

            // 填充卡token信息
            request.setCardToken(aePayInfo.getCardToken());
            request.setNetworkTransactionId(aePayInfo.getNetworkTransactionId());
        } else {
            log.error("paymentMethod not support: {}", paymentMethod);
            initiateDeductionFail(tradeRecord);
            return null;
        }

        request.setPaymentRequestId(tradeRecord.getTradeNo());

        Money orderAmount = Money.of(tradeRecord.getTradeAmount(), AEPaymentConstants.CURRENCY_CODE);
        request.setPaymentAmount(orderAmount);

        request.setOrder(builOrder(tradeRecord, orderAmount, user));

        // 支付完成跳转地址，协议代扣的没有任何意思，但必须要传，只能写死一个
        request.setPaymentRedirectUrl(PAYMENT_REDIRECT_URL);

        return request;
    }

    /**
     * 构建订单
     */
    @NotNull
    private static Order builOrder(TradeRecord tradeRecord, Money orderAmount, User user) {
        Order order = new Order();
        order.setOrderAmount(orderAmount);
        order.setReferenceOrderId(String.valueOf(tradeRecord.getSubscriptionOrderId()));
        order.setOrderDescription(IEnum.of(AppEnum.class, tradeRecord.getAppCode()).getCode());

        Goods goods = buildOrderGoods(tradeRecord);
        order.setGoods(Lists.newArrayList(goods));

        Buyer buyer = new Buyer();
        buyer.setReferenceBuyerId(String.valueOf(tradeRecord.getUserId()));
        buyer.setBuyerRegistrationTime(user.getAttributes().getRegisterTime().getTime());
        order.setBuyer(buyer);
        return order;
    }

    /**
     * 构建订单上的商品
     */
    @NotNull
    private static Goods buildOrderGoods(TradeRecord tradeRecord) {
        Goods goods = new Goods();
        goods.setReferenceGoodsId(String.valueOf(tradeRecord.getAttributes().getSubscriptionPlanId()));
        goods.setGoodsName(tradeRecord.getAttributes().getSubscriptionPlanName());
        goods.setGoodsQuantity(1);
        goods.setDeliveryMethodType(AEPaymentConstants.GOODS_DELIVERY_METHOD_TYPE);
        return goods;
    }
}
