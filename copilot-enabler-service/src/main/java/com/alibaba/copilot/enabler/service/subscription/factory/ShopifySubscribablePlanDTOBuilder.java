package com.alibaba.copilot.enabler.service.subscription.factory;

import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifySubscribablePlansResultDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.subscription.dto.ShopifyDiscountResultDTO;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionFeature;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/10/30
 */
@Setter
public class ShopifySubscribablePlanDTOBuilder implements Builder<ShopifySubscribablePlansResultDTO.PlanDTO> {
    private ShopifyDiscountResultDTO discountResultDTO;
    private SubscriptionPlan subscriptionPlan;
    private List<SubscriptionPlan> plans;

    @Override
    public ShopifySubscribablePlansResultDTO.PlanDTO build() {
        List<String> featureNames = subscriptionPlan.getFeatures()
                .stream()
                .map(SubscriptionFeature::getName)
                .collect(Collectors.toList());

        boolean hasDiscount = discountResultDTO != null && discountResultDTO.getHasDiscount();
        return new ShopifySubscribablePlansResultDTO.PlanDTO()
                .setPlanId(subscriptionPlan.getId())
                .setPlanName(subscriptionPlan.getName())
                .setAppName(AppEnum.getAppByCode(subscriptionPlan.getAppCode()).getName())
                .setFeatureNames(featureNames)
                .setOriginPlanPrice(subscriptionPlan.getPrice().setScale(2, RoundingMode.HALF_UP))
                .setDiscount(hasDiscount ? discountResultDTO.getDiscountRate().multiply(BigDecimal.valueOf(10)).setScale(1, RoundingMode.HALF_UP): null)
                .setDiscountPlanPrice(hasDiscount ? discountResultDTO.getDiscountPrice().setScale(2, RoundingMode.HALF_UP) : null)
                .setDiscountEndTime(discountResultDTO.getDiscountEndTime())
                .setCrossedPrice(getCrossedPrice())
                .setPlanDuration(subscriptionPlan.getDuration())
                .setPlanDurationUnit(subscriptionPlan.getDurationUnit().name())
                .setPlanDescription(subscriptionPlan.getDescription())
                ;
    }


    private BigDecimal getCrossedPrice() {
//        if (subscriptionPlan.getDurationUnit() == DurationUnit.MONTH) {
//            return null;
//        }
        if(subscriptionPlan.getAttributes() != null) {
            Double crossedPrice = subscriptionPlan.getAttributes().getAsDouble("crossedPrice");
            if(crossedPrice != null) {
                return BigDecimal.valueOf(crossedPrice);
            }
        }
        if (subscriptionPlan.getDurationUnit() == DurationUnit.YEAR) {
            Optional<SubscriptionPlan> opSubscriptionPlan = plans.stream().
                    filter(plan -> StringUtils.equals(plan.getName(), subscriptionPlan.getName()) &&
                            StringUtils.equals(plan.getDurationUnit().name(), DurationUnit.MONTH.name())).findFirst();
            if (opSubscriptionPlan.isPresent()) {
                SubscriptionPlan monthPlan = opSubscriptionPlan.get();
                return monthPlan.getPrice().multiply(new BigDecimal(12));
            } else {
                return null;
            }
        }
        return null;
    }
}
