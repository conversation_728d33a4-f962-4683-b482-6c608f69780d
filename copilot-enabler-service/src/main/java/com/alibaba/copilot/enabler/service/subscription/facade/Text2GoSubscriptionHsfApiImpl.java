package com.alibaba.copilot.enabler.service.subscription.facade;

import java.util.List;
import java.util.Map;
import java.util.Date;
import java.util.stream.Collectors;
import java.util.Collections;
import java.util.Calendar;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.Set;
import java.util.Arrays;

import javax.annotation.Resource;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.dto.DscSubscriptionInfoDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.DscSubscriptionUsageInfoDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyFeatureAllDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyFeatureQuery;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifySubscribedPlanQueryDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifySubscribedPlanResultDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.TrialDurationDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.UseSubscriptionFeatureDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.UserFeatureRewardsDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.UserInvitationRewardDTO;
import com.alibaba.copilot.enabler.client.subscription.enums.FeatureUseType;
import com.alibaba.copilot.enabler.client.subscription.facade.DscSubscriptionHsfApi;
import com.alibaba.copilot.enabler.client.subscription.facade.Text2GoSubscriptionHsfApi;
import com.alibaba.copilot.enabler.client.subscription.service.ShopifySubscriptionService;
import com.alibaba.copilot.enabler.client.subscription.service.Text2GoSubscriptionService;
import com.alibaba.copilot.enabler.client.subscription.service.UserInvitationRewardService;
import com.alibaba.copilot.enabler.client.subscription.service.UserInvitationService;
import com.alibaba.copilot.enabler.domain.subscription.model.FeatureUsage;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.infra.base.utils.ModelConvertUtils;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.FeatureUsageDO;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionFeatureDO;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionPlanFeatureDO;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.UserInvitationRewardDO;
import com.alibaba.copilot.enabler.infra.subscription.factory.FeatureUsageConverter;
import com.alibaba.copilot.enabler.infra.subscription.mapper.FeatureUsageMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionFeatureMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionPlanFeatureMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.UserInvitationRewardMapper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionOrderQuery;

/**
 * @ClassName Text2GoSubscriptionHsfApi
 * <AUTHOR>
 * @Date 2025/2/21 14:13
 */
@Slf4j
@Service
@HSFProvider(serviceInterface = Text2GoSubscriptionHsfApi.class)
public class Text2GoSubscriptionHsfApiImpl implements Text2GoSubscriptionHsfApi {

    @Resource
    private Text2GoSubscriptionService text2GoSubscriptionService;

    @Resource
    private FeatureUsageMapper featureUsageMapper;

    @Resource
    private SubscriptionFeatureMapper subscriptionFeatureMapper;

    @Resource
    private ShopifySubscriptionService shopifySubscriptionService;

    @Resource
    private SubscriptionPlanFeatureMapper subscriptionPlanFeatureMapper;

    @Resource
    private UserInvitationRewardService userInvitationRewardService;

    @Resource
    private UserInvitationService userInvitationService;

    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;

    @Autowired
    private UserInvitationRewardMapper userInvitationRewardMapper;

    @Override
    public SingleResult<DscSubscriptionUsageInfoDTO> currentSubscriptionInfo(Long userId) {
        log.info("Text2Go currentSubscriptionInfo start, userId={}", userId);
        try {
            DscSubscriptionUsageInfoDTO infoDTO = text2GoSubscriptionService.currentSubscriptionInfo(userId);

            // 查询用户未过期的邀请奖励额度并添加到结果中
            if (infoDTO != null && infoDTO.getFeatureInfoList() != null) {
                Date currentTime = new Date();
                addInvitationRewardToSubscriptionInfo(infoDTO, userId, currentTime);
            }

            log.info("Text2Go currentSubscriptionInfo success, userId={}, result={}", userId, JSON.toJSONString(infoDTO));
            return SingleResult.buildSuccess(infoDTO);
        } catch (Exception e) {
            log.error("Text2Go currentSubscriptionInfo error, userId={}, error={}", userId, e.getMessage(), e);
            return SingleResult.buildSuccess(null);
        }
    }

    /**
     * Add invitation rewards to subscription information
     *
     * @param infoDTO subscription information DTO
     * @param userId user ID
     * @param currentTime current time
     */
    private void addInvitationRewardToSubscriptionInfo(DscSubscriptionUsageInfoDTO infoDTO, Long userId, Date currentTime) {
        try {
            // Get all valid reward types
            List<String> rewardTypes = userInvitationRewardService.getValidRewardTypes(userId, currentTime);

            if (CollectionUtils.isEmpty(rewardTypes)) {
                return;
            }

            log.info("Found user invitation reward types: {}", JSON.toJSONString(rewardTypes));

            // Add or update feature info for each reward type
            for (String rewardType : rewardTypes) {
                Integer rewardAmount = userInvitationRewardService.getValidRewardAmount(userId, rewardType, currentTime);
                if (rewardAmount == null || rewardAmount <= 0) {
                    continue;
                }

                log.info("User invitation reward amount: type={}, amount={}", rewardType, rewardAmount);

                // Check if feature info of this type already exists
                boolean found = false;
                for (DscSubscriptionUsageInfoDTO.SubscriptionFeatureUsageInfo featureInfo : infoDTO.getFeatureInfoList()) {
                    if (rewardType.equals(featureInfo.getType())) {
                        // Update existing feature quota
                        // quota is original amount, usageCount is used amount
                        featureInfo.setQuota(featureInfo.getQuota() + rewardAmount);
                        found = true;
                        break;
                    }
                }

                // If feature info doesn't exist, add new feature info
                if (!found) {
                    DscSubscriptionUsageInfoDTO.SubscriptionFeatureUsageInfo newFeatureInfo = new DscSubscriptionUsageInfoDTO.SubscriptionFeatureUsageInfo();
                    newFeatureInfo.setType(rewardType);
                    newFeatureInfo.setName(getFeatureNameByType(rewardType));
                    newFeatureInfo.setQuota((long) rewardAmount);
                    newFeatureInfo.setUsageCount(0L);
                    newFeatureInfo.setIsDepletion(true);
                    newFeatureInfo.setDescription("Invitation Reward Quota");

                    infoDTO.getFeatureInfoList().add(newFeatureInfo);
                }
            }
        } catch (Exception e) {
            log.error("Error adding invitation rewards to subscription info: {}", e.getMessage(), e);
        }
    }

    /**
     * Get feature name by feature type
     *
     * @param featureType feature type
     * @return feature name
     */
    private String getFeatureNameByType(String featureType) {
        switch (featureType) {
            case "ai_detection":
                return "AI detection";
            case "ai_rewrite":
                return "AI rewrite";
            case "ai_detection_limit":
                return "AI detection limit";
            case "total_words_per_month":
                return "Total words per month";
            default:
                return featureType;
        }
    }

    @Override
    public SingleResult<DscSubscriptionInfoDTO> useSubscriptionFeature(UseSubscriptionFeatureDTO featureLimitDTO,
        Long usageCount) {
        if (featureLimitDTO == null || featureLimitDTO.getUserId() == null || featureLimitDTO.getUseType() == null) {
            log.warn("Text2Go useSubscriptionFeature invalid params, featureLimitDTO={}, usageCount={}", 
                JSON.toJSONString(featureLimitDTO), usageCount);
            return SingleResult.buildSuccess(null);
        }

        if (!FeatureUseType.USE.equals(featureLimitDTO.getUseType())) {
            log.info("Text2Go useSubscriptionFeature not USE type, useType={}", featureLimitDTO.getUseType());
            return SingleResult.buildSuccess(null);
        }

        try {
            log.info("Text2Go useSubscriptionFeature start, featureLimitDTO={}, usageCount={}", 
                JSON.toJSONString(featureLimitDTO), usageCount);
            
            // 查询当前订阅的使用信息
            List<FeatureUsageDO> featureUsageDOS = queryFeatureUsage(featureLimitDTO);
            
            // 查询当前的订阅信息
            DscSubscriptionUsageInfoDTO usageInfoDTO = text2GoSubscriptionService.currentSubscriptionInfo(
                featureLimitDTO.getUserId());
            log.info("Text2Go useSubscriptionFeature query subscription info, result={}", JSON.toJSONString(usageInfoDTO));
            
            // 更新使用信息
            DscSubscriptionInfoDTO result = updateUsageInfo(usageInfoDTO, featureUsageDOS, featureLimitDTO, usageCount);
            
            log.info("Text2Go useSubscriptionFeature complete, result={}", JSON.toJSONString(result));
            return SingleResult.buildSuccess(result);
        } catch (Exception e) {
            log.error("Text2Go useSubscriptionFeature error, error={}", e.getMessage(), e);
            return SingleResult.buildSuccess(null);
        }
    }
    
    private List<FeatureUsageDO> queryFeatureUsage(UseSubscriptionFeatureDTO featureLimitDTO) {
        QueryWrapper<FeatureUsageDO> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", featureLimitDTO.getUserId());
        wrapper.eq("app_code", featureLimitDTO.getAppCode());
        wrapper.eq("deleted", 0);
        
        List<FeatureUsageDO> featureUsageDOS = featureUsageMapper.selectList(wrapper);
        log.info("Text2Go useSubscriptionFeature query usage, result size={}", 
            featureUsageDOS == null ? 0 : featureUsageDOS.size());
        return featureUsageDOS;
    }

    private DscSubscriptionInfoDTO updateUsageInfo(DscSubscriptionUsageInfoDTO usageInfoDTO,
        List<FeatureUsageDO> featureUsageDOS, UseSubscriptionFeatureDTO featureLimitDTO, Long usageCount) {
        log.info("Text2Go updateUsageInfo start, usageInfoDTO={}, featureUsageDOS={}, featureLimitDTO={}, usageCount={}", 
            JSON.toJSONString(usageInfoDTO), JSON.toJSONString(featureUsageDOS), 
            JSON.toJSONString(featureLimitDTO), usageCount);

        DscSubscriptionInfoDTO subscriptionInfoDTO = new DscSubscriptionInfoDTO();

        // 确定计划名称
        String planNameStr = null;
        if (usageInfoDTO == null) {
            planNameStr = "Basic";
            log.info("Text2Go updateUsageInfo no subscription, use default plan={}", planNameStr);
        } else {
            planNameStr = usageInfoDTO.getSubscriptionPlanName();
            log.info("Text2Go updateUsageInfo found subscription plan={}", planNameStr);
        }

        subscriptionInfoDTO.setPlanName(planNameStr);
        subscriptionInfoDTO.setFeatureType(featureLimitDTO.getFeatureType());

        try {
            // 查询当前订阅计划
            ShopifySubscribedPlanQueryDTO shopifySubscribedPlanQueryDTO = new ShopifySubscribedPlanQueryDTO()
                .setAppCode(featureLimitDTO.getAppCode())
                .setUserId(featureLimitDTO.getUserId());
            ShopifySubscribedPlanResultDTO subscribedPlan = shopifySubscriptionService.getSubscribedPlan(
                shopifySubscribedPlanQueryDTO);
            log.info("Text2Go updateUsageInfo query subscribed plan, query={}, result={}",
                JSON.toJSONString(shopifySubscribedPlanQueryDTO), JSON.toJSONString(subscribedPlan));

            if (subscribedPlan != null) {
                featureLimitDTO.setPlanId(subscribedPlan.getPlanId());
            }

            // 先判断特性是否为消耗型特性，以决定是否需要消耗奖励额度
            boolean isDepletionFeature = isDepletionFeature(featureLimitDTO);
            log.info("Text2Go updateUsageInfo feature is depletion={}, featureType={}",
                isDepletionFeature, featureLimitDTO.getFeatureType());

            // 保留原始请求使用量
            Long originalUsageCount = usageCount;

            // 只有消耗型特性才考虑消耗邀请奖励额度和检查超限
            if (isDepletionFeature) {
                // 获取订阅计划剩余配额
                long subscriptionRemainingQuota = 0;
                
                // 存在特性使用记录
                if (!CollectionUtils.isEmpty(featureUsageDOS)) {
                    if (subscribedPlan == null) {
                        log.warn("Text2Go updateUsageInfo no subscribed plan found");
                        return null;
                    }

                    // 获取当前计划中的使用记录
                    Date currentTime = new Date();
                    FeatureUsageDO first = featureUsageDOS.stream().filter(usage -> {
                        if (StringUtils.isNotBlank(usage.getAttributes()) && !"{}".equals(usage.getAttributes())) {
                            JSONObject jsonObject = JSONObject.parseObject(usage.getAttributes());
                            if (jsonObject != null) {
                                String typeFromJson = String.valueOf(jsonObject.get("featureType"));
                                String planName = String.valueOf(jsonObject.get("planName"));

                                // 检查当前时间是否在使用记录的有效期内
                                boolean isInValidPeriod = currentTime.after(usage.getStartTime()) &&
                                    currentTime.before(usage.getEndTime());

                                return StringUtils.isNotBlank(typeFromJson) &&
                                    subscribedPlan.getPlanName().equals(planName) &&
                                    typeFromJson.equals(featureLimitDTO.getFeatureType()) &&
                                    isInValidPeriod; // 添加有效期检查
                            }
                        }
                        return false;
                    }).findFirst().orElse(null);

                    log.info("Text2Go updateUsageInfo filter usage record, result={}", JSON.toJSONString(first));

                    if (first == null) {
                        // 没有匹配的使用记录，应从订阅计划获取初始配额
                        log.info("Text2Go updateUsageInfo no matching usage record found, attempting to get quota from subscription plan");
                        
                        // 从usageInfoDTO中获取特性配额信息
                        if (usageInfoDTO != null && !CollectionUtils.isEmpty(usageInfoDTO.getFeatureInfoList())) {
                            for (DscSubscriptionUsageInfoDTO.SubscriptionFeatureUsageInfo featureInfo : usageInfoDTO.getFeatureInfoList()) {
                                if (featureInfo.getType() != null && 
                                    featureInfo.getType().equalsIgnoreCase(featureLimitDTO.getFeatureType()) && 
                                    featureInfo.getQuota() != null) {
                                    
                                    // 找到匹配的特性配额
                                    subscriptionRemainingQuota = featureInfo.getQuota();
                                    log.info("Text2Go updateUsageInfo found initial quota from subscription plan: type={}, quota={}", 
                                        featureInfo.getType(), subscriptionRemainingQuota);
                                    break;
                                }
                            }
                        }
                        
                        if (subscriptionRemainingQuota == 0) {
                            log.warn("Text2Go updateUsageInfo could not find quota from subscription plan for feature type: {}", 
                                featureLimitDTO.getFeatureType());
                        }
                    } else {
                        // 计算订阅计划剩余配额
                        long totalUsageCount = calculateTotalUsage(featureUsageDOS, featureLimitDTO);
                        subscriptionRemainingQuota = Math.max(first.getQuota() - totalUsageCount, 0);
                        log.info("Text2Go updateUsageInfo calculated subscription remaining quota: {}", subscriptionRemainingQuota);
                    }
                }

                // 从user_invitation_reward表中查询未过期的可用额度
                Date currentTime = new Date();
                Long userId = featureLimitDTO.getUserId();
                String featureType = featureLimitDTO.getFeatureType();

                Integer invitationRemainingAmount = userInvitationRewardService.getValidRemainingAmount(
                    userId, featureType, currentTime);

                log.info("Text2Go updateUsageInfo query invitation reward remaining amount, userId={}, featureType={}, result={}",
                    userId, featureType, invitationRemainingAmount);

                // 计算总可用额度
                long totalAvailableQuota = subscriptionRemainingQuota + (invitationRemainingAmount != null ? invitationRemainingAmount : 0);
                log.info("Text2Go updateUsageInfo total available quota: subscription={}, invitation={}, total={}",
                    subscriptionRemainingQuota, invitationRemainingAmount, totalAvailableQuota);

                // 判断总配额是否足够
                if (originalUsageCount > totalAvailableQuota) {
                    log.warn("Text2Go updateUsageInfo quota exceeded, requested={}, available={}", originalUsageCount, totalAvailableQuota);
                    
                    // 超限 - 返回超限信息
                    QueryWrapper<SubscriptionFeatureDO> queryWrapper = new QueryWrapper<SubscriptionFeatureDO>();
                    queryWrapper.eq("name", featureLimitDTO.getFeatureName() + planNameStr);
                    queryWrapper.eq("app_code", featureLimitDTO.getAppCode());

                    List<SubscriptionFeatureDO> subscriptionFeatureDOS = subscriptionFeatureMapper.selectList(
                        queryWrapper);
                    log.info("Text2Go updateUsageInfo query feature, result={}",
                        JSON.toJSONString(subscriptionFeatureDOS));

                    if (!CollectionUtils.isEmpty(subscriptionFeatureDOS)) {
                        SubscriptionFeatureDO subscriptionFeatureDO = subscriptionFeatureDOS.get(0);
                        if (subscriptionFeatureDO != null) {
                            subscriptionInfoDTO.setFeatureId(subscriptionFeatureDO.getId());
                        }
                    }

                    subscriptionInfoDTO.setPlanName(subscribedPlan.getPlanName());
                    subscriptionInfoDTO.setFeatureName(featureLimitDTO.getFeatureName());
                    return subscriptionInfoDTO;
                }
                subscriptionInfoDTO.setQuota(String.valueOf(totalAvailableQuota));
                subscriptionInfoDTO.setUsageCount(String.valueOf(originalUsageCount));

                // 总配额足够，开始扣除
                // 先从邀请奖励中扣除
                long consumedFromInvitationReward = 0;
                if (invitationRemainingAmount != null && invitationRemainingAmount > 0) {
                    // 按过期时间从早到晚排序消耗奖励额度
                    List<UserInvitationRewardDTO> rewardList = userInvitationRewardService.getValidRewards(
                        userId, featureType, currentTime);

                    if (!CollectionUtils.isEmpty(rewardList)) {
                        Long remainingUsage = usageCount;

                        for (UserInvitationRewardDTO reward : rewardList) {
                            if (remainingUsage <= 0) {
                                break;
                            }

                            // 计算该奖励的剩余可用量
                            Integer rewardRemainingAmount = reward.getRemainingAmount();
                            if (rewardRemainingAmount <= 0) {
                                continue;
                            }

                            // 计算本次从该奖励中消耗的额度
                            int amountToUse = Math.min(rewardRemainingAmount, remainingUsage.intValue());

                            // 更新奖励使用量
                            Integer currentUsage = reward.getRewardUsage() != null ? reward.getRewardUsage() : 0;
                            userInvitationRewardService.increaseRewardUsage(reward.getId(), amountToUse);

                            // 减少剩余使用量
                            remainingUsage -= amountToUse;
                            // 增加已消耗的邀请奖励量
                            consumedFromInvitationReward += amountToUse;

                            log.info("Text2Go updateUsageInfo consume invitation reward, reward ID={}, consumed amount={}, new usage={}, remaining={}",
                                reward.getId(), amountToUse, (currentUsage + amountToUse), (rewardRemainingAmount - amountToUse));
                        }

                        // 更新剩余使用量供后续处理
                        usageCount = remainingUsage;

                        // 如果完全使用邀请奖励额度就足够了，直接返回
                        if (usageCount <= 0) {
                            log.info("Text2Go updateUsageInfo all usage covered by invitation rewards, original request={}, consumed from rewards={}",
                                originalUsageCount, consumedFromInvitationReward);
                            return subscriptionInfoDTO;
                        }

                        log.info("Text2Go updateUsageInfo partially consumed from invitation rewards, original request={}, consumed from rewards={}, remaining to use={}",
                            originalUsageCount, consumedFromInvitationReward, usageCount);
                    }
                }
            }

            // 开始处理订阅配额部分
            // 存在特性使用记录
            if (!CollectionUtils.isEmpty(featureUsageDOS)) {
                if (subscribedPlan == null) {
                    log.warn("Text2Go updateUsageInfo no subscribed plan found");
                    return null;
                }

                // 获取当前计划中的使用记录
                Date currentTime = new Date();
                FeatureUsageDO first = featureUsageDOS.stream().filter(usage -> {
                    if (StringUtils.isNotBlank(usage.getAttributes()) && !"{}".equals(usage.getAttributes())) {
                        JSONObject jsonObject = JSONObject.parseObject(usage.getAttributes());
                        if (jsonObject != null) {
                            String typeFromJson = String.valueOf(jsonObject.get("featureType"));
                            String planName = String.valueOf(jsonObject.get("planName"));

                            // 检查当前时间是否在使用记录的有效期内
                            boolean isInValidPeriod = currentTime.after(usage.getStartTime()) &&
                                currentTime.before(usage.getEndTime());

                            return StringUtils.isNotBlank(typeFromJson) &&
                                subscribedPlan.getPlanName().equals(planName) &&
                                typeFromJson.equals(featureLimitDTO.getFeatureType()) &&
                                isInValidPeriod; // 添加有效期检查
                        }
                    }
                    return false;
                }).findFirst().orElse(null);

                log.info("Text2Go updateUsageInfo filter usage record, result={}", JSON.toJSONString(first));

                if (first == null) {
                    // 写入使用量
                    log.info("Text2Go updateUsageInfo no matching usage record, create new usage record");
                    
                    // 记录是否已经找到该特性的初始配额
                    boolean foundInitialQuota = false;
                    Long initialQuota = null;
                    
                    // 从usageInfoDTO中获取特性配额信息
                    if (usageInfoDTO != null && !CollectionUtils.isEmpty(usageInfoDTO.getFeatureInfoList())) {
                        for (DscSubscriptionUsageInfoDTO.SubscriptionFeatureUsageInfo featureInfo : usageInfoDTO.getFeatureInfoList()) {
                            if (featureInfo.getType() != null && 
                                featureInfo.getType().equalsIgnoreCase(featureLimitDTO.getFeatureType())) {
                                
                                // 找到匹配的特性配额
                                initialQuota = featureInfo.getQuota();
                                foundInitialQuota = true;
                                log.info("Text2Go updateUsageInfo found initial quota from subscription plan for new record: type={}, quota={}", 
                                    featureInfo.getType(), initialQuota);
                                break;
                            }
                        }
                    }
                    
                    if (foundInitialQuota) {
                        // 如果找到了初始配额，使用特定的方法创建带有初始配额的使用记录
                        log.info("Text2Go updateUsageInfo creating usage record with initial quota: {}", initialQuota);
                        text2GoSubscriptionService.createSubscriptionFeatureWithQuota(
                            featureLimitDTO.getUserId(),
                            featureLimitDTO.getFeatureType(), 
                            planNameStr, 
                            Boolean.TRUE, 
                            usageCount,
                            initialQuota);
                    } else {
                        // 如果没找到初始配额，使用原来的方法创建使用记录
                        log.warn("Text2Go updateUsageInfo could not find initial quota, creating usage record without quota");
                        text2GoSubscriptionService.updateSubscriptionFeature(
                            featureLimitDTO.getUserId(),
                            featureLimitDTO.getFeatureType(), 
                            planNameStr, 
                            Boolean.TRUE, 
                            usageCount);
                    }
                    
                    return null;
                } else {
                    // 非消耗型特性 - 直接更新使用记录，不进行超限检查
                    if (!isDepletionFeature) {
                        log.info("Text2Go updateUsageInfo non-depletion feature, skip quota check and update usage");
                        FeatureUsage featureUsage = FeatureUsageConverter.INSTANCE.convertA2B(first);
                        text2GoSubscriptionService.updateSubscriptionFeature(first.getUserId(),
                            featureLimitDTO.getFeatureType(), featureUsage.getAttributes().getPlanName(), Boolean.FALSE,
                            usageCount);
                        return null;
                    }

                    // 消耗型特性 - 更新剩余使用量
                    log.info("Text2Go updateUsageInfo updating depletion feature usage");
                    FeatureUsage featureUsage = FeatureUsageConverter.INSTANCE.convertA2B(first);
                    text2GoSubscriptionService.updateSubscriptionFeature(first.getUserId(),
                        featureLimitDTO.getFeatureType(), featureUsage.getAttributes().getPlanName(), Boolean.FALSE,
                        usageCount);
                    return subscriptionInfoDTO;
                }
            } else {
                // 不存在特性使用记录
                log.info("Text2Go updateUsageInfo no usage records, create new usage record");
                text2GoSubscriptionService.updateSubscriptionFeature(featureLimitDTO.getUserId(),
                    featureLimitDTO.getFeatureType(), planNameStr, Boolean.TRUE, usageCount);
                return null;
            }
        } catch (Exception e) {
            log.error("Text2Go updateUsageInfo error, error={}", e.getMessage(), e);
            throw e;
        }
    }
    
    private boolean isDepletionFeature(UseSubscriptionFeatureDTO featureLimitDTO) {
        try {
            // 首先通过shopifySubscriptionService获取当前订阅计划
            ShopifySubscribedPlanQueryDTO query = new ShopifySubscribedPlanQueryDTO()
                .setAppCode(featureLimitDTO.getAppCode())
                .setUserId(featureLimitDTO.getUserId());
            
            ShopifySubscribedPlanResultDTO subscribedPlan = shopifySubscriptionService.getSubscribedPlan(query);
            if (subscribedPlan == null || subscribedPlan.getPlanId() == null) {
                log.warn("Text2Go isDepletionFeature no subscribed plan found");
                return false;
            }
            
            Long planId = subscribedPlan.getPlanId();
            log.info("Text2Go isDepletionFeature found plan, planId={}, planName={}", 
                planId, subscribedPlan.getPlanName());
            
            // 先查询subscription_plan_feature关联表，获取特定计划下的特性
            QueryWrapper<SubscriptionPlanFeatureDO> planFeatureQuery = new QueryWrapper<>();
            planFeatureQuery.eq("subscription_plan_id", planId);
            
            List<SubscriptionPlanFeatureDO> planFeatures = subscriptionPlanFeatureMapper.selectList(planFeatureQuery);
            log.info("Text2Go isDepletionFeature query plan features, planId={}, result size={}", 
                planId, planFeatures == null ? 0 : planFeatures.size());
            
            if (CollectionUtils.isEmpty(planFeatures)) {
                log.warn("Text2Go isDepletionFeature no plan features found for planId={}", planId);
                return false;
            }
            
            // 获取所有feature_id，使用subscription_feature_id字段
            List<Long> featureIds = planFeatures.stream()
                .map(SubscriptionPlanFeatureDO::getSubscriptionFeatureId)
                .collect(Collectors.toList());
            
            // 查询特性表，判断是否为消耗型特性
            QueryWrapper<SubscriptionFeatureDO> featureQuery = new QueryWrapper<>();
            featureQuery.eq("app_code", featureLimitDTO.getAppCode());
            featureQuery.eq("type", featureLimitDTO.getFeatureType());
            featureQuery.in("id", featureIds);
            
            List<SubscriptionFeatureDO> features = subscriptionFeatureMapper.selectList(featureQuery);
            log.info("Text2Go isDepletionFeature query features, featureType={}, featureIds={}, result size={}", 
                featureLimitDTO.getFeatureType(), featureIds, features == null ? 0 : features.size());
            
            if (!CollectionUtils.isEmpty(features)) {
                for (SubscriptionFeatureDO feature : features) {
                    if (Boolean.TRUE.equals(feature.getIsDepletion())) {
                        return true;
                    }
                }
            }
            return false;
        } catch (Exception e) {
            log.error("Text2Go isDepletionFeature error, error={}", e.getMessage(), e);
            // 出错时默认返回false，不影响正常逻辑
            return false;
        }
    }

    private long calculateTotalUsage(List<FeatureUsageDO> featureUsageDOS, UseSubscriptionFeatureDTO featureLimitDTO) {
        // 获取当前时间用于周期检查
        Date currentTime = new Date();
        return featureUsageDOS.stream()
            .filter(usage -> {
                if (StringUtils.isNotBlank(usage.getAttributes()) && !"{}".equals(usage.getAttributes())) {
                    JSONObject jsonObject = JSONObject.parseObject(usage.getAttributes());
                    if (jsonObject != null) {
                        String typeFromJson = String.valueOf(jsonObject.get("featureType"));

                        // 检查当前时间是否在使用记录的有效期内
                        boolean isInValidPeriod = currentTime.after(usage.getStartTime()) &&
                            currentTime.before(usage.getEndTime());

                        return StringUtils.isNotBlank(typeFromJson) &&
                            typeFromJson.equals(featureLimitDTO.getFeatureType()) &&
                            isInValidPeriod; // 添加有效期检查，只计算当前周期内的使用量
                    }
                }
                return false;
            })
            .mapToLong(FeatureUsageDO::getUsageCount)
            .sum();
    }
    
    private boolean isQuotaExceeded(long totalUsage, long quota, long requestUsage, long remainQuota) {
        return totalUsage >= quota || requestUsage > remainQuota;
    }
    
    private DscSubscriptionInfoDTO buildExceededResponse(UseSubscriptionFeatureDTO featureLimitDTO, 
                                                       String planName, 
                                                       ShopifySubscribedPlanResultDTO subscribedPlan) {
        DscSubscriptionInfoDTO subscriptionInfoDTO = new DscSubscriptionInfoDTO();
        
        QueryWrapper<SubscriptionFeatureDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", featureLimitDTO.getFeatureName() + planName);
        queryWrapper.eq("app_code", featureLimitDTO.getAppCode());

        List<SubscriptionFeatureDO> features = subscriptionFeatureMapper.selectList(queryWrapper);
        log.info("Text2Go updateUsageInfo query feature, result={}", JSON.toJSONString(features));
            
        if (!CollectionUtils.isEmpty(features) && features.get(0) != null) {
            subscriptionInfoDTO.setFeatureId(features.get(0).getId());
        }

        subscriptionInfoDTO.setPlanName(subscribedPlan.getPlanName());
        subscriptionInfoDTO.setFeatureName(featureLimitDTO.getFeatureName());
        
        return subscriptionInfoDTO;
    }

    @Override
    public SingleResult<Long> returnSubscriptionFeature(Long userId, Long featureId, String featureType,
        Long usageCount) {
        log.info("Text2Go returnSubscriptionFeature start, userId={}, featureId={}, featureType={}, usageCount={}", 
            userId, featureId, featureType, usageCount);
            
        if (featureId == null || userId == null) {
            log.warn("Text2Go returnSubscriptionFeature invalid params");
            SingleResult.buildSuccess(null);
        }

        try {
            DscSubscriptionUsageInfoDTO usageInfoDTO = text2GoSubscriptionService.currentSubscriptionInfo(userId);
            log.info("Text2Go returnSubscriptionFeature get usage info, result={}", JSON.toJSONString(usageInfoDTO));
            
            if (usageInfoDTO == null || CollectionUtils.isEmpty(usageInfoDTO.getFeatureInfoList())) {
                log.warn("Text2Go returnSubscriptionFeature no usage info found");
                return SingleResult.buildSuccess(null);
            }

            DscSubscriptionUsageInfoDTO.SubscriptionFeatureUsageInfo info = usageInfoDTO.getFeatureInfoList().stream()
                .filter(feature -> feature.getFeatureId().equals(featureId))
                .findFirst()
                .orElse(null);
            log.info("Text2Go returnSubscriptionFeature filter feature info, result={}", JSON.toJSONString(info));
                
            if (info == null) {
                log.warn("Text2Go returnSubscriptionFeature no matching feature found");
                return SingleResult.buildSuccess(null);
            }

            text2GoSubscriptionService.rollbackFeatureUsage(userId, featureId, featureType, usageCount);
            log.info("Text2Go returnSubscriptionFeature rollback complete");
            
            return SingleResult.buildSuccess(featureId);
        } catch (Exception e) {
            log.error("Text2Go returnSubscriptionFeature error, userId={}, featureId={}, error={}", 
                userId, featureId, e.getMessage(), e);
            return SingleResult.buildSuccess(null);
        }
    }

    @Override
    public SingleResult<ShopifyFeatureAllDTO> getFeatureAll(ShopifyFeatureQuery dto) {
        log.info("Text2Go getFeatureAll start, dto={}", JSON.toJSONString(dto));
        try {
            ShopifyFeatureAllDTO result = text2GoSubscriptionService.getFeatureAll(dto);
            log.info("Text2Go getFeatureAll success, dto={}, result={}", 
                JSON.toJSONString(dto), JSON.toJSONString(result));
            return SingleResult.buildSuccess(result);
        } catch (Exception e) {
            log.error("Text2Go getFeatureAll error, dto={}, error={}", 
                JSON.toJSONString(dto), e.getMessage(), e);
            return SingleResult.buildSuccess(null);
        }
    }

    @Override
    public SingleResult<Boolean> processInvitationAndReward(Long inviterId, Long inviteeId) {
        log.info("Start processing user invitation and rewards, inviterId={}, inviteeId={}", inviterId, inviteeId);
        try {
            SingleResult<Boolean> result = userInvitationService.processInvitationAndReward(inviterId, inviteeId);
            log.info("Completed processing user invitation and rewards, inviterId={}, inviteeId={}, result={}", inviterId, inviteeId, JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            log.error("Error processing user invitation and rewards, inviterId={}, inviteeId={}, error={}", inviterId, inviteeId, e.getMessage(), e);
            return SingleResult.buildFailure("Failed to process invitation: " + e.getMessage());
        }
    }

    @Override
    public SingleResult<Integer> getUserInvitationStats(Long userId) {
        try {
            log.info("Start getting user invitation count, userId={}", userId);
            if (userId == null) {
                log.warn("Invalid userId for getting invitation count");
                return SingleResult.buildFailure("User ID cannot be null");
            }

            SingleResult<Integer> result = userInvitationService.getUserInvitationStats(userId);
            log.info("Successfully retrieved invitation count, userId={}, count={}", userId, result.getData());
            return result;
        } catch (Exception e) {
            log.error("Error getting invitation count: {}", e.getMessage(), e);
            return SingleResult.buildFailure("Failed to get invitation count: " + e.getMessage());
        }
    }

    @Override
    public SingleResult<UserFeatureRewardsDTO> getUserFeatureRewards(Long userId, List<String> featureTypes) {
        try {
            log.info("开始获取用户特性奖励，userId={}, featureTypes={}", userId, featureTypes);
            if (userId == null) {
                log.warn("获取特性奖励的用户ID无效");
                return SingleResult.buildFailure("用户ID不能为空");
            }

            if (featureTypes == null || featureTypes.isEmpty()) {
                log.warn("特性类型列表为空，userId={}", userId);
                return SingleResult.buildFailure("特性类型列表不能为空");
            }

            // 创建返回对象
            UserFeatureRewardsDTO result = new UserFeatureRewardsDTO();
            result.setUserId(userId);
            result.setQueryTime(new Date());

            // 获取当前时间
            Date currentTime = new Date();

            // 【优化点1】一次性查询用户指定类型的所有有效奖励记录
            List<UserInvitationRewardDTO> allValidRewards = userInvitationRewardService.getAllValidRewardsByTypes(
                userId, featureTypes, currentTime);

            if (allValidRewards.isEmpty()) {
                // 如果没有有效奖励，直接返回空结果
                List<UserFeatureRewardsDTO.FeatureRewardDetail> emptyDetails = new ArrayList<>();
                for (String featureType : featureTypes) {
                    emptyDetails.add(createEmptyRewardDetail(featureType));
                }
                result.setRewardDetails(emptyDetails);
                return SingleResult.buildSuccess(result);
            }

            // 【优化点2】在内存中按类型分组处理数据
            Map<String, List<UserInvitationRewardDTO>> rewardsByType = allValidRewards.stream()
                    .collect(Collectors.groupingBy(UserInvitationRewardDTO::getRewardType));

            // 有效奖励类型集合 (已按请求过滤)
            Set<String> validRewardTypes = rewardsByType.keySet();
            log.info("查询到用户有效奖励类型: {}，userId={}", JSON.toJSONString(validRewardTypes), userId);

            List<UserFeatureRewardsDTO.FeatureRewardDetail> rewardDetails = new ArrayList<>();

            // 处理每个请求的特性类型
            for (String featureType : featureTypes) {
                if (validRewardTypes.contains(featureType)) {
                    List<UserInvitationRewardDTO> rewards = rewardsByType.get(featureType);

                    // 【优化点3】在内存中计算总额、已使用量和剩余量
                    int totalAmount = 0;
                    int totalUsage = 0;
                    Date latestExpiry = null;

                    for (UserInvitationRewardDTO reward : rewards) {
                        totalAmount += reward.getRewardAmount() != null ? reward.getRewardAmount() : 0;
                        totalUsage += reward.getRewardUsage() != null ? reward.getRewardUsage() : 0;

                        // 更新最晚过期时间
                        if (latestExpiry == null || (reward.getExpiryTime() != null && reward.getExpiryTime().after(latestExpiry))) {
                            latestExpiry = reward.getExpiryTime();
                        }
                    }

                    int remainingAmount = Math.max(0, totalAmount - totalUsage);

                    log.info("特性类型 {} 总额={}, 已使用={}, 剩余={}",
                            featureType, totalAmount, totalUsage, remainingAmount);

                    // 创建奖励详情
                    UserFeatureRewardsDTO.FeatureRewardDetail detail = new UserFeatureRewardsDTO.FeatureRewardDetail();
                    detail.setFeatureType(featureType);
                    detail.setFeatureName(getFeatureNameByType(featureType));
                    detail.setTotalQuota((long)totalAmount);
                    detail.setUsedQuota((long)totalUsage);
                    detail.setRemainingQuota((long)remainingAmount);
                    detail.setExpiryTime(latestExpiry);

                    // 判断是否为永久有效（1000年以上视为永久）
                    if (latestExpiry != null) {
                        Calendar calendar = Calendar.getInstance();
                        calendar.add(Calendar.YEAR, 999);
                        detail.setIsPermanent(latestExpiry.after(calendar.getTime()));
                    } else {
                        detail.setIsPermanent(false);
                    }

                    // 添加额外属性
                    Map<String, Object> attributes = new HashMap<>();
                    attributes.put("recordCount", rewards.size());
                    detail.setAttributes(attributes);

                    rewardDetails.add(detail);
                } else {
                    // 该类型没有有效奖励
                    rewardDetails.add(createEmptyRewardDetail(featureType));
                }
            }

            result.setRewardDetails(rewardDetails);
            log.info("成功获取用户特性奖励, userId={}", userId);
            return SingleResult.buildSuccess(result);
        } catch (Exception e) {
            log.error("获取特性奖励出错: {}", e.getMessage(), e);
            return SingleResult.buildFailure("获取特性奖励失败: " + e.getMessage());
        }
    }

    @Override
    public SingleResult<TrialDurationDTO> subscriptionIsTrial(Long userId) {
        log.info("Text2Go subscriptionIsTrial start, userId={}", userId);
        if (userId == null) {
            log.warn("Failed to check trial eligibility, userId is null");
            return SingleResult.buildFailure("User ID cannot be null");
        }

        try {
            TrialDurationDTO trialDurationDTO = text2GoSubscriptionService.subscriptionIsTrial(userId);
            
            if (trialDurationDTO == null) {
                log.warn("Error checking trial eligibility, service returned null");
                return SingleResult.buildFailure("Failed to check trial eligibility");
            }
            
            log.info("Text2Go subscriptionIsTrial success, userId={}, isTrial={}, remainTrialDay={}", 
                userId, trialDurationDTO.getIsTrial(), trialDurationDTO.getRemainTrialDay());
            return SingleResult.buildSuccess(trialDurationDTO);
        } catch (Exception e) {
            log.error("Text2Go subscriptionIsTrial error, userId={}, error={}", userId, e.getMessage(), e);
            return SingleResult.buildFailure("Failed to check trial eligibility: " + e.getMessage());
        }
    }

    /**
     * 创建空的奖励详情（无奖励时使用）
     */
    private UserFeatureRewardsDTO.FeatureRewardDetail createEmptyRewardDetail(String featureType) {
        UserFeatureRewardsDTO.FeatureRewardDetail detail = new UserFeatureRewardsDTO.FeatureRewardDetail();
        detail.setFeatureType(featureType);
        detail.setFeatureName(getFeatureNameByType(featureType));
        detail.setTotalQuota(0L);
        detail.setUsedQuota(0L);
        detail.setRemainingQuota(0L);
        detail.setIsPermanent(false);
        detail.setAttributes(new HashMap<>());
        return detail;
    }

    /**
     * 获取当前订阅计划
     */
    private ShopifySubscribedPlanResultDTO getSubscribedPlan(UseSubscriptionFeatureDTO featureLimitDTO) {
        ShopifySubscribedPlanQueryDTO query = new ShopifySubscribedPlanQueryDTO()
            .setAppCode(featureLimitDTO.getAppCode())
            .setUserId(featureLimitDTO.getUserId());
            
        ShopifySubscribedPlanResultDTO result = shopifySubscriptionService.getSubscribedPlan(query);
        log.info("Text2Go updateUsageInfo query subscribed plan, query={}, result={}", 
            JSON.toJSONString(query), JSON.toJSONString(result));
        return result;
    }
}
