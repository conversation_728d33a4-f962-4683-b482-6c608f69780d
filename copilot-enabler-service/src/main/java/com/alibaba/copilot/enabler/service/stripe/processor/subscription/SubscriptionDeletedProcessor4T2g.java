package com.alibaba.copilot.enabler.service.stripe.processor.subscription;

import com.alibaba.copilot.enabler.client.base.event.DomainEventJsonProducer;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.event.SubscriptionCanceledEvent;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEventMetadata;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.service.stripe.StripeConsts;
import com.alibaba.copilot.enabler.service.stripe.processor.AbstractStripeEventProcessor;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeExecuteContext;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Component
@Slf4j
public class SubscriptionDeletedProcessor4T2g extends AbstractStripeEventProcessor {
    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;
    @Autowired
    private DomainEventJsonProducer eventPublisher;

    @Override
    public String getEventType() {
        return StripeConsts.SUBSCRIPTION_DELETED;
    }

    @Override
    public String getAppCode() {
        return AppEnum.TEXT2GO.getCode();
    }

    @Override
    protected void doProcess(StripeExecuteContext context) throws Exception {
        log.info("SubscriptionDeletedProcessor4T2g context={}", JSON.toJSONString(context));

        StripeEventMetadata metaData = context.getMetaData();
        String subscriptionOrderId = metaData.getSubscriptionOrderId();
        if (StringUtils.isBlank(subscriptionOrderId)) {
            log.info("SubscriptionDeletedProcessor4T2g subscriptionOrderId is empty context={}", JSON.toJSONString(context));
        }
        Long orderId = Long.valueOf(subscriptionOrderId);

        SubscriptionOrder subscriptionOrder = new SubscriptionOrder();
        subscriptionOrder.setId(orderId);
        subscriptionOrder.setStatus(SubscriptionOrderStatus.COMPLETED);
        subscriptionOrder.setAppCode(getAppCode());
        String endedAt = context.getEvent().fetch("ended_at");

        if (NumberUtils.isNumber(endedAt)) {
            Date endTime = new Date(Long.parseLong(endedAt) * 1000L);
            subscriptionOrder.setPerformEndTime(endTime);
        }

        subscriptionOrderRepository.saveSubscriptionOrder(subscriptionOrder);

        // 发送事件
        sendCancelOrderEvent(subscriptionOrder);
    }

    private void sendCancelOrderEvent(SubscriptionOrder order) {
        SubscriptionCanceledEvent subscriptionCanceledEvent = SubscriptionCanceledEvent.builder()
                .appCode(order.getAppCode())
                .subId(order.getId())
                .unix(System.currentTimeMillis())
                .build();
        eventPublisher.publish(subscriptionCanceledEvent);
    }
}
