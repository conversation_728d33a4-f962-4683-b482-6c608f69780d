package com.alibaba.copilot.enabler.service.subscription.manager;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/27
 */
public class SubscriptionManagerFactory {

    private static final Map<String, SubscriptionManager> MAP = new ConcurrentHashMap<>();


    public static void register(SubscriptionManager manager) {
        String key = buildKey(manager);
        if (StringUtils.isNotBlank(key)) {
            MAP.put(key, manager);
        }
    }

    public static SubscriptionManager getManagerNotNull(BizSubscriptionContext context) {
        String type = context.getSubscriptionPayType().name();
        String appCode = context.getAppCode();
        SubscriptionManager processor = MapUtils.getObject(MAP, buildKey(type, appCode));
        if (Objects.isNull(processor)) {
            throw new RuntimeException("no subscription manager for: " + type + ", " + appCode);
        }
        return processor;
    }


    public static SubscriptionManager getManagerNotNull(String type, String appCode) {
        return MapUtils.getObject(MAP, buildKey(type, appCode));
    }

    private static String buildKey(SubscriptionManager manager) {
        if (Objects.nonNull(manager)) {
            String type = manager.getSubscriptionPayType().name();
            String appCode = manager.getAppCode();
            return buildKey(type, appCode);
        }
        return null;
    }

    private static String buildKey(String type, String appCode) {
        if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(appCode)) {
            return type + "#" + appCode;
        }
        return null;
    }

}