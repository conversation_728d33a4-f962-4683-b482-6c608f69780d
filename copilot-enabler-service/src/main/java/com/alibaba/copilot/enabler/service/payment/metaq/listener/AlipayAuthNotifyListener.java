package com.alibaba.copilot.enabler.service.payment.metaq.listener;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.aepay.fund.business.api.payment.dto.notify.AuthorizationNotifyDTO;
import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.infra.base.utils.MetaqUtils;
import com.alibaba.copilot.enabler.service.payment.metaq.handler.AlipayAuthNotifyHandler;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.taobao.eagleeye.EagleEye;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Alipay签约授权结果结果通知listener
 *
 * <AUTHOR>
 * @version 2024/1/11
 */
@Component(value = "alipayAuthNotifyListener")
public class AlipayAuthNotifyListener implements MessageListenerConcurrently {

    public static final Logger TASK_LOG = LoggerFactory.getLogger("taskLogger");

    @Resource
    private AlipayAuthNotifyHandler alipayAuthNotifyHandler;

    @Override
    @Monitor(name = "Alipay授权结果通知listener", level = Monitor.Level.P1, layer = Monitor.Layer.CONSUMER)
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        MessageExt messageExt = CollectionUtil.getFirst(msgs);
        if (messageExt == null) {
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        JSONObject notifyContentJsonObject = MetaqUtils.getJSONObjOfMessageExtBody(messageExt);
        String notifyContentStr = notifyContentJsonObject == null ? null : notifyContentJsonObject.toJSONString();
        try {
            EagleEye.startTrace(null, this.getClass().getName(), EagleEye.TYPE_CUSTOM_MESSAGE_SUB);

            TASK_LOG.info("alipayAuthNotifyListener notifyContent:::{}", notifyContentStr);
            if (notifyContentJsonObject == null) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

            AuthorizationNotifyDTO authorizationNotifyDTO = notifyContentJsonObject.toJavaObject(AuthorizationNotifyDTO.class);
            Assertor.assertNonNull(authorizationNotifyDTO, "alipayAuthNotifyListener msg authorizationNotifyType illegal");

            // 支付授权处理
            alipayAuthNotifyHandler.handle(messageExt.getMsgId(), notifyContentStr, authorizationNotifyDTO);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (BizException bizException) {
            TASK_LOG.error("payAlarm-alipayAuthNotifyListener bizError,msg:{},notifyContent:{}",
                    bizException.getMessage(), notifyContentStr, bizException);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            TASK_LOG.error("payAlarm-alipayAuthNotifyListener error,msg:{},notifyContent:{}",
                    e.getMessage(), notifyContentStr, e);

            // Metaq消费自动重试
            if (MetaqUtils.isResumeAutoRetry(messageExt, context, notifyContentStr)) {
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } finally {
            EagleEye.endTrace(null);
        }
    }
}
