package com.alibaba.copilot.enabler.service.subscription.factory;

import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.payment.dto.BuyerDTO;
import com.alibaba.copilot.enabler.client.payment.dto.CreatePaymentSessionDTO;
import com.alibaba.copilot.enabler.client.payment.dto.GoodsDTO;
import com.alibaba.copilot.enabler.client.payment.dto.OrderDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanDTO;
import com.alibaba.copilot.enabler.domain.base.utils.PaymentUtils;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentToken;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.repository.PaymentTokenRepository;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.CreateOrderAndTradeResultDTO;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.SubscribeContext;
import com.alibaba.copilot.enabler.domain.user.model.AEPayInfo;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * {@link CreatePaymentSessionDTO}构造器<hr/>
 * 注: 一般构造器只做属性聚合逻辑, 但该类涉及到支付token信息的查询逻辑, 而且需要区分Card和Alipay, 因此也作为SpringBean
 *
 * <AUTHOR>
 * @version 2024/1/15
 */
@Slf4j
@Component
public class CreatePaymentSessionDTOBuilder {

    @Resource
    private PaymentTokenRepository paymentTokenRepository;

    @Resource
    private UserRepository userRepository;

    public CreatePaymentSessionDTO build(SubscribePlanDTO subscribePlanDTO, CreateOrderAndTradeResultDTO handleResult, SubscribeContext subscribeContext) {
        String appCode = subscribePlanDTO.getAppCode();
        Long userId = subscribePlanDTO.getUserId();
        Long planId = subscribePlanDTO.getPlanId();
        SubscriptionPlan newPlan = subscribeContext.getNewPlan();
        TradeRecord payRecord = handleResult.getPayRecord();

        Long orderId = payRecord.getSubscriptionOrderId();
        String payTradeNo = payRecord.getTradeNo();
        BigDecimal price = payRecord.getTradeAmount();
        PaymentMethodEnum paymentMethod = PaymentMethodEnum.ofName(payRecord.getPaymentMethod());

        // 在业务上传进来的redirectUrl后面, 拼接paymentRequestId参数
        String bizRedirectUrl = subscribePlanDTO.getRedirectUrl();
        String finalRedirectUrl = appendBizParamToUrl(bizRedirectUrl, planId, payTradeNo);

        // 公共信息
        CreatePaymentSessionDTO sessionDTO = new CreatePaymentSessionDTO()
                .setPaymentMethod(paymentMethod)
                .setPaymentRequestId(payTradeNo)
                .setActualPayAmount(price)
                .setRedirectUrl(finalRedirectUrl)
                .setClientIp(subscribePlanDTO.getClientIp())
                .setOrder(new OrderDTO()
                        .setOrderAmount(price)
                        .setReferenceOrderId(String.valueOf(orderId))
                        .setOrderDescription(appCode)
                        .setBuyer(new BuyerDTO()
                                .setBuyerId(String.valueOf(userId))
                                .setBuyerRegistrationTime(subscribePlanDTO.getUserRegistrationTime())
                        )
                        .setGoodsList(Lists.newArrayList(
                                new GoodsDTO()
                                        .setGoodsId(String.valueOf(newPlan.getId()))
                                        .setGoodsName(newPlan.getName())
                        ))
                );

        // 填充支付token信息
        fillPaymentTokenInfo(subscribePlanDTO, paymentMethod, sessionDTO);

        return sessionDTO;
    }

    /**
     * 填充支付token信息
     */
    private void fillPaymentTokenInfo(SubscribePlanDTO subscribePlanDTO, PaymentMethodEnum paymentMethod, CreatePaymentSessionDTO sessionDTO) {
        if (PaymentMethodEnum.isAlipayPaymentMethod(paymentMethod)) {
            // Alipay支付, 查询Alipay Token
            fillAlipayTokenInfo(subscribePlanDTO, paymentMethod, sessionDTO);
        } else {
            // 信用卡支付, 查询卡token和卡组ID
            fillCardTokenInfo(subscribePlanDTO, sessionDTO);
        }

    }

    private void fillAlipayTokenInfo(SubscribePlanDTO subscribePlanDTO, PaymentMethodEnum paymentMethod, CreatePaymentSessionDTO sessionDTO) {
        String appCode = subscribePlanDTO.getAppCode();
        Long userId = subscribePlanDTO.getUserId();
        String paymentTokenId = subscribePlanDTO.getPaymentTokenId();

        if (StringUtils.isEmpty(paymentTokenId)) {
            // 首次支付, 没有Token时, 请求授权 + 支付
            String authState = PaymentUtils.generateAlipayAuthState();
            paymentTokenRepository.create(appCode, userId, paymentMethod, authState);
            sessionDTO.setAuthState(authState);
        } else {
            // 二次支付, 有Token时, 免密支付
            String paymentToken = queryAlipayTokenByTokenId(paymentTokenId);
            sessionDTO.setAlipayToken(paymentToken);
        }
    }

    private void fillCardTokenInfo(SubscribePlanDTO subscribePlanDTO, CreatePaymentSessionDTO sessionDTO) {
        Long userId = subscribePlanDTO.getUserId();
        String paymentTokenId = subscribePlanDTO.getPaymentTokenId();

        // 注: 此处为兼容老逻辑, 未指定支付方式的均作为信用卡支付处理, 且优先取新参数paymentTokenId, 次选老参数cardId
        // noinspection deprecation
        String cardPaymentId = Optional.ofNullable(paymentTokenId)
                .orElse(subscribePlanDTO.getCardId());
        AEPayInfo aePayInfo = queryCardInfo(userId, cardPaymentId);
        if (aePayInfo != null) {
            sessionDTO
                    .setCardToken(aePayInfo.getCardToken())
                    .setNetworkTransactionId(aePayInfo.getNetworkTransactionId());
        }
    }

    private String appendBizParamToUrl(String url, Long planId, String paymentRequestId) {
        return UriComponentsBuilder.fromHttpUrl(url)
                .queryParam(AEPaymentConstants.KEY_PLAN_ID, planId)
                .queryParam(AEPaymentConstants.KEY_PAYMENT_REQUEST_ID, paymentRequestId)
                .toUriString();
    }

    /**
     * 根据支付tokenId查询支付token
     */
    private String queryAlipayTokenByTokenId(String paymentTokenId) {
        PaymentToken paymentToken = paymentTokenRepository.queryByTokenId(paymentTokenId);
        if (paymentToken == null) {
            log.error("query token failed, paymentTokenId={}", paymentTokenId);
            throw new BizException(ErrorCode.INVALID_PARAM, "query token failed");
        }

        String token = paymentToken.getToken();
        if (StringUtils.isEmpty(token)) {
            log.error("queried data, but token is empty, paymentToken={}", JSON.toJSONString(paymentToken));
            throw new BizException(ErrorCode.INVALID_PARAM, "query token failed");
        }

        return token;
    }

    private AEPayInfo queryCardInfo(Long userId, String cardId) {
        User user = userRepository.getUser(userId);
        AEPayInfo aePayInfo = Optional.ofNullable(user)
                .map(userTemp -> userTemp.getPayCardInfoByCardId(cardId))
                .orElse(null);
        // 刷新最近使用的对应卡的时间
        if (user != null && aePayInfo != null) {
            aePayInfo.setUpdateTime(System.currentTimeMillis());
            user.savePaymentCardInfo(userRepository, aePayInfo);
        }

        return aePayInfo;
    }
}
