package com.alibaba.copilot.enabler.service.payment.strategy;

import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.payment.request.CashierPayRequest;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.domain.base.utils.PaymentUtils;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.domain.payment.gateway.geoip.GeoIpGateway;
import com.alibaba.copilot.enabler.domain.payment.gateway.geoip.dto.GetAddressInfoByIpResponse;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.StripeGateway;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeCreateSessionRequest;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeCreateSessionResponse;
import com.alibaba.copilot.enabler.domain.payment.model.IpAddressInfo;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecordAttributes;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.payment.strategy.CreateCashierPayStrategy;
import com.alibaba.copilot.enabler.domain.payment.strategy.CreateCashierPayStrategyFactory;
import com.alibaba.copilot.enabler.service.stripe.StripeService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * CreateCashierPay Strategy抽象类
 */

@Slf4j
public abstract class AbstractCreateCashierPayStrategy implements CreateCashierPayStrategy {

    @Resource
    private TradeRecordRepository tradeRecordRepository;

    @Resource
    private StripeGateway stripeGateway;

    @Resource
    private StripeService stripeService;

    @Resource
    private GeoIpGateway geoIpGateway;

    @PostConstruct
    public void init() {
        CreateCashierPayStrategyFactory.register(app(), this);
    }


    /**
     * 创建TradeRecord
     *
     * @param request
     * @param tradeCurrency
     * @param payProductCode
     * @return
     */
    protected TradeRecord createCashierPayRecord(CashierPayRequest request, String tradeCurrency, String payProductCode) {
        TradeRecord tradeRecord = new TradeRecord();
        tradeRecord.setTradeNo(PaymentUtils.generateTradeNo(request.getUserId()));
        tradeRecord.setUserId(request.getUserId());
        tradeRecord.setAppCode(request.getAppCode());
        tradeRecord.setPaymentType(PaymentTypeEnum.ONE_TIME_PAYMENT);
        tradeRecord.setTradeAmount(request.getPaymentAmount());
        tradeRecord.setTradeCurrency(tradeCurrency);
        tradeRecord.setTradeDirection(TradeDirection.FORWARD);
        tradeRecord.setPaymentMethod(request.getPaymentMethod());
        tradeRecord.setStatus(TradeRecordStatus.TODO);
        tradeRecord.setTaxAmount(null);
        tradeRecord.setTaxCurrency(null);
        tradeRecord.setTransactionAmount(null);
        tradeRecord.setTransactionCurrency(null);
        tradeRecord.setHadInitiatePay(true);
        tradeRecord.setDeleted(false);

        TradeRecordAttributes attributes = new TradeRecordAttributes(null);
        attributes.setProductCode(payProductCode);
        attributes.setOrder(request.getOrder());

        if (StringUtils.isNotBlank(request.getUserClientIp())) {
            GetAddressInfoByIpResponse addressInfoByIp = geoIpGateway.getAddressInfoByIp(request.getUserClientIp());
            IpAddressInfo ipAddressInfo = new IpAddressInfo();
            BeanUtils.copyProperties(addressInfoByIp, ipAddressInfo);
            attributes.setIpAddressInfo(ipAddressInfo);
        }

        tradeRecord.setAttributes(attributes);

        log.info("AbstractCreateCashierPayStrategy.createCashierPayRecord, tradeRecord={}", JSON.toJSONString(tradeRecord));
        tradeRecordRepository.createOrUpdateTradeRecord(tradeRecord);
        return tradeRecord;
    }

    /**
     * 使用WEB SDK 对接 Stripe支付
     *
     * @param request
     * @param tradeRecord
     */
    protected void doCreateByStripeWithWebSDK(CashierPayRequest request, TradeRecord tradeRecord) {

        // 获取customer
        String stripeCustomerId = stripeService.getOrCreateStripeCustomer(request.getUserId());

        // 创建session
        createStripeSession(request, tradeRecord, stripeCustomerId);
    }

    private void createStripeSession(CashierPayRequest request, TradeRecord tradeRecord, String stripeCustomerId) {


        StripeCreateSessionRequest sessionRequest = buildCreateCheckoutSession(request, tradeRecord, stripeCustomerId);
        log.info("AbstractCreateCashierPayStrategy.createStripeSession, sessionRequest={}", JSON.toJSONString(sessionRequest));
        StripeCreateSessionResponse checkoutSession = stripeGateway.createCheckoutSession(sessionRequest);
        tradeRecord.getAttributes().setPaymentSessionExpiryTime(checkoutSession.getExpiresAt());
        tradeRecord.getAttributes().setStripeSessionClientSecret(checkoutSession.getClientSecret());
    }

    protected abstract StripeCreateSessionRequest buildCreateCheckoutSession(CashierPayRequest request, TradeRecord tradeRecord, String stripeCustomerId);

    protected String buildReturnUrl(CashierPayRequest request, TradeRecord tradeRecord) {
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromHttpUrl(request.getPaymentRedirectUrl())
                .queryParam(AEPaymentConstants.KEY_PAYMENT_REQUEST_ID, tradeRecord.getTradeNo());
        if (MapUtils.isNotEmpty(request.getMetadata())) {
            request.getMetadata().forEach((key, value) -> {
                if (AEPaymentConstants.KEY_PAYMENT_REQUEST_ID.equals(key)) {
                    return;
                }
                uriComponentsBuilder.queryParam(key, value);
            });
        }
        return uriComponentsBuilder.toUriString();
    }

    /**
     * 更新TradeRecord
     *
     * @param tradeRecord
     */
    protected void updateCashierPayRecord(TradeRecord tradeRecord) {
        tradeRecordRepository.createOrUpdateTradeRecord(tradeRecord);
    }
}