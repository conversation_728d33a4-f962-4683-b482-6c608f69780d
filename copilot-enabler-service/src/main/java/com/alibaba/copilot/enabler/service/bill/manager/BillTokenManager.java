package com.alibaba.copilot.enabler.service.bill.manager;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;

/**
 * 账单Token管理器
 *
 * <AUTHOR>
 * @version 2024/3/28
 */
public class BillTokenManager {

    private static final String SALT_KEY = "payment-bill-token-salt";

    /**
     * 生成加密Token
     *
     * @param privateToken 原始token (建议直接通过uuid生成, 以确保唯一性)
     * @return 加密后的token
     */
    public static String convertPrivateToken2PublicToken(String privateToken) {
        return MD5.create()
                .setSalt(SALT_KEY.getBytes())
                .digestHex(privateToken);
    }

    /**
     * 解析加密Token
     *
     * @param privateToken 原始token
     * @return 昵称
     */
    public static String parseNickname(String privateToken) {
        if (StrUtil.isEmpty(privateToken)) {
            return null;
        }

        String publicToken = convertPrivateToken2PublicToken(privateToken);
        return SwitchConfig.billTokenConfig.get(publicToken);
    }
}
