package com.alibaba.copilot.enabler.service.antom.processor;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Antom 事件处理器工厂
 *
 * <AUTHOR>
 * @date 2025/05/01
 */
@Slf4j
public class AntomEventProcessorFactory {

    /**
     * 处理器缓存
     * key: notifyType + "#" + appCode
     * value: processor
     */
    private static final Map<String, AntomEventProcessor> PROCESSOR_MAP = new ConcurrentHashMap<>();

    /**
     * 注册处理器
     *
     * @param processor 处理器
     */
    public static void register(AntomEventProcessor processor) {
        String key = buildKey(processor);
        if (StringUtils.isNotBlank(key)) {
            PROCESSOR_MAP.put(key, processor);
            log.info("Register antom event processor: {}, notifyType={}, appCode={}",
                    processor.getClass().getSimpleName(), processor.getNotifyType(), processor.getAppCode());
        } else {
            log.warn("Failed to register processor: {}", processor.getClass().getName());
        }
    }

    /**
     * 构建缓存键
     *
     * @param processor 处理器
     * @return 缓存键
     */
    private static String buildKey(AntomEventProcessor processor) {
        if (Objects.nonNull(processor)) {
            String notifyType = processor.getNotifyType();
            String appCode = processor.getAppCode();
            return buildKey(notifyType, appCode);
        }
        return null;
    }

    /**
     * 构建缓存键
     *
     * @param notifyType 通知类型
     * @param appCode 应用代码
     * @return 缓存键
     */
    private static String buildKey(String notifyType, String appCode) {
        if (StringUtils.isNotBlank(notifyType) && StringUtils.isNotBlank(appCode)) {
            return notifyType + "#" + appCode;
        }
        return null;
    }

    /**
     * 获取处理器
     *
     * @param context 执行上下文
     * @return 处理器
     */
    public static AntomEventProcessor getProcessor(AntomExecuteContext context) {
        if (Objects.isNull(context) || Objects.isNull(context.getAppCode())) {
            return null;
        }

        String notifyType = context.getNotifyType();
        String appCode = context.getAppCode();

        if (StringUtils.isBlank(notifyType) || StringUtils.isBlank(appCode)) {
            return null;
        }

        String key = buildKey(notifyType, appCode);
        return PROCESSOR_MAP.get(key);
    }

    /**
     * 获取处理器，如果不存在则抛出异常
     *
     * @param context 执行上下文
     * @return 处理器
     * @throws RuntimeException 如果处理器不存在
     */
    public static AntomEventProcessor getProcessorNotNull(AntomExecuteContext context) {
        AntomEventProcessor processor = getProcessor(context);
        if (processor == null) {
            String notifyType = context.getNotifyType();
            String appCode = context.getAppCode();
            throw new RuntimeException(
                    String.format("No antom processor for: %s, %s", notifyType, appCode));
        }
        return processor;
    }

    /**
     * 获取处理器，如果不存在则抛出异常
     *
     * @param notifyType 通知类型
     * @param appCode 应用代码
     * @return 处理器
     */
    public static AntomEventProcessor getProcessorNotNull(String notifyType, String appCode) {
        String key = buildKey(notifyType, appCode);
        AntomEventProcessor processor = PROCESSOR_MAP.get(key);
        if (processor == null) {
            throw new RuntimeException(
                    String.format("No antom processor for: %s, %s", notifyType, appCode));
        }
        return processor;
    }
}
