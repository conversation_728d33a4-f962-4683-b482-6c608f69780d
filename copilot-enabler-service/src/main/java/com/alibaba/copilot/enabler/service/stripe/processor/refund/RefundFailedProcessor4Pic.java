package com.alibaba.copilot.enabler.service.stripe.processor.refund;

import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.service.stripe.processor.AbstractStripeEventProcessor;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeExecuteContext;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.alibaba.copilot.enabler.service.stripe.StripeConsts.REFUND_FAILED;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/18
 */
@Component
@Slf4j
public class RefundFailedProcessor4Pic extends AbstractStripeEventProcessor {

    @Override
    public String getEventType() {
        return REFUND_FAILED;
    }

    @Override
    public String getAppCode() {
        return AppEnum.PIC_COPILOT.getCode();
    }

    @Override
    protected void doProcess(StripeExecuteContext context) throws Exception {
        log.error("stripe refund failed, metadata={}", JSON.toJSONString(context));
    }
}