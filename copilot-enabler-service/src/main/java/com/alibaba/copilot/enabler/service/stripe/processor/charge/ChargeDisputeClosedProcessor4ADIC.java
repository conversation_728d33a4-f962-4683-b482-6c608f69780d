package com.alibaba.copilot.enabler.service.stripe.processor.charge;

import com.alibaba.copilot.enabler.client.payment.constant.dispute.DisputeResult;
import com.alibaba.copilot.enabler.client.payment.constant.dispute.DisputeStatus;
import com.alibaba.copilot.enabler.client.payment.dto.DisputeEventDTO;
import com.alibaba.copilot.enabler.client.payment.dto.OrderDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEventMetadata;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.service.stripe.StripeConsts;
import com.alibaba.copilot.enabler.service.stripe.processor.AbstractStripeEventProcessor;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeExecuteContext;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 处理 charge.dispute.closed webhook 事件
 */

@Slf4j
@Component
public class ChargeDisputeClosedProcessor4ADIC extends AbstractStripeEventProcessor {

    @Override
    public String getEventType() {
        return StripeConsts.CHARGE_DISPUTE_CLOSED;
    }

    @Override
    public String getAppCode() {
        return AppEnum.ADIC.getCode();
    }

    @Override
    protected void doProcess(StripeExecuteContext context) {
        log.info("ChargeDisputeClosedProcessor4ADIC context={}", JSON.toJSONString(context));

        StripeEventMetadata metaData = context.getMetaData();
        DisputeResult disputeResult = DisputeResult.getByValue(context.getEvent().fetchDisputeStatus());
        if (disputeResult == null) {
            log.error("ChargeDisputeClosedProcessor4ADIC disputeResult is null, metaData={}", JSON.toJSONString(metaData));
            return;
        }
        DisputeEventDTO disputeEventDTO = new DisputeEventDTO();
        disputeEventDTO.setTradeNo(metaData.getTradeNo());
        disputeEventDTO.setUserId(Long.valueOf(metaData.getUserId()));
        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setReferenceOrderId(metaData.getSubscriptionOrderId());
        disputeEventDTO.setOrder(orderDTO);

        disputeEventDTO.setStatus(DisputeStatus.CLOSED.name());
        disputeEventDTO.setResult(disputeResult.name());

        domainEventJsonProducer.publish(disputeEventDTO);
    }
}