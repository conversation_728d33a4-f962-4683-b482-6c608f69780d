package com.alibaba.copilot.enabler.service.stripe.mq;

import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.domain.base.exception.BizNeedRetryException;
import com.alibaba.copilot.enabler.domain.base.mq.MessageHandler;
import com.alibaba.copilot.enabler.domain.payment.constant.StripeCheckoutSessionMode;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEvent;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEventMetadata;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.service.PaymentDomainService;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeEventProcessor;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeEventProcessorFactory;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeExecuteContext;
import com.alibaba.copilot.enabler.service.subscription.constant.SubscriptionConstants;
import com.alibaba.copilot.enabler.service.subscription.utils.BizUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.Charge;
import com.stripe.model.Invoice;
import com.stripe.model.PaymentIntent;
import com.stripe.model.Subscription;
import io.fury.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.alibaba.copilot.enabler.service.stripe.StripeConsts.*;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/12
 */
@Component
@Slf4j
public class StripeWebhookMsgHandler implements MessageHandler<JSONObject> {

    @Autowired
    private PaymentDomainService paymentDomainService;

    @Override
    public void handle(JSONObject eventJsonObj) {
        try {
            Assertor.assertNonNull(eventJsonObj, "event obj is null");
            String eventType = eventJsonObj.getString("type");
            Assertor.assertNotBlank(eventType, "eventType is blank");

            // 解析 meta data
            StripeEvent event = StripeEvent.of(eventJsonObj);
            StripeEventMetadata stripeEventMetaData = exportMetaData(eventJsonObj);
            if (Objects.isNull(stripeEventMetaData)) {
                log.warn("stripe event metadata is null, event={}", eventJsonObj);
                return;
            }
            // 设置上下文
            StripeExecuteContext context = new StripeExecuteContext();
            context.setEventType(eventType);
            context.setEvent(event);
            context.setMetaData(stripeEventMetaData);
            // 获取 processor 进行处理
            StripeEventProcessor processor = StripeEventProcessorFactory.getProcessorNotNull(context);
            processor.process(context);
        } catch (BizNeedRetryException e) {
            log.error("StripeWebhookMsgHandler error, will retry, event={}", eventJsonObj, e);
            // 异常继续向上抛，mq 消息会重试
            throw e;
        } catch (Exception e) {
            // 仅打印日志，不处理异常
            log.error("StripeWebhookMsgHandler error, no retry, event={}", eventJsonObj, e);
        }
    }


    private StripeEventMetadata exportMetaData(JSONObject eventJsonObj) {
        String eventType = eventJsonObj.getString("type");
        JSONObject data = eventJsonObj.getJSONObject("data");
        JSONObject object = data.getJSONObject("object");
        JSONObject metadataObject = object.getJSONObject("metadata");

        if (INVOICE_TYPE.contains(eventType)) {
            return exportMetaDataForInvoice(object);
        }

        if (SUB_TYPE.contains(eventType)) {
            return exportMetaDataForSub(object);
        }

        if (REFUND_UPDATED.equals(eventType)) {
            return exportMetadataDataForRefund(object);
        }


        if (CHECKOUT_SESSION_TYPES.contains(eventType) ||
                REFUND_TYPES.contains(eventType) ||
                PAYMENT_INTENT_TYPES.contains(eventType)) {
            return JSON.parseObject(JSON.toJSONString(metadataObject), StripeEventMetadata.class);
        } else if (CHARGE_DISPUTE_TYPES.contains(eventType) ) {
            String paymentIntentId = object.getString("payment_intent");

            TradeRecord tradeRecord = paymentDomainService.queryByOutTradeNoAndTradeDirection(paymentIntentId, TradeDirection.FORWARD);
            if (tradeRecord == null) {
               // subscription 模式下，可能还没有创建 tradeRecord 记录
                Subscription subscriptionFromPaymentIntent = getSubscriptionFromPaymentIntent(paymentIntentId);
                if (subscriptionFromPaymentIntent == null) {
                    throw new BizNeedRetryException("tradeRecord and subscription is null");
                }
                String subscriptionOrderId = subscriptionFromPaymentIntent.getMetadata().get(SubscriptionConstants.META_SUB_ID);
                String appCode = subscriptionFromPaymentIntent.getMetadata().get(SubscriptionConstants.META_APP_CODE);
                String userId = subscriptionFromPaymentIntent.getMetadata().get(SubscriptionConstants.META_USER_ID);
                StripeEventMetadata metadata = new StripeEventMetadata();
                metadata.setSessionMode(StripeCheckoutSessionMode.SUBSCRIPTION.name());
                metadata.setSubscriptionOrderId(subscriptionOrderId);
                metadata.setAppCode(appCode);
                metadata.setUserId(userId);
                return metadata;
            }

            StripeEventMetadata metadata = new StripeEventMetadata();
            metadata.setSessionMode(exportSessionMode(tradeRecord));
            metadata.setAppCode(tradeRecord.getAppCode());
            metadata.setTradeNo(tradeRecord.getTradeNo());
            metadata.setUserId(tradeRecord.getUserId().toString());
            metadata.setSubscriptionOrderId(Optional.ofNullable(tradeRecord.getSubscriptionOrderId())
                    .map(Object::toString)
                    .orElse(null));
            return metadata;
        }
        return null;
    }



    public static Subscription getSubscriptionFromPaymentIntent(String paymentIntentId) {
        try {
            // Step 1: 获取 PaymentIntent 对象
            PaymentIntent paymentIntent = PaymentIntent.retrieve(paymentIntentId);

            // Step 2: 获取与 PaymentIntent 相关的 Invoice ID
            String invoiceId = paymentIntent.getInvoice();

            if (invoiceId != null) {
                // Step 3: 获取 Invoice 对象
                Invoice invoice = Invoice.retrieve(invoiceId);

                // Step 4: 获取与 Invoice 相关的 Subscription ID
                String subscriptionId = invoice.getSubscription();

                if (subscriptionId != null) {
                    // Step 5: 获取 Subscription 对象
                    return Subscription.retrieve(subscriptionId);
                } else {
                    log.info("getSubscriptionFromPaymentIntent not subscription");
                }
            } else {
                log.info("getSubscriptionFromPaymentIntent not invoice");
            }
        } catch (StripeException e) {
            log.error("getSubscriptionFromPaymentIntent error");
        }
        return null; // 如果没有找到相关订阅，返回 null
    }


    private StripeEventMetadata exportMetaDataForSub(JSONObject dataObject) {
        StripeEventMetadata stripeEventMetadata = new StripeEventMetadata();
        JSONObject metadataObject = dataObject.getJSONObject("metadata");
        String appCode = metadataObject.getString(SubscriptionConstants.META_APP_CODE);
        stripeEventMetadata.setAppCode(appCode);
        stripeEventMetadata.setSubscriptionOrderId(metadataObject.getString(SubscriptionConstants.META_SUB_ID));
        stripeEventMetadata.setUserId(metadataObject.getString(SubscriptionConstants.META_USER_ID));
        return stripeEventMetadata;
    }

    private StripeEventMetadata exportMetaDataForInvoice(JSONObject dataObject) {
        StripeEventMetadata stripeEventMetadata = new StripeEventMetadata();
        String billingReason = dataObject.getString("billing_reason");
        if (StringUtils.isBlank(billingReason)) {
            return stripeEventMetadata;
        }
        if (!billingReason.contains("subscription")) {
            return stripeEventMetadata;
        }
        stripeEventMetadata.setSessionMode("subscription");
        JSONObject metadataObject = dataObject.getJSONObject("subscription_details").getJSONObject("metadata");
        String appCode = metadataObject.getString(SubscriptionConstants.META_APP_CODE);
        stripeEventMetadata.setAppCode(appCode);
        stripeEventMetadata.setSubscriptionOrderId(metadataObject.getString(SubscriptionConstants.META_SUB_ID));
        stripeEventMetadata.setUserId(metadataObject.getString(SubscriptionConstants.META_USER_ID));
        return stripeEventMetadata;

    }

    private StripeEventMetadata exportMetadataDataForRefund(JSONObject dataObject) {
        Stripe.apiKey = SwitchConfig.stripeApiKey;
        String chargeId = dataObject.getString("charge");
        if (chargeId == null) {
            return null;
        }
        try {
            Charge charge = Charge.retrieve(chargeId);
            String invoiceId = charge.getInvoice();
            Invoice invoice = Invoice.retrieve(invoiceId);
            Subscription subscription = Subscription.retrieve(invoice.getSubscription());
            Map<String, String> metadata = subscription.getMetadata();
            StripeEventMetadata stripe = new StripeEventMetadata();
            stripe.setSessionMode("subscription");
            stripe.setUserId(subscription.getMetadata().get(SubscriptionConstants.META_USER_ID));
            stripe.setAppCode(subscription.getMetadata().get(SubscriptionConstants.META_APP_CODE));
            stripe.setSubscriptionOrderId(subscription.getMetadata().get(SubscriptionConstants.META_SUB_ID));
            return stripe;
        } catch (StripeException e) {
            log.error("exportMetadataDataForRefund error", e);
            return null;
        }
    }


    private String exportSessionMode(TradeRecord tradeRecord) {
        PaymentTypeEnum paymentType = tradeRecord.getPaymentType();
        if (PaymentTypeEnum.ONE_TIME_PAYMENT.equals(paymentType)) {
            return StripeCheckoutSessionMode.PAYMENT.name();
        }
        // Pic 比较特殊，采用 Setup 模式实现订阅功能
        if (BizUtils.isPicApp(tradeRecord)) {
            return StripeCheckoutSessionMode.SETUP.name();
        }
        return StripeCheckoutSessionMode.SUBSCRIPTION.name();
    }
}