package com.alibaba.copilot.enabler.service.subscription.factory;

import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribablePlanDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.subscription.dto.FinalDiscountDTO;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionFeature;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;

import javax.annotation.Nonnull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@AllArgsConstructor
public class SubscribablePlanDTOBuilder implements Builder<SubscribablePlanDTO> {

    /**
     * 当前计划信息
     */
    private SubscriptionPlan subscriptionPlan;

    /**
     * 折扣信息
     */
    private FinalDiscountDTO finalDiscountDTO;

    /**
     * 计划信息集合
     */
    private List<SubscriptionPlan> plans;

    /**
     * 历史订单
     */
    private List<SubscriptionOrder> subscriptionOrders;

    @Override
    public SubscribablePlanDTO build() {
        Assertor.asserts(subscriptionPlan != null, "SubscribablePlanDTOBuilder:subscriptionPlan can not null!");
        String planDurationUnit = Optional.ofNullable(subscriptionPlan.getDurationUnit())
                .map(DurationUnit::name)
                .orElse(null);

        // 构建当前公共的拓展信息 (此处应该独立出来一个DTO比较合适, 但HSF老签名 SubscriptionHsfApi#getSubscribablePlanList 返回值已经是List)
        PlanCommonExtInfo planCommonExtInfo = buildPlanCommonExtInfo();

        return SubscribablePlanDTO.builder()
                .appName(AppEnum.getAppByCode(subscriptionPlan.getAppCode()).getName())
                .planId(subscriptionPlan.getId())
                .planDurationUnit(planDurationUnit)
                .planName(subscriptionPlan.getName())
                .planDescription(subscriptionPlan.getDescription())
                .crossedPrice(getCrossedPrice())
                .discount(getDiscount())
                .currentPlanId(planCommonExtInfo.currentPlanId)
                .currentPlanEndTime(planCommonExtInfo.currentPlanEndTime)
                .nextPlanId(planCommonExtInfo.nextPlanId)
                .nextPlanName(planCommonExtInfo.nextPlanName)
                .originPlanPrice(subscriptionPlan.getPrice())
                .discountDuration(getDiscountDuration())
                .discountPlanPrice(getDiscountPlanPrice())
                .discountDurationUnit(getDiscountDurationUnit())
                .featureNames(getFeatureNames())
                .build();
    }

    @Nonnull
    private PlanCommonExtInfo buildPlanCommonExtInfo() {
        // 1. 有生效订单时, 用生效订单
        // 查找生效的订单
        SubscriptionOrder effectOrder = findEffectOrder();
        if (effectOrder != null) {
            return new PlanCommonExtInfo(
                    effectOrder.getSubscriptionPlanId(),
                    effectOrder.getPerformEndTime(),
                    effectOrder.getNextPlanId(),
                    effectOrder.getNextPlanName()
            );
        }

        // 2. 无生效订单时, 用免费套餐
        // 查找免费的套餐
        SubscriptionPlan freePlan = findFreePlan();
        if (freePlan != null) {
            return new PlanCommonExtInfo(freePlan.getId(), null, null, null);
        }

        // 3. 生效订单和免费套餐都没有时, 返回空对象
        return new PlanCommonExtInfo(null, null, null, null);
    }

    /**
     * 查找生效的订单
     */
    @Nullable
    private SubscriptionOrder findEffectOrder() {
        return Optional.ofNullable(subscriptionOrders)
                .flatMap(orders -> orders.stream()
                        .filter(order -> SubscriptionOrderStatus.IN_EFFECT.equals(order.getStatus()))
                        .findFirst()
                )
                .orElse(null);
    }

    /**
     * 查找免费的套餐
     */
    private SubscriptionPlan findFreePlan() {
        return Optional.ofNullable(plans)
                .flatMap(ps -> ps.stream()
                        .filter(SubscriptionPlan::isFree)
                        .findFirst()
                )
                .orElse(null);
    }

    /**
     * 折扣周期
     *
     * @return
     */
    private Long getDiscountDuration() {
        if (finalDiscountDTO != null && finalDiscountDTO.getIsDiscount()) {
            return finalDiscountDTO.getRemainDiscountDuration();
        }
        return null;
    }

    /**
     * 折扣周期单位
     *
     * @return
     */
    private String getDiscountDurationUnit() {
        if (finalDiscountDTO == null || !finalDiscountDTO.getIsDiscount()) {
            return null;
        }
        if (StringUtils.equals(subscriptionPlan.getDurationUnit().name(), DurationUnit.YEAR.name())) {
            return DurationUnit.YEAR.name();
        } else {
            return DurationUnit.MONTH.name();
        }
    }

    /**
     * 计算折扣价
     *
     * @return
     */
    private BigDecimal getDiscountPlanPrice() {
        if (finalDiscountDTO != null && finalDiscountDTO.getIsDiscount()) {
            return finalDiscountDTO.getDiscountPrice();
        }
        return null;
    }


    /**
     * 当前计划
     *
     * @return
     */
    private Long getCurrentPlanId() {
        if (CollectionUtils.isNotEmpty(subscriptionOrders)) {
            Optional<SubscriptionOrder> effcOrder = subscriptionOrders.stream()
                    .filter(order -> SubscriptionOrderStatus.IN_EFFECT.equals(order.getStatus())).findFirst();
            if (effcOrder.isPresent()) {
                return effcOrder.get().getSubscriptionPlanId();
            }
        }
        if (CollectionUtils.isNotEmpty(plans)) {
            Optional<SubscriptionPlan> freePlan = plans.stream()
                    .filter(SubscriptionPlan::isFree)
                    .findFirst();
            if (freePlan.isPresent()) {
                return freePlan.get().getId();
            }
        }
        return null;
    }

    /**
     * 特性集合名称
     *
     * @return
     */
    private List<String> getFeatureNames() {
        List<SubscriptionFeature> features = subscriptionPlan.getFeatures();
        if (CollectionUtils.isEmpty(features)) {
            return new ArrayList<>();
        }
        return features.stream().map(SubscriptionFeature::getName).collect(Collectors.toList());
    }

    /**
     * 计算划线价
     *
     * @return
     */
    private BigDecimal getCrossedPrice() {
        if (subscriptionPlan.getDurationUnit() == DurationUnit.MONTH) {
            return null;
        }
        Optional<SubscriptionPlan> opSubscriptionPlan = plans.stream().
                filter(plan -> StringUtils.equals(plan.getName(), subscriptionPlan.getName()) &&
                        StringUtils.equals(plan.getDurationUnit().name(), DurationUnit.MONTH.name())).findFirst();
        if (opSubscriptionPlan.isPresent()) {
            SubscriptionPlan monthPlan = opSubscriptionPlan.get();
            return monthPlan.getPrice().multiply(new BigDecimal(12));
        } else {
            return null;
        }
    }

    /**
     * 折扣
     *
     * @return
     */
    private BigDecimal getDiscount() {
        if (finalDiscountDTO != null && finalDiscountDTO.getIsDiscount()) {
            return BigDecimal.valueOf(finalDiscountDTO.getDiscountInfoDTO().getDiscount())
                    .divide(BigDecimal.valueOf(10));
        }
        return null;
    }

    /**
     * PlanDTO的拓展信息, 仅用于该类中做属性聚合, 因此使用私有静态内部类方式声明
     */
    @AllArgsConstructor
    private static class PlanCommonExtInfo {

        /**
         * 当前选中的planId
         */
        private final Long currentPlanId;

        /**
         * 当前套餐结束的时间
         */
        private final Date currentPlanEndTime;

        /**
         * 当前套餐结束后的下一笔planId
         */
        private final Long nextPlanId;

        /**
         * 当前套餐结束后的下一笔planName
         */
        private final String nextPlanName;
    }
}
