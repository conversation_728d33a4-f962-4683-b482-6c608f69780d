package com.alibaba.copilot.enabler.service.subscription.manager;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanResultDTO;

import javax.annotation.PostConstruct;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/27
 */
public abstract class AbstractSubscriptionManager implements SubscriptionManager {

    @PostConstruct
    public void init() {
        SubscriptionManagerFactory.register(this);
    }


    @Override
    public SingleResult<SubscribePlanResultDTO> subscribe(BizSubscriptionContext context) {
        // todo 3分钟内有待支付的订单，则不能再创建订阅
        return doSubscribe(context);
    }




    protected abstract SingleResult<SubscribePlanResultDTO> doSubscribe(BizSubscriptionContext context);

}