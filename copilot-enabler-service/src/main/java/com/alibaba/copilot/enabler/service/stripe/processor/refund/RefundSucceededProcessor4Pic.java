package com.alibaba.copilot.enabler.service.stripe.processor.refund;

import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEventMetadata;
import com.alibaba.copilot.enabler.service.stripe.processor.AbstractStripeEventProcessor;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeExecuteContext;
import org.springframework.stereotype.Component;

import static com.alibaba.copilot.enabler.service.stripe.StripeConsts.CHARGE_REFUNDED;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/18
 */
@Component
public class RefundSucceededProcessor4Pic extends AbstractStripeEventProcessor {

    @Override
    public String getEventType() {
        return CHARGE_REFUNDED;
    }

    @Override
    public String getAppCode() {
        return AppEnum.PIC_COPILOT.getCode();
    }

    @Override
    protected void doProcess(StripeExecuteContext context) throws Exception {
        StripeEventMetadata metadata = context.getMetaData();
        if (metadata.whetherSetupMode()) {
            picStripeSubscriptionService.handleRefundSucceed4SetupMode(context);
        }

    }
}