package com.alibaba.copilot.enabler.service.subscription.callback;

import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.shopify.web.client.ShopifyClient;
import com.alibaba.copilot.boot.shopify.web.client.ShopifyClientFactory;
import com.alibaba.copilot.boot.shopify.web.data.billing.RecurringApplicationChargeResponse;
import com.alibaba.copilot.enabler.client.base.event.DomainEventJsonProducer;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.subscription.callback.ShopifyCallback;
import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.client.subscription.dto.AuthorizeEventDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyAppSubscriptionDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyAppUninstalledEventDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscriptionEventDTO;
import com.alibaba.copilot.enabler.client.subscription.event.SubscriptionAppliedEvent;
import com.alibaba.copilot.enabler.client.subscription.event.SubscriptionCanceledEvent;
import com.alibaba.copilot.enabler.client.user.event.AuthorizeBackEvent;
import com.alibaba.copilot.enabler.domain.base.utils.TimeUtils;
import com.alibaba.copilot.enabler.domain.payment.service.OrderDomainService;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrderAttributes;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import com.alibaba.copilot.enabler.infra.base.metaq.MetaqMessageProducer;
import com.alibaba.copilot.enabler.service.subscription.constant.ShopifySubscriptionStatus;
import com.alibaba.copilot.enabler.service.subscription.constant.ShopifySubscriptionSwitchType;
import com.alibaba.copilot.enabler.service.subscription.holder.ShopifySubscriptionHolder;
import com.alibaba.copilot.enabler.service.subscription.holder.ShopifySubscriptionHolderDTO;
import com.alibaba.copilot.enabler.service.subscription.log.helper.PlanSwitchHelper;
import com.alibaba.copilot.enabler.service.subscription.utils.ShopifySubscriptionIdUtils;
import com.alibaba.fastjson.JSON;
import io.reactivex.Single;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 2023/10/30
 */
@Slf4j
@Service
public class ShopifyCallbackImpl implements ShopifyCallback {

    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;

    @Resource
    private SubscriptionPlanRepository subscriptionPlanRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private PlanSwitchHelper planSwitchHelper;

    @Autowired
    private DomainEventJsonProducer eventPublisher;

    @Autowired
    private MetaqMessageProducer publisher;

    @Autowired
    private OrderDomainService orderDomainService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onSubscriptionEvent(SubscriptionEventDTO event, String shareCode) {
        log.info("ShopifyCallbackImpl#onSubscriptionEvent event={},shareCode={}", JSON.toJSONString(event), shareCode);
        ShopifyAppSubscriptionDTO appSubscription = event.getAppSubscription();
        if (appSubscription == null) {
            return;
        }
        Long apiId = ShopifySubscriptionIdUtils.getShopifySubscriptionId(appSubscription.getAdminGraphqlApiId());
        String shopifySubscriptionStatus = appSubscription.getStatus();

        // 查询执行订阅变更操作时的上下文信息
        ShopifySubscriptionHolderDTO subscriptionHolderDTO = ShopifySubscriptionHolder.getFromShopifySubscriptionHolder(apiId);
        if (subscriptionHolderDTO == null) {
            // 查不到相关信息时不做处理
            log.warn("ShopifyCallbackImpl#onSubscriptionEvent#subscriptionHolderDTO is null");

            // 对于apiId取不到的取消事件, 单独进行处理
            SubscriptionOrder orderOfCurrentEvent = subscriptionOrderRepository.queryByShopifySubscriptionId(apiId);
            if (orderOfCurrentEvent != null) {
                handleCancelEvent(shopifySubscriptionStatus, orderOfCurrentEvent);
            }
            return;
        }

        ShopifySubscriptionSwitchType switchType = subscriptionHolderDTO.getSwitchType();
        if (switchType == null) {
            log.warn("ShopifyCallbackImpl#onSubscriptionEvent#switchType is null");
            return;
        }

        // 查询DB订单, 有则更新, 无则新增
        SubscriptionOrder orderOfCurrentEvent = subscriptionOrderRepository.queryByShopifySubscriptionId(apiId);

        switch (switchType) {
            case FREE_TO_CHARGE:
            case CHARGE_TO_CHARGE:
                // 新订阅或更改订阅, 免费=>付费 或 付费=>付费
                handleActiveEvent(shopifySubscriptionStatus, subscriptionHolderDTO, orderOfCurrentEvent, shareCode);
                break;
            case CHARGE_TO_FREE:
                // 处理取消订阅事件, 付费=>免费
                handleCancelEvent(shopifySubscriptionStatus, orderOfCurrentEvent);
                break;
            default:
                throw new BizException(ErrorCode.SYS_ERROR);
        }
    }

    /**
     * 处理取消订阅事件
     */
    private void handleCancelEvent(String subscriptionStatus, SubscriptionOrder orderOfCurrentEvent) {
        log.info("ShopifyCallbackImpl#handleCancelSubscription#subscriptionStatus is {}, orderOfCurrentEvent={{}",
                subscriptionStatus, JSON.toJSONString(orderOfCurrentEvent));
        // 状态校验
        if (!ShopifySubscriptionStatus.isInvalidStatus(subscriptionStatus)
                && !ShopifySubscriptionStatus.FROZEN.getCode().equals(subscriptionStatus)) {
            log.warn("ShopifyCallbackImpl#handleCancelSubscription#subscriptionStatus is {}", subscriptionStatus);
            return;
        }

        // 订单数据校验
        if (orderOfCurrentEvent == null) {
            log.warn("ShopifyCallbackImpl#handleCancelSubscription#orderOfCurrentEvent is null");
            return;
        }

        // 变更数据库
        boolean cancelSuccess = cancelSubscription(orderOfCurrentEvent);

        // 事件埋点: 取消老套餐
        if (cancelSuccess) {
            String appCode = orderOfCurrentEvent.getAppCode();
            Long userId = orderOfCurrentEvent.getUserId();
            planSwitchHelper.onSwitchPlan(appCode, userId, orderOfCurrentEvent, null);

            // 生成退款交易流水
            log.info("handleCancelEvent createShopifyTradeRecord,subscriptionStatus:{},order:{}", subscriptionStatus, JSON.toJSONString(orderOfCurrentEvent));
            SubscriptionPlan subscriptionPlan = subscriptionPlanRepository.queryByPlanId(orderOfCurrentEvent.getSubscriptionPlanId(), Boolean.FALSE);
            if (orderOfCurrentEvent.currentIsInTrial()) {
                orderDomainService.createShopifyTradeRecord(TradeDirection.REFUND, orderOfCurrentEvent, PaymentTypeEnum.INITIATED_PAYMENT, subscriptionPlan, null);
            }
        }
    }

    /**
     * 处理订单激活事件
     */
    private void handleActiveEvent(String subscriptionStatus, ShopifySubscriptionHolderDTO subscriptionHolderDTO,
                                   SubscriptionOrder orderOfCurrentEvent, String shareCode) {
        Long apiId = subscriptionHolderDTO.getApiId();

        // 状态校验
        if (!ShopifySubscriptionStatus.ACTIVE.getCode().equals(subscriptionStatus)) {
            log.warn("ShopifyCallbackImpl#handleActiveEvent#subscriptionStatus is {}", subscriptionStatus);
            return;
        }

        String appCode = subscriptionHolderDTO.getAppCode();
        Long userId = subscriptionHolderDTO.getUserId();
        if (StringUtils.isBlank(appCode) || userId == null) {
            log.warn("ShopifyCallbackImpl#handleActiveEvent#param is invalid");
            return;
        }

        if (orderOfCurrentEvent != null) {
            // 该订单已经创建过, 不再重复创建
            log.warn("ShopifyCallbackImpl#handleActiveEvent#orderOfCurrentEvent has existed !");
            return;
        }

        // 如果当前存在生效的订单, 先对其进行取消
        // 主要为了规避A->B时, 先收到B的ACTIVE事件, 后收到A的CANCELLED时间, 进而引发的"两笔订单同时生效"的问题
        SubscriptionOrder oldEffectOrder = Optional.ofNullable(subscriptionHolderDTO.getOldEffectOrderId())
                .map(subscriptionOrderRepository::getByOrderId)
                .orElse(null);
        if (oldEffectOrder != null) {
            log.info("ShopifyCallbackImpl#handleActiveEvent cancel subscription");
            cancelSubscription(oldEffectOrder);
        }

        // 创建新订单
        log.info("handleActiveEvent createShopifyTradeRecord,subscriptionStatus:{},order:{}", subscriptionStatus, JSON.toJSONString(oldEffectOrder));
        SubscriptionPlan subscriptionPlan = subscriptionPlanRepository.queryByPlanId(subscriptionHolderDTO.getPlanId(), Boolean.FALSE);
        SubscriptionOrder newOrder = createSubscription(subscriptionHolderDTO, apiId, subscriptionPlan);

        // 生成付款交易流水
        orderDomainService.createShopifyTradeRecord(TradeDirection.FORWARD, newOrder, PaymentTypeEnum.INITIATED_PAYMENT, subscriptionPlan, subscriptionHolderDTO.getClientIp());

        //给推广发送消息
        sendSubscriptionAppliedEventSEO(subscriptionHolderDTO, shareCode);

        if (newOrder != null) {
            // 事件埋点: 新增或变更套餐
            planSwitchHelper.onSwitchPlan(appCode, userId, oldEffectOrder, newOrder);
        }
    }

    /**
     * 订阅：给推广发送消息
     *
     * @param subscriptionHolderDTO
     */
    private void sendSubscriptionAppliedEventSEO(ShopifySubscriptionHolderDTO subscriptionHolderDTO, String shareCode) {

        SubscriptionPlan subscriptionPlan = subscriptionPlanRepository.queryByPlanId(subscriptionHolderDTO.getPlanId(), Boolean.FALSE);
        if (subscriptionPlan == null) {
            return;
        }

        //订阅周期
        Integer subCycle = 0;
        if (subscriptionPlan.getDurationUnit().equals(DurationUnit.YEAR)) {
            subCycle = 365 * subscriptionPlan.getDuration().intValue();
        } else if (subscriptionPlan.getDurationUnit().equals(DurationUnit.MONTH)) {
            subCycle = 30 * subscriptionPlan.getDuration().intValue();
        }
        SubscriptionAppliedEvent subscriptionAppliedEvent = SubscriptionAppliedEvent.builder()
                .appCode(subscriptionPlan.getAppCode())
                .userCode(shareCode)
                .userId(subscriptionHolderDTO.getUserId() + "")
                .email(StringUtils.EMPTY)
                .subName(subscriptionPlan.getName())
                .subCycle(subCycle)
                .trialCycle(subscriptionHolderDTO.getTrialDays())
                .actualFee(subscriptionHolderDTO.getActualPaymentAmount().multiply(new BigDecimal("100")).setScale(0, RoundingMode.DOWN).longValueExact())
                .subId(subscriptionHolderDTO.getApiId())
                .unix(System.currentTimeMillis())
                .build();
        eventPublisher.publish(subscriptionAppliedEvent);
        log.info("sendSubscriptionAppliedEventSEO subscriptionAppliedEvent={}", JSON.toJSONString(subscriptionAppliedEvent));
    }

    /**
     * 取消订阅：给推广发送消息
     *
     * @param order
     */
    private void sendCancelOrderEventSEO(SubscriptionOrder order) {
        SubscriptionCanceledEvent subscriptionCanceledEvent = SubscriptionCanceledEvent.builder()
                .appCode(order.getAppCode())
                .subId(order.getShopifySubscriptionId())
                .unix(System.currentTimeMillis())
                .build();
        eventPublisher.publish(subscriptionCanceledEvent);
        log.info("sendCancelOrderEventSEO message,appcode={},subId={}", order.getAppCode(), order.getShopifySubscriptionId());
    }

    /**
     * shopify 店铺安装事件
     *
     * @param authorizeEventDTO
     */
    private void sendAuthorizeEventEvent(AuthorizeEventDTO authorizeEventDTO) {
        AuthorizeBackEvent authorizeBackEvent = AuthorizeBackEvent.builder()
                .userId(String.valueOf(authorizeEventDTO.getShopifyShopDTO().getId()))
                .userCode("")
                .appCode(authorizeEventDTO.getAppCode())
                .email(authorizeEventDTO.getShopifyShopDTO().getEmail())
                .shopDomain(authorizeEventDTO.getShopifyShopDTO().getDomain())
                .shopifyShopDomain(authorizeEventDTO.getShopDomain())
                .unix(System.currentTimeMillis())
                .build();
        log.info("ShopifyCallbackImpl#sendAuthorizeEventEvent authorizeBackEvent: {}", JSON.toJSONString(authorizeBackEvent));
        publisher.publish(authorizeBackEvent);
    }

    /**
     * 取消订阅
     *
     * @param subscriptionOrder
     */
    private boolean cancelSubscription(SubscriptionOrder subscriptionOrder) {
        log.info("ShopifyCallbackImpl#cancelSubscription subscriptionOrder is : {}",
                JSON.toJSONString(subscriptionOrder));
        if (subscriptionOrder.getStatus() != SubscriptionOrderStatus.IN_EFFECT) {
            log.warn("ShopifyCallbackImpl#cancelSubscription invalid status, status={}", subscriptionOrder.getStatus());
            return false;
        }
        subscriptionOrder.setStatus(SubscriptionOrderStatus.UNSUBSCRIBE);
        subscriptionOrder.setPerformEndTime(new Date());
        subscriptionOrderRepository.saveSubscriptionOrder(subscriptionOrder);
        //取消订阅：给推广发送消息    用户点free
        sendCancelOrderEventSEO(subscriptionOrder);
        return true;
    }

    /**
     * 创建订单
     *
     * @param subscriptionHolder
     */
    private SubscriptionOrder createSubscription(ShopifySubscriptionHolderDTO subscriptionHolder, Long apiId, SubscriptionPlan subscriptionPlan) {
        Long planId = subscriptionHolder.getPlanId();
        if (planId == null) {
            log.warn("plan id is null ! subscription holder is : {}", JSON.toJSONString(subscriptionHolder));
            return null;
        }

        if (subscriptionPlan == null) {
            log.warn("subscription plan is not exist! plan id is : {}", planId);
            return null;
        }

        Long userId = subscriptionHolder.getUserId();
        User user = userRepository.getUser(userId);
        String appCode = subscriptionHolder.getAppCode();

        Integer trialDays = subscriptionHolder.getTrialDays();
        Boolean includeTrial = trialDays != null && trialDays > 0;

        if (user == null) {
            log.warn("subscription user is not exist! user id is : {}", userId);
            return null;
        }

        Date now = new Date();
        SubscriptionOrderAttributes subscriptionOrderAttributes = new SubscriptionOrderAttributes();

        long planDays = TimeUtils.calculateDayCount(subscriptionPlan.getDuration(), subscriptionPlan.getDurationUnit());
        Date endTime = DateUtils.addDays(now, (int) planDays);
        subscriptionOrderAttributes.setPlanDays(planDays);

        if (trialDays != null) {
            subscriptionOrderAttributes.setTrialDays(Long.parseLong(String.valueOf(trialDays)));
            endTime = DateUtils.addDays(endTime, trialDays);
        }

        SubscriptionOrder order = SubscriptionOrder.builder()
                .userId(userId)
                .email(StringUtils.isBlank(user.getEmail()) ? "" : user.getEmail())
                .appCode(appCode)
                .userAppRelationId(0L)
                .nextPlanId(null)
                .nextPlanName(null)
                .status(SubscriptionOrderStatus.IN_EFFECT)
                .autoRenew(Boolean.FALSE)
                .hadNextRenew(Boolean.FALSE)
                .performStartTime(now)
                .performEndTime(endTime)
                // TODO @笔雁 任务扫描忽略
                .nextRenewalTime(endTime)
                .planPrice(subscriptionPlan.getPrice())
                .actualFee(subscriptionHolder.getActualPaymentAmount())
                .isIncludeTrial(includeTrial)
                .subscriptionPlanId(subscriptionPlan.getId())
                .subscriptionPlanName(subscriptionPlan.getName())
                .subscriptionDiscountTag(null)
                .shopifySubscriptionId(apiId)
                .attributes(subscriptionOrderAttributes)
                .deleted(Boolean.FALSE)
                .build();
        subscriptionOrderRepository.saveSubscriptionOrder(order);
        return order;
    }

    @Override
    @Monitor(name = "Shopify App 卸载 Webhook 回调", level = Monitor.Level.P1, layer = Monitor.Layer.CONSUMER)
    public void onAppUninstalledEvent(ShopifyAppUninstalledEventDTO event) {
        log.info("ShopifyCallbackImpl#onAppUninstalledEvent param: {}", JSON.toJSONString(event));
        Long userId = event.getId();
        if (userId == null) {
            return;
        }

        SubscriptionOrder orderInfo = subscriptionOrderRepository.queryEffectOrder(event.getAppCode(), userId);
        log.info("ShopifyCallbackImpl#onAppUninstalledEvent orderInfo is: {}", JSON.toJSONString(orderInfo));
        if (orderInfo == null) {
            return;
        }

        // 卸载应用后取消订阅
        orderInfo.setStatus(SubscriptionOrderStatus.UNSUBSCRIBE);
        orderInfo.setPerformEndTime(new Date());
        subscriptionOrderRepository.saveSubscriptionOrder(orderInfo);
        //取消订阅：给推广发送消息     卸载应用后取消订阅
        sendCancelOrderEventSEO(orderInfo);

        // 事件埋点: 取消老套餐
        planSwitchHelper.onSwitchPlan(orderInfo.getAppCode(), userId, orderInfo, null);

//        Long shopifySubscriptionId = orderInfo.getShopifySubscriptionId();
//        if (shopifySubscriptionId == null) {
//            return;
//        }
//        RecurringApplicationChargeResponse executeResponse = getRecurringApplicationChargeResponse(event.getShopDomain(), event.getAccessToken(), shopifySubscriptionId);
//        if (executeResponse == null) {
//            // TODO: 2023/10/30 确认卸载完应用后订阅信息是否还能查到
//            log.warn("ShopifyCallbackImpl#onAppUninstalledEvent#RecurringApplicationChargeResponse is null, event is: {}", JSON.toJSONString(event));
//            return;
//        }
//
//        if (ShopifySubscriptionStatus.isInvalidStatus(executeResponse.getRecurringApplicationCharge().getStatus())) {
//            // 卸载应用后取消订阅
//            orderInfo.setStatus(SubscriptionOrderStatus.UNSUBSCRIBE);
//            subscriptionOrderRepository.saveSubscriptionOrder(orderInfo);
//        }
    }

    @Override
    public void onAuthorizeEvent(AuthorizeEventDTO event) {
        //给推广发消息
        sendAuthorizeEventEvent(event);
    }

    /**
     * 查 shopify 信息确认卸载状态
     *
     * @param shopDomain
     * @param accessToken
     * @param shopifySubscriptionId
     * @return
     */
    private RecurringApplicationChargeResponse getRecurringApplicationChargeResponse(String shopDomain, String accessToken, Long shopifySubscriptionId) {
        log.info("ShopifyCallbackImpl#getRecurringApplicationChargeResponse shopDomain is: {}, shopifySubscriptionId is: {},accessToken is:{}", shopDomain, shopifySubscriptionId, accessToken);
        try {
            ShopifyClient shopifyClient = ShopifyClientFactory.getShopifyClient(shopDomain, accessToken);
            Single<RecurringApplicationChargeResponse> applicationCharge = shopifyClient.getApi().getRecurringApplicationCharge(shopifySubscriptionId);
            RecurringApplicationChargeResponse executeResponse = shopifyClient.execute(applicationCharge);
            log.info("ShopifyCallbackImpl#onAppUninstalledEvent#executeResponse is: {}", JSON.toJSONString(executeResponse));
            return executeResponse;
        } catch (Exception e) {
            log.error("getRecurringApplicationChargeResponse shopDomain={},accessToken={},shopifySubscriptionId={}", shopDomain, accessToken, shopifySubscriptionId, e);
        }
        return null;
    }
}
