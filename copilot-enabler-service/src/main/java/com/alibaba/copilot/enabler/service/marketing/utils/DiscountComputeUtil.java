package com.alibaba.copilot.enabler.service.marketing.utils;

import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.marketing.constant.DiscountWay;
import com.alibaba.copilot.enabler.domain.marketing.model.DiscountRule;
import com.alibaba.copilot.enabler.domain.marketing.model.DiscountRuleAttributes;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @version 2024/2/27
 */
public final class DiscountComputeUtil {

    public static BigDecimal computeDiscount(BigDecimal originPrice, DiscountRule discountRule) {
        Assertor.assertNonNull(originPrice, "originPrice is null");

        if (discountRule == null) {
            return originPrice;
        }

        DiscountRuleAttributes attributes = discountRule.getAttributes();
        if (attributes == null) {
            return originPrice;
        }

        DiscountWay discountWay = attributes.getDiscountWay();
        BigDecimal discountValue = attributes.getDiscountValue();
        if (discountWay == null || discountValue == null) {
            return originPrice;
        }

        switch (discountWay) {
            case FIXED:
                return discountValue;
            case RATE:
                return originPrice.multiply(discountValue).setScale(2, RoundingMode.HALF_UP);
            default:
                throw new BizException(ErrorCode.INVALID_PARAM, "unrecognized discountWay: " + discountWay);
        }
    }
}
