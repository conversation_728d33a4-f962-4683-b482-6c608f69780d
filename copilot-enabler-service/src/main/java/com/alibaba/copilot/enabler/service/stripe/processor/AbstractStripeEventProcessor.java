package com.alibaba.copilot.enabler.service.stripe.processor;

import com.alibaba.copilot.enabler.client.base.event.DomainEventJsonProducer;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.domain.payment.constant.PaymentConst;
import com.alibaba.copilot.enabler.domain.payment.dto.PaymentAsyncResultDTO;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.StripeGateway;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEventMetadata;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripePaymentMethodResponse;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentToken;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentTokenAttributes;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.repository.PaymentTokenRepository;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.payment.service.OrderDomainService;
import com.alibaba.copilot.enabler.domain.payment.service.PaymentDomainService;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrderAttributes;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.user.model.StripeInfo;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import com.alibaba.copilot.enabler.service.subscription.service.pic.PicStripeSubscriptionService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Slf4j
public abstract class AbstractStripeEventProcessor implements StripeEventProcessor {

    @Resource
    protected PaymentDomainService paymentDomainService;

    @Resource
    protected DomainEventJsonProducer domainEventJsonProducer;

    @Resource
    protected OrderDomainService orderDomainService;
    @Resource
    protected PicStripeSubscriptionService picStripeSubscriptionService;

    @Resource
    protected TradeRecordRepository tradeRecordRepository;
    @Resource
    protected SubscriptionOrderRepository subscriptionOrderRepository;
    @Autowired
    protected PaymentTokenRepository paymentTokenRepository;
    @Autowired
    protected StripeGateway stripeGateway;

    @Autowired
    private UserRepository userRepository;

    @PostConstruct
    public void init() {
        StripeEventProcessorFactory.register(this);
    }

    /**
     * 由子类实现具体的处理方法
     *
     * @param context
     */
    protected abstract void doProcess(StripeExecuteContext context) throws Exception;

    @Override
    public void process(StripeExecuteContext context) {
        try {
            log.info("{} start, context={}", this.getClass().getSimpleName(), JSON.toJSONString(context));
            doProcess(context);
        } catch (Exception e) {
            log.error("{} error, context={}", this.getClass().getSimpleName(), JSON.toJSONString(context), e);
        }
    }


    /**
     * 处理失败情况
     */
    protected void processFailed(StripeEventMetadata metaData) {
        if (metaData.whetherPaymentMode()) {
            // 校验订单状态
            TradeRecord tradeRecord = orderDomainService.getTradeRecord(metaData.getTradeNo());
            if (tradeRecord == null) {
                log.error("PaymentIntentCanceledProcessor, tradeRecord not found, metaData={}", JSON.toJSONString(metaData));
                return;
            }

            if (TradeRecordStatus.SUCC.equals(tradeRecord.getStatus())) {
                log.error("PaymentIntentCanceledProcessor, tradeRecord status is succ, metaData={}", JSON.toJSONString(metaData));
                return;
            }

            // PAYMENT 模式
            PaymentAsyncResultDTO result = buildPaymentResult(metaData, PaymentConst.PAYMENT_FAIL);
            paymentDomainService.updateCashierPayStatus(result);
        }
    }

    /**
     * 构建PAYMENT模式支付结果
     *
     * @param metaData
     * @param status
     * @return
     */
    protected PaymentAsyncResultDTO buildPaymentResult(StripeEventMetadata metaData, String status) {
        PaymentAsyncResultDTO result = new PaymentAsyncResultDTO();
        // S-成功 F-失败
        result.setResultStatus(status);
        // trade no
        result.setPaymentRequestId(metaData.getTradeNo());
        // out trade no
        result.setPaymentId("");
        return result;
    }

    protected Long saveCardInfo2PaymentToken(String paymentMethodId, StripeEventMetadata metaData) {
        log.info("saveCardInfo paymentMethodId={}, metaData={}", paymentMethodId, JSON.toJSONString(metaData));
        try {
            if (StringUtils.isBlank(paymentMethodId)) {
                return null;
            }

            PaymentToken paymentToken = paymentTokenRepository.queryByToken(paymentMethodId);
            if (paymentToken != null
                    && paymentToken.getPaymentMethod() == PaymentMethodEnum.STRIPE) {
                log.info("paymentToken already exists, paymentToken={}", JSON.toJSONString(paymentToken));
                return paymentToken.getId();
            }

            // 支付信息
            TradeRecord tradeRecord = paymentDomainService.queryByTradeNo(metaData.getTradeNo());

            if (tradeRecord == null) {
                log.info("tradeRecord is null, tradeNo={}", metaData.getTradeNo());
                return null;
            }

            // 用户信息
            User user = userRepository.getUser(tradeRecord.getUserId());
            if (user == null) {
                log.info("user is null, userId={}", tradeRecord.getUserId());
                return null;
            }
            // stripe
            StripePaymentMethodResponse stripePaymentMethodResponse = stripeGateway.retrievePaymentMethod(paymentMethodId);

            if (!stripePaymentMethodResponse.isAllowRedisplay()) {
                log.info("stripePaymentMethodResponse is not allow redisplay, paymentMethodId={}", paymentMethodId);
                return null;
            }

            if (!StringUtils.equals(stripePaymentMethodResponse.getCustomerId(), user.getAttributes().getStripeInfoNotNull().getCustomerId())) {
                log.info("stripePaymentMethodResponse customerId is not equal, paymentMethodId={}", paymentMethodId);
                return null;
            }

            // create
            StripeInfo stripeInfo = new StripeInfo();
            stripeInfo.setCustomerId(user.getAttributes().getStripeInfoNotNull().getCustomerId());
            stripeInfo.setSessionMode(metaData.getSessionMode());
            stripeInfo.setCardBrand(StringUtils.upperCase(stripePaymentMethodResponse.getBrand()));
            stripeInfo.setCardLast4(stripePaymentMethodResponse.getLast4());
            stripeInfo.setFunding(StringUtils.upperCase(stripePaymentMethodResponse.getFunding()));

            PaymentTokenAttributes paymentTokenAttributes = new PaymentTokenAttributes(null);
            paymentTokenAttributes.setStripeInfo(stripeInfo);
            PaymentToken token = paymentTokenRepository.createByToken(tradeRecord.getAppCode(), tradeRecord.getUserId(), PaymentMethodEnum.STRIPE, paymentMethodId, paymentTokenAttributes);
            return token.getId();
        } catch (Exception e) {
            log.error("saveCardInfo error, paymentMethodId={}, metaData={}", paymentMethodId, JSON.toJSONString(metaData), e);
            return null;
        }
    }

    protected void savePaymentToken2SubOrder(Long paymentTokenId, String paymentMethodId, SubscriptionOrder subscriptionOrder) {
        // 保存到 order 表
        SubscriptionOrderAttributes attributes = subscriptionOrder.getAttributes();
        attributes.setPaymentTokenId(paymentTokenId.toString());
        attributes.setStripePaymentMethodId(paymentMethodId);
        subscriptionOrder.setAttributes(attributes);
        subscriptionOrderRepository.saveSubscriptionOrder(subscriptionOrder);
    }
}