package com.alibaba.copilot.enabler.service.payment.schedule;

import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.payment.request.TradeRecordsQuery;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.service.payment.schedule.handler.InitiateDeductionHandler;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.taobao.eagleeye.EagleEye;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 发起代扣处理的定时任务
 */
@Component
public class InitiateDeductionTask extends MapJobProcessor {
    public static final Logger TASK_LOG = LoggerFactory.getLogger("taskLogger");

    @Autowired
    private TradeRecordRepository tradeRecordRepository;
    @Autowired
    private InitiateDeductionHandler initiateDeductionHandler;

    @Override
    @Monitor(name = "发起代扣处理的定时任务", level = Monitor.Level.P1, layer = Monitor.Layer.OTHER)
    public ProcessResult process(JobContext context) {

        try {
            EagleEye.startTrace(null, this.getClass().getName(), EagleEye.TYPE_CUSTOM_MESSAGE_SUB);
            TASK_LOG.info("InitiateDeductionTask start……");

            // 查询待发起代扣的订单流水
            TradeRecordsQuery paymentRecordsQuery = TradeRecordsQuery.builder()
                    .tradeDirection(TradeDirection.FORWARD)
                    .status(TradeRecordStatus.TODO)
                    .hadInitiatePay(false)
                    .build();
            List<TradeRecord> tradeRecords = tradeRecordRepository.scanTradeRecords(paymentRecordsQuery);
            // 针对每笔订单流水发起代扣
            for (TradeRecord tradeRecord : tradeRecords) {
                // 目前产生待支付的流水均为AE支付产生的，shopify支付的不会产生待支付的流水
                if (tradeRecord == null) {
                    continue;
                }

                // 发起系统定期代扣支付
                try {
                    initiateDeductionHandler.initiateDeduction(tradeRecord);
                    TASK_LOG.info("InitiateDeductionTask appCode:{},userId:{},tradeNo:{}",
                            tradeRecord.getAppCode(), tradeRecord.getUserId(), tradeRecord.getTradeNo());
                } catch (Exception e) {
                    initiateDeductionHandler.initiateDeductionFail(tradeRecord);
                    TASK_LOG.error("InitiateDeductionTask error,appCode:{},userId:{},tradeNo:{}",
                            tradeRecord.getAppCode(), tradeRecord.getUserId(), tradeRecord.getTradeNo(), e);
                }
            }

            TASK_LOG.info("InitiateDeductionTask completed-ordersCount:{}", tradeRecords.size());
            return new ProcessResult(true);
        } catch (Throwable e) {
            TASK_LOG.error("payAlarm-发起代扣处理的定时任务失败,msg:{}", e.getMessage(), e);
            return new ProcessResult(false);
        } finally {
            EagleEye.endTrace(null);
        }
    }
}
