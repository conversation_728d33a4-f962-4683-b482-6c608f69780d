package com.alibaba.copilot.enabler.service.base.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/31
 */
@Slf4j
@Component
public class CopilotHandlerInterceptor implements HandlerInterceptor {
    private static final String BIZ_CSRF_TOKEN_NAME = "X-CSRF-TOKEN";
    private static final String SESSION_CSRF_TOKEN_NAME = "_tb_token_";

    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           @Nullable ModelAndView modelAndView) throws Exception {
        try {
            if (modelAndView != null) {
                CopilotCsrfToken copilotCsrfToken = new CopilotCsrfToken();
                copilotCsrfToken.setName(BIZ_CSRF_TOKEN_NAME);
                copilotCsrfToken.setToken((String) request.getSession().getAttribute(SESSION_CSRF_TOKEN_NAME));
                modelAndView.addObject(SESSION_CSRF_TOKEN_NAME, copilotCsrfToken);
            }
        } catch (Exception e) {
            log.warn(e.getMessage());
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        CopilotContext.remove();
    }
}
