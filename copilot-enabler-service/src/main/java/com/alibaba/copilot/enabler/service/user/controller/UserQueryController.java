package com.alibaba.copilot.enabler.service.user.controller;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.user.dto.UserOverviewInfoDTO;
import com.alibaba.copilot.enabler.client.user.service.UserQueryService;
import com.alibaba.copilot.enabler.infra.base.constants.ErrorCodes;
import com.alibaba.copilot.enabler.service.user.dto.UserOverviewInfoVO;
import com.alibaba.copilot.enabler.service.user.factory.UserSubscriptionInfoConverter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/29
 */
@Slf4j
@RestController
@RequestMapping("/user")
@Api(tags = "user", value = "user")
public class UserQueryController {

    @Resource
    private UserQueryService userQueryService;

    /**
     * 个人信息
     *
     * @param userId
     * @param appCode
     * @return
     */
    @ApiOperation(value = "个人信息查询", nickname = "query user overview info")
    @RequestMapping(value = "/overview", method = RequestMethod.GET)
    @Monitor(name = "个人订阅信息查询", level = Monitor.Level.P0, layer = Monitor.Layer.WEB)
    public SingleResult<UserOverviewInfoVO> queryUserOverviewInfo(@ApiParam(value = "账号 id") @RequestParam(value = "userId") Long userId,
                                                                  @ApiParam(value = "要开通的服务 / 官网标识") @RequestParam(value = "appCode") String appCode) {
        log.info("UserQueryController#queryUserOverviewInfo# request userId: {}, appCode: {}", userId, appCode);
        UserOverviewInfoDTO userOverviewInfoDTO = userQueryService.queryUserOverviewInfo(userId, appCode);
        UserOverviewInfoVO userOverviewInfoVO = UserSubscriptionInfoConverter.INSTANCE.convertA2B(userOverviewInfoDTO);
        return SingleResult.buildSuccess(userOverviewInfoVO);
    }

}
