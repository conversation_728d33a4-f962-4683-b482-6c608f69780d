package com.alibaba.copilot.enabler.service.user.facade;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.user.constants.UserDTO;
import com.alibaba.copilot.enabler.client.user.dto.UserOverviewInfoDTO;
import com.alibaba.copilot.enabler.client.user.dto.UserSourceDTO;
import com.alibaba.copilot.enabler.client.user.facade.UserQueryHsfApi;
import com.alibaba.copilot.enabler.client.user.service.UserQueryService;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/11
 */
@Service
@Slf4j
@HSFProvider(serviceInterface = UserQueryHsfApi.class)
public class UserQueryHsfApiImpl implements UserQueryHsfApi {

    @Autowired
    private UserQueryService userQueryService;

    @Override
    public SingleResult<UserOverviewInfoDTO> queryUserOverviewInfo(Long userId, String appCode) {
        UserOverviewInfoDTO userOverviewInfoDTO = null;
        try {
            userOverviewInfoDTO = userQueryService.queryUserOverviewInfo(userId, appCode);
        } catch (Exception e) {
            return SingleResult.buildFailure(e.getMessage());
        }

        return SingleResult.buildSuccess(userOverviewInfoDTO);
    }

    @Override
    public SingleResult<UserDTO> queryUserDTO(Long userId, String appCode) {
        UserDTO userDTO = userQueryService.queryUserDTO(userId, appCode);
        return SingleResult.buildSuccess(userDTO);
    }

    @Override
    public SingleResult<UserSourceDTO> queryUserSource(Long userId, String email) {
        UserSourceDTO userSourceDTO = userQueryService.queryUserSource(userId, email);
        return SingleResult.buildSuccess(userSourceDTO);
    }
}
