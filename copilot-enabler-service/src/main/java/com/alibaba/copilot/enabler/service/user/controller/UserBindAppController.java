package com.alibaba.copilot.enabler.service.user.controller;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.user.dto.UserAppRelationDTO;
import com.alibaba.copilot.enabler.client.user.request.AppBindingRequest;
import com.alibaba.copilot.enabler.client.user.request.UserAppRelationQuery;
import com.alibaba.copilot.enabler.client.user.service.UserAppRelationService;
import com.alibaba.copilot.enabler.infra.base.constants.ErrorCodes;
import com.alibaba.copilot.enabler.service.base.interceptor.CopilotContext;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/31
 */
@Slf4j
@RestController
@RequestMapping("/user/app")
@Api(tags = "user", value = "user")
public class UserBindAppController {

    @Resource
    private UserAppRelationService userAppRelationService;

    /**
     * 检查产品绑定关系
     *
     * @param appCode
     * @return
     */
    @ApiOperation(value = "检查产品绑定关系", nickname = "check app binding")
    @RequestMapping(value = "/checkAppBinding", method = RequestMethod.GET)
    @Monitor(name = "检查产品绑定关系", level = Monitor.Level.P1, layer = Monitor.Layer.WEB)
    public SingleResult<Boolean> checkAppBinding(@ApiParam(value = "邮箱") @RequestParam(value = "email")String email,
                                                 @ApiParam(value = "产品服务") @RequestParam(value = "appCode") String appCode) {
        log.info("UserController#checkAppBinding request email: {}, appCode: {}", email, appCode);
        UserAppRelationQuery appRelationQuery = UserAppRelationQuery.builder().email(email).appCode(appCode).build();
        UserAppRelationDTO appRelation = userAppRelationService.queryUserAppRelation(appRelationQuery);
        if (appRelation == null) {
            return SingleResult.buildSuccess(Boolean.FALSE);
        }
        return SingleResult.buildSuccess(Boolean.TRUE);
    }

    /**
     * 登录成功写入产品绑定关系
     *
     * @param appBindingRequest
     * @return
     */
    @ApiOperation(value = "登录成功写入产品绑定关系", nickname = "do app binding")
    @RequestMapping(value = "/appBinding", method = RequestMethod.POST)
    @Monitor(name = "登录成功写入产品绑定关系", level = Monitor.Level.P0, layer = Monitor.Layer.WEB)
    public SingleResult<Boolean> doAppBinding(@ApiParam(value = "绑定产品请求")
                                              @RequestBody
                                              @Valid AppBindingRequest appBindingRequest) {
        log.info("UserController#doAppBinding request is; {}", JSON.toJSONString(appBindingRequest));
        long userId = CopilotContext.getUserId();
        UserAppRelationDTO userAppRelationDTO = userAppRelationService.createUserAppRelation(userId, appBindingRequest);
        if (userAppRelationDTO == null) {
            SingleResult.buildSuccess(Boolean.FALSE);
        }
        return SingleResult.buildSuccess(Boolean.TRUE);
    }
}
