package com.alibaba.copilot.enabler.service.subscription.service;

import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.robot.dto.TradeRecordDTO;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscriptionOrderDTO;
import com.alibaba.copilot.enabler.client.subscription.service.SubscriptionService;
import com.alibaba.copilot.enabler.client.user.dto.UserAppRelationDTO;
import com.alibaba.copilot.enabler.client.user.request.UserAppRelationQuery;
import com.alibaba.copilot.enabler.client.user.service.UserAppRelationService;
import com.alibaba.copilot.enabler.domain.base.utils.PaymentUtils;
import com.alibaba.copilot.enabler.domain.base.utils.TimeUtils;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.domain.payment.gateway.geoip.GeoIpGateway;
import com.alibaba.copilot.enabler.domain.payment.gateway.geoip.dto.GetAddressInfoByIpResponse;
import com.alibaba.copilot.enabler.domain.payment.model.IpAddressInfo;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecordAttributes;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrderAttributes;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import com.alibaba.copilot.enabler.infra.base.constants.ErrorCodes;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 订阅基础服务
 *
 * <AUTHOR>
 * @date 2024/9/27 上午11:17
 */
@Slf4j
@Service
public class SubscriptionServiceImpl implements SubscriptionService {

    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;

    @Resource
    private SubscriptionPlanRepository subscriptionPlanRepository;

    @Resource
    private TradeRecordRepository tradeRecordRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private UserAppRelationService userAppRelationService;
    @Autowired
    private GeoIpGateway geoIpGateway;

    /**
     * 之前基建会保存订阅计划。现在不会。
     */
    @Override
    public Long createSubscriptionOrderNoPlan(SubscriptionOrderDTO subscriptionOrderDTO) {
        log.info("SubscriptionManageServiceImpl.createSubscriptionOrder, subscriptionOrderDTO={}", JSON.toJSONString(subscriptionOrderDTO));

        User user = userRepository.getUser(subscriptionOrderDTO.getUserId());
        Assertor.assertNonNull(user, "user is null");

        UserAppRelationDTO userAppRelationDTO = getUserAppRelation(subscriptionOrderDTO.getAppCode(), subscriptionOrderDTO.getUserId());
        Assertor.assertNonNull(userAppRelationDTO, "userAppRelationDTO is null");



        SubscriptionOrder order = SubscriptionOrder.builder()
                .userId(subscriptionOrderDTO.getUserId())
                .email(StringUtils.isBlank(subscriptionOrderDTO.getEmail()) ? "" : subscriptionOrderDTO.getEmail())
                .appCode(subscriptionOrderDTO.getAppCode())
                .userAppRelationId(userAppRelationDTO.getId())
                .nextPlanId(null)
                .nextPlanName(null)
                .status(SubscriptionOrderStatus.PENDING_PAYMENT)
                .autoRenew(Boolean.TRUE)
                .hadNextRenew(Boolean.FALSE)
                .performStartTime(subscriptionOrderDTO.getPerformStartTime())
                .performEndTime(subscriptionOrderDTO.getPerformEndTime())
                .nextRenewalTime(subscriptionOrderDTO.getPerformEndTime())
                .planPrice(subscriptionOrderDTO.getPlanPrice())
                .isIncludeTrial(subscriptionOrderDTO.getIsIncludeTrial())
                .subscriptionPlanId(subscriptionOrderDTO.getSubscriptionPlanId())
                .subscriptionPlanName(subscriptionOrderDTO.getSubscriptionPlanName())
                .subscriptionDiscountTag(null)
                .subscriptionPayType(null)
                .outerSubscriptionId(subscriptionOrderDTO.getOuterSubscriptionId())
                .attributes(new SubscriptionOrderAttributes())
                .deleted(Boolean.FALSE)
                .build();
        subscriptionOrderRepository.saveSubscriptionOrder(order);
        return order.getId();
    }

    /**
     * 创建订单
     *
     * @param subscriptionOrderDTO
     * @return
     */
    @Override
    public Long createSubscriptionOrder(SubscriptionOrderDTO subscriptionOrderDTO) {
        log.info("SubscriptionManageServiceImpl.createSubscriptionOrder, subscriptionOrderDTO={}", JSON.toJSONString(subscriptionOrderDTO));

        User user = userRepository.getUser(subscriptionOrderDTO.getUserId());
        Assertor.assertNonNull(user, "user is null");

        UserAppRelationDTO userAppRelationDTO = getUserAppRelation(subscriptionOrderDTO.getAppCode(), subscriptionOrderDTO.getUserId());
        Assertor.assertNonNull(userAppRelationDTO, "userAppRelationDTO is null");

        SubscriptionPlan subscriptionPlan = subscriptionPlanRepository.queryByPlanId(subscriptionOrderDTO.getSubscriptionPlanId(), Boolean.FALSE);
        Assertor.assertNonNull(subscriptionPlan, "subscriptionPlan is null");

        Date now = new Date();
        SubscriptionOrderAttributes subscriptionOrderAttributes = new SubscriptionOrderAttributes();

        long planDays = TimeUtils.calculateDayCount(subscriptionPlan.getDuration(), subscriptionPlan.getDurationUnit());
        Date endTime = DateUtils.addDays(now, (int) planDays);
        subscriptionOrderAttributes.setPlanDays(planDays);

        long totalTrailDay = 0;
        Boolean includeTrial = false;
        Long trialDuration = subscriptionPlan.getTrialDuration();
        String trialDurationUnit = subscriptionPlan.getTrialDurationUnit();
        if (trialDuration != null && StringUtils.isNotBlank(trialDurationUnit)) {
            totalTrailDay = TimeUtils.calculateDayCount(trialDuration, trialDurationUnit);
        }

        if (totalTrailDay > 0) {
            subscriptionOrderAttributes.setTrialDays(Long.parseLong(String.valueOf(totalTrailDay)));
            endTime = DateUtils.addDays(endTime, (int) totalTrailDay);
            includeTrial = true;
        }

        SubscriptionOrder order = SubscriptionOrder.builder()
                .userId(subscriptionOrderDTO.getUserId())
                .email(StringUtils.isBlank(subscriptionOrderDTO.getEmail()) ? "" : subscriptionOrderDTO.getEmail())
                .appCode(subscriptionOrderDTO.getAppCode())
                .userAppRelationId(userAppRelationDTO.getId())
                .nextPlanId(null)
                .nextPlanName(null)
                .status(SubscriptionOrderStatus.PENDING_PAYMENT)
                .autoRenew(Boolean.TRUE)
                .hadNextRenew(Boolean.FALSE)
                .performStartTime(now)
                .performEndTime(endTime)
                .nextRenewalTime(endTime)
                .planPrice(subscriptionPlan.getPrice())
                .isIncludeTrial(includeTrial)
                .subscriptionPlanId(subscriptionPlan.getId())
                .subscriptionPlanName(subscriptionPlan.getName())
                .subscriptionDiscountTag(null)
                .subscriptionPayType(null)
                .outerSubscriptionId(subscriptionOrderDTO.getOuterSubscriptionId())
                .attributes(subscriptionOrderAttributes)
                .deleted(Boolean.FALSE)
                .build();
        subscriptionOrderRepository.saveSubscriptionOrder(order);

        return order.getId();
    }

    /**
     * 查询用户关联信息
     */
    private UserAppRelationDTO getUserAppRelation(String appCode, Long userId) {
        UserAppRelationQuery userAppRelationQuery = UserAppRelationQuery.builder()
                .appCode(appCode)
                .userId(userId)
                .build();
        UserAppRelationDTO userAppRelationDTO = userAppRelationService.queryUserAppRelation(userAppRelationQuery);
        Assertor.assertNonNull(userAppRelationDTO, ErrorCodes.USER_NOT_SUBSCRIBE_APP);
        return userAppRelationDTO;
    }

    /**
     * 更新订单
     *
     * @param subscriptionOrderDTO
     * @return
     */
    @Override
    public Boolean updateSubscriptionOrder(SubscriptionOrderDTO subscriptionOrderDTO) {
        log.info("SubscriptionManageServiceImpl.updateSubscriptionOrder, subscriptionOrderDTO={}", JSON.toJSONString(subscriptionOrderDTO));

        SubscriptionOrder subscriptionOrder = null;
        if (subscriptionOrderDTO.getId() != null) {
            subscriptionOrder = subscriptionOrderRepository.getByOrderId(subscriptionOrderDTO.getId());
        } else if (StringUtils.isNotBlank(subscriptionOrderDTO.getOuterSubscriptionId())) {
            subscriptionOrder = subscriptionOrderRepository.getByOuterSubscriptionId(null, subscriptionOrderDTO.getOuterSubscriptionId());
        }
        log.info("SubscriptionManageServiceImpl.updateSubscriptionOrder, subscriptionOrder={}", JSON.toJSONString(subscriptionOrder));
        if (subscriptionOrder == null) {
            return false;
        }

        if (subscriptionOrderDTO.getStatus() != null) {
            subscriptionOrder.setStatus(subscriptionOrderDTO.getStatus());
        }
        if (StringUtils.isNotBlank(subscriptionOrderDTO.getStripeSubscriptionId())) {
            subscriptionOrder.getAttributes().setStripeSubscriptionId(subscriptionOrderDTO.getStripeSubscriptionId());
        }
        if (subscriptionOrderDTO.getSubscriptionPayType() != null) {
            subscriptionOrder.setSubscriptionPayType(subscriptionOrderDTO.getSubscriptionPayType());
        }
        if (subscriptionOrderDTO.getNextRenewalTime() != null) {
            subscriptionOrder.setNextRenewalTime(subscriptionOrderDTO.getNextRenewalTime());
        }
        if (subscriptionOrderDTO.getPerformStartTime() != null) {
            subscriptionOrder.setPerformStartTime(subscriptionOrderDTO.getPerformStartTime());
        }
        if (subscriptionOrderDTO.getPerformEndTime() != null) {
            subscriptionOrder.setPerformEndTime(subscriptionOrderDTO.getPerformEndTime());
        }
        subscriptionOrder.setGmtModified(new Date());

        log.info("SubscriptionManageServiceImpl.updateSubscriptionOrder, before, subscriptionOrder={}", JSON.toJSONString(subscriptionOrder));
        subscriptionOrderRepository.saveSubscriptionOrder(subscriptionOrder);
        log.info("SubscriptionManageServiceImpl.updateSubscriptionOrder, succeed, subscriptionOrder={}", JSON.toJSONString(subscriptionOrder));

        return true;
    }

    /**
     * 创建流水
     *
     * @param tradeRecordDTO
     * @return
     */
    @Override
    public Long createTradeRecord(TradeRecordDTO tradeRecordDTO) {
        log.info("SubscriptionManageServiceImpl.createPayRecord, tradeRecordDTO={}", JSON.toJSONString(tradeRecordDTO));

        SubscriptionOrder subscriptionOrder = subscriptionOrderRepository.getByOrderId(tradeRecordDTO.getSubscriptionOrderId());
        Assertor.assertNonNull(subscriptionOrder, "subscriptionPlan is null");

        SubscriptionPlan subscriptionPlan = subscriptionPlanRepository.queryByPlanId(tradeRecordDTO.getSubscriptionPlanId(), Boolean.FALSE);
        Assertor.assertNonNull(subscriptionPlan, "subscriptionPlan is null");


        TradeRecord payRecord = TradeRecord.builder()
                .userId(subscriptionOrder.getUserId())
                .subscriptionOrderId(subscriptionOrder.getId())
                .appCode(subscriptionOrder.getAppCode())
                .tradeTime(tradeRecordDTO.getTradeTime())
                .tradeAmount(tradeRecordDTO.getTradeAmount())
                .tradeCurrency(tradeRecordDTO.getTradeCurrency() != null ? tradeRecordDTO.getTradeCurrency() : AEPaymentConstants.CURRENCY_CODE)
                .tradeDirection(TradeDirection.valueOf(tradeRecordDTO.getTradeDirection()))
                .paymentMethod(tradeRecordDTO.getPaymentMethod())
                .status(TradeRecordStatus.valueOf(tradeRecordDTO.getStatus()))
                .taxAmount(tradeRecordDTO.getTaxAmount())
                .taxCurrency(tradeRecordDTO.getTaxCurrency())
                .transactionAmount(tradeRecordDTO.getTransactionAmount())
                .transactionCurrency(tradeRecordDTO.getTransactionCurrency())
                .tradeNo(PaymentUtils.generateTradeNo(subscriptionOrder.getUserId()))
                .deleted(false)
                .attributes(new TradeRecordAttributes(null))
                .build();

        TradeRecordAttributes attributes = payRecord.getAttributes();
        attributes.setSubscriptionPlanId(subscriptionPlan.getId());
        attributes.setSubscriptionPlanName(subscriptionPlan.getName());
        if (StringUtils.isNotBlank(tradeRecordDTO.getClientIp())) {
            GetAddressInfoByIpResponse addressInfoByIp = geoIpGateway.getAddressInfoByIp(tradeRecordDTO.getClientIp());
            if (addressInfoByIp != null) {
                IpAddressInfo ipAddressInfo = new IpAddressInfo();
                BeanUtils.copyProperties(addressInfoByIp, ipAddressInfo);
                attributes.setIpAddressInfo(ipAddressInfo);
            }
        }

        tradeRecordRepository.createOrUpdateTradeRecord(payRecord);

        return payRecord.getId();
    }
}
