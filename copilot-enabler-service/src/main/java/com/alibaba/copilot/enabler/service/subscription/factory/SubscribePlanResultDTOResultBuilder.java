package com.alibaba.copilot.enabler.service.subscription.factory;

import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.payment.dto.CreatePaymentSessionDTO;
import com.alibaba.copilot.enabler.client.payment.dto.PaymentSessionDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanResultDTO;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.infra.base.utils.ModelConvertUtils;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 * @version 2024/1/16
 */
@AllArgsConstructor
public class SubscribePlanResultDTOResultBuilder implements Builder<SingleResult<SubscribePlanResultDTO>> {

    private final TradeRecord payRecord;
    private final CreatePaymentSessionDTO createSessionDTO;
    private final SingleResult<PaymentSessionDTO> paymentSession;

    @Override
    public SingleResult<SubscribePlanResultDTO> build() {
        return ModelConvertUtils.convertSingResult(paymentSession, this::convertDTO);
    }

    @NotNull
    private SubscribePlanResultDTO convertDTO(PaymentSessionDTO session) {
        return new SubscribePlanResultDTO()
                .setNeedPay(true)
                .setPaymentRequestId(createSessionDTO.getPaymentRequestId())
                .setOrderId(payRecord.getSubscriptionOrderId())
                .setPaymentSessionData(session.getPaymentSessionData())
                .setPaymentSessionExpiryTime(session.getPaymentSessionExpiryTime())
                .setPaymentSessionId(session.getPaymentSessionId())
                .setEnvironment(session.getEnvironment());
    }
}
