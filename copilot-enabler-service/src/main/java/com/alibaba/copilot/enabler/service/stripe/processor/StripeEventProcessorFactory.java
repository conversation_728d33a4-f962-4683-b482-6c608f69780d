package com.alibaba.copilot.enabler.service.stripe.processor;

import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEventMetadata;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
public class StripeEventProcessorFactory {

    private static final Map<String, StripeEventProcessor> MAP = new ConcurrentHashMap<>();


    public static void register(StripeEventProcessor processor) {
        String key = buildKey(processor);
        if (StringUtils.isNotBlank(key)) {
            MAP.put(key, processor);
        }
    }

    public static StripeEventProcessor getProcessorNotNull(StripeExecuteContext context) {
        String type = context.getEventType();
        StripeEventMetadata metaData = context.getMetaData();
        String appCode = metaData.getAppCode();
        StripeEventProcessor processor = MapUtils.getObject(MAP, buildKey(type, appCode));
        if (Objects.isNull(processor)) {
            throw new RuntimeException("no stripe processor for: " + type + ", " + appCode);
        }
        return processor;
    }


    public static StripeEventProcessor getProcessorNotNull(String type, String appCode) {
        return MapUtils.getObject(MAP, buildKey(type, appCode));
    }

    private static String buildKey(StripeEventProcessor processor) {
        if (Objects.nonNull(processor)) {
            String type = processor.getEventType();
            String appCode = processor.getAppCode();
            return buildKey(type, appCode);
        }
        return null;
    }

    private static String buildKey(String type, String appCode) {
        if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(appCode)) {
            return type + "#" + appCode;
        }
        return null;
    }

}