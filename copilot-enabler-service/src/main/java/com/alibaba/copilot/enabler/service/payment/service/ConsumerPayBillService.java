package com.alibaba.copilot.enabler.service.payment.service;

import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.payment.dto.PayBillConfigDTO;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.infra.base.utils.DateUtils;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.io.font.constants.StandardFonts;
import com.itextpdf.io.image.ImageData;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.property.TabAlignment;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.layout.property.UnitValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.RoundingMode;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Objects;
import java.util.UUID;

@Service
@Slf4j
public class ConsumerPayBillService {
    @Resource
    private PdfOssService pdfOssService;

    @Resource
    private TradeRecordRepository tradeRecordRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    protected SubscriptionPlanRepository subscriptionPlanRepository;

    /**
     * 获取pdf url
     *
     * @param appEnum       业务身份
     * @param userId        用户id
     * @param tradeRecordNo 交易流水号
     * @return pdf的URL
     * @throws IOException
     */
    public String queryBillInvoice(AppEnum appEnum, Long userId, String tradeRecordNo) throws IOException {
        Assertor.assertParam(appEnum != null, "appEnum illegal");
        PayBillConfigDTO payBillConfigDTO = SwitchConfig.appPayInfoConfigs.get(appEnum.name());
        Assertor.assertParam(payBillConfigDTO != null, "payBillConfig Switch not config");

        Assertor.assertParam(userId != null, "userId illegal");
        Assertor.assertParam(StringUtils.isNotBlank(tradeRecordNo), "tradeRecordNo illegal");

        TradeRecord tradeRecord = tradeRecordRepository.queryByTradeNo(tradeRecordNo);
        Assertor.asserts(tradeRecord != null, "tradeRecordNo illegal");
        Assertor.asserts(Objects.equals(userId, tradeRecord.getUserId()), "tradeRecordNo illegal");
        // 仅支持正向交易
        Assertor.asserts(tradeRecord.getTradeDirection() == TradeDirection.FORWARD, "tradeRecordNo illegal");

        String payBillInvoiceFileName = tradeRecord.getAttributes().getPayBillInvoiceFileName();
        if (StringUtils.isNotBlank(payBillInvoiceFileName)) {
            String fileUrl = pdfOssService.getFileUrl(appEnum, userId, payBillInvoiceFileName);
            if (StringUtils.isNotBlank(fileUrl)) {
                return fileUrl;
            }
        }

        User user = userRepository.getUser(userId);

        String fileName = UUID.randomUUID().toString();
        fileName = fileName.replace("-", "_");

        // 生成pdf
        ByteArrayOutputStream os = generatePdf(payBillConfigDTO, user, tradeRecord);

        InputStream is = new ByteArrayInputStream(os.toByteArray());

        // 保存到OSS
        String url = pdfOssService.saveFile(appEnum, userId, fileName, is);
        if (StringUtils.isNotBlank(url)) {
            // 更新文件名到DB
            tradeRecord.getAttributes().setPayBillInvoiceFileName(fileName);
            tradeRecordRepository.createOrUpdateTradeRecord(tradeRecord);
        }

        return url;
    }

    /**
     * 生成pdf
     *
     * @param payBillConfigDTO 业务配置信息
     * @param user             消费者信息
     * @param tradeRecord      交易流水
     * @return pdf
     * @throws IOException
     */
    @NotNull
    private ByteArrayOutputStream generatePdf(PayBillConfigDTO payBillConfigDTO, User user, TradeRecord tradeRecord) throws IOException {
        String logo = payBillConfigDTO.getProductLogo();
        String companyNameStr = payBillConfigDTO.getCompanyName();
        String companyAddressStr = payBillConfigDTO.getCompanyAddress();
        String productName = payBillConfigDTO.getProductName();
        String userEmail = user == null ? StringUtils.EMPTY : user.getEmail();
        String time = DateUtils.format(tradeRecord.getTradeTime(), DateUtils.YYYY_MM_DD_HH_MM_SS);
        String planName = tradeRecord.getAttributes().getSubscriptionPlanName();
        String actualFee = "$" + (tradeRecord.getTradeAmount().setScale(2, RoundingMode.HALF_UP));
        String companyEmail = payBillConfigDTO.getCompanyEmail();

        ByteArrayOutputStream os = new ByteArrayOutputStream();
        PdfWriter writer = new PdfWriter(os);
        PdfDocument pdfDoc = new PdfDocument(writer);

        pdfDoc.addNewPage();
        Document doc = new Document(pdfDoc);

        SubscriptionPlan subscriptionPlan = subscriptionPlanRepository.queryByPlanId(tradeRecord.getAttributes().getSubscriptionPlanId(), false);
        if (subscriptionPlan != null && StringUtils.isNotBlank(subscriptionPlan.getDescription())) {
            planName = subscriptionPlan.getDescription();
        }

        // 设置字体
        String fontProgram = "/usr/share/fonts/Inter-Regular.ttf";
        PdfFont font = PdfFontFactory.createFont(fontProgram, PdfEncodings.IDENTITY_H);

        // 设置文档边距
        doc.setMargins(20, 30, 30, 50);

        // 添加公司logo
        addCompanyLogo(logo, pdfDoc, doc);

        // 添加公司头信息（名称和地址）
        addCompanyHead(companyNameStr, font, doc, companyAddressStr);

        // 添加发票标题
        addInvoiceTitle(doc);

        String tradeRecordNo = tradeRecord.getTradeNo();

        // 添加发票头信息（商品名、用户邮箱、交易时间等）
        addInvoiceHead(productName, tradeRecordNo, doc, userEmail, time);

        // 添加发票表格
        addInvoiceTable(font, tradeRecordNo, planName, actualFee, doc);

        // 添加发票备注区域
        addInvoiceRemark(doc, font, companyNameStr, companyEmail);

        // 关闭文档
        doc.close();
        return os;
    }

    /**
     * 添加公司logo
     */
    private void addCompanyLogo(String logo, PdfDocument pdfDoc, Document doc) throws MalformedURLException {
        ImageData imageData = ImageDataFactory.create(new URL(logo));
        Image image = new Image(imageData);
        image.scaleAbsolute(95, 20);
        image.setFixedPosition(50, pdfDoc.getPage(1).getPageSize().getHeight() - 50);
        doc.add(image);
    }

    /**
     * 添加公司头信息（名称和地址）
     */
    private void addCompanyHead(String companyNameStr, PdfFont font, Document doc, String companyAddressStr) {
        // 添加公司名称和地址
        Paragraph companyName = new Paragraph()
                .add(new Text(companyNameStr)
                        .setFont(font)
                        .setFontSize(8)
                        .setFontColor(new DeviceRgb(140, 140, 140)));
        companyName.setTextAlignment(TextAlignment.RIGHT);
        companyName.setMarginBottom(2);
        doc.add(companyName);

        Paragraph companyAddress = new Paragraph()
                .add(new Text(companyAddressStr)
                        .setFont(font)
                        .setFontSize(8)
                        .setFontColor(new DeviceRgb(140, 140, 140)));
        companyAddress.setTextAlignment(TextAlignment.RIGHT);
        doc.add(companyAddress);
    }

    /**
     * 添加发票标题
     */
    private void addInvoiceTitle(Document doc) throws IOException {
        Paragraph invoiceTitle = new Paragraph("Invoice")
                .setFont(PdfFontFactory.createFont(StandardFonts.HELVETICA_BOLD))
                .setFontSize(16);
        invoiceTitle.setTextAlignment(TextAlignment.CENTER);
        doc.add(invoiceTitle);
    }

    /**
     * 添加发票头信息（商品名、用户邮箱、交易时间等）
     */
    private void addInvoiceHead(String productName, String tradeRecordNo, Document doc, String userEmail, String time) {
        Paragraph customerNameAndInvoiceNo = new Paragraph()
                .addTabStops(new TabStop(290, TabAlignment.RIGHT))
                .setFontSize(8)
                .add("For: " + productName + " Subscription")
                .add(new Tab()).add(new Tab())
                .add("Invoice No.: " + tradeRecordNo);
        doc.add(customerNameAndInvoiceNo);

        Paragraph emailAddressAndInvoiceDate = new Paragraph()
                .addTabStops(new TabStop(290, TabAlignment.RIGHT))
                .setFontSize(8)
                .add("Email Address: " + userEmail)
                .add(new Tab()).add(new Tab())
                .add("Invoice Date: " + time + " UTC+8");
        emailAddressAndInvoiceDate.setMarginBottom(15);
        doc.add(emailAddressAndInvoiceDate);
    }

    /**
     * 添加发票表格
     */
    private void addInvoiceTable(PdfFont font, String tradeRecordNo, String planName, String actualFee, Document doc) {
        // 添加表格
        float[] columnWidths = {130, 551, 221};
        UnitValue[] columnWidthsInPoints = {
                UnitValue.createPointValue(columnWidths[0]),
                UnitValue.createPointValue(columnWidths[1]),
                UnitValue.createPointValue(columnWidths[2])
        }; // 将宽度值转换为UnitValue对象
        Table table = new Table(columnWidthsInPoints);

        // 添加表头
        table.addHeaderCell(createCell("", font, false, false));
        table.addHeaderCell(createCell("Description", font, false, false));
        table.addHeaderCell(createCell("Amount", font, false, true));

        // 添加表格数据
        table.addCell(createCell("", font, true, false));
        table.addCell(createCell("OrderID:" + tradeRecordNo, font, true, false));
        table.addCell(createCell("", font, true, false));
        table.addCell(createCell("", font, false, false));
        table.addCell(createCell(planName, font, false, false));
        table.addCell(createCell(actualFee, font, false, true));

        Cell cell = new Cell(1, 2);
        cell.add(new Paragraph("Total").setFont(font).setFontSize(8));
        cell.setTextAlignment(TextAlignment.RIGHT);
        table.addCell(cell);
        table.addCell(createCell(actualFee, font, false, true));

        // 将表格添加到文档
        doc.add(table);
    }

    /**
     * 添加发票备注区域
     */
    private void addInvoiceRemark(Document doc, PdfFont font, String companyNameStr, String companyEmail) {
        doc.add(createParagraph("E. & O.E.", font).setTextAlignment(TextAlignment.LEFT));
        doc.add(createParagraph("\nNote:", font));
        doc.add(createParagraph("1. " + companyNameStr + " reserves all rights to determine and conclude all items and amounts set " +
                "out in this invoice and all services and products defined herein.", font));
        doc.add(createParagraph("2. All enquires shall be directed to our authorized agent: " + companyEmail, font));
        doc.add(createParagraph("3. This is a computer-generated document and no signature is required.", font));
        doc.add(createParagraph("4. For Singapore customers, price payable includes GST. For non-Singapore customers, no GST is applied.", font));
        doc.add(createParagraph("5. Kindly notify us in writing within seven (7) days from the date of this invoice if you have any objections to" +
                " the amounts stated above, failing which this invoice shall be deemed to be correct in all respects.", font));
        doc.add(createParagraph("6. We reserve the right to impose a late interest charge at the rate of two percent (2%) per month" +
                " or the maximum amount permitted by law, whichever is higher, on the amounts remaining unpaid the date when full payment is received.", font));
    }

    private Cell createCell(String content, PdfFont font, boolean isHeader, boolean isRight) {
        if(content == null){
            content = StringUtils.EMPTY;
        }

        Cell cell = new Cell();
        cell.add(new Paragraph(content).setFont(font).setFontSize(8));
        if (isHeader) {
            Color bgColor = new DeviceRgb(217, 217, 217);
            cell.setBackgroundColor(bgColor);
        }
        cell.setTextAlignment(isRight ? TextAlignment.RIGHT : TextAlignment.LEFT);
        return cell;
    }

    private Paragraph createParagraph(String content, PdfFont font) {
        return new Paragraph(content)
                .setFont(font)
                .setFontSize(8)
                .setMultipliedLeading(0.8f);
    }
}
