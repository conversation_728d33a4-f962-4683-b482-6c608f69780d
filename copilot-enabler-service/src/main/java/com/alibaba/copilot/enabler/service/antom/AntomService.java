package com.alibaba.copilot.enabler.service.antom;

import com.alibaba.copilot.enabler.domain.base.mq.MessageQueueProducer;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.alibaba.copilot.enabler.service.antom.AntomConsts.METAQ_TAG;
import static com.alibaba.copilot.enabler.service.antom.AntomConsts.METAQ_TOPIC;

/**
 * Antom 服务类
 *
 * <AUTHOR>
 * @date 2025/05/01
 */
@Service
@Slf4j
public class AntomService {

    @Resource(name = "antomMessageProducer")
    private MessageQueueProducer messageQueueProducer;

    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;

    /**
     * 发送 webhook 事件到消息队列
     *
     * @param webhookBody webhook 请求体
     */
    public void sendWebhookEvent(String webhookBody) {
        log.info("AntomService sendWebhookEvent, webhookBody={}", webhookBody);
        messageQueueProducer.send(METAQ_TOPIC, METAQ_TAG, webhookBody);
    }
}
