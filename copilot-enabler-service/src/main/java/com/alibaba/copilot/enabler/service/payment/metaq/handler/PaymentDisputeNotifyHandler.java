package com.alibaba.copilot.enabler.service.payment.metaq.handler;

import com.alibaba.aepay.fund.business.api.payment.dto.notify.DisputeNotifyDTO;
import com.alibaba.copilot.enabler.client.email.dto.CancelSubscribeForRejectPayEmailDTO;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.constant.StatusFlowReasonEnum;
import com.alibaba.copilot.enabler.client.payment.constant.dispute.DisputeResult;
import com.alibaba.copilot.enabler.client.payment.constant.dispute.DisputeStatus;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageStatusEnum;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageTypeEnum;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.model.MessageInfo;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentInfo;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.service.OrderDomainService;
import com.alibaba.copilot.enabler.domain.payment.service.PaymentDomainService;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategyFactory;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 拒付通知handler
 */
@Slf4j
@Component
public class PaymentDisputeNotifyHandler {

    @Autowired
    private OrderDomainService orderDomainService;

    @Autowired
    private SubscriptionOrderRepository subscriptionOrderRepository;

    @Autowired
    private PaymentDomainService paymentDomainService;


    /**
     * 支付拒付处理
     */
    @Transactional(rollbackFor = Exception.class)
    public void handle(String tradeNo, String msgId, String notifyContentStr, DisputeNotifyDTO disputeNotifyDTO) {

        TradeRecord tradeRecord = orderDomainService.getTradeRecord(tradeNo);
        if (tradeRecord == null) {
            return;
        }

        if (PaymentTypeEnum.ONE_TIME_PAYMENT.equals(tradeRecord.getPaymentType())) {
            // 一次型
            paymentDomainService.publishDisputeEvent(tradeRecord, DisputeStatus.CREATED.name(), null);
            return;
        }

        MessageInfo messageInfo = MessageInfo.builder()
                .msgId(msgId)
                .notifyContentOrRequestParam(notifyContentStr)
                .response(ConsumeConcurrentlyStatus.CONSUME_SUCCESS.name())
                .typeEnum(MessageTypeEnum.PAYMENT_REFUND_NOTIFY)
                .statusEnum(MessageStatusEnum.SUCCESS)
                .entityId(tradeNo)
                .build();

        PaymentInfo paymentInfo = PaymentInfo.builder()
                .outTradeNo(disputeNotifyDTO.getDisputeId())
                .tradeTime(disputeNotifyDTO.getDisputeTime())
                .build();

        // 取消订阅
        orderDomainService.refundSuccess(tradeRecord, messageInfo, paymentInfo, StatusFlowReasonEnum.USER_HAD_DISPUTE);

        // 发送「由于拒付导致取消订单」的邮件
        sendCancelSubscribeForRejectPayEmail(tradeRecord);
    }

    public void handleFail(String tradeNo, String msgId, String notifyContentStr) {

        TradeRecord payRecord = orderDomainService.getTradeRecord(tradeNo);
        if (payRecord == null) {
            return;
        }

        // 告警通知
        log.error("payAlarm-支付拒付通知失败,tradeNo:{},msgId:{},appCode:{},userId:{},notifyContent:{}",
                tradeNo, msgId, payRecord.getAppCode(), payRecord.getUserId(), notifyContentStr);

        // 保存消息记录
        MessageInfo messageInfo = MessageInfo.builder()
                .msgId(msgId)
                .notifyContentOrRequestParam(notifyContentStr)
                .response(ConsumeConcurrentlyStatus.CONSUME_SUCCESS.name())
                .typeEnum(MessageTypeEnum.PAYMENT_DISPUTE_NOTIFY)
                .statusEnum(MessageStatusEnum.FAIL)
                .entityId(tradeNo)
                .build();
        orderDomainService.saveMessageRecord(messageInfo, payRecord.getAppCode(), payRecord.getUserId());
    }

    /**
     * 发送「由于拒付导致取消订单」的邮件
     */
    private void sendCancelSubscribeForRejectPayEmail(TradeRecord payRecord) {
        String appCode = payRecord.getAppCode();
        Long userId = payRecord.getUserId();
        AppEnum appEnum = AppEnum.getAppByCode(appCode);
        SubscriptionOrder order = subscriptionOrderRepository.getByOrderId(payRecord.getSubscriptionOrderId());
        CancelSubscribeForRejectPayEmailDTO emailDTO = new CancelSubscribeForRejectPayEmailDTO()
                .setOrderNo(payRecord.getTradeNo())
                .setAppName(appEnum.getName())
                .setPlanName(order.getSubscriptionPlanName());
        SubscriptionStrategyFactory.getStrategy(appEnum, order.getSubscriptionPayType()).sendEmail(userId, emailDTO);
    }
}
