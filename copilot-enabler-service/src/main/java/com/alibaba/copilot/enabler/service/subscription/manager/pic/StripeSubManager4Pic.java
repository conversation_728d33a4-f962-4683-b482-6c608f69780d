package com.alibaba.copilot.enabler.service.subscription.manager.pic;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanResultDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.service.subscription.manager.AbstractSubscriptionManager;
import com.alibaba.copilot.enabler.service.subscription.manager.BizSubscriptionContext;
import com.alibaba.copilot.enabler.service.subscription.service.pic.PicSubscriptionService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/27
 */
@Component
public class StripeSubManager4Pic extends AbstractSubscriptionManager {

    @Resource
    private PicSubscriptionService picSubscriptionService;

    @Override
    public SubscriptionPayType getSubscriptionPayType() {
        return SubscriptionPayType.STRIPE;
    }

    @Override
    public String getAppCode() {
        return AppEnum.PIC_COPILOT.getCode();
    }

    @Override
    protected SingleResult<SubscribePlanResultDTO> doSubscribe(BizSubscriptionContext context) {
        return picSubscriptionService.subscribePlanByStripe(context.getSubscribePlanDTO());
    }
}