package com.alibaba.copilot.enabler.service.marketing.service;

import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.marketing.constant.DiscountRuleStatus;
import com.alibaba.copilot.enabler.client.marketing.constant.Language;
import com.alibaba.copilot.enabler.client.marketing.dto.DiscountInfoDTO;
import com.alibaba.copilot.enabler.client.marketing.dto.FirstMonthDiscountDTO;
import com.alibaba.copilot.enabler.client.marketing.dto.QueryFirstMonthDiscountDTO;
import com.alibaba.copilot.enabler.client.marketing.dto.RestoreFirstMonthDiscountDTO;
import com.alibaba.copilot.enabler.client.marketing.service.FirstMonthDiscountService;
import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribablePlanDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.marketing.model.DiscountRule;
import com.alibaba.copilot.enabler.domain.marketing.model.DiscountRuleAttributes;
import com.alibaba.copilot.enabler.domain.marketing.repository.DiscountRecordRepository;
import com.alibaba.copilot.enabler.domain.marketing.repository.DiscountRuleRepository;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.service.marketing.utils.DiscountCodeUtil;
import com.alibaba.copilot.enabler.service.marketing.utils.DiscountComputeUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2024/2/27
 */
@Slf4j
@Service
public class FirstMonthDiscountServiceImpl implements FirstMonthDiscountService {

    @Resource
    private DiscountRuleRepository discountRuleRepository;

    @Resource
    private DiscountRecordRepository discountRecordRepository;

    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;

    @Resource
    private SubscriptionPlanRepository subscriptionPlanRepository;

    @Override
    public Map<Long, FirstMonthDiscountDTO> queryFirstMonthDiscount(QueryFirstMonthDiscountDTO dto) {
        log.info("queryFirstMonthDiscount, dto={}", JSON.toJSONString(dto));

        String appCode = dto.getAppCode();
        Long userId = dto.getUserId();
        Language language = dto.getLanguage();
        List<SubscribablePlanDTO> plans = dto.getPlans();

        Assertor.assertNotBlank(appCode, "appCode is blank");
        Assertor.assertNonNull(userId, "userId is null");
        Assertor.assertNonNull(language, "language is null");
        Assertor.assertNotEmpty(plans, "plans is empty");

        if (!canApplyFirstMonthDiscount(appCode, userId)) {
            log.info("queryFirstMonthDiscount, can not apply first month discount");
            return Collections.emptyMap();
        }

        List<Long> planIds = new ArrayList<>();
        Map<Long, BigDecimal> planId2OriginPrice = new HashMap<>();
        plans.stream()
                .filter(this::isMonthUnitAndChargePlan)
                .forEach(plan -> {
                    Long planId = plan.getPlanId();
                    planIds.add(planId);
                    planId2OriginPrice.put(planId, plan.getOriginPlanPrice());
                });
        log.info("queryFirstMonthDiscount, target planIds={}", JSON.toJSONString(planIds));

        Map<Long, FirstMonthDiscountDTO> result = new HashMap<>();
        Map<Long, DiscountRule> planId2DiscountRule = discountRuleRepository.queryFirstMonthRules(planIds);
        planId2DiscountRule.forEach((planId, discountRule) -> {
            if (languageNotMatch(discountRule, language)) {
                return;
            }

            // 获取套餐原价
            BigDecimal originPrice = planId2OriginPrice.get(planId);
            // 生成折扣码
            String discountCode = generateDiscountCode(planId, discountRule, appCode, userId);
            // 计算折扣价
            BigDecimal discountPrice = DiscountComputeUtil.computeDiscount(originPrice, discountRule);

            FirstMonthDiscountDTO firstMonthDiscountDTO = new FirstMonthDiscountDTO()
                    .setDiscountRuleId(discountRule.getId())
                    .setDiscountCode(discountCode)
                    .setDiscountPrice(discountPrice);
            result.put(planId, firstMonthDiscountDTO);
        });
        log.info("queryFirstMonthDiscount, result={}", JSON.toJSONString(result));

        return result;
    }

    private static boolean languageNotMatch(DiscountRule discountRule, Language language) {
        DiscountRuleAttributes attributes = discountRule.getAttributes();
        Language ruleLanguage = attributes.getLanguage();
        return ruleLanguage != null && ruleLanguage != language;
    }

    @Override
    public FirstMonthDiscountDTO restoreAndValidateFirstMonthDiscount(RestoreFirstMonthDiscountDTO dto) {
        log.info("restoreAndValidateFirstMonthDiscount, dto={}", JSON.toJSONString(dto));

        String discountCode = dto.getDiscountCode();
        String appCode = dto.getAppCode();
        Long planId = dto.getPlanId();
        Long userId = dto.getUserId();

        Assertor.assertNotBlank(discountCode, "discountCode is blank");
        Assertor.assertNotBlank(appCode, "appCode is blank");
        Assertor.assertNonNull(userId, "userId is null");
        Assertor.assertNonNull(planId, "planId is null");

        // 校验折扣码的有效性
        DiscountInfoDTO discountInfoDTO = DiscountCodeUtil.restoreByDiscountCode(discountCode);
        log.info("restoreAndValidateFirstMonthDiscount, discountInfoDTO={}", JSON.toJSONString(discountInfoDTO));
        Assertor.asserts(
                discountInfoDTO != null
                        && Objects.equals(discountInfoDTO.getAppCode(), appCode)
                        && Objects.equals(discountInfoDTO.getUserId(), userId)
                        && Objects.equals(discountInfoDTO.getPlanId(), planId),
                "discountCode is illegal"
        );
        Assertor.asserts(canApplyFirstMonthDiscount(appCode, userId), "discountCode is expired");

        // 查询折扣规则
        Long discountRuleId = discountInfoDTO.getDiscountRuleId();
        DiscountRule discountRule = discountRuleRepository.queryById(discountRuleId);
        Assertor.assertNonNull(discountRule, "discountRule is null");
        Assertor.asserts(
                discountRule.getStatus() == DiscountRuleStatus.ENABLED,
                "current discount rule is disabled"
        );

        // 根据折扣规则计算折扣信息
        SubscriptionPlan subscriptionPlan = subscriptionPlanRepository.queryByPlanId(planId, false);
        BigDecimal discountPrice = DiscountComputeUtil.computeDiscount(subscriptionPlan.getPrice(), discountRule);

        return new FirstMonthDiscountDTO()
                .setDiscountCode(discountCode)
                .setDiscountPrice(discountPrice)
                .setDiscountRuleId(discountRuleId);
    }

    /**
     * 生成折扣码
     */
    private String generateDiscountCode(Long planId, DiscountRule discountRule, String appCode, Long userId) {
        DiscountInfoDTO discountInfoDTO = new DiscountInfoDTO()
                .setAppCode(appCode)
                .setUserId(userId)
                .setPlanId(planId)
                .setDiscountRuleId(discountRule.getId());
        return DiscountCodeUtil.generateDiscountCode(discountInfoDTO, Duration.ofDays(1));
    }

    /**
     * 是否可以领取首月优惠
     */
    private boolean canApplyFirstMonthDiscount(String appCode, Long userId) {
        log.info("canApplyFirstMonthDiscount, appCode={}, userId={}", appCode, userId);

        // 1. 仅限于PicCopilot用户
        if (!AppEnum.PIC_COPILOT.getCode().equals(appCode)) {
            log.info("canApplyFirstMonthDiscount, appCode is not PicCopilot");
            return false;
        }

        // 2. 仅限于非会员用户
        if (isVip(appCode, userId)) {
            log.info("canApplyFirstMonthDiscount, user is vip");
            return false;
        }

        // 3. 仅限于未享受过首月减免的用户
        if (hasUsedFirstMonthDiscount(appCode, userId)) {
            log.info("canApplyFirstMonthDiscount, user has used first month discount");
            return false;
        }

        return true;
    }

    private boolean isVip(String appCode, Long userId) {
        SubscriptionOrder subscriptionOrder = subscriptionOrderRepository.queryEffectOrder(appCode, userId);
        return Optional.ofNullable(subscriptionOrder)
                .map(SubscriptionOrder::getSubscriptionPlanId)
                .map(planId -> subscriptionPlanRepository.queryByPlanId(planId, false))
                .map(plan -> !plan.isFree())
                .orElse(false);
    }

    private boolean hasUsedFirstMonthDiscount(String appCode, Long userId) {
        // 查询应用下的所有首月减免规则
        List<DiscountRule> discountRules = discountRuleRepository.queryFirstMonthRulesByAppCode(appCode);
        List<Long> discountRuleIds = discountRules.stream()
                .map(DiscountRule::getId)
                .collect(Collectors.toList());

        // 判断当前用户是否享受过首月减免优惠
        return discountRecordRepository.exists(appCode, userId, discountRuleIds);
    }

    /**
     * 判断是否是月度收费套餐
     */
    private boolean isMonthUnitAndChargePlan(SubscribablePlanDTO planDTO) {
        return DurationUnit.MONTH.name().equals(planDTO.getPlanDurationUnit()) && !planDTO.isFree();
    }
}
