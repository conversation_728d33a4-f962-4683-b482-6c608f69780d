package com.alibaba.copilot.enabler.service.subscription.service;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.copilot.enabler.client.subscription.dto.UserInvitationRewardDTO;
import com.alibaba.copilot.enabler.client.subscription.service.UserInvitationRewardService;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.UserInvitationRewardDO;
import com.alibaba.copilot.enabler.infra.subscription.mapper.UserInvitationRewardMapper;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.ArrayList;

/**
 * User Invitation Reward Service Implementation
 *
 * <AUTHOR>
 * @date 2025/2/21
 */
@Slf4j
@Service
public class UserInvitationRewardServiceImpl implements UserInvitationRewardService {

    @Autowired
    private UserInvitationRewardMapper userInvitationRewardMapper;

    @Override
    public List<String> getValidRewardTypes(Long userId, Date currentTime) {
        try {
            List<UserInvitationRewardDO> rewards = userInvitationRewardMapper.selectValidRewardsByUserId(userId, currentTime);
            return rewards.stream()
                .map(UserInvitationRewardDO::getRewardType)
                .distinct()
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error getting valid reward types, userId={}, error={}", userId, e.getMessage(), e);
            return Lists.newArrayList();
        }
    }

    @Override
    public Integer getValidRewardAmount(Long userId, String rewardType, Date currentTime) {
        try {
            return userInvitationRewardMapper.sumValidRewardAmount(userId, rewardType, currentTime);
        } catch (Exception e) {
            log.error("Error getting valid reward amount, userId={}, rewardType={}, error={}", 
                userId, rewardType, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public List<UserInvitationRewardDTO> getValidRewards(Long userId, String rewardType, Date currentTime) {
        try {
            List<UserInvitationRewardDO> rewards = userInvitationRewardMapper.selectValidRewardsByUserIdAndType(
                userId, rewardType, currentTime);
            
            return rewards.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error getting valid reward records, userId={}, rewardType={}, error={}", 
                userId, rewardType, e.getMessage(), e);
            return Lists.newArrayList();
        }
    }

    @Override
    public boolean updateRewardAmount(Long rewardId, Integer newAmount) {
        try {
            UserInvitationRewardDO reward = userInvitationRewardMapper.selectById(rewardId);
            if (reward == null) {
                log.warn("Failed to update reward amount, reward record not found, rewardId={}", rewardId);
                return false;
            }
            
            reward.setRewardAmount(newAmount);
            int result = userInvitationRewardMapper.updateById(reward);
            return result > 0;
        } catch (Exception e) {
            log.error("Error updating reward amount, rewardId={}, newAmount={}, error={}", 
                rewardId, newAmount, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public Integer getValidRemainingAmount(Long userId, String rewardType, Date currentTime) {
        try {
            // 获取有效奖励的总量
            Integer totalReward = userInvitationRewardMapper.sumValidRewardAmount(userId, rewardType, currentTime);
            
            // 获取有效奖励的已使用量
            Integer totalUsage = userInvitationRewardMapper.sumValidRewardUsage(userId, rewardType, currentTime);
            
            // 确保值不为null
            if (totalReward == null) {
                totalReward = 0;
            }
            if (totalUsage == null) {
                totalUsage = 0;
            }
            
            // 计算剩余可用量
            int remainingAmount = totalReward - totalUsage;
            
            log.info("Calculated valid remaining amount efficiently, userId={}, rewardType={}, totalReward={}, totalUsage={}, remainingAmount={}", 
                userId, rewardType, totalReward, totalUsage, remainingAmount);
            
            return Math.max(remainingAmount, 0); // 确保不返回负值
        } catch (Exception e) {
            log.error("Error getting valid remaining amount, userId={}, rewardType={}, error={}", 
                userId, rewardType, e.getMessage(), e);
            return 0;
        }
    }
    
    @Override
    public boolean increaseRewardUsage(Long rewardId, Integer usageAmount) {
        try {
            if (usageAmount == null || usageAmount <= 0) {
                log.warn("Invalid usage amount: {}", usageAmount);
                return false;
            }
            
            UserInvitationRewardDO reward = userInvitationRewardMapper.selectById(rewardId);
            if (reward == null) {
                log.warn("Failed to increase reward usage, reward record not found, rewardId={}", rewardId);
                return false;
            }
            
            // 计算当前使用量
            Integer currentUsage = reward.getRewardUsage();
            if (currentUsage == null) {
                currentUsage = 0;
            }
            
            // 计算新的使用量
            Integer newUsage = currentUsage + usageAmount;
            
            // 确保不超过总额度
            Integer rewardAmount = reward.getRewardAmount();
            if (rewardAmount != null && newUsage > rewardAmount) {
                log.warn("Cannot increase usage beyond reward amount, rewardId={}, rewardAmount={}, currentUsage={}, requestedIncrease={}", 
                    rewardId, rewardAmount, currentUsage, usageAmount);
                newUsage = rewardAmount;
            }
            
            // 更新使用量
            reward.setRewardUsage(newUsage);
            int result = userInvitationRewardMapper.updateById(reward);
            
            log.info("Increased reward usage, rewardId={}, oldUsage={}, increment={}, newUsage={}, success={}", 
                rewardId, currentUsage, usageAmount, newUsage, (result > 0));
            
            return result > 0;
        } catch (Exception e) {
            log.error("Error increasing reward usage, rewardId={}, usageAmount={}, error={}", 
                rewardId, usageAmount, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public Integer getRewardUsage(Long rewardId) {
        try {
            UserInvitationRewardDO reward = userInvitationRewardMapper.selectById(rewardId);
            if (reward == null) {
                log.warn("Failed to get reward usage, reward record not found, rewardId={}", rewardId);
                return 0;
            }
            
            Integer usage = reward.getRewardUsage();
            return usage != null ? usage : 0;
        } catch (Exception e) {
            log.error("Error getting reward usage, rewardId={}, error={}", rewardId, e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * 获取奖励的剩余可用量（总量减去已使用量）
     *
     * @param rewardId 奖励ID
     * @return 剩余可用量
     */
    @Override
    public Integer getRemainingAmount(Long rewardId) {
        try {
            UserInvitationRewardDO reward = userInvitationRewardMapper.selectById(rewardId);
            if (reward == null) {
                log.warn("Failed to get remaining amount, reward record not found, rewardId={}", rewardId);
                return 0;
            }
            
            Integer amount = reward.getRewardAmount();
            Integer usage = reward.getRewardUsage();
            
            // 计算剩余量
            int remaining = (amount != null ? amount : 0) - (usage != null ? usage : 0);
            return Math.max(remaining, 0); // 确保不返回负值
        } catch (Exception e) {
            log.error("Error getting remaining amount, rewardId={}, error={}", rewardId, e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * 获取用户所有有效的奖励记录（未过期）
     *
     * @param userId 用户ID
     * @param currentTime 当前时间
     * @return 有效的奖励记录列表
     */
    @Override
    public List<UserInvitationRewardDTO> getAllValidRewards(Long userId, Date currentTime) {
        try {
            // 获取所有未过期的奖励记录
            List<UserInvitationRewardDO> allRewards = userInvitationRewardMapper.selectValidRewardsByUserId(userId, currentTime);
            
            // 转换为DTO
            List<UserInvitationRewardDTO> result = allRewards.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
                
            log.info("Retrieved all valid rewards for userId={}, found {} records", userId, result.size());
            return result;
        } catch (Exception e) {
            log.error("Error retrieving all valid rewards, userId={}, error={}", userId, e.getMessage(), e);
            return Lists.newArrayList();
        }
    }
    
    /**
     * 获取用户特定类型的所有有效奖励记录（未过期）
     *
     * @param userId 用户ID
     * @param featureTypes 特性类型列表
     * @param currentTime 当前时间
     * @return 有效的奖励记录列表
     */
    @Override
    public List<UserInvitationRewardDTO> getAllValidRewardsByTypes(Long userId, List<String> featureTypes, Date currentTime) {
        try {
            if (userId == null || CollectionUtils.isEmpty(featureTypes)) {
                log.warn("Invalid parameters for getAllValidRewardsByTypes: userId={}, featureTypes={}", userId, featureTypes);
                return Lists.newArrayList();
            }
            
            List<UserInvitationRewardDTO> result = new ArrayList<>();
            
            // 为每个类型查询记录，批量处理
            for (String featureType : featureTypes) {
                List<UserInvitationRewardDO> rewardsForType = userInvitationRewardMapper.selectValidRewardsByUserIdAndType(
                    userId, featureType, currentTime);
                
                // 转换为DTO并添加到结果中
                List<UserInvitationRewardDTO> dtoList = rewardsForType.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
                
                result.addAll(dtoList);
            }
            
            log.info("Retrieved valid rewards by types for userId={}, types={}, found {} records", 
                userId, featureTypes, result.size());
            
            return result;
        } catch (Exception e) {
            log.error("Error retrieving valid rewards by types, userId={}, featureTypes={}, error={}", 
                userId, featureTypes, e.getMessage(), e);
            return Lists.newArrayList();
        }
    }
    
    /**
     * Convert DO to DTO
     */
    private UserInvitationRewardDTO convertToDTO(UserInvitationRewardDO rewardDO) {
        if (rewardDO == null) {
            return null;
        }
        
        UserInvitationRewardDTO dto = new UserInvitationRewardDTO();
        BeanUtils.copyProperties(rewardDO, dto);
        return dto;
    }
} 