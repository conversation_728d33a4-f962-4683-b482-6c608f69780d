package com.alibaba.copilot.enabler.service.payment.metaq.trade;

import com.alibaba.copilot.boot.basic.enums.IEnum;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentStatusEnum;
import com.alibaba.copilot.enabler.client.payment.constant.RefundStatusEnum;
import com.alibaba.copilot.enabler.client.payment.constant.StatusFlowReasonEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageDirectionEnum;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageTypeEnum;
import com.alibaba.copilot.enabler.client.payment.dto.QueryRefundDTO;
import com.alibaba.copilot.enabler.client.payment.dto.QueryRefundResultDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.dto.PaymentSyncResultDTO;
import com.alibaba.copilot.enabler.domain.payment.factory.IPaymentChannelStrategy;
import com.alibaba.copilot.enabler.domain.payment.factory.PaymentChannelFactory;
import com.alibaba.copilot.enabler.domain.payment.model.MessageInfo;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentInfo;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.service.OrderDomainService;
import com.alibaba.copilot.enabler.domain.subscription.request.InquiryPaymentRequest;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.taobao.metaq.client.MetaPushConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 2023/10/16
 */
@Slf4j
@Component
public class TradeStartConsumer {

    @Resource
    private OrderDomainService orderDomainService;

    @PostConstruct
    private void init() throws MQClientException {
        MetaPushConsumer consumer = new MetaPushConsumer(TradeConst.CONSUMER_GROUP);
        consumer.subscribe(TradeConst.TOPIC, "*");
        consumer.setConsumeMessageBatchMaxSize(1);

        consumer.registerMessageListener((MessageListenerConcurrently) (messages, context) -> {
            log.info("TradeStartConsumer#init, messages={}", JSON.toJSONString(messages));
            MessageExt message = messages.get(0);
            try {
                final Optional<ConsumeConcurrentlyStatus> statusOpt;
                String tags = message.getTags();
                switch (tags) {
                    case TradeConst.TAG_PAY:
                        statusOpt = ifNeedQueryTradeStatus(message).map(this::handlePayStart);
                        break;
                    case TradeConst.TAG_REFUND:
                        statusOpt = ifNeedQueryTradeStatus(message).map(this::handleRefundStart);
                        break;
                    default:
                        statusOpt = Optional.empty();
                        log.error("TradeStartConsumer#init, unknown tag: {}", tags);
                        break;
                }
                log.info("TradeStartConsumer#init, handle finished");
                return statusOpt.orElse(ConsumeConcurrentlyStatus.CONSUME_SUCCESS);
            } catch (Exception e) {
                log.error("TradeStartConsumer#init, handle message error", e);
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
        consumer.start();
    }

    private ConsumeConcurrentlyStatus handlePayStart(TradeStartDTO tradeStartDTO) {
        InquiryPaymentRequest request = new InquiryPaymentRequest();
        request.setPaymentRequestId(tradeStartDTO.getTradeNo());
        request.setAppEnum(IEnum.of(AppEnum.class, tradeStartDTO.getAppCode()));
        log.info("handlePayStart request={}", JSON.toJSONString(request));

        IPaymentChannelStrategy paymentChannelStrategy = PaymentChannelFactory.getPaymentByApp(tradeStartDTO.getAppCode());
        SingleResult<PaymentSyncResultDTO> payResult = paymentChannelStrategy.inquiryPayment(request);
        log.info("handlePayStart query finished, result={}", JSON.toJSONString(payResult));

        return ifSuccess(payResult)
                .map(result -> {
                    if (result.getPaymentStatus() == PaymentStatusEnum.PROCESSING) {
                        // 交易仍在处理, 继续投递消息, 待下一次再查
                        return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                    }

                    PaymentSyncResultDTO payResultData = payResult.getData();

                    TradeRecord tradeRecord = orderDomainService.getTradeRecord(payResultData.getPaymentRequestId());
                    if (tradeRecord == null) {
                        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                    }

                    // 处理支付成功
                    if (result.getPaymentStatus() == PaymentStatusEnum.SUCCESS) {
                        handlePaySucess(tradeStartDTO, request, payResultData, tradeRecord);
                        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                    }

                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                })
                // 查询失败, 继续投递消息, 待下一次再查
                .orElse(ConsumeConcurrentlyStatus.RECONSUME_LATER);
    }

    private ConsumeConcurrentlyStatus handleRefundStart(TradeStartDTO tradeStartDTO) {
        QueryRefundDTO request = new QueryRefundDTO();
        request.setRefundRequestId(tradeStartDTO.getTradeNo());
        request.setAppEnum(IEnum.of(AppEnum.class, tradeStartDTO.getAppCode()));
        log.info("handleRefundStart, request={}", JSON.toJSONString(request));

        IPaymentChannelStrategy paymentChannelStrategy = PaymentChannelFactory.getPaymentByApp(tradeStartDTO.getAppCode());
        SingleResult<QueryRefundResultDTO> refundResult = paymentChannelStrategy.queryRefundResult(request);
        log.info("handleRefundStart, query finished, result={}", JSON.toJSONString(refundResult));

        return ifSuccess(refundResult)
                .map(result -> {
                    if (result.getRefundStatus() == RefundStatusEnum.PROCESSING) {
                        // 交易仍在处理, 继续投递消息, 待下一次再查
                        return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                    }

                    QueryRefundResultDTO refundResultDTO = refundResult.getData();

                    TradeRecord tradeRecord = orderDomainService.getTradeRecord(refundResultDTO.getRefundRequestId());
                    if (tradeRecord == null) {
                        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                    }

                    // 处理退款成功
                    if (result.getRefundStatus() == RefundStatusEnum.SUCCESS) {
                        handleRefundSuccess(tradeStartDTO, request, refundResult, refundResultDTO, tradeRecord);
                    }

                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                })
                // 查询失败, 继续投递消息, 待下一次再查
                .orElse(ConsumeConcurrentlyStatus.RECONSUME_LATER);
    }

    /**
     * 处理支付成功
     */
    private void handlePaySucess(TradeStartDTO tradeStartDTO, InquiryPaymentRequest request, PaymentSyncResultDTO payResultData, TradeRecord tradeRecord) {
        // 执行支付成功处理逻辑
        MessageInfo messageInfo = MessageInfo.builder()
                .msgId(tradeStartDTO.getMsgId())
                .notifyContentOrRequestParam(JSON.toJSONString(request))
                .response(JSON.toJSONString(payResultData))
                .typeEnum(MessageTypeEnum.INQUIRY_CAPTURE_RESULT)
                .directionEnum(MessageDirectionEnum.OUT)
                .entityId(payResultData.getPaymentId())
                .build();

        PaymentInfo paymentInfo = PaymentInfo.builder()
                .outTradeNo(payResultData.getPaymentId())
                .tradeTime(payResultData.getPaymentTime())
                .build();

        orderDomainService.paymentSuccess(tradeRecord, messageInfo, paymentInfo);
    }

    /**
     * 处理退款成功
     */
    private void handleRefundSuccess(TradeStartDTO tradeStartDTO, QueryRefundDTO request, SingleResult<QueryRefundResultDTO> refundResult, QueryRefundResultDTO refundResultDTO, TradeRecord tradeRecord) {
        MessageInfo messageInfo = MessageInfo.builder()
                .msgId(tradeStartDTO.getMsgId())
                .notifyContentOrRequestParam(JSON.toJSONString(request))
                .response(JSON.toJSONString(refundResult.getData()))
                .typeEnum(MessageTypeEnum.PAYMENT_REFUND_NOTIFY)
                .directionEnum(MessageDirectionEnum.OUT)
                .entityId(tradeStartDTO.getTradeNo())
                .build();

        PaymentInfo paymentInfo = PaymentInfo.builder()
                .outTradeNo(refundResultDTO.getRefundId())
                .tradeTime(System.currentTimeMillis())
                .build();

        orderDomainService.refundSuccess(tradeRecord, messageInfo, paymentInfo, StatusFlowReasonEnum.USER_CANCEL_SUBSCRIBE);
    }

    /**
     * 判断是否需要查询流水状态
     */
    private Optional<TradeStartDTO> ifNeedQueryTradeStatus(MessageExt message) {
        log.info("ifNeedQueryTradeStatus, message={}", JSON.toJSONString(message));

        TradeStartDTO payDTO = parseStartDTO(message);
        String tradeNo = payDTO.getTradeNo();
        payDTO.setMsgId(message.getMsgId());
        TradeRecord tradeRecord = orderDomainService.getTradeRecord(tradeNo);
        log.info("ifNeedQueryTradeStatus, queryByTradeNo, tradeNo={}, result={}", tradeNo, JSON.toJSONString(tradeRecord));

        if (SwitchConfig.allowForceRefreshOrderStatus) {
            // 允许强制刷新时, 不需要判断订单状态
            log.info("ifNeedQueryTradeStatus, allowForceRefreshOrderStatus, tradeNo={}", tradeNo);
            return Optional.of(payDTO);
        }

        if (tradeRecord != null && tradeRecord.getStatus() == TradeRecordStatus.TODO) {
            // 结果未知时, 需要再查询
            return Optional.of(payDTO);
        }
        // 否则, 不再继续查询
        return Optional.empty();
    }

    private <T> Optional<T> ifSuccess(SingleResult<T> singleResult) {
        return Optional.ofNullable(singleResult)
                .filter(SingleResult::isSuccess)
                .map(SingleResult::getData);
    }

    private TradeStartDTO parseStartDTO(MessageExt message) {
        return JSON.parseObject(message.getBody(), TradeStartDTO.class);
    }
}
