package com.alibaba.copilot.enabler.service.subscription.controller;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.client.user.constants.UserAppBindStatusEnum;
import com.alibaba.copilot.enabler.client.user.request.AppBindingRequest;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionFeature;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionPlanQuery;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionPlanQueryInfo;
import com.alibaba.copilot.enabler.domain.user.model.UserAppRelation;
import com.alibaba.copilot.enabler.domain.user.repository.UserAppRelationRepository;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionFeatureDO;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionPlanDO;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionPlanFeatureDO;
import com.alibaba.copilot.enabler.infra.subscription.factory.SubscriptionFeatureConverter;
import com.alibaba.copilot.enabler.infra.subscription.factory.SubscriptionPlanConverter;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionFeatureMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionPlanFeatureMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionPlanMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 该类仅供导入plan数据使用, 仅用于在swagger调用, 简单一些, 直接调用Mapper层进行操作
 *
 * <AUTHOR>
 * @version 2023/9/28
 */
@Api(tags = "subscription-plan-importer", value = "订阅计划导入接口")
@Slf4j
@RestController
@RequestMapping("/api/subscription-plan-importer")
public class SubscriptionPlanImporterController {

    @Resource
    private SubscriptionPlanMapper subscriptionPlanMapper;

    @Resource
    private SubscriptionPlanFeatureMapper subscriptionPlanFeatureMapper;

    @Resource
    private SubscriptionFeatureMapper subscriptionFeatureMapper;

    @Resource
    private SubscriptionPlanRepository subscriptionPlanRepository;

    @Resource
    private UserAppRelationRepository userAppRelationRepository;

    @ApiOperation("导入Plan")
    @PostMapping("/importPlan")
    public SingleResult<List<SubscriptionPlanDO>> importPlan(
            @ApiParam(value = "plan列表") @RequestBody List<SubscriptionPlanDO> plans) {
        List<SubscriptionPlanDO> planWithIds = plans.stream()
                .peek(plan -> subscriptionPlanMapper.insert(plan))
                .collect(Collectors.toList());
        return SingleResult.buildSuccess(planWithIds);
    }

    @ApiOperation("修改Plan")
    @PostMapping("/updatePlan")
    public SingleResult<SubscriptionPlanDO> updatePlan(
            @ApiParam(value = "待修改的plan") @RequestBody SubscriptionPlanDO plan) {
        subscriptionPlanMapper.updateById(plan);
        return SingleResult.buildSuccess(plan);
    }

    @ApiOperation("查询Plan")
    @GetMapping("/queryPlan")
    public SingleResult<List<?>> queryPlan(
            @ApiParam(value = "App标识") @RequestParam String appCode,
            @ApiParam(value = "显示精简结果") @RequestParam(required = false, defaultValue = "true") boolean showSimple) {
        List<SubscriptionPlanDO> planDOs = subscriptionPlanMapper.selectList(Wrappers.lambdaQuery(SubscriptionPlanDO.class)
                .eq(SubscriptionPlanDO::getAppCode, appCode)
        );
        if (!showSimple) {
            return SingleResult.buildSuccess(planDOs);
        }

        List<Object> simpleList = planDOs.stream()
                .map(plan -> {
                    HashMap<String, Object> map = Maps.newHashMap();
                    map.put("planId", plan.getId());
                    map.put("planName", plan.getName());
                    return map;
                })
                .collect(Collectors.toList());
        return SingleResult.buildSuccess(simpleList);
    }

    @ApiOperation("导入Feature")
    @PostMapping("/importFeature")
    public SingleResult<List<SubscriptionFeatureDO>> importFeature(
            @ApiParam(value = "feature列表") @RequestBody List<SubscriptionFeatureDO> features) {
        List<SubscriptionFeatureDO> planWithIds = features.stream()
                .peek(plan -> subscriptionFeatureMapper.insert(plan))
                .collect(Collectors.toList());
        return SingleResult.buildSuccess(planWithIds);
    }

    @ApiOperation("修改Feature")
    @PostMapping("/updateFeature")
    public SingleResult<SubscriptionFeatureDO> updateFeature(
            @ApiParam(value = "待修改的feature") @RequestBody SubscriptionFeatureDO feature) {
        subscriptionFeatureMapper.updateById(feature);
        return SingleResult.buildSuccess(feature);
    }

    @ApiOperation("查询Feature")
    @GetMapping("/queryFeature")
    public SingleResult<List<?>> queryFeature(
            @ApiParam(value = "App标识") @RequestParam String appCode,
            @ApiParam(value = "显示精简结果") @RequestParam(required = false, defaultValue = "true") boolean showSimple) {
        List<SubscriptionFeatureDO> planDOs = subscriptionFeatureMapper.selectList(Wrappers.lambdaQuery(SubscriptionFeatureDO.class)
                .eq(SubscriptionFeatureDO::getAppCode, appCode)
        );
        if (!showSimple) {
            return SingleResult.buildSuccess(planDOs);
        }

        List<Object> simpleList = planDOs.stream()
                .map(feature -> {
                    HashMap<String, Object> map = Maps.newHashMap();
                    map.put("featureId", feature.getId());
                    map.put("featureName", feature.getName());
                    return map;
                })
                .collect(Collectors.toList());
        return SingleResult.buildSuccess(simpleList);
    }

    @ApiOperation("绑定Plan和Feature")
    @PostMapping("/bindPlanAndFeature")
    public SingleResult<List<SubscriptionPlanFeatureDO>> bindPlanAndFeature(
            @ApiParam(value = "绑定关系") @RequestBody List<SubscriptionPlanFeatureDO> planFeatures) {
        List<SubscriptionPlanFeatureDO> result = planFeatures.stream()
                .peek(planFeature -> {
                    Long id = planFeature.getId();
                    if (id == null) {
                        subscriptionPlanFeatureMapper.insert(planFeature);
                    } else {
                        subscriptionPlanFeatureMapper.updateById(planFeature);
                    }
                })
                .collect(Collectors.toList());
        return SingleResult.buildSuccess(result);
    }

    @ApiOperation("查询带Feature的Plan")
    @GetMapping("/getPlanWithFeatures")
    public SingleResult<List<SubscriptionPlan>> getPlanWithFeatures(
            @ApiParam(value = "App Code") @RequestParam String appCode) {
        SubscriptionPlanQuery subscriptionPlanQuery = SubscriptionPlanQuery.builder()
                .appCode(appCode)
                .queryInfos(Lists.newArrayList(SubscriptionPlanQueryInfo.FEATURE))
                .build();
        List<SubscriptionPlan> subscriptionPlans = subscriptionPlanRepository.querySubscriptionPlans(subscriptionPlanQuery);
        return SingleResult.buildSuccess(subscriptionPlans);
    }

    @ApiOperation("临时添加-绑定UserId & AppCode")
    @GetMapping("/bindAppAndUser")
    public SingleResult<UserAppRelation> bindAppAndUser(
            @RequestParam String appCode, @RequestParam Long userId) {
        AppBindingRequest bindingRequest = new AppBindingRequest();
        bindingRequest.setAppCode(appCode);
        bindingRequest.setEmail("");
        bindingRequest.setBindSource(appCode);

        AppEnum appEnum = AppEnum.getAppByCode(appCode);
        UserAppRelation userAppRelation = UserAppRelation.builder()
                .userId(userId)
                .email("")
                .appCode(appCode)
                .appName(appEnum.getName())
                .appType(appEnum.getType())
                .bindSource(appCode)
                .bindStatus(UserAppBindStatusEnum.BINDING.value())
                .build();
        UserAppRelation saveUserAppRelation = userAppRelationRepository.saveUserAppRelation(userAppRelation);
        return SingleResult.buildSuccess(saveUserAppRelation);
    }

    // ====================== 以下三个接口仅供更改套餐数据使用, 使用时再手动开启注解信息 ======================

//    @ApiOperation("删除Plan")
//    @PostMapping("/deletePlan")
    public SingleResult<Void> deletePlan(@RequestBody List<Long> planIds) {
        planIds.forEach(subscriptionPlanMapper::deleteById);
        return SingleResult.buildSuccess(null);
    }

//    @ApiOperation("删除Feature")
//    @PostMapping("/deleteFeature")
    public SingleResult<Void> deleteFeature(@RequestBody List<Long> featureIds) {
        featureIds.forEach(subscriptionFeatureMapper::deleteById);
        return SingleResult.buildSuccess(null);
    }

//    @ApiOperation("批量指定数据导入Plan和Feature")
//    @GetMapping("/importPlanAndFeature")
    public SingleResult<Void> importPlanAndFeature() {
        String appCode = AppEnum.PIC_COPILOT.getCode();
        // 免费套餐
        SubscriptionPlan freePlan = SubscriptionPlan.builder()
                .appCode(appCode)
                .name("Free Plan")
                .description("For all sellers, this plan offers 200 Pcoins, To explore our high-CTR Ad images and useful tools for increase your product sales.")
                .price(BigDecimal.ZERO)
                .duration(1000L)
                .durationUnit(DurationUnit.YEAR)
                .isHasTrial(false)
                .trialDuration(0L)
                .trialDurationUnit(DurationUnit.DAY.name())
                .deleted(false)
                .features(Lists.newArrayList(
                        buildFeature(appCode, "200 Pcoins"),
                        buildFeature(appCode, "Basic marketing styles & templates"),
                        buildFeature(appCode, "AI-powered Ad graphics creation"),
                        buildFeature(appCode, "AI background removal"),
                        buildFeature(appCode, "Multi-language image translation")
                ))
                .build();

        // 月度标准套餐
        SubscriptionPlan monthStandardPlan = SubscriptionPlan.builder()
                .appCode(appCode)
                .name("Standard Plan")
                .description("Tailored for small to mid-sized sellers with less than 20 items, offering 2,400 Pcoins yearly(200 Pcoins monthly) to produce 480 high-CTR ads, ensuring increased visibility and exposure for your products.")
                .price(BigDecimal.valueOf(9.9))
                .duration(1L)
                .durationUnit(DurationUnit.MONTH)
                .isHasTrial(true)
                .trialDuration(7L)
                .trialDurationUnit(DurationUnit.DAY.name())
                .deleted(false)
                .features(Lists.newArrayList(
                        buildFeature(appCode, "2,400 Pcoins (200/month)"),
                        buildFeature(appCode, "Basic marketing styles & templates"),
                        buildFeature(appCode, "Pro-exclusive marketing styles & templates"),
                        buildFeature(appCode, "AI-powered Ad graphics creation"),
                        buildFeature(appCode, "AI background removal"),
                        buildFeature(appCode, "Multi-language image translation"),
                        buildFeature(appCode, "Access to new feature lab")
                ))
                .build();
        // 年度标准套餐
        SubscriptionPlan yearStandardPlan = SubscriptionPlan.builder()
                .appCode(appCode)
                .name("Standard Plan")
                .description("Tailored for small to mid-sized sellers with less than 20 items, offering 2,400 Pcoins yearly(200 Pcoins monthly) to produce 480 high-CTR ads, ensuring increased visibility and exposure for your products.")
                .price(BigDecimal.valueOf(3.99 * 12))
                .duration(1L)
                .durationUnit(DurationUnit.YEAR)
                .isHasTrial(true)
                .trialDuration(7L)
                .trialDurationUnit(DurationUnit.DAY.name())
                .deleted(false)
                .features(Lists.newArrayList(
                        buildFeature(appCode, "2,400 Pcoins (200/month)"),
                        buildFeature(appCode, "Basic marketing styles & templates"),
                        buildFeature(appCode, "Pro-exclusive marketing styles & templates"),
                        buildFeature(appCode, "AI-powered Ad graphics creation"),
                        buildFeature(appCode, "AI background removal"),
                        buildFeature(appCode, "Multi-language image translation"),
                        buildFeature(appCode, "Access to new feature lab")
                ))
                .build();

        // 月度高级套餐
        SubscriptionPlan monthProPlan = SubscriptionPlan.builder()
                .appCode(appCode)
                .name("Pro Plan")
                .description("For professional sellers with over 30 items, this plan offers up to 12,000 Pcoins yearly(1,000 Pcoins monthly), enabling the creation of 2,400 high-CTR ads to effectively cater to a diverse and dynamic market.")
                .price(BigDecimal.valueOf(9.9))
                .duration(1L)
                .durationUnit(DurationUnit.MONTH)
                .isHasTrial(true)
                .trialDuration(7L)
                .trialDurationUnit(DurationUnit.DAY.name())
                .deleted(false)
                .features(Lists.newArrayList(
                        buildFeature(appCode, "12,000 Pcoins (1,000/month)"),
                        buildFeature(appCode, "Basic marketing styles & templates"),
                        buildFeature(appCode, "Pro-exclusive marketing styles & templates"),
                        buildFeature(appCode, "AI-powered Ad graphics creation"),
                        buildFeature(appCode, "AI background removal"),
                        buildFeature(appCode, "Multi-language image translation"),
                        buildFeature(appCode, "Access to new feature lab")
                ))
                .build();
        // 年度高级套餐
        SubscriptionPlan yearProPlan = SubscriptionPlan.builder()
                .appCode(appCode)
                .name("Pro Plan")
                .description("For professional sellers with over 30 items, this plan offers up to 12,000 Pcoins yearly(1,000 Pcoins monthly), enabling the creation of 2,400 high-CTR ads to effectively cater to a diverse and dynamic market.")
                .price(BigDecimal.valueOf(8.99 * 12))
                .duration(1L)
                .durationUnit(DurationUnit.YEAR)
                .isHasTrial(true)
                .trialDuration(7L)
                .trialDurationUnit(DurationUnit.DAY.name())
                .deleted(false)
                .features(Lists.newArrayList(
                        buildFeature(appCode, "12,000 Pcoins (1,000/month)"),
                        buildFeature(appCode, "Basic marketing styles & templates"),
                        buildFeature(appCode, "Pro-exclusive marketing styles & templates"),
                        buildFeature(appCode, "AI-powered Ad graphics creation"),
                        buildFeature(appCode, "AI background removal"),
                        buildFeature(appCode, "Multi-language image translation"),
                        buildFeature(appCode, "Access to new feature lab")
                ))
                .build();

        importPlanAndFeatures(Lists.newArrayList(
                freePlan, monthStandardPlan, yearStandardPlan, monthProPlan, yearProPlan
        ));

        return SingleResult.buildSuccess(null);
    }

    private SubscriptionFeature buildFeature(String appCode, String name) {
        return SubscriptionFeature.builder()
                .appCode(appCode)
                .name(name)
                .description("")
                .type("AI")
                .isDepletion(false)
                .deleted(false)
                .build();
    }

    private void importPlanAndFeatures(List<SubscriptionPlan> plans) {
        Map<String, Long> featureName2FeatureId = new HashMap<>();
        plans.forEach(plan -> {
            // 导入Plan
            SubscriptionPlanDO planDO = SubscriptionPlanConverter.INSTANCE.convertB2A(plan);
            subscriptionPlanMapper.insert(planDO);
            Long planId = planDO.getId();

            List<SubscriptionFeature> features = Optional.ofNullable(plan.getFeatures())
                    .orElseGet(Lists::newArrayList);
            features.forEach(feature -> {
                // 导入Feature (相同Feature只导入一次)
                Long featureId = featureName2FeatureId.computeIfAbsent(feature.getName(), featureName -> {
                    SubscriptionFeatureDO featureDO = SubscriptionFeatureConverter.INSTANCE.convertB2A(feature);
                    subscriptionFeatureMapper.insert(featureDO);
                    return featureDO.getId();
                });

                // 绑定Plan和Feature
                SubscriptionPlanFeatureDO planFeature = new SubscriptionPlanFeatureDO();
                planFeature.setDeleted(false);
                planFeature.setSubscriptionPlanId(planId);
                planFeature.setSubscriptionFeatureId(featureId);
                subscriptionPlanFeatureMapper.insert(planFeature);
            });
        });
    }
}
