package com.alibaba.copilot.enabler.service.subscription.factory;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionSource;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanResultDTO;
import com.alibaba.copilot.enabler.client.subscription.service.SubscriptionService;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 抽象订阅策略
 *
 * <AUTHOR>
 * @date 2024/9/27 上午11:04
 */
@Slf4j
public abstract class AbstractWebSubscriptionStrategy implements WebSubscriptionStrategy {

    @Resource
    protected SubscriptionOrderRepository subscriptionOrderRepository;

    @Resource
    protected SubscriptionPlanRepository subscriptionPlanRepository;

    @Autowired
    protected SubscriptionService subscriptionService;

    @PostConstruct
    public void init() {
        WebSubscriptionStrategyFactory.register(getSubscriptionSource(), this);
    }

    /**
     * 订阅来源
     *
     * @return
     */
    protected abstract SubscriptionSource getSubscriptionSource();

    /**
     * 订阅
     *
     * @param context
     * @return
     */
    public abstract SubscribePlanResultDTO subscribe(WebSubscriptionContext context);

    /**
     * 取消订阅
     *
     * @param context
     * @return
     */
    public abstract Boolean cancelSubscribedPlan(WebSubscriptionContext context);
}
