package com.alibaba.copilot.enabler.service.payment.facade;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.boot.basic.result.PageResult;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.payment.dto.*;
import com.alibaba.copilot.enabler.client.payment.facade.AepayHsfApi;
import com.alibaba.copilot.enabler.client.payment.request.TradeRecordPageQuery;
import com.alibaba.copilot.enabler.client.payment.service.PaymentService;
import com.alibaba.copilot.enabler.client.subscription.dto.QueryTradeRecordDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.QueryTradeRecordResultDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentToken;
import com.alibaba.copilot.enabler.domain.payment.repository.PaymentTokenRepository;
import com.alibaba.copilot.enabler.infra.base.utils.ModelConvertUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @version 2023/9/27
 */
@Slf4j
@Service
@HSFProvider(serviceInterface = AepayHsfApi.class)
public class AepayHsfApiImpl implements AepayHsfApi {

    @Resource
    private PaymentService paymentService;

    @Override
    public SingleResult<InquiryPaymentResultDTO> inquiryPayment(InquiryPaymentDTO paymentDTO) {
        return paymentService.inquiryPayment(paymentDTO);
    }

    @Override
    public PageResult<TradeRecordDetailDTO> queryTradeRecordsByPage(TradeRecordPageQuery query) {
        return paymentService.queryTradeRecordsByPage(query);
    }

    /**
     * 查询交易流水分页信息
     *
     * @param query 查询参数
     * @return 分页信息
     */
    @Override
    public PageResult<TradeRecordDetailDTO> queryTradeRecordsByPageWithStripe(TradeRecordPageQuery query) {
        return paymentService.queryTradeRecordsByPageWithStripe(query);
    }

    @Override
    public PageResult<TradeRecordForPicCopilotDTO> queryTradeRecordsPageForPicCopilot(TradeRecordPageQuery query) {
        query.setAppEnum(AppEnum.PIC_COPILOT);
        PageResult<TradeRecordDetailDTO> pageResult = queryTradeRecordsByPage(query);
        List<TradeRecordForPicCopilotDTO> recordList = Optional.ofNullable(pageResult.getData())
                .map(records -> ModelConvertUtils.copyListByReflect(records, TradeRecordForPicCopilotDTO::new))
                .orElseGet(Lists::newArrayList);
        return PageResult.buildSuccess(recordList)
                .setPageNum(pageResult.getPageNum())
                .setPageSize(pageResult.getPageSize())
                .setTotal(pageResult.getTotal())
                .setTotalPage(pageResult.getTotalPage());
    }

    @Override
    public SingleResult<QueryTradeRecordResultDTO> queryTradeRecord(QueryTradeRecordDTO dto) {
        try {
            return paymentService.queryTradeRecord(dto);
        } catch (Exception e) {
            return wrapExceptionResult(e);
        }
    }

    @Override
    public SingleResult<String> queryBillInvoice(QueryTradeRecordDTO dto) {
        try {
            return paymentService.queryBillInvoice(dto);
        } catch (Exception e) {
            log.error("queryBillInvoice error query:{}", JSON.toJSONString(dto), e);
            return wrapExceptionResult(e);
        }
    }

    @Resource
    private PaymentTokenRepository paymentTokenRepository;

    @Override
    @SuppressWarnings("unchecked")
    public SingleResult<UserPaymentInfoResultDTO> queryUserPaymentInfo(UserPaymentInfoDTO dto) {
        try {
            Assertor.assertNonNull(dto, "UserPaymentInfoDTO can't be null");
            Long userId = dto.getUserId();
            String appCode = dto.getAppCode();
            Map<PaymentMethodEnum, PaymentToken> map =
                    paymentTokenRepository.queryAuthedTokensByUser(appCode, userId);

            UserPaymentInfoResultDTO result = new UserPaymentInfoResultDTO();
            result.setUserId(userId);
            result.setAppCode(appCode);

            if (MapUtils.isNotEmpty(map)) {
                Map<String, Boolean> hasPaymentTokenMap = new HashMap<>();
                for (Map.Entry<PaymentMethodEnum, PaymentToken> entry : map.entrySet()) {
                    hasPaymentTokenMap.put(entry.getKey().getValue(), Objects.nonNull(entry.getValue()));
                }
                result.setHasPaymentTokenMap(hasPaymentTokenMap);
            }

            log.warn("queryUserPaymentInfo result={}", JSON.toJSONString(result));

            return SingleResult.buildSuccess(result);
        } catch (Exception e) {
            log.error("queryUserPaymentInfo, requestDTO={}", JSON.toJSONString(dto), e);
            return SingleResult.buildFailure(e.getMessage());
        }
    }

    @SuppressWarnings("unchecked")
    private <T> SingleResult<T> wrapExceptionResult(Exception e) {
        if (e instanceof BizException) {
            ErrorCode errorCode = ((BizException) e).getErrorCode();
            if (errorCode != null) {
                return SingleResult.buildFailure(errorCode.getCode(), errorCode.getMessage());
            }
        }
        return SingleResult.buildFailure(e.getMessage());
    }
}
