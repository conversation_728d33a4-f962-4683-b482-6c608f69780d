package com.alibaba.copilot.enabler.service.subscription.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 订阅计划状态
 *
 * <AUTHOR> @ alibaba-inc.com>
 * @since 2023/6/5
 */

@Getter
public enum ShopifySubscriptionStatus {

    ACTIVE("ACTIVE", "有效"),

    /*** 无效的订阅状态 ***/
    PENDING("PENDING", "待付款"),
    DECLINED("DECLINED", "拒绝付款"),
    EXPIRED("EXPIRED", "2 天内未付款，过期"),
    CANCELLED("CANCELLED", "取消订阅"),

    /**
     * Shopify: 未续费，过期
     */
    FROZEN("FROZEN", "冻结"),
    ;

    private String code;

    private String desc;

    ShopifySubscriptionStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 是否是无效状态
     *
     * @param shopifyStatus
     * @return
     */
    public static Boolean isInvalidStatus(String shopifyStatus) {
        if (StringUtils.isBlank(shopifyStatus)) {
            return Boolean.TRUE;
        }
        List<String> invalidStatus = Arrays.asList(
                ShopifySubscriptionStatus.PENDING.getCode(),
                ShopifySubscriptionStatus.DECLINED.getCode(),
                ShopifySubscriptionStatus.EXPIRED.getCode(),
                ShopifySubscriptionStatus.CANCELLED.getCode()
        );
        return invalidStatus.contains(shopifyStatus);
    }
}
