package com.alibaba.copilot.enabler.service.subscription.factory;

import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanDTO;
import com.alibaba.copilot.enabler.client.subscription.request.SubscribePlanRequest;
import org.springframework.beans.BeanUtils;

/**
 * 类工厂（构建器/转换器/工厂类）
 */
public class SubscribePlanRequestConverter implements Converter<SubscribePlanRequest, SubscribePlanDTO> {

    public static final Converter<SubscribePlanRequest, SubscribePlanDTO> INSTANCE = new SubscribePlanRequestConverter();

    @Override
    public SubscribePlanDTO convertA2B(SubscribePlanRequest subscribePlanRequest) {
        SubscribePlanDTO planWithPayDTO = new SubscribePlanDTO();
        if (subscribePlanRequest == null) {
            return planWithPayDTO;
        }
        BeanUtils.copyProperties(subscribePlanRequest, planWithPayDTO);
        return planWithPayDTO;
    }

    @Override
    public SubscribePlanRequest convertB2A(SubscribePlanDTO planWithPayDTO) {
        SubscribePlanRequest subscribePlanRequest = new SubscribePlanRequest();
        if (planWithPayDTO == null) {
            return subscribePlanRequest;
        }
        BeanUtils.copyProperties(planWithPayDTO, subscribePlanRequest);
        return subscribePlanRequest;
    }

}
