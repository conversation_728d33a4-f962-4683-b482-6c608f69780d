package com.alibaba.copilot.enabler.service.subscription.service;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyFeatureQuery;
import com.alibaba.copilot.enabler.client.subscription.dto.SourceRewardDTO;
import com.alibaba.copilot.enabler.client.subscription.enums.UserInvitationSourceType;
import com.alibaba.copilot.enabler.client.subscription.service.Text2GoSubscriptionService;
import com.alibaba.copilot.enabler.client.subscription.service.UserInvitationService;
import com.alibaba.copilot.enabler.client.subscription.service.UserSourceRewardService;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.FeatureUsageDO;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.UserInvitationDO;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.UserInvitationRewardDO;
import com.alibaba.copilot.enabler.infra.subscription.mapper.FeatureUsageMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.UserInvitationMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.UserInvitationRewardMapper;
import com.alibaba.fastjson.JSON;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * User Invitation Service Implementation
 *
 * <AUTHOR>
 * @date 2025/2/21
 */
@Slf4j
@Service
@HSFProvider(serviceInterface = UserInvitationService.class)
public class UserInvitationServiceImpl implements UserInvitationService {

    private static final int MONTHLY_INVITATION_LIMIT = 100;
    private static final String AI_DETECTION_LIMIT = "AI_DETECTION_LIMIT";
    private static final String TOTAL_WORDS_PER_MONTH = "TOTAL_WORDS_PER_MONTH";
    private static final int AI_DETECTION_LIMIT_REWARD_AMOUNT = 5;
    private static final int TOTAL_WORDS_PER_MONTH_REWARD_AMOUNT = 500;

    @Autowired
    private UserInvitationMapper userInvitationMapper;

    @Autowired
    private UserInvitationRewardMapper userInvitationRewardMapper;

    @Autowired
    private UserSourceRewardService userSourceRewardService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SingleResult<Boolean> processInvitationAndReward(Long inviterId, Long inviteeId) {
        try {
            log.info("Start processing invitation and reward, inviterId={}, inviteeId={}", inviterId, inviteeId);
            // 1. Validate invitation parameters
            if (inviterId == null || inviteeId == null) {
                return SingleResult.buildFailure("Inviter ID or invitee ID cannot be null");
            }

            if (inviterId.equals(inviteeId)) {
                return SingleResult.buildFailure("Cannot invite yourself");
            }

            // 2. Check if invitation relationship already exists
            UserInvitationDO existingInvitation = userInvitationMapper.selectByInviterAndInvitee(inviterId, inviteeId);
            if (existingInvitation != null) {
                return SingleResult.buildFailure("Invitation relationship already exists");
            }

            // 3. Check monthly invitation limit
            int monthlyCount = getMonthlyInvitationCount(inviterId);
            if (monthlyCount >= MONTHLY_INVITATION_LIMIT) {
                return SingleResult.buildFailure("Monthly invitation limit reached");
            }

            // 4. Save invitation relationship
            Long inviteId = saveInvitationRelation(inviterId, inviteeId);

            // 5. Create permanent rewards (1000 years validity)
            createRewardRecord(inviterId, inviteId, AI_DETECTION_LIMIT, AI_DETECTION_LIMIT_REWARD_AMOUNT, 1000);
            createRewardRecord(inviterId, inviteId, TOTAL_WORDS_PER_MONTH, TOTAL_WORDS_PER_MONTH_REWARD_AMOUNT, 1000);

            log.info("Successfully processed invitation and rewards, inviterId={}, inviteeId={}", inviterId, inviteeId);
            return SingleResult.buildSuccess(true);
        } catch (Exception e) {
            log.error("Error processing invitation reward", e);
            return SingleResult.buildFailure("Failed to process invitation: " + e.getMessage());
        }
    }

    @Override
    public SingleResult<Integer> getUserInvitationStats(Long userId) {
        try {
            if (userId == null) {
                return SingleResult.buildFailure("User ID cannot be null");
            }

            // 只获取正常邀请渠道的邀请数量
            int totalCount = getNormalInvitationCount(userId);

            log.info("Successfully retrieved normal invitation count, userId={}, totalCount={}", userId, totalCount);
            return SingleResult.buildSuccess(totalCount);
        } catch (Exception e) {
            log.error("Error retrieving invitation count, userId={}", userId, e);
            return SingleResult.buildFailure("Failed to get invitation count: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SingleResult<Boolean> processSourceReward(SourceRewardDTO rewardDTO, UserInvitationSourceType sourceType) {
        try {
            if (rewardDTO == null || sourceType == null || rewardDTO.getUserId() == null) {
                log.warn("Invalid parameters for source reward: rewardDTO={}, sourceType={}", 
                    rewardDTO, sourceType);
                return SingleResult.buildFailure("Invalid parameters");
            }
            
            // 判断是否为NORMAL_INVITATION类型，如果是则拒绝处理
            if (UserInvitationSourceType.NORMAL_INVITATION.equals(sourceType)) {
                log.warn("Normal invitation type should not use processSourceReward method, userId={}", 
                    rewardDTO.getUserId());
                return SingleResult.buildFailure("Normal invitation type should use processInvitationAndReward method");
            }
            
            Long userId = rewardDTO.getUserId();
            log.info("Processing source reward, userId={}, sourceType={}, rewardItems={}",
                userId, sourceType, rewardDTO.getRewardItems().size());
                
            // 1. 参数校验 - 确保有奖励项
            if (rewardDTO.getRewardItems() == null || rewardDTO.getRewardItems().isEmpty()) {
                log.warn("No reward items found in DTO: userId={}, sourceType={}", userId, sourceType);
                return SingleResult.buildFailure("Reward items cannot be empty");
            }

            // 2. 如果是一次性福利，检查是否已经领取过
            if (sourceType.isOneTimeReward()) {
                SingleResult<Boolean> hasReceivedResult = userSourceRewardService.hasReceivedSourceReward(
                    userId, sourceType, null);

                if (hasReceivedResult.isSuccess() && Boolean.TRUE.equals(hasReceivedResult.getData())) {
                    log.info("User has already received one-time reward from this source, userId={}, sourceType={}",
                        userId, sourceType);
                    return SingleResult.buildFailure("Reward from this source has already been claimed");
                }
            }
            
            // 3. 准备奖励详情数据
            Map<String, Integer> rewardMap = new HashMap<>();
            for (SourceRewardDTO.RewardItem item : rewardDTO.getRewardItems()) {
                if (item.getFeatureType() != null && item.getAmount() != null) {
                    rewardMap.put(item.getFeatureType(), item.getAmount());
                }
            }
            
            // 4. 创建来源福利记录
            Map<String, Object> rewardDetails = new HashMap<>();
            rewardDetails.put("items", rewardMap);
            rewardDetails.put("timestamp", System.currentTimeMillis());

            // 调用UserSourceRewardService创建奖励记录
            SingleResult<Boolean> createResult = userSourceRewardService.createSourceReward(
                userId,
                sourceType,
                sourceType.name(),
                JSON.toJSONString(rewardDetails)
            );

            if (!createResult.isSuccess()) {
                log.error("Failed to create source reward: {}", createResult.getMessage());
                return SingleResult.buildFailure("Failed to create source reward: " + createResult.getMessage());
            }

            log.info("Successfully processed source reward, userId={}, sourceType={}", userId, sourceType);
            return SingleResult.buildSuccess(true);
        } catch (Exception e) {
            log.error("Error processing source reward: {}", e.getMessage(), e);
            return SingleResult.buildFailure("Error processing source reward: " + e.getMessage());
        }
    }

    /**
     * Save invitation relationship
     *
     * @param inviterId inviter ID
     * @param inviteeId invitee ID
     * @return invitation record ID
     */
    public Long saveInvitationRelation(Long inviterId, Long inviteeId) {
        UserInvitationDO invitationDO = new UserInvitationDO();
        invitationDO.setInviterId(inviterId);
        invitationDO.setInviteeId(inviteeId);

        userInvitationMapper.insert(invitationDO);
        log.info("Invitation relationship saved, inviterId={}, inviteeId={}, inviteId={}",
            inviterId, inviteeId, invitationDO.getId());

        return invitationDO.getId();
    }

    /**
     * Get total invitation count from normal invitation channel only
     *
     * @param userId user ID
     * @return normal invitation count
     */
    public int getNormalInvitationCount(Long userId) {
        // 获取所有系统用户ID列表
        List<Long> systemUserIds = getSystemUserIds();
        
        // 调用mapper方法，使用动态SQL排除系统用户ID
        int normalCount = userInvitationMapper.countByInviterAndNotFromSystem(userId, systemUserIds);
        
        log.info("Successfully retrieved normal invitation count for userId={}, count={}", userId, normalCount);
        return normalCount;
    }

    /**
     * Get monthly invitation count (excluding system users)
     *
     * @param userId user ID
     * @return monthly invitation count
     */
    public int getMonthlyInvitationCount(Long userId) {
        // Get start time of current month
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date monthStart = calendar.getTime();

        // Get start time of next month
        calendar.add(Calendar.MONTH, 1);
        Date nextMonthStart = calendar.getTime();

        // 获取所有系统用户ID列表
        List<Long> systemUserIds = getSystemUserIds();

        // 使用动态SQL排除系统用户的查询方法
        int normalMonthlyCount = userInvitationMapper.countByInviterAndTimeRangeExcludeSystem(
            userId, monthStart, nextMonthStart, systemUserIds);
            
        log.info("Successfully retrieved normal monthly invitation count for userId={}, count={}", 
            userId, normalMonthlyCount);
        return normalMonthlyCount;
    }
    
    /**
     * 获取所有系统用户ID列表
     * 
     * @return 系统用户ID列表
     */
    private List<Long> getSystemUserIds() {
        List<Long> systemUserIds = new ArrayList<>();
        
        // 添加所有非NORMAL_INVITATION类型的用户ID
        for (UserInvitationSourceType sourceType : UserInvitationSourceType.values()) {
            if (!UserInvitationSourceType.NORMAL_INVITATION.equals(sourceType)) {
                systemUserIds.add(sourceType.getUserId());
            }
        }
        
        return systemUserIds;
    }

    /**
     * Create reward record
     *
     * @param userId     user ID
     * @param inviteId   invitation record ID
     * @param rewardType reward type
     * @param amount     reward amount
     * @param yearsValid number of years until expiration
     * @return reward record ID
     */
    public Long createRewardRecord(Long userId, Long inviteId, String rewardType, Integer amount, int yearsValid) {
        UserInvitationRewardDO rewardDO = new UserInvitationRewardDO();
        rewardDO.setUserId(userId);
        rewardDO.setInviteId(inviteId);
        rewardDO.setRewardType(rewardType);
        rewardDO.setRewardAmount(amount);

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, yearsValid);
        rewardDO.setExpiryTime(calendar.getTime());

        userInvitationRewardMapper.insert(rewardDO);
        log.info("Created invitation reward: type={}, amount={}, expiryYears={}, userId={}",
            rewardType, amount, yearsValid, userId);

        return rewardDO.getId();
    }
} 