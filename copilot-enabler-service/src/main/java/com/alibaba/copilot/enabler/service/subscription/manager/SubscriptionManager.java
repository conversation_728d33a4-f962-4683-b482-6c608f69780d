package com.alibaba.copilot.enabler.service.subscription.manager;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanResultDTO;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/27
 */
public interface SubscriptionManager {

    /**
     * Subscription Pay Type
     *
     * @return
     */
    SubscriptionPayType getSubscriptionPayType();

    /**
     * App Code
     *
     * @return
     */
    String getAppCode();

    /**
     * 订阅
     *
     * @param context
     * @return
     */
    SingleResult<SubscribePlanResultDTO> subscribe(BizSubscriptionContext context);

}