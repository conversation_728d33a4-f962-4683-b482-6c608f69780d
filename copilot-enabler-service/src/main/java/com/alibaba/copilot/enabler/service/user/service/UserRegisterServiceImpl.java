package com.alibaba.copilot.enabler.service.user.service;

import com.alibaba.copilot.enabler.client.payment.request.TradeRecordsQuery;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyFeatureQuery;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.client.user.constants.UserAppBindStatusEnum;
import com.alibaba.copilot.enabler.client.user.event.UserFlowedBackEvent;
import com.alibaba.copilot.enabler.client.user.request.*;
import com.alibaba.copilot.enabler.client.user.service.UserAppRelationService;
import com.alibaba.copilot.enabler.client.user.service.UserRegisterService;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.subscription.model.FeatureUsage;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionOrderQuery;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.model.UserAppRelation;
import com.alibaba.copilot.enabler.domain.user.repository.UserAppRelationRepository;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import com.alibaba.copilot.enabler.domain.user.request.UserQuery;
import com.alibaba.copilot.enabler.infra.base.metaq.MetaqMessageProducer;
import com.alibaba.copilot.enabler.service.user.factory.NewRegisterUserConverter;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/31
 */
@Slf4j
@Service
public class UserRegisterServiceImpl implements UserRegisterService {

    @Resource
    private UserRepository userRepository;

    @Resource
    private UserAppRelationRepository userAppRelationRepository;

    @Autowired
    private MetaqMessageProducer publisher;

    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;

    @Resource
    private TradeRecordRepository tradeRecordRepository;

    @Autowired
    private SubscriptionPlanRepository subscriptionPlanRepository;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private UserAppRelationService userAppRelationService;

    @Override
    public void register(UserRegisterRequest request) {
        registerInternal(request, false);
    }

    @Override
    public void registerForSubscription(UserRegisterRequest request) {
        log.info("registerForSubscription, request={}", JSON.toJSONString(request));

        // 先注册
        registerInternal(request, true);

        // 如果没有绑定, 再绑定
        String appCode = request.getAppCode();
        Long userId = request.getUserId();
        String email = request.getEmail();
        bindUserRelationIfAbsent(appCode, userId, email);
    }

    private void bindUserRelationIfAbsent(String appCode, Long userId, String email) {
        UserAppRelation relation = userAppRelationRepository.getUserAppRelation(userId, appCode, null);
        if (relation == null) {
            log.info("registerForSubscription, relation is null, need to create");
            AppEnum appEnum = AppEnum.getAppByCode(appCode);
            UserAppRelation userAppRelation = UserAppRelation.builder()
                    .userId(userId)
                    .email(email == null ? "" : email)
                    .appCode(appCode)
                    .appName(appEnum.getName())
                    .appType(appEnum.getType())
                    .bindSource(appCode)
                    .bindStatus(UserAppBindStatusEnum.BINDING.value())
                    .build();
            userAppRelationRepository.saveUserAppRelation(userAppRelation);
        }
    }

    private void registerInternal(UserRegisterRequest request, boolean needUpdateNewestRegisterTimeAndEmail) {
        User dbUser = userRepository.getUser(request.getAppCode(), request.getUserId());
        if (dbUser == null) {
            // 新用户, 走注册逻辑
            User user = NewRegisterUserConverter.INSTANCE.convertA2B(request);
            if (user != null) {
                userRepository.createUser(user);
            }
        } else {
            // 老用户, 满足更新条件时, 更新注册时间
            if (needUpdateNewestRegisterTimeAndEmail) {
                Date registerTime = request.getRegisterTime();
                boolean needUpdateRegisterTime = registerTime != null;
                if (needUpdateRegisterTime) {
                    dbUser.getAttributes().setRegisterTime(registerTime);
                }

                String newEmail = request.getEmail();
                String oldEmail = dbUser.getEmail();
                boolean needUpdateEmail = StringUtils.isNotEmpty(newEmail) && !newEmail.equals(oldEmail);
                if (needUpdateEmail) {
                    dbUser.setEmail(newEmail);
                }

                if (needUpdateRegisterTime || needUpdateEmail) {
                    userRepository.updateUser(dbUser);
                    log.info("registerInternal, update finished, registerTime={}, email={}", registerTime, newEmail);
                }
            }
        }

        // 发送注册回流消息
        sendSpreadReflowEvent(request);
    }

    /**
     * 发送推广回流消息
     *
     * @param request
     */
    private void sendSpreadReflowEvent(UserRegisterRequest request) {

//        if (StringUtils.equals(request.getAppCode(), AppEnum.SEO_COPILOT_SITE.getCode())
//                || StringUtils.equals(request.getAppCode(), AppEnum.SEO_COPILOT.getCode())) {
//            //该场景暂时没有推广
//            log.info("UserRegisterService sendSpreadReflowEvent appcode={},userId={},email={}", request.getAppCode(), request.getUserId(), request.getEmail());
//            return;
//        }
        try {
            UserFlowedBackEvent userFlowedBackEvent = UserFlowedBackEvent.builder()
                    .userId(String.valueOf(request.getUserId()))
                    .userCode(request.getShareCode())
                    .appCode(request.getAppCode())
                    .email(request.getEmail())
                    .unix(System.currentTimeMillis())
                    .build();
            log.info("UserRegisterServiceImpl#sendSpreadReflowEvent userFlowedBackEvent: {}", JSON.toJSONString(userFlowedBackEvent));
            publisher.publish(userFlowedBackEvent);
        } catch (Throwable e) {
            log.error("sendSpreadReflowEvent exp,request={}", JSON.toJSONString(request), e);
        }
    }

    private boolean isNewUser(Long userId) {
        return userRepository.getUser(userId) == null;
    }

    private boolean isAlreadyBindApp(Long userId, String appCode, String email) {
        return userAppRelationRepository.getUserAppRelation(userId, appCode, email) != null;
    }

    @Override
    public Boolean checkEmailExist(String email) {
        if (StringUtils.isBlank(email)) {
            return Boolean.FALSE;
        }
        return userRepository.queryUser(UserQuery.builder().email(email).build()) != null;
    }

    /**
     * updateRegisterData
     *
     * @param seoRegisterRequest
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRegisterData(SeoRegisterRequest seoRegisterRequest) {
        log.info("UserRegisterServiceImpl.updateRegisterData, seoRegisterRequest={}", JSON.toJSONString(seoRegisterRequest));

        User user = userRepository.getUser(seoRegisterRequest.getShopifyShopId());
        if (user == null) {
            log.info("UserRegisterServiceImpl.updateRegisterData, user not exist");
            return;
        }
        user.setUserId(seoRegisterRequest.getUserId());
        userRepository.updateUser(user);

        UserAppRelationQuery userAppRelationQuery = UserAppRelationQuery.builder()
                .appCode(seoRegisterRequest.getAppCode())
                .userId(seoRegisterRequest.getShopifyShopId())
                .build();
        List<UserAppRelation> userAppRelationList = userAppRelationRepository.queryUserAppRelationList(userAppRelationQuery);
        if (CollectionUtils.isNotEmpty(userAppRelationList)) {
            log.info("UserRegisterServiceImpl.updateRegisterData, userAppRelationList.size={}", userAppRelationList.size());
            for (UserAppRelation userAppRelation : userAppRelationList) {
                userAppRelation.setUserId(seoRegisterRequest.getUserId());
                userAppRelationRepository.updateUserAppRelation(userAppRelation);
            }
        }

        SubscriptionOrderQuery subscriptionOrderQuery = new SubscriptionOrderQuery();
        subscriptionOrderQuery.setAppCode(seoRegisterRequest.getAppCode());
        subscriptionOrderQuery.setUserId(seoRegisterRequest.getShopifyShopId());
        List<SubscriptionOrder> subscriptionOrders = subscriptionOrderRepository.querySubscriptionOrders(subscriptionOrderQuery);
        if (CollectionUtils.isNotEmpty(subscriptionOrders)) {
            log.info("UserRegisterServiceImpl.updateRegisterData, subscriptionOrders.size={}", subscriptionOrders.size());
            for (SubscriptionOrder subscriptionOrder : subscriptionOrders) {
                subscriptionOrder.setUserId(seoRegisterRequest.getUserId());
                subscriptionOrderRepository.saveSubscriptionOrder(subscriptionOrder);
            }
        }

        TradeRecordsQuery tradeRecordsQuery = new TradeRecordsQuery();
        tradeRecordsQuery.setAppCode(seoRegisterRequest.getAppCode());
        tradeRecordsQuery.setUserId(seoRegisterRequest.getShopifyShopId());
        List<TradeRecord> tradeRecords = tradeRecordRepository.queryTradeRecords(tradeRecordsQuery);
        if (CollectionUtils.isNotEmpty(tradeRecords)) {
            log.info("UserRegisterServiceImpl.updateRegisterData, tradeRecords.size={}", tradeRecords.size());
            for (TradeRecord tradeRecord : tradeRecords) {
                tradeRecord.setUserId(seoRegisterRequest.getUserId());
                tradeRecordRepository.createOrUpdateTradeRecord(tradeRecord);
            }
        }

        ShopifyFeatureQuery shopifyFeatureQuery = new ShopifyFeatureQuery();
        shopifyFeatureQuery.setAppCode(seoRegisterRequest.getAppCode());
        shopifyFeatureQuery.setId(seoRegisterRequest.getShopifyShopId());
        List<FeatureUsage> featureUsages = subscriptionPlanRepository.queryFeatureUsages(shopifyFeatureQuery);
        if (CollectionUtils.isNotEmpty(featureUsages)) {
            log.info("UserRegisterServiceImpl.updateRegisterData, featureUsages.size={}", featureUsages.size());
            for (FeatureUsage featureUsage : featureUsages) {
                featureUsage.setUserId(seoRegisterRequest.getUserId());
                subscriptionPlanRepository.saveFeatureUsage(featureUsage);
            }
        }

        log.info("UserRegisterServiceImpl.updateRegisterData, succeed, seoRegisterRequest={}", JSON.toJSONString(seoRegisterRequest));
    }

    @Override
    public Boolean registerUserAndAppRelation(UserAndAppRelationRegisterRequest registerRequest) {

        return transactionTemplate.execute(t->{
            // user
            User user = userRepository.getUser(registerRequest.getUserId());
            log.info("UserRegisterServiceImpl.registerUserAndAppRelation, user={}", JSON.toJSONString(user));
            if (user == null) {
                UserRegisterRequest userRegisterRequest = new UserRegisterRequest();
                userRegisterRequest.setRegisterTime(registerRequest.getRegisterTime());
                userRegisterRequest.setUserId(registerRequest.getUserId());
                userRegisterRequest.setEmail(registerRequest.getEmail());
                userRegisterRequest.setSignUpSource(registerRequest.getSignUpSource());
                userRegisterRequest.setSignUpChannel(registerRequest.getSignUpChannel());
                userRegisterRequest.setAppName(registerRequest.getAppName());
                userRegisterRequest.setAppCode(registerRequest.getAppCode());
                userRegisterRequest.setAppType(registerRequest.getAppType());
                userRegisterRequest.setBindStatus(registerRequest.getBindStatus());
                userRegisterRequest.setBindSource(registerRequest.getBindSource());
                userRegisterRequest.setShareCode(registerRequest.getShareCode());
                userRegisterRequest.setFirstChannel(registerRequest.getFirstChannel());
                userRegisterRequest.setLastChannel(registerRequest.getLastChannel());

                register(userRegisterRequest);
                log.info("UserRegisterServiceImpl.registerUserAndAppRelation, register finished, userRegisterRequest={}", JSON.toJSONString(userRegisterRequest));
            }

            // app relation
            UserAppRelation userAppRelation = userAppRelationRepository.getUserAppRelation(
                    registerRequest.getUserId(),
                    registerRequest.getAppCode(),
                    registerRequest.getEmail());

            log.info("UserRegisterServiceImpl.registerUserAndAppRelation, userAppRelation={}", JSON.toJSONString(userAppRelation));

            if (userAppRelation == null) {
                AppBindingRequest appBindingRequest = new AppBindingRequest();
                appBindingRequest.setEmail(registerRequest.getEmail());
                appBindingRequest.setAppCode(registerRequest.getAppCode());
                appBindingRequest.setBindSource(registerRequest.getBindSource());
                userAppRelationService.createUserAppRelation(registerRequest.getUserId(), appBindingRequest);
                log.info("UserRegisterServiceImpl.registerUserAndAppRelation, createUserAppRelation finished, appBindingRequest={}", JSON.toJSONString(appBindingRequest));
            }

            return true;
        });
    }
}
