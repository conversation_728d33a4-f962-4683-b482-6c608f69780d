package com.alibaba.copilot.enabler.service.robot.facade;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.robot.dto.*;
import com.alibaba.copilot.enabler.client.robot.facade.RobotHsfApi;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecordAttributes;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrderAttributes;
import com.alibaba.copilot.enabler.domain.user.model.UserAttributes;
import com.alibaba.copilot.enabler.infra.base.utils.ModelConvertUtils;
import com.alibaba.copilot.enabler.infra.payment.dataobject.TradeRecordDO;
import com.alibaba.copilot.enabler.infra.payment.mapper.TradePaymentRecordMapper;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.FeatureUsageDO;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionOrderDO;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionPlanDO;
import com.alibaba.copilot.enabler.infra.subscription.mapper.FeatureUsageMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionOrderMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionPlanMapper;
import com.alibaba.copilot.enabler.infra.user.dataobject.UserDO;
import com.alibaba.copilot.enabler.infra.user.mapper.UserMapper;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2023/11/9
 */
@Slf4j
@Service
@SuppressWarnings("unchecked")
@HSFProvider(serviceInterface = RobotHsfApi.class)
public class RobotHsfApiImpl implements RobotHsfApi {

    @Resource
    private UserMapper userMapper;

    @Resource
    private SubscriptionOrderMapper subscriptionOrderMapper;

    @Resource
    private TradePaymentRecordMapper tradePaymentRecordMapper;

    @Resource
    private SubscriptionPlanMapper subscriptionPlanMapper;

    @Resource
    private FeatureUsageMapper featureUsageMapper;

    @Override
    public SingleResult<QueryResultDTO<UserDTO>> queryUser(QueryUserDTO dto) {
        log.info("queryUser, dto={}", JSON.toJSONString(dto));
        Long userId = dto.getUserId();
        String email = dto.getEmail();

        // 前置校验
        boolean hasUserId = userId != null;
        boolean hasEmail = StringUtils.isNotEmpty(email);
        if (!hasUserId && !hasEmail) {
            return SingleResult.buildFailure("No query parameter");
        }

        // DB查询
        LambdaQueryWrapper<UserDO> queryWrapper = new LambdaQueryWrapper<UserDO>()
                .eq(hasUserId, UserDO::getUserId, userId)
                .eq(hasEmail, UserDO::getEmail, email);
        List<UserDO> doList = userMapper.selectList(queryWrapper);

        // 结果转型
        List<UserDTO> dtoList = doList.stream()
                .map(modelConverter(UserDTO::new, (fromDO, toDTO) -> {
                    UserAttributes attributes = new UserAttributes(fromDO.getAttributes());
                    toDTO.setRegisterTime(attributes.getRegisterTime());
                }))
                .collect(Collectors.toList());

        // 结果返回
        return SingleResult.buildSuccess(QueryResultDTO.of(dtoList));
    }

    @Override
    public SingleResult<QueryResultDTO<OrderDTO>> queryOrder(QueryOrderDTO dto) {
        log.info("queryOrder, dto={}", JSON.toJSONString(dto));
        Long id = dto.getId();
        Long userId = dto.getUserId();
        String email = dto.getEmail();
        Long shopifySubscriptionId = dto.getShopifySubscriptionId();

        // 前置校验
        boolean hasId = id != null;
        boolean hasUserId = userId != null;
        boolean hasEmail = StringUtils.isNotEmpty(email);
        boolean hasShopifySubscriptionId = shopifySubscriptionId != null;
        if (!hasId && !hasUserId && !hasEmail && !hasShopifySubscriptionId) {
            return SingleResult.buildFailure("No query parameter");
        }

        // DB查询
        LambdaQueryWrapper<SubscriptionOrderDO> queryWrapper = new LambdaQueryWrapper<SubscriptionOrderDO>()
                .eq(hasId, SubscriptionOrderDO::getId, id)
                .eq(hasUserId, SubscriptionOrderDO::getUserId, userId)
                .eq(hasEmail, SubscriptionOrderDO::getEmail, email)
                .eq(hasShopifySubscriptionId, SubscriptionOrderDO::getShopifySubscriptionId, shopifySubscriptionId);
        List<SubscriptionOrderDO> doList = subscriptionOrderMapper.selectList(queryWrapper);

        // 结果转型
        List<OrderDTO> dtoList = doList.stream()
                .map(modelConverter(OrderDTO::new, (fromDO, toDTO) -> {
                    SubscriptionOrderAttributes attributes = new SubscriptionOrderAttributes(fromDO.getAttributes());
                    toDTO.setStatusFlowReason(attributes.getStatusFlowReason());
                    toDTO.setShareCode(attributes.getShareCode());
                    toDTO.setTrialDays(attributes.getTrialDays());
                    toDTO.setPlanDays(attributes.getPlanDays());
                    toDTO.setWhetherSentDeductionNotifyEmail(attributes.getWhetherSentDeductionNotifyEmail());
                }))
                .collect(Collectors.toList());

        // 结果返回
        return SingleResult.buildSuccess(QueryResultDTO.of(dtoList));
    }

    @Override
    public SingleResult<QueryResultDTO<TradeRecordDTO>> queryTradeRecord(QueryTradeRecordDTO dto) {
        log.info("queryTradeRecord, dto={}", JSON.toJSONString(dto));
        Long id = dto.getId();
        Long userId = dto.getUserId();
        Long orderId = dto.getOrderId();
        String tradeNo = dto.getTradeNo();
        String outTradeNo = dto.getOutTradeNo();

        // 前置校验
        boolean hasId = id != null;
        boolean hasUserId = userId != null;
        boolean hasOrderId = orderId != null;
        boolean hasTradeNo = StringUtils.isNotEmpty(tradeNo);
        boolean hasOutTradeNo = StringUtils.isNotEmpty(outTradeNo);
        if (!hasId && !hasOrderId && !hasTradeNo && !hasOutTradeNo) {
            return SingleResult.buildFailure("No query parameter");
        }

        // DB查询
        LambdaQueryWrapper<TradeRecordDO> queryWrapper = new LambdaQueryWrapper<TradeRecordDO>()
                .eq(hasId, TradeRecordDO::getId, id)
                .eq(hasUserId, TradeRecordDO::getUserId, userId)
                .eq(hasOrderId, TradeRecordDO::getSubscriptionOrderId, orderId)
                .eq(hasTradeNo, TradeRecordDO::getTradeNo, tradeNo)
                .eq(hasOutTradeNo, TradeRecordDO::getOutTradeNo, outTradeNo);
        List<TradeRecordDO> doList = tradePaymentRecordMapper.selectList(queryWrapper);

        // 结果转型
        List<TradeRecordDTO> dtoList = doList.stream()
                .map(modelConverter(TradeRecordDTO::new, (fromDO, toDTO) -> {
                    TradeRecordAttributes attributes = new TradeRecordAttributes(fromDO.getAttributes());
                    toDTO.setSubscriptionPlanId(attributes.getSubscriptionPlanId());
                    toDTO.setSubscriptionPlanName(attributes.getSubscriptionPlanName());
                }))
                .collect(Collectors.toList());

        // 结果返回
        return SingleResult.buildSuccess(QueryResultDTO.of(dtoList));
    }

    @Override
    public SingleResult<QueryResultDTO<PlanDTO>> queryPlan(QueryPlanDTO dto) {
        log.info("queryPlan, dto={}", JSON.toJSONString(dto));
        Long id = dto.getId();

        // 前置校验
        boolean hasId = id != null;
        if (!hasId) {
            return SingleResult.buildFailure("No query parameter");
        }

        // DB查询
        LambdaQueryWrapper<SubscriptionPlanDO> queryWrapper = new LambdaQueryWrapper<SubscriptionPlanDO>()
                .eq(hasId, SubscriptionPlanDO::getId, id);
        List<SubscriptionPlanDO> doList = subscriptionPlanMapper.selectList(queryWrapper);

        // 结果转型
        List<PlanDTO> dtoList = doList.stream()
                .map(modelConverter(PlanDTO::new))
                .collect(Collectors.toList());

        // 结果返回
        return SingleResult.buildSuccess(QueryResultDTO.of(dtoList));
    }

    @Override
    public SingleResult<QueryResultDTO<FeatureUsageDTO>> queryFeatureUsage(QueryFeatureUsageDTO dto) {
        log.info("queryFeatureUsage, dto={}", JSON.toJSONString(dto));
        Long userId = dto.getUserId();

        // 前置校验
        boolean hasUserId = userId != null;
        if (!hasUserId) {
            return SingleResult.buildFailure("No query parameter");
        }

        // DB查询
        LambdaQueryWrapper<FeatureUsageDO> queryWrapper = new LambdaQueryWrapper<FeatureUsageDO>()
                .eq(hasUserId, FeatureUsageDO::getUserId, userId);
        List<FeatureUsageDO> doList = featureUsageMapper.selectList(queryWrapper);

        // 结果转型
        List<FeatureUsageDTO> dtoList = doList.stream()
                .map(modelConverter(FeatureUsageDTO::new))
                .collect(Collectors.toList());

        // 结果返回
        return SingleResult.buildSuccess(QueryResultDTO.of(dtoList));
    }

    private static <T, R> Function<T, R> modelConverter(Supplier<R> supplier) {
        return modelConverter(supplier, null);
    }

    private static <T, R> Function<T, R> modelConverter(Supplier<R> supplier, @Nullable BiConsumer<T, R> biConsumer) {
        return origin -> {
            R result = ModelConvertUtils.copyByReflect(origin, supplier);
            if (biConsumer != null) {
                biConsumer.accept(origin, result);
            }
            return result;
        };
    }
}
