package com.alibaba.copilot.enabler.service.subscription.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.dto.DscSubscriptionUsageInfoDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyFeatureQuery;
import com.alibaba.copilot.enabler.client.subscription.service.DscSubscriptionService;
import com.alibaba.copilot.enabler.client.subscription.service.ShopifySubscriptionService;
import com.alibaba.copilot.enabler.domain.base.utils.TimeUtils;
import com.alibaba.copilot.enabler.domain.subscription.model.*;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionOrderQuery;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.FeatureUsageDO;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionFeatureDO;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionPlanFeatureDO;
import com.alibaba.copilot.enabler.infra.subscription.factory.FeatureUsageConverter;
import com.alibaba.copilot.enabler.infra.subscription.mapper.FeatureUsageMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionFeatureMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionPlanFeatureMapper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2024/1/30
 */
@Slf4j
@Service
public class DscSubscriptionServiceImpl implements DscSubscriptionService {
    private static final String DSC_APP_CODE = "DS_COPILOT";
    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;

    @Resource
    private SubscriptionPlanFeatureMapper subscriptionPlanFeatureMapper;

    @Resource
    private SubscriptionFeatureMapper subscriptionFeatureMapper;

    @Resource
    private FeatureUsageMapper featureUsageMapper;

    @Resource
    private ShopifySubscriptionService shopifySubscriptionService;

    @Resource
    private SubscriptionPlanRepository subscriptionPlanRepository;

    @Resource
    private SubscriptionOrderRepository orderRepository;

    @Override
    public DscSubscriptionUsageInfoDTO currentSubscriptionInfo(Long userId) {
        if (userId == null) {
            return null;
        }
        SubscriptionOrderQuery query = new SubscriptionOrderQuery();
        query.setUserId(userId);
        query.setAppCode(DSC_APP_CODE);
        // 查生效的订单
        query.setStatus(Collections.singletonList(SubscriptionOrderStatus.IN_EFFECT));
        List<SubscriptionOrder> subscriptionOrders = subscriptionOrderRepository.querySubscriptionOrders(query);
        if (CollectionUtils.isEmpty(subscriptionOrders)) {
            return null;
        }

        SubscriptionOrder subscriptionOrder = subscriptionOrders.get(0);
        if (subscriptionOrder == null) {
            return null;
        }

        QueryWrapper<SubscriptionPlanFeatureDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("subscription_plan_id", subscriptionOrder.getSubscriptionPlanId());
        List<SubscriptionPlanFeatureDO> subscriptionPlanFeatureDOS = subscriptionPlanFeatureMapper.selectList(queryWrapper);

        DscSubscriptionUsageInfoDTO infoDTO = new DscSubscriptionUsageInfoDTO();
        infoDTO.setUserId(userId);
        infoDTO.setAppCode(DSC_APP_CODE);
        infoDTO.setEmail(subscriptionOrder.getEmail());
        infoDTO.setSubscriptionPlanId(subscriptionOrder.getSubscriptionPlanId());
        infoDTO.setSubscriptionPlanName(subscriptionOrder.getSubscriptionPlanName());
        infoDTO.setStatus(subscriptionOrder.getStatus().name());
        if (CollectionUtils.isEmpty(subscriptionPlanFeatureDOS)) {
            return infoDTO;
        }

        // 查询特性
        List<Long> featureIds = subscriptionPlanFeatureDOS.stream().map(SubscriptionPlanFeatureDO::getSubscriptionFeatureId).collect(Collectors.toList());
        QueryWrapper<SubscriptionFeatureDO> featureDOQueryWrapper = new QueryWrapper<>();
        featureDOQueryWrapper.in("id", featureIds);
        featureDOQueryWrapper.eq("deleted", Boolean.FALSE);
        List<SubscriptionFeatureDO> subscriptionFeatureDOS = subscriptionFeatureMapper.selectList(featureDOQueryWrapper);


        QueryWrapper<FeatureUsageDO> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("app_code", DSC_APP_CODE);
        queryWrapper1.eq("user_id", userId);
        queryWrapper1.in("feature_type", subscriptionFeatureDOS.stream().map(SubscriptionFeatureDO::getType).collect(Collectors.toList()));
        List<FeatureUsageDO> featureUsageDOS = featureUsageMapper.selectList(queryWrapper1);

        List<DscSubscriptionUsageInfoDTO.SubscriptionFeatureUsageInfo> featureInfoList = new ArrayList<>();
        for (SubscriptionFeatureDO subscriptionFeatureDO : subscriptionFeatureDOS) {
            DscSubscriptionUsageInfoDTO.SubscriptionFeatureUsageInfo info = new DscSubscriptionUsageInfoDTO.SubscriptionFeatureUsageInfo();
            info.setFeatureId(subscriptionFeatureDO.getId());
            info.setAppCode(DSC_APP_CODE);
            info.setName(subscriptionFeatureDO.getName());
            info.setDescription(subscriptionFeatureDO.getDescription());
            info.setType(subscriptionFeatureDO.getType());
            info.setIsDepletion(subscriptionFeatureDO.getIsDepletion());
            String benefit = subscriptionFeatureDO.getBenefit();
            if (StringUtils.isNotBlank(benefit) && !"null".equals(benefit)) {
                JSONObject jsonObject = JSONObject.parseObject(benefit);
                if (jsonObject != null) {
                    JSONObject attributes = jsonObject.getJSONObject("attributes");
                    if (attributes != null && attributes.containsKey("quota")) {
                        info.setQuota(attributes.getLongValue("quota"));
                    }
                }
            }
            Optional<FeatureUsageDO> usageDO = featureUsageDOS.stream().filter(item -> item.getFeatureType().equals(subscriptionFeatureDO.getType())).findFirst();
            usageDO.ifPresent(usage -> {
                info.setUsageCount(usage.getUsageCount());
            });
            if (info.getUsageCount() == null) {
                info.setUsageCount(0L);
            }
            featureInfoList.add(info);
        }
        infoDTO.setFeatureInfoList(featureInfoList);
        return infoDTO;
    }

    @Override
    public void updateSubscriptionFeature(Long userId, String featureType, String planName, Boolean isNew) {
        ShopifyFeatureQuery shopifyFeatureQuery = new ShopifyFeatureQuery();
        shopifyFeatureQuery.setFeatureType(featureType);
        shopifyFeatureQuery.setAppCode("DS_COPILOT");
        shopifyFeatureQuery.setId(userId);

        if (shopifyFeatureQuery == null || org.apache.commons.lang.StringUtils.isBlank(featureType)
                || org.apache.commons.lang.StringUtils.isBlank(shopifyFeatureQuery.getAppCode())
                || shopifyFeatureQuery.getId() == null) {
            return;
        }

        List<SubscriptionFeature> effectPlanFeatures = getEffectiveFeatures(shopifyFeatureQuery);
        if (CollectionUtil.isEmpty(effectPlanFeatures)) {
            return;
        }
        for (SubscriptionFeature feature : effectPlanFeatures) {
            if (org.apache.commons.lang.StringUtils.equals(feature.getType(), shopifyFeatureQuery.getFeatureType())) {
                if (feature.getIsDepletion()) {
                    FeatureUsage featureUsage = null;
                    if (isNew) {
                        featureUsage = buildFeatureUsage(feature, shopifyFeatureQuery, planName);
                        featureUsage.setUsageCount(1L);
                        subscriptionPlanRepository.saveFeatureUsage(featureUsage);
                        return;
                    }

                    //消耗型选择性
                    List<FeatureUsage> list = queryAllFeatureUsage(shopifyFeatureQuery);
                    if (CollectionUtils.isEmpty(list)) {
                        featureUsage = buildFeatureUsage(feature, shopifyFeatureQuery, planName);
                        featureUsage.setUsageCount(1L);
                        subscriptionPlanRepository.saveFeatureUsage(featureUsage);
                        return;
                    } else {

                        FeatureUsage first = list.stream().filter(item -> {
                                    return item.getAttributes() != null
                                            && StringUtils.isNotBlank(item.getAttributes().getPlanName())
                                            && item.getAttributes().getPlanName().equals(planName);
                                }).findFirst()
                                .orElse(null);
                        if (first == null) {
                            // 在有效期内无用量记录
                            featureUsage = buildFeatureUsage(feature, shopifyFeatureQuery, planName);
                            featureUsage.setUsageCount(1L);
                            subscriptionPlanRepository.saveFeatureUsage(featureUsage);
                            return;
                        } else {

                            // 在有效期内有用量记录 +1
                            first.setUsageCount(first.getUsageCount() + 1);
                            subscriptionPlanRepository.saveFeatureUsage(first);
                            return;
                        }

                    }
                }
            }
        }
    }

    private List<FeatureUsage> queryAllFeatureUsage(ShopifyFeatureQuery shopifyFeatureQuery) {
        List<FeatureUsage> list = new ArrayList<>();
        try {
            Assertor.asserts(StringUtils.isNotBlank(shopifyFeatureQuery.getAppCode()), "queryFeatureUsage:appCode can not null!");
            Assertor.asserts(shopifyFeatureQuery.getId() != null, "queryFeatureUsage:id can not null!");
            Assertor.asserts(StringUtils.isNotBlank(shopifyFeatureQuery.getFeatureType()), "queryFeatureUsage:featureType can not null!");

            LambdaQueryWrapper<FeatureUsageDO> shopifyFeatureQueryWrapper = new LambdaQueryWrapper<>();
            shopifyFeatureQueryWrapper.eq(FeatureUsageDO::getAppCode, shopifyFeatureQuery.getAppCode());
            shopifyFeatureQueryWrapper.eq(FeatureUsageDO::getUserId, shopifyFeatureQuery.getId());
            shopifyFeatureQueryWrapper.eq(FeatureUsageDO::getFeatureType, shopifyFeatureQuery.getFeatureType());

            List<FeatureUsageDO> featureUsageDOS = featureUsageMapper.selectList(shopifyFeatureQueryWrapper);
            for (FeatureUsageDO featureUsageDO : featureUsageDOS) {
                if (isWithinTimeRange(featureUsageDO.getStartTime(), featureUsageDO.getEndTime())) {
                    FeatureUsage featureUsage = FeatureUsageConverter.INSTANCE.convertA2B(featureUsageDO);
                    list.add(featureUsage);
                }
            }
        } catch (Exception e) {
            log.error("SubscriptionPlanRepositoryImpl queryFeatureUsage exp,shopifyFeatureQuery={}", JSON.toJSONString(shopifyFeatureQuery), e);
        }
        return list;
    }

    /**
     * 判断当前时间是否在startTime和endTime之间
     *
     * @param startTime
     * @param endTime
     * @return
     */
    private boolean isWithinTimeRange(Date startTime, Date endTime) {
        Date currentTime = new Date();
        return currentTime.after(startTime) && currentTime.before(endTime);
    }

    /**
     * 获取当前用户在appcode下的所有有效features
     *
     * @param shopifyFeatureQuery
     * @return
     */
    private List<SubscriptionFeature> getEffectiveFeatures(ShopifyFeatureQuery shopifyFeatureQuery) {
        SubscriptionPlan plan = getSubscriptionPlan(shopifyFeatureQuery);
        if (plan == null) {
            return null;
        }
        return plan.getFeatures();
    }

    private SubscriptionPlan getSubscriptionPlan(ShopifyFeatureQuery shopifyFeatureQuery) {
        if (shopifyFeatureQuery == null
                || org.apache.commons.lang.StringUtils.isBlank(shopifyFeatureQuery.getAppCode())
                || shopifyFeatureQuery.getId() == null) {
            return null;
        }
        try {
            // 查询当前生效的订单
            SubscriptionOrder effectOrder = orderRepository.queryEffectOrder(shopifyFeatureQuery.getAppCode(), shopifyFeatureQuery.getId());
            log.info("orderRepository.queryEffectOrder, getEffectOrder, result={}", JSON.toJSONString(effectOrder));
            // 查询当前生效的套餐
            SubscriptionPlan effectPlan = Optional.ofNullable(effectOrder)
                    .map(SubscriptionOrder::getSubscriptionPlanId)
                    .map(planId ->
                            subscriptionPlanRepository.queryByPlanId(planId, true))
                    // 查不到当前生效的订单时, 默认取免费套餐使用
                    .orElseGet(() ->
                            subscriptionPlanRepository.queryFreePlan(shopifyFeatureQuery.getAppCode(), true));
            log.info("ShopifySubscriptionServiceImpl#getFeature, query plan finished, result={}", JSON.toJSONString(effectPlan));

            if (effectPlan == null || org.apache.commons.collections.CollectionUtils.isEmpty(effectPlan.getFeatures())) {
                return null;
            }
            return effectPlan;
        } catch (Exception e) {
            log.error("getEffectiveFeatures exp,shopifyFeatureQuery={}", shopifyFeatureQuery, e);
        }
        return null;
    }

    /**
     * build FeatureUsage
     *
     * @param feature
     * @param shopifyFeatureQuery
     * @return
     */
    private FeatureUsage buildFeatureUsage(SubscriptionFeature feature, ShopifyFeatureQuery shopifyFeatureQuery, String planName) {
        Date now = new Date();
        FeatureUsageAttributes featureUsageAttributes = new FeatureUsageAttributes(null);
        featureUsageAttributes.setFeatureType(feature.getType());
        featureUsageAttributes.setPlanName(planName);

        FeatureUsage featureUsage = FeatureUsage.builder()
                .gmtCreate(now)
                .gmtCreate(now)
                .appCode(feature.getAppCode())
                .userId(shopifyFeatureQuery.getId())
                .featureType(feature.getType())
                .quota(feature.getBenefit().getAsLong("quota"))
                .usageCount(0L)
                .startTime(TimeUtils.getStartOfDay())
                .endTime(TimeUtils.getEndTime(now, Long.parseLong(String.valueOf(feature.getBenefit().getDuration())), feature.getBenefit().getDurationUnit()))
                .deleted(false)
                .attributes(featureUsageAttributes)
                .build();
        return featureUsage;
    }

    @Override
    public void rollbackFeatureUsage(Long userId, Long featureId, String featureType) {
        ShopifyFeatureQuery shopifyFeatureQuery = new ShopifyFeatureQuery();
        shopifyFeatureQuery.setFeatureType(featureType);
        shopifyFeatureQuery.setAppCode("DS_COPILOT");
        shopifyFeatureQuery.setId(userId);
        shopifySubscriptionService.rollbackFeatureUsage(shopifyFeatureQuery);
    }

    @Override
    public List<Long> querySubscribeToSeventhDayUsers() {
        SubscriptionOrderQuery query = new SubscriptionOrderQuery();
        query.setAppCode(DSC_APP_CODE);
        // 设置开始和结束时间为7天前的开始和结束
        LocalDateTime sevenDaysAgoStart = LocalDateTime.now().minusDays(7).withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime sevenDaysAgoEnd = LocalDateTime.now().minusDays(7).withHour(23).withMinute(59).withSecond(59).withNano(999999999);
        Date startTime = Date.from(sevenDaysAgoStart.atZone(ZoneId.systemDefault()).toInstant());
        Date endTime = Date.from(sevenDaysAgoEnd.atZone(ZoneId.systemDefault()).toInstant());
        query.setStartTime(startTime);
        query.setEndTime(endTime);
        // 查生效的第7天订单
        query.setStatus(Collections.singletonList(SubscriptionOrderStatus.IN_EFFECT));
        log.info("querySubscribeToSeventhDayUsers#query={}", JSON.toJSONString(query));
        List<SubscriptionOrder> subscriptionOrders = subscriptionOrderRepository.queryStartSubscriptionOrders(query);
        log.info("querySubscribeToSeventhDayUsers#subscriptionOrders={}", JSON.toJSONString(subscriptionOrders));
        if (CollectionUtils.isEmpty(subscriptionOrders)) {
            return Collections.emptyList();
        }
        return subscriptionOrders.stream()
            .map(SubscriptionOrder::getUserId)
            .collect(Collectors.toList());
    }

    @Override
    public List<Long> querySubscribeToFourteenDayUsers() {
        SubscriptionOrderQuery query = new SubscriptionOrderQuery();
        query.setAppCode(DSC_APP_CODE);
        // 设置开始和结束时间为14天前的开始和结束
        LocalDateTime fourteenDaysAgoStart = LocalDateTime.now().minusDays(14).withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime fourteenDaysAgoEnd = LocalDateTime.now().minusDays(14).withHour(23).withMinute(59).withSecond(59).withNano(999999999);
        Date startTime = Date.from(fourteenDaysAgoStart.atZone(ZoneId.systemDefault()).toInstant());
        Date endTime = Date.from(fourteenDaysAgoEnd.atZone(ZoneId.systemDefault()).toInstant());
        query.setStartTime(startTime);
        query.setEndTime(endTime);
        // 查生效的第14天订单
        query.setStatus(Collections.singletonList(SubscriptionOrderStatus.IN_EFFECT));
        List<SubscriptionOrder> subscriptionOrders = subscriptionOrderRepository.queryStartSubscriptionOrders(query);
        if (CollectionUtils.isEmpty(subscriptionOrders)) {
            return Collections.emptyList();
        }
        return subscriptionOrders.stream()
            .map(SubscriptionOrder::getUserId)
            .collect(Collectors.toList());
    }

    @Override
    public List<Long> queryRenewSubscribeFiveDayUsers() {
        // 设置开始和结束时间为5天后的开始和结束
        LocalDateTime queryStartTime = LocalDateTime.now().plusDays(5).withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime queryEndTime = LocalDateTime.now().plusDays(5).withHour(23).withMinute(59).withSecond(59).withNano(999999999);
        Date startTime = Date.from(queryStartTime.atZone(ZoneId.systemDefault()).toInstant());
        Date endTime = Date.from(queryEndTime.atZone(ZoneId.systemDefault()).toInstant());
        SubscriptionOrderQuery subscriptionOrderQuery = SubscriptionOrderQuery.builder()
            .status(Collections.singletonList(SubscriptionOrderStatus.IN_EFFECT))
            .appCode(DSC_APP_CODE)
            .autoRenew(false)
            .nextRenewalStartTime(startTime)
            .nextRenewalEndTime(endTime)
            .build();
        log.info("queryRenewSubscribeFiveDayUsers#query={}", JSON.toJSONString(subscriptionOrderQuery));
        List<SubscriptionOrder> subscriptionOrders = subscriptionOrderRepository.queryCancelSubscriptionOrders(subscriptionOrderQuery);
        log.info("queryRenewSubscribeFiveDayUsers#subscriptionOrders={}", JSON.toJSONString(subscriptionOrders));
        if (CollectionUtils.isEmpty(subscriptionOrders)) {
            return Collections.emptyList();
        }
        return subscriptionOrders.stream()
            .map(SubscriptionOrder::getUserId)
            .collect(Collectors.toList());
    }

    @Override
    public List<Long> queryCancelSubscribeThreeDayUsers() {
        // 设置退订后开始和结束时间为3天前的开始和结束
        LocalDateTime queryStartTime = LocalDateTime.now().minusDays(3).withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime queryEndTime = LocalDateTime.now().minusDays(3).withHour(23).withMinute(59).withSecond(59).withNano(999999999);
        Date startTime = Date.from(queryStartTime.atZone(ZoneId.systemDefault()).toInstant());
        Date endTime = Date.from(queryEndTime.atZone(ZoneId.systemDefault()).toInstant());
        SubscriptionOrderQuery subscriptionOrderQuery = SubscriptionOrderQuery.builder()
            .status(Collections.singletonList(SubscriptionOrderStatus.UNSUBSCRIBE))
            .appCode(DSC_APP_CODE)
            .nextRenewalStartTime(startTime)
            .nextRenewalEndTime(endTime)
            .build();
        log.info("queryCancelSubscribeThreeDayUsers#query={}", JSON.toJSONString(subscriptionOrderQuery));
        List<SubscriptionOrder> subscriptionOrders = subscriptionOrderRepository.queryCancelSubscriptionOrders(subscriptionOrderQuery);
        log.info("queryCancelSubscribeThreeDayUsers#subscriptionOrders={}", JSON.toJSONString(subscriptionOrders));
        if (CollectionUtils.isEmpty(subscriptionOrders)) {
            return Collections.emptyList();
        }
        return subscriptionOrders.stream()
            .map(SubscriptionOrder::getUserId)
            .collect(Collectors.toList());
    }

}
