package com.alibaba.copilot.enabler.service.payment.schedule;

import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionOrderQuery;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.taobao.eagleeye.EagleEye;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Antom订阅到期处理定时任务
 *
 * 专门处理Antom支付方式的订阅到期逻辑：
 * 1. 扫描状态为IN_EFFECT且scheduledCancel=true的Antom订阅
 * 2. 检查订阅是否已到期（performEndTime <= 当前时间）
 * 3. 将到期的订阅状态改为COMPLETED
 */
@Component
public class AntomSubscriptionExpirationTask extends MapJobProcessor {

    public static final Logger TASK_LOG = LoggerFactory.getLogger("taskLogger");

    @Autowired
    private SubscriptionOrderRepository subscriptionOrderRepository;

    @Override
    @Monitor(name = "Antom订阅到期处理定时任务", level = Monitor.Level.P1, layer = Monitor.Layer.OTHER)
    public ProcessResult process(JobContext context) {
        try {
            EagleEye.startTrace(null, this.getClass().getName(), EagleEye.TYPE_CUSTOM_MESSAGE_SUB);
            TASK_LOG.info("AntomSubscriptionExpirationTask start……");

            long currentTimeMillis = System.currentTimeMillis();
            Date currentTime = new Date(currentTimeMillis);

            // 扫描条件：
            // 1. 状态为IN_EFFECT
            // 2. 订阅支付类型为ANTOM
            SubscriptionOrderQuery subscriptionOrderQuery = SubscriptionOrderQuery.builder()
                    .status(Collections.singletonList(SubscriptionOrderStatus.IN_EFFECT))
                    .subscriptionPayType(SubscriptionPayType.ANTOM)
                    .build();

            List<SubscriptionOrder> subscriptionOrders = subscriptionOrderRepository.scanSubscriptionOrders(subscriptionOrderQuery);

            // 过滤出符合条件的Antom订阅：
            // 1. 已设置计划取消标记（scheduledCancel=true）
            // 2. 履约结束时间已到期
            List<SubscriptionOrder> expiredAntomOrders = subscriptionOrders.stream()
                    .filter(order -> order != null)
                    .filter(order -> Boolean.TRUE.equals(order.getAttributes().getScheduledCancel()))
                    .filter(order -> order.getPerformEndTime() != null && order.getPerformEndTime().before(currentTime))
                    .collect(Collectors.toList());

            Set<Long> processedOrderIds = expiredAntomOrders.stream()
                    .map(this::processExpiredOrder)
                    .filter(orderId -> orderId != null)
                    .collect(Collectors.toSet());

            TASK_LOG.info("AntomSubscriptionExpirationTask completed - processedOrdersCount:{}, orderIds:{}",
                    processedOrderIds.size(), processedOrderIds);

            return new ProcessResult(true);

        } catch (Throwable e) {
            TASK_LOG.error("AntomSubscriptionExpirationTask error", e);
            return new ProcessResult(false);
        } finally {
            EagleEye.endTrace(null);
        }
    }

    /**
     * 处理单个到期的订阅订单
     *
     * @param expiredOrder 到期的订阅订单
     * @return 处理成功返回订单ID，失败返回null
     */
    private Long processExpiredOrder(SubscriptionOrder expiredOrder) {
        Long orderId = expiredOrder.getId();
        String appCode = expiredOrder.getAppCode();
        Long userId = expiredOrder.getUserId();

        try {
            // 直接修改订单状态为COMPLETED，不复用原有方法
            completeAntomSubscriptionOrder(expiredOrder);

            TASK_LOG.info("AntomSubscriptionExpirationTask processExpiredOrder success - appCode:{}, userId:{}, orderId:{}",
                    appCode, userId, orderId);

            return orderId;

        } catch (Exception e) {
            TASK_LOG.error("AntomSubscriptionExpirationTask processExpiredOrder failed - appCode:{}, userId:{}, orderId:{}",
                    appCode, userId, orderId, e);
            return null;
        }
    }

    /**
     * 完结Antom订阅订单
     * 单独的状态修改逻辑，不复用原有方法
     *
     * @param subscriptionOrder 订阅订单
     */
    private void completeAntomSubscriptionOrder(SubscriptionOrder subscriptionOrder) {
        if (subscriptionOrder == null) {
            return;
        }

        // 检查订单状态
        if (SubscriptionOrderStatus.IN_EFFECT != subscriptionOrder.getStatus()) {
            TASK_LOG.warn("AntomSubscriptionExpirationTask completeAntomSubscriptionOrder - order status is not IN_EFFECT, orderId:{}, status:{}",
                    subscriptionOrder.getId(), subscriptionOrder.getStatus());
            return;
        }

        // 检查是否为Antom支付
        if (!SubscriptionPayType.ANTOM.equals(subscriptionOrder.getSubscriptionPayType())) {
            TASK_LOG.warn("AntomSubscriptionExpirationTask completeAntomSubscriptionOrder - order is not ANTOM payment, orderId:{}, payType:{}",
                    subscriptionOrder.getId(), subscriptionOrder.getSubscriptionPayType());
            return;
        }

        // 检查是否设置了计划取消标记
        if (!Boolean.TRUE.equals(subscriptionOrder.getAttributes().getScheduledCancel())) {
            TASK_LOG.warn("AntomSubscriptionExpirationTask completeAntomSubscriptionOrder - order scheduledCancel is not true, orderId:{}",
                    subscriptionOrder.getId());
            return;
        }

        // 检查是否已到期
        if (subscriptionOrder.getPerformEndTime() == null ||
            subscriptionOrder.getPerformEndTime().after(new Date())) {
            TASK_LOG.warn("AntomSubscriptionExpirationTask completeAntomSubscriptionOrder - order not expired yet, orderId:{}, performEndTime:{}",
                    subscriptionOrder.getId(), subscriptionOrder.getPerformEndTime());
            return;
        }

        // 修改订单状态为COMPLETED
        subscriptionOrder.setStatus(SubscriptionOrderStatus.COMPLETED);
        subscriptionOrder.setGmtModified(new Date());

        // 保存订单
        subscriptionOrderRepository.saveSubscriptionOrder(subscriptionOrder);

        TASK_LOG.info("AntomSubscriptionExpirationTask completeAntomSubscriptionOrder success - orderId:{}, status changed to COMPLETED",
                subscriptionOrder.getId());
    }
}
