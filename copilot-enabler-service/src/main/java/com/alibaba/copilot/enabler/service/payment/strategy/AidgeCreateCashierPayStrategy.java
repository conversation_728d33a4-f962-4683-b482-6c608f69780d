package com.alibaba.copilot.enabler.service.payment.strategy;

import com.alibaba.aepay.fund.business.api.payment.enums.ProductCodeEnum;
import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.enabler.client.payment.constant.ClientType;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.payment.dto.GoodsDTO;
import com.alibaba.copilot.enabler.client.payment.request.CashierPayRequest;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.constant.PaymentConst;
import com.alibaba.copilot.enabler.domain.payment.constant.StripeCheckoutSessionMode;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.StripeGateway;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeCreateSessionRequest;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEventMetadata;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import com.alibaba.copilot.enabler.service.stripe.StripeService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Aidge 业务创建支付
 */

@Service
@Slf4j
public class AidgeCreateCashierPayStrategy extends AbstractCreateCashierPayStrategy {

    @Resource
    private UserRepository userRepository;

    @Resource
    private StripeGateway stripeGateway;

    @Resource
    private StripeService stripeService;

    @Override
    public AppEnum app() {
        return AppEnum.AIDGE;
    }

    @Override
    public TradeRecord create(CashierPayRequest request) {

        log.info("AidgeCreateCashierPayStrategy.create, request={}", JSON.toJSONString(request));

        PaymentMethodEnum paymentMethod = PaymentMethodEnum.ofName(request.getPaymentMethod());

        switch (paymentMethod) {
            case STRIPE:
                // stripe
                return doCreateByStripe(request);
            default:
                throw new BizException(ErrorCode.SYS_ERROR, "unsupported paymentMethod.");
        }
    }

    /**
     * 走Stripe相关支付逻辑
     *
     * @param request
     * @return
     */
    private TradeRecord doCreateByStripe(CashierPayRequest request) {
        // 1. 生成TradeRecord记录
        TradeRecord tradeRecord = createCashierPayRecord(request, PaymentConst.CURRENCY_USD, ProductCodeEnum.CASHIER_PAYMENT.name());

        // 2. 调用渠道接口
        ClientType clientType = ClientType.valueOf(request.getClientType());
        if (!ClientType.WEB_SDK.equals(clientType)) {
            log.error("AidgeCreateCashierPayStrategy.create, unsupported clientType, request={}", JSON.toJSONString(request));
            throw new BizException(ErrorCode.SYS_ERROR, "unsupported clientType.");
        }
        doCreateByStripeWithWebSDK(request, tradeRecord);

        // 3. 更新TradeRecord记录
        updateCashierPayRecord(tradeRecord);
        return tradeRecord;
    }

    @Override
    protected StripeCreateSessionRequest buildCreateCheckoutSession(CashierPayRequest request, TradeRecord tradeRecord, String stripeCustomerId) {
        // stripe metadata
        StripeEventMetadata eventMetadata = StripeEventMetadata.of(
                StripeCheckoutSessionMode.PAYMENT,
                request.getAppCode(),
                tradeRecord.getTradeNo());
        eventMetadata.setUserId(request.getUserId().toString());
        eventMetadata.setGoodsName(request.getOrder().getGoodsList().get(0).getGoodsName());

        // return url
        String returnUrl = buildReturnUrl(request, tradeRecord);

        // goods
        List<GoodsDTO> goodsList = request.getOrder().getGoodsList();
        GoodsDTO goods = new GoodsDTO();
        goods.setGoodsName(goodsList.get(0).getGoodsName());
        goods.setDescription(goodsList.get(0).getDescription());
        goods.setUnitAmount(goodsList.get(0).getUnitAmount());
        goods.setQuantity(goodsList.get(0).getQuantity());

        // request
        StripeCreateSessionRequest sessionRequest = new StripeCreateSessionRequest();
        sessionRequest.setMode(StripeCheckoutSessionMode.PAYMENT);
        sessionRequest.setReturnUrl(returnUrl);
        sessionRequest.setMetadata(eventMetadata);
        sessionRequest.setGoodsList(Lists.newArrayList(goods));
        sessionRequest.setStripeCustomerId(stripeCustomerId);
        sessionRequest.setTimeout(3600L);
        return sessionRequest;
    }
}