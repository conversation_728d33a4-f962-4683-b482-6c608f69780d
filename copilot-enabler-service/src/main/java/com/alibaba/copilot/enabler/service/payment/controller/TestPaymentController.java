package com.alibaba.copilot.enabler.service.payment.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.boot.basic.result.PageResult;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.tools.verify.AssertionError;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.payment.dto.CashierPayResultDTO;
import com.alibaba.copilot.enabler.client.payment.dto.TradeRecordForPicCopilotDTO;
import com.alibaba.copilot.enabler.client.payment.dto.UserAccountDTO;
import com.alibaba.copilot.enabler.client.payment.request.CashierPayRequest;
import com.alibaba.copilot.enabler.client.payment.request.TradeRecordPageQuery;
import com.alibaba.copilot.enabler.client.payment.request.TradeRecordsQuery;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.client.subscription.request.CancelSubscribedPlanRequest;
import com.alibaba.copilot.enabler.client.subscription.service.SubscriptionManageService;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.payment.service.OrderDomainService;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrderAttributes;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionOrderQuery;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.infra.base.utils.EnvUtils;
import com.alibaba.copilot.enabler.infra.payment.mapper.TradePaymentRecordMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionOrderMapper;
import com.alibaba.copilot.enabler.service.payment.facade.AepayHsfApiImpl;
import com.alibaba.copilot.enabler.service.payment.metaq.trade.TradeStartProducer;
import com.alibaba.copilot.enabler.service.payment.service.PaymentServiceImpl;
import com.alibaba.copilot.enabler.service.payment.vo.TestUserOrderInfoVO;
import com.alibaba.copilot.enabler.service.user.facade.UserTokenHsfApiImpl;
import com.alibaba.fastjson2.util.DateUtils;
import com.alibaba.schedulerx.common.util.CronExpression;
import com.aliexpress.geoip.GeoipLocation;
import com.aliexpress.geoip.GeoipLocationV2;
import com.aliexpress.geoip.GeoipService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Api(tags = "测试接口")
@Slf4j
@RestController
@RequestMapping("/test/payment")
public class TestPaymentController {

    @Autowired
    private SubscriptionOrderRepository subscriptionOrderRepository;
    @Autowired
    private TradeRecordRepository tradeRecordRepository;
    @Autowired
    private OrderDomainService orderDomainService;
    @Autowired
    private SubscriptionOrderMapper subscriptionOrderMapper;
    @Autowired
    private TradePaymentRecordMapper tradePaymentRecordMapper;
    @Autowired
    private TradeStartProducer tradeStartProducer;
    @Autowired
    private SubscriptionManageService subscriptionManageService;

    private static final String timeFormat = "yyyy-MM-dd HH:mm:ss";
    @Autowired
    private PaymentServiceImpl paymentServiceImpl;
    @Resource
    private AepayHsfApiImpl aepayHsfApiImpl;

    @Resource
    private UserTokenHsfApiImpl userTokenHsfApi;

    @Resource
    private GeoipService geoipService;

    @ApiOperation("Pic账单流水查询")
    @GetMapping("/queryForPic")
    public PageResult<TradeRecordForPicCopilotDTO> queryTradeRecordsPageForPicCopilot(@RequestParam("userId") @ApiParam(value = "userId") Long userId,
                                                                                      @RequestParam(value = "pageNum", defaultValue = "1") @ApiParam(value = "pageNum") Integer pageNum,
                                                                                      @RequestParam(value = "pageSize", defaultValue = "20") @ApiParam(value = "pageSize") Integer pageSize) {
        TradeRecordPageQuery query = new TradeRecordPageQuery();
        query.setUserId(userId);
        query.setAppEnum(AppEnum.PIC_COPILOT);
        query.setPageNum(pageNum);
        query.setPageSize(pageSize);
        return aepayHsfApiImpl.queryTradeRecordsPageForPicCopilot(query);

    }

    @ApiOperation("收银台支付")
    @PostMapping("/cashierPay")
    public SingleResult<CashierPayResultDTO> cashierPay(@RequestBody CashierPayRequest request) {
        request.getOrder().setReferenceOrderId("ORD" + System.currentTimeMillis());
        request.getOrder().getBuyer().setBuyerId("USER" + System.nanoTime());
        return paymentServiceImpl.cashierPay(request);
    }

    @ApiOperation("userCard")
    @GetMapping("/userCard")
    public SingleResult<UserAccountDTO> userCard(@RequestParam Long userId, @RequestParam String appCode) {
        return userTokenHsfApi.getUserToken(userId, appCode);
    }

    @ApiOperation("查询用户所有有效的订单及对应流水情况（最新前50条）")
    @GetMapping("/queryUserOrdersAndTradeRecords")
    public SingleResult<List<TestUserOrderInfoVO>> queryUserOrdersAndTradeRecords(@ApiParam(value = "userId") @RequestParam Long userId,
                                                                                  @ApiParam(value = "DS_COPILOT", defaultValue = "DS_COPILOT")
                                                                                  @RequestParam(defaultValue = "DS_COPILOT") String appCode,
                                                                                  @ApiParam(value = "订单状态(不传表示查全部)", defaultValue = "ALL")
                                                                                  @RequestParam(defaultValue = "ALL", required = false) String orderStatus) {

        Assertor.asserts(!EnvUtils.isOnline(), "线上环境不允许执行");

        List<TestUserOrderInfoVO> result = Lists.newArrayList();

        int index = 0;

        SubscriptionOrderQuery query = new SubscriptionOrderQuery();
        query.setUserId(userId);
        query.setAppCode(appCode);
        if (StringUtils.isBlank(orderStatus) || orderStatus.equals("ALL")) {
            query.setStatus(Lists.newArrayList(SubscriptionOrderStatus.PENDING_PAYMENT,
                    SubscriptionOrderStatus.IN_EFFECT,
                    SubscriptionOrderStatus.UNSUBSCRIBE,
                    SubscriptionOrderStatus.PAY_CANCELLED,
                    SubscriptionOrderStatus.COMPLETED));
        } else {
            SubscriptionOrderStatus subscriptionOrderStatus = null;
            try {
                subscriptionOrderStatus = SubscriptionOrderStatus.of(orderStatus);
            } catch (Exception e) {
                throw new AssertionError(ErrorCode.SYS_ERROR, "订单状态不正确，需要为：PENDING_PAYMENT、IN_EFFECT、UNSUBSCRIBE或PAY_CANCELLED");
            }
            query.setStatus(Lists.newArrayList(subscriptionOrderStatus));
        }
        query.setLimit(50);
        List<SubscriptionOrder> subscriptionOrders = subscriptionOrderRepository.querySubscriptionOrders(query);
        for (SubscriptionOrder subscriptionOrder : subscriptionOrders) {
            if (subscriptionOrder == null) {
                continue;
            }

            TestUserOrderInfoVO testUserOrderInfoVO = new TestUserOrderInfoVO();
            testUserOrderInfoVO
                    .setId(++index)
                    .setOrderId(subscriptionOrder.getId())
                    .setGmtCreate(DateUtils.format(subscriptionOrder.getGmtCreate()))
                    .setUserId(subscriptionOrder.getUserId())
                    .setAppCode(subscriptionOrder.getAppCode())
                    .setSubscriptionPlanId(subscriptionOrder.getSubscriptionPlanId())
                    .setSubscriptionPlanName(subscriptionOrder.getSubscriptionPlanName())
                    .setNextPlanId(subscriptionOrder.getNextPlanId() != null ? subscriptionOrder.getNextPlanId() : subscriptionOrder.getSubscriptionPlanId())
                    .setNextPlanName(StringUtils.isNotBlank(subscriptionOrder.getNextPlanName())
                            ? subscriptionOrder.getNextPlanName() : subscriptionOrder.getSubscriptionPlanName())
                    .setStatus(subscriptionOrder.getStatus().name())
                    .setStatusDesc(subscriptionOrder.getStatus().getDescription())
                    .setSubscriptionDiscountTag(subscriptionOrder.getSubscriptionDiscountTag())
                    .setAutoRenew(subscriptionOrder.getAutoRenew() ? "继续续订" : "不再续订")
                    .setNextRenewalTime(DateUtils.format(subscriptionOrder.getNextRenewalTime()))
                    .setIsIncludeTrial(subscriptionOrder.getIsIncludeTrial() ? "包含试用期" : "不包含试用期")
                    .setPlanOriginPrice(subscriptionOrder.getPlanPrice().toPlainString())
                    .setPerformStartTime(DateUtils.format(subscriptionOrder.getPerformStartTime()))
                    .setPerformEndTime(DateUtils.format(subscriptionOrder.getPerformEndTime()));

            TradeRecordsQuery paymentRecordsQuery = TradeRecordsQuery.builder().orderId(subscriptionOrder.getId()).build();
            List<TradeRecord> tradeRecords = tradeRecordRepository.queryTradeRecords(paymentRecordsQuery);
            for (TradeRecord tradeRecord : tradeRecords) {
                if (tradeRecord == null) {
                    continue;
                }

                TestUserOrderInfoVO.TestTradeRecordInfoVO tradeRecordInfoVO = new TestUserOrderInfoVO.TestTradeRecordInfoVO();
                tradeRecordInfoVO.setTradeNo(tradeRecord.getTradeNo())
                        .setOutTradeNo(tradeRecord.getOutTradeNo())
                        .setGmtCreate(DateUtils.format(tradeRecord.getGmtCreate()))
                        .setPaymentType((tradeRecord.getPaymentType() == null || tradeRecord.getPaymentType() == PaymentTypeEnum.INITIATED_PAYMENT)
                                ? "用户主动支付" : "系统定期代扣")
                        .setTradeAmount(tradeRecord.getTradeAmount().toPlainString())
                        .setTradeCurrency(tradeRecord.getTradeCurrency())
                        .setTradeDirection(tradeRecord.getTradeDirection().name().equals(TradeDirection.FORWARD.name()) ? "扣款" : "退款")
                        .setTradeTime(DateUtils.format(tradeRecord.getTradeTime()))
                        .setStatus(tradeRecord.getStatus().getDescription())
                        .setHadInitiatePay((tradeRecord.getHadInitiatePay() == null || tradeRecord.getHadInitiatePay())
                                ? "已请求第三方支付平台发起支付" : "尚未请求第三方支付平台发起支付");
                testUserOrderInfoVO.getTradeRecords().add(tradeRecordInfoVO);
            }

            result.add(testUserOrderInfoVO);

        }

        return SingleResult.buildSuccess(result);
    }

    @ApiOperation("更改定期代扣时间(将下次续订时间和履约时间整体前移)")
    @GetMapping("/changeAutoDeductTime")
    public SingleResult<List<String>> changeAutoDeductTime(@ApiParam(value = "userId") @RequestParam Long userId,
                                                           @ApiParam(value = "DS_COPILOT", defaultValue = "DS_COPILOT")
                                                           @RequestParam(defaultValue = "DS_COPILOT") String appCode,
                                                           @ApiParam(value = "orderId") @RequestParam Long orderId,
                                                           @ApiParam(value = "期望的下次续订时间(格式为yyyy-MM-dd HH:mm:ss)", defaultValue = "2023-10-19 16:00:00")
                                                           @RequestParam(defaultValue = "2023-10-19 16:00:00") String expectNextRenewalTime
    ) {
        Assertor.asserts(!EnvUtils.isOnline(), "线上环境不允许执行");

        Date date = new Date();
        try {
            date = DateUtils.parseDate(expectNextRenewalTime, timeFormat);
        } catch (Exception e) {
            throw new AssertionError(ErrorCode.SYS_ERROR, "expectNextRenewalTime的格式不正确，格式要求：yyyy-MM-dd HH:mm:ss");
        }

        SubscriptionOrderQuery query = new SubscriptionOrderQuery();
        query.setUserId(userId);
        query.setAppCode(appCode);
        query.setOrderIds(Lists.newArrayList(orderId));
        List<SubscriptionOrder> subscriptionOrders = subscriptionOrderRepository.querySubscriptionOrders(query);
        Assertor.asserts(subscriptionOrders != null && subscriptionOrders.size() == 1, "未找到这个订单，请检查userId、appCode和orderId是否正确！");
        SubscriptionOrder subscriptionOrder = subscriptionOrders.get(0);
        Assertor.asserts(subscriptionOrder.getStatus() == SubscriptionOrderStatus.IN_EFFECT, "该订单不是'生效中'的订单，只能修改正在生效中的订单！");
        // 测试路径需要覆盖该场景, 因此需要放开此处的拦截
        // Assertor.asserts(subscriptionOrder.getAutoRenew(), "该订单不是'继续续订'的订单，只能修改正在继续续订的订单！");

        long diffTime = subscriptionOrder.getNextRenewalTime().getTime() - date.getTime();
        subscriptionOrder.setNextRenewalTime(new Date(subscriptionOrder.getNextRenewalTime().getTime() - diffTime));
        subscriptionOrder.setPerformStartTime(new Date(subscriptionOrder.getPerformStartTime().getTime() - diffTime));
        subscriptionOrder.setPerformEndTime(new Date(subscriptionOrder.getPerformEndTime().getTime() - diffTime));
        subscriptionOrderRepository.saveSubscriptionOrder(subscriptionOrder);

        List<String> result = Lists.newArrayList();
        String nextExecutionTime = getNextExecutionTime(expectNextRenewalTime, "9 */10 * * * ?");
        result.add("下次开始代扣的时间约为：" + getNextExecutionTime(nextExecutionTime, "06 05/10 * * * ?"));
        result.add("下次完成代扣的时间约为：" + getNextExecutionTime(nextExecutionTime, "30 05/10 * * * ?"));
        return SingleResult.buildSuccess(result);
    }


    @ApiOperation("更改订阅订单中的 Stripe PaymentMethod")
    @GetMapping("/changeStripePaymentMethod")
    public SingleResult<List<String>> changeStripePaymentMethod(
            @ApiParam(value = "PIC_COPILOT", defaultValue = "PIC_COPILOT") @RequestParam(defaultValue = "PIC_COPILOT") String appCode,
            @ApiParam(value = "orderId") @RequestParam Long orderId,
            @ApiParam(value = "pm_card_visa_chargeDeclinedInsufficientFunds", defaultValue = "pm_card_visa_chargeDeclinedInsufficientFunds")
            @RequestParam(defaultValue = "pm_card_visa_chargeDeclinedInsufficientFunds") String expectPaymentMethod
    ) {
        Assertor.asserts(!EnvUtils.isOnline(), "线上环境不允许执行");


        SubscriptionOrder subscriptionOrder = subscriptionOrderRepository.getByOrderId(orderId);
        Assertor.asserts(subscriptionOrder != null, "未找到这个订单，请检查appCode和orderId是否正确！");
        Assertor.asserts(subscriptionOrder.getStatus() == SubscriptionOrderStatus.IN_EFFECT, "该订单不是'生效中'的订单，只能修改正在生效中的订单！");

        SubscriptionPayType subscriptionPayType = subscriptionOrder.getSubscriptionPayType();
        Assertor.asserts(subscriptionPayType == SubscriptionPayType.STRIPE, "该订单不是 Stripe 支付的订单，只能修改 Stripe 支付的订单！");

        SubscriptionOrderAttributes attributes = subscriptionOrder.getAttributes();
        attributes.setStripePaymentMethodId(expectPaymentMethod);
        subscriptionOrderRepository.saveSubscriptionOrder(subscriptionOrder);

        List<String> result = Lists.newArrayList();
        result.add("paymentMethod修改为：" + expectPaymentMethod);
        return SingleResult.buildSuccess(result);
    }

    @ApiOperation("让未支付订单超期时间提前而快速取消(创建交易流水创建时间前移)")
    @GetMapping("/changeTradeRecordCreateTime")
    public SingleResult<List<String>> changeTradeRecordCreateTime(@ApiParam(value = "userId") @RequestParam Long userId,
                                                                  @ApiParam(value = "DS_COPILOT", defaultValue = "DS_COPILOT")
                                                                  @RequestParam(defaultValue = "DS_COPILOT") String appCode,
                                                                  @ApiParam(value = "orderId") @RequestParam Long orderId,
                                                                  @ApiParam(value = "期望的执行时间(格式为yyyy-MM-dd HH:mm:ss)", defaultValue = "2023-10-19 16:00:00")
                                                                  @RequestParam(defaultValue = "2023-10-19 16:00:00") String expectExecuteTime
    ) {
        Assertor.asserts(!EnvUtils.isOnline(), "线上环境不允许执行");

        Date date = new Date();
        try {
            date = DateUtils.parseDate(expectExecuteTime, timeFormat);
        } catch (Exception e) {
            throw new AssertionError(ErrorCode.SYS_ERROR, "expectExecuteTime的格式不正确，格式要求：yyyy-MM-dd HH:mm:ss");
        }
        TradeRecordsQuery tradeRecordsQuery = new TradeRecordsQuery();
        tradeRecordsQuery.setOrderId(orderId);
        List<TradeRecord> tradeRecords = tradeRecordRepository.queryTradeRecords(tradeRecordsQuery);
        Assertor.asserts(tradeRecords != null && tradeRecords.size() == 1, "未找到这个交易流水，请检查orderId是否正确！");

        TradeRecord tradeRecord = tradeRecords.get(0);
        Assertor.asserts(tradeRecord.getStatus() == TradeRecordStatus.TODO, "该交易流水状态'待支付'的订单，只能修改待支付的交易流水！");
        Assertor.asserts(tradeRecord.getTradeDirection() == TradeDirection.FORWARD, "该交易流水非付款类型的交易流水！");

        long expectCreateTime = date.getTime() - SwitchConfig.unpaidOrderOverdueSeconds * 1000;
        tradeRecord.setGmtCreate(new Date(expectCreateTime));
        tradeRecordRepository.createOrUpdateTradeRecord(tradeRecord);

        SubscriptionOrderQuery query = new SubscriptionOrderQuery();
        query.setUserId(userId);
        query.setAppCode(appCode);
        query.setOrderIds(Lists.newArrayList(orderId));
        List<SubscriptionOrder> subscriptionOrders = subscriptionOrderRepository.querySubscriptionOrders(query);
        Assertor.asserts(subscriptionOrders != null && subscriptionOrders.size() == 1, "未找到这个订单，请检查userId、appCode和orderId是否正确！");
        SubscriptionOrder subscriptionOrder = subscriptionOrders.get(0);
        Assertor.asserts(subscriptionOrder.getStatus() == SubscriptionOrderStatus.PENDING_PAYMENT, "该订单不是'待支付'的订单，只能修改待支付的订单！");
        Assertor.asserts(subscriptionOrder.getAutoRenew(), "该订单不是'待支付'的订单，只能修改待支付的订单！");

        subscriptionOrder.setGmtCreate(new Date(expectCreateTime));
        subscriptionOrderRepository.saveSubscriptionOrder(subscriptionOrder);

        List<String> result = Lists.newArrayList();
        result.add("下次开始取消订单的时间约为：" + getNextExecutionTime(expectExecuteTime, "18 */05 * * * ?"));
        result.add("下次完成取消订单的时间约为：" + getNextExecutionTime(expectExecuteTime, "59 */05 * * * ?"));
        return SingleResult.buildSuccess(result);
    }

    @ApiOperation("推进订单履约天数(将下次续订时间和履约时间整体前移)")
    @GetMapping("/advancePerformDay")
    public SingleResult<List<String>> advancePerformDay(@ApiParam(value = "userId") @RequestParam Long userId,
                                                        @ApiParam(value = "DS_COPILOT", defaultValue = "DS_COPILOT")
                                                        @RequestParam(defaultValue = "DS_COPILOT") String appCode,
                                                        @ApiParam(value = "orderId") @RequestParam Long orderId,
                                                        @ApiParam(value = "履约推进天数", defaultValue = "1") @RequestParam(defaultValue = "1") Integer advanceDays
    ) {
        Assertor.asserts(!EnvUtils.isOnline(), "线上环境不允许执行");

        SubscriptionOrderQuery query = new SubscriptionOrderQuery();
        query.setUserId(userId);
        query.setAppCode(appCode);
        query.setOrderIds(Lists.newArrayList(orderId));
        List<SubscriptionOrder> subscriptionOrders = subscriptionOrderRepository.querySubscriptionOrders(query);
        Assertor.asserts(subscriptionOrders != null && subscriptionOrders.size() == 1, "未找到这个订单，请检查userId、appCode和orderId是否正确！");
        SubscriptionOrder subscriptionOrder = subscriptionOrders.get(0);

        long diffTime = advanceDays * 24 * 60 * 60 * 1000L;
        subscriptionOrder.setNextRenewalTime(new Date(subscriptionOrder.getNextRenewalTime().getTime() + diffTime));
        subscriptionOrder.setPerformStartTime(new Date(subscriptionOrder.getPerformStartTime().getTime() + diffTime));
        subscriptionOrder.setPerformEndTime(new Date(subscriptionOrder.getPerformEndTime().getTime() + diffTime));
        subscriptionOrderRepository.saveSubscriptionOrder(subscriptionOrder);

        List<String> result = Lists.newArrayList();
        result.add("该订单开始履约时间已修改为：" + DateUtil.format(subscriptionOrder.getPerformStartTime(), timeFormat));
        result.add("该订单开始履约时间已修改为：" + DateUtil.format(subscriptionOrder.getPerformEndTime(), timeFormat));
        result.add("该订单开始下次续订时间已修改为：" + DateUtil.format(subscriptionOrder.getNextRenewalTime(), timeFormat));
        return SingleResult.buildSuccess(result);
    }

    @ApiOperation("测试下单幂等性")
    @GetMapping("/orderIdempotent")
    public SingleResult<String> orderIdempotent(@ApiParam(value = "userId") @RequestParam Long userId,
                                                @ApiParam(value = "DS_COPILOT", defaultValue = "DS_COPILOT")
                                                @RequestParam(defaultValue = "DS_COPILOT") String appCode,
                                                @ApiParam(value = "orderId") @RequestParam Long orderId

    ) {
        Assertor.asserts(!EnvUtils.isOnline(), "线上环境不允许执行");


        long currentTimeMillis = System.currentTimeMillis();
        SubscriptionOrder order = subscriptionOrderRepository.getByOrderId(orderId);
        TradeRecord tradeRecord = orderDomainService.createPreparePayOrder(order, appCode, userId);
        return SingleResult.buildSuccess(tradeRecord.getTradeNo() + ":" + (System.currentTimeMillis() - currentTimeMillis));
    }

    @ApiOperation("(慎用)物理删除某用户的所有订单及流水")
    @GetMapping("/deleteAllOrderAndTradeRecord")
    public SingleResult<String> deleteAllOrderAndTradeRecord(@ApiParam(value = "userId") @RequestParam Long userId,
                                                             @ApiParam(value = "DS_COPILOT", defaultValue = "DS_COPILOT")
                                                             @RequestParam(defaultValue = "DS_COPILOT") String appCode,
                                                             @ApiParam(value = "email") @RequestParam String email,
                                                             @ApiParam(value = "password") @RequestParam String password

    ) {
        Assertor.asserts(!EnvUtils.isOnline(), "线上环境不允许执行");

        Assertor.asserts("alibabanb".equals(password), "密码不正确！该接口慎用！物理删除很危险的！");

        SubscriptionOrderQuery query = new SubscriptionOrderQuery();
        query.setUserId(userId);
        query.setAppCode(appCode);

        int count = 0;
        int recordCount = 0;

        List<SubscriptionOrder> subscriptionOrders = subscriptionOrderRepository.querySubscriptionOrders(query);
        for (SubscriptionOrder subscriptionOrder : subscriptionOrders) {
            if (subscriptionOrder == null) {
                continue;
            }

            String emailTemp = subscriptionOrder.getEmail();
            Assertor.asserts(email.equals(emailTemp), "邮箱和用户对不上！该接口慎用！物理删除很危险的！");
            subscriptionOrderMapper.deleteById(subscriptionOrder.getId());

            TradeRecordsQuery recordsQuery = new TradeRecordsQuery();
            recordsQuery.setOrderId(subscriptionOrder.getId());
            List<TradeRecord> tradeRecords = tradeRecordRepository.queryTradeRecords(recordsQuery);
            for (TradeRecord tradeRecord : tradeRecords) {
                if (tradeRecord == null) {
                    continue;
                }
                tradePaymentRecordMapper.deleteById(tradeRecord.getId());
                recordCount++;
            }
            count++;
        }
        return SingleResult.buildSuccess(email + " 已删除" + count + "条订单，" + recordCount + "条交易流水");

    }

    @ApiOperation("触发交易流水的主动查询逻辑")
    @GetMapping("/triggerTradeQuery")
    public SingleResult<String> triggerTradeQuery(@ApiParam(value = "交易号") @RequestParam String tradeNo) {
        Assertor.assertNotBlank(tradeNo, "交易号不能为空");
        if (EnvUtils.isOnline() && !SwitchConfig.enableTriggerTradeQueryForOnline) {
            return SingleResult.buildSuccess("非法请求");
        }

        TradeRecord tradeRecord = tradeRecordRepository.queryByTradeNo(tradeNo);
        Assertor.assertNonNull(tradeRecord, "未找到该笔交易流水");

        String appCode = tradeRecord.getAppCode();
        switch (tradeRecord.getTradeDirection()) {
            case FORWARD:
                tradeStartProducer.postPayStartMessage(tradeNo, appCode);
                break;
            case REFUND:
                tradeStartProducer.postRefundStartMessage(tradeNo, appCode);
                break;
        }
        return SingleResult.buildSuccess("已触发MQ消息");
    }

    @ApiOperation("取消用户订阅并退款")
    @GetMapping("/cancelSubscriptionAndRefund")
    public SingleResult<String> cancelSubscriptionAndRefund(
            @ApiParam(value = "应用标识") @RequestParam AppEnum appCode,
            @ApiParam(value = "用户ID") @RequestParam Long userId
    ) {
        if (!CollectionUtil.contains(SwitchConfig.userWhitelistForAllowRefund, userId)) {
            return SingleResult.buildSuccess("非法请求");
        }

        CancelSubscribedPlanRequest request = new CancelSubscribedPlanRequest();
        request.setAppCode(appCode.getCode());
        subscriptionManageService.cancelSubscribedPlan(request, userId);
        return SingleResult.buildSuccess("已处理完成");
    }

    private String getNextExecutionTime(String strDate, String cronExpression) {
        try {
            // 1. 将字符串转换为日期类型
            SimpleDateFormat formatter = new SimpleDateFormat(timeFormat);
            Date date = formatter.parse(strDate);

            // 2. 根据cron表达式获取下次执行时间
            CronExpression cron = new CronExpression(cronExpression);
            Date nextExecution = cron.getNextValidTimeAfter(date);

            // 3. 将下次执行时间转换为字符串
            return formatter.format(nextExecution);
        } catch (ParseException e) {
            return "你猜！\uD83D\uDE04";
        }
    }

    @ApiOperation("IP地址解析额")
    @GetMapping("/ipAddress")
    public SingleResult<GeoipLocationV2> ipAddress(
            @ApiParam(value = "IP") @RequestParam String ip) {

        log.info("ipAddress ip:{}", ip);

        if (!EnvUtils.isPre()) {
            return SingleResult.buildFailure("非法请求");
        }

        GeoipLocationV2 geoipLocation = geoipService.queryV2(ip, "en");

        return SingleResult.buildSuccess(geoipLocation);
    }
}
