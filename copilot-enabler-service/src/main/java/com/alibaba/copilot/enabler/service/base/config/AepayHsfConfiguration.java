package com.alibaba.copilot.enabler.service.base.config;

import com.alibaba.aepay.fund.business.api.payment.PaymentFacade;
import com.alibaba.aepay.fund.business.api.payment.PaymentInquiryFacade;
import com.google.common.collect.Lists;
import com.taobao.hsf.app.spring.util.HSFSpringConsumerBean;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 2023/9/26
 */
@Configuration
public class AepayHsfConfiguration {

    @Value("${hsf.aepay.version}")
    private String aepayVersion;

    @Value("${hsf.aepay.unit}")
    private String aepayUnit;

    @Bean
    public HSFSpringConsumerBean aepayHsfApi() {
        HSFSpringConsumerBean consumer = new HSFSpringConsumerBean();
        consumer.setInterfaceClass(PaymentFacade.class);
        consumer.setGroup("HSF");
        // 定期代扣接口返回8秒多，为了保险起见，这里设置15秒
        consumer.setClientTimeout(15000);
        consumer.setVersion(aepayVersion);
        if (StringUtils.isNotEmpty(aepayUnit)) {
            consumer.setConfigserverCenter(Lists.newArrayList(aepayUnit));
        }
        return consumer;
    }

    @Bean
    public HSFSpringConsumerBean aepayReadHsfApi() {
        HSFSpringConsumerBean consumer = new HSFSpringConsumerBean();
        consumer.setInterfaceClass(PaymentInquiryFacade.class);
        consumer.setGroup("HSF");
        consumer.setVersion(aepayVersion);
        if (StringUtils.isNotEmpty(aepayUnit)) {
            consumer.setConfigserverCenter(Lists.newArrayList(aepayUnit));
        }
        return consumer;
    }
}
