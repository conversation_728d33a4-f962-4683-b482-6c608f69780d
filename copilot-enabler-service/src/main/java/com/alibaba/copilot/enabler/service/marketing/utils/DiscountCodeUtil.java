package com.alibaba.copilot.enabler.service.marketing.utils;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.marketing.dto.DiscountInfoDTO;
import com.alibaba.copilot.enabler.domain.base.utils.RedisUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version 2024/2/27
 */
@Slf4j
public class DiscountCodeUtil {

    private static final String REDIS_KEY_PREFIX = "MARKETING_DISCOUNT_CODE_";

    /**
     * 生成折扣码
     *
     * @param dto           存储的折扣信息
     * @param validDuration 折扣信息存储的有效期
     * @return 折扣码
     */
    public static String generateDiscountCode(DiscountInfoDTO dto, Duration validDuration) {
        log.info("generateDiscountCode, dto={}", JSON.toJSONString(dto));

        Assertor.assertNotBlank(dto.getAppCode(), "dto is blank");
        Assertor.assertNonNull(dto.getUserId(), "userId is null");
        Assertor.assertNonNull(dto.getPlanId(), "planId is null");
        Assertor.assertNonNull(dto.getDiscountRuleId(), "discountRuleId is null");
        Assertor.assertNonNull(validDuration, "validDuration is null");

        String discountCode = RandomUtil.randomString(10);

        String redisKey = getRedisKey(discountCode);
        String redisValue = JSON.toJSONString(dto);

        long validSeconds = validDuration.getSeconds();
        RedisUtils.setex(redisKey, validSeconds, redisValue);

        return discountCode;
    }

    /**
     * 验证折扣码 (验证失败时会抛出异常)
     */
    public static DiscountInfoDTO restoreByDiscountCode(String discountCode) {
        log.info("validateDiscountCode, discountCode={}", JSON.toJSONString(discountCode));

        String redisKey = getRedisKey(discountCode);
        String redisValue = RedisUtils.get(redisKey);
        if (StrUtil.isEmpty(redisValue)) {
            throw new BizException(ErrorCode.INVALID_PARAM, "discountCode is invalid or expired");
        }

        return JSON.parseObject(redisValue, DiscountInfoDTO.class);
    }

    private static String getRedisKey(String discountCode) {
        return REDIS_KEY_PREFIX + discountCode;
    }
}
