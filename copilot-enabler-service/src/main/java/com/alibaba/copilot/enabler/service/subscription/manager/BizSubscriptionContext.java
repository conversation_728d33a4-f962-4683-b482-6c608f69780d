package com.alibaba.copilot.enabler.service.subscription.manager;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.service.subscription.utils.BizUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/27
 */
@Data
public class BizSubscriptionContext {

    private SubscribePlanDTO subscribePlanDTO;

    public String getAppCode() {
        return subscribePlanDTO.getAppCode();
    }

    public SubscriptionPayType getSubscriptionPayType() {
        return exportSubscriptionPayType(subscribePlanDTO);
    }

    private SubscriptionPayType exportSubscriptionPayType(SubscribePlanDTO subscribePlanDTO) {
        if (Objects.isNull(subscribePlanDTO)) {
            return null;
        }
        String name = subscribePlanDTO.getSubscriptionPayTypeName();
        if (StringUtils.isNotBlank(name)) {
            return SubscriptionPayType.valueOf(SubscriptionPayType.class, name);
        }
        // 兼容老逻辑
        SubscriptionPayType subscriptionPayType = subscribePlanDTO.getSubscriptionPayType();
        if (subscriptionPayType == null) {
            subscriptionPayType = AppEnum.getAppByCode(subscribePlanDTO.getAppCode()).getDefaultSubscriptionPayType();
        }
        return subscriptionPayType;
    }


}