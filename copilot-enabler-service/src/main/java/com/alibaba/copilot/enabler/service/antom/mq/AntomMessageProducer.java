package com.alibaba.copilot.enabler.service.antom.mq;

import com.alibaba.copilot.enabler.domain.base.mq.impl.BaseMessageQueueProducer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Antom 消息生产者
 *
 * <AUTHOR>
 * @date 2025/05/01
 */
@Component
public class AntomMessageProducer extends BaseMessageQueueProducer {

    @Value("${inner.metaq.message.producerUnitName}")
    private String unitName;

    @Override
    protected String getProducerGroup() {
        return "PID_antom_producer";
    }

    @Override
    protected String getProducerUnitName() {
        return unitName;
    }
}
