package com.alibaba.copilot.enabler.service.payment.metaq.listener;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.aepay.fund.business.api.payment.dto.notify.RefundNotifyDTO;
import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.infra.base.utils.MetaqUtils;
import com.alibaba.copilot.enabler.service.payment.metaq.handler.PaymentRefundNotifyHandler;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.taobao.eagleeye.EagleEye;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 退款结果通知listener
 */
@Component(value = "paymentRefundNotifyListener")
public class PaymentRefundNotifyListener implements MessageListenerConcurrently {
    public static final Logger TASK_LOG = LoggerFactory.getLogger("taskLogger");

    @Autowired
    private PaymentRefundNotifyHandler refundNotifyHandler;

    @Override
    @Monitor(name = "退款结果通知listener", level = Monitor.Level.P1, layer = Monitor.Layer.CONSUMER)
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        MessageExt messageExt = CollectionUtil.getFirst(msgs);
        if (messageExt == null) {
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        String msgId = messageExt.getMsgId();

        JSONObject notifyContentJsonObject = MetaqUtils.getJSONObjOfMessageExtBody(messageExt);
        String notifyContentStr = notifyContentJsonObject == null ? null : notifyContentJsonObject.toJSONString();

        // 交易流水id
        String tradeNo = null;
        try {
            EagleEye.startTrace(null, this.getClass().getName(), EagleEye.TYPE_CUSTOM_MESSAGE_SUB);
            TASK_LOG.info("paymentRefundNotifyListener notifyContent:::{}", notifyContentStr);

            if (notifyContentJsonObject == null) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

            RefundNotifyDTO refundNotifyDTO = notifyContentJsonObject.toJavaObject(RefundNotifyDTO.class);
            if (!Objects.equals(refundNotifyDTO.getNotifyType(), AEPaymentConstants.REFUND_PAYMENT_RESULT_NOTIFY_TYPE)) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

            // 只接收"退款成功"消息，"退款处理中"和"退款失败"消息暂不处理
            if (!Objects.equals(refundNotifyDTO.getRefundStatus(), AEPaymentConstants.REFUND_RESULT_NOTIFY_SUCCESS)) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

            tradeNo = refundNotifyDTO.getRefundRequestId();

            Assertor.asserts(StringUtils.isNotBlank(tradeNo), "paymentRefundNotifyListener msg refundRequestId illegal");
            Assertor.asserts(refundNotifyDTO.getResult() != null
                            && Objects.equals(refundNotifyDTO.getResult().getResultCode(), AEPaymentConstants.PAYMENT_RESULT_NOTIFY_SUCCESS),
                    "paymentRefundNotifyListener msg not success");

            refundNotifyHandler.handle(tradeNo, msgId, notifyContentStr, refundNotifyDTO);

            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (BizException bizException) {
            TASK_LOG.error("paymentRefundNotifyListener bizError,msg:{},notifyContent:{}",
                    bizException.getMessage(), notifyContentStr, bizException);
            refundNotifyHandler.handleFail(tradeNo, msgId, notifyContentStr);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            TASK_LOG.error("paymentRefundNotifyListener error,msg:{},notifyContent:{}",
                    e.getMessage(), notifyContentStr, e);

            // Metaq消费自动重试
            if (MetaqUtils.isResumeAutoRetry(messageExt, context, notifyContentStr)) {
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }

            refundNotifyHandler.handleFail(tradeNo, msgId, notifyContentStr);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } finally {
            EagleEye.endTrace(null);
        }
    }
}