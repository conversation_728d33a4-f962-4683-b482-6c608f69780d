package com.alibaba.copilot.enabler.service.subscription.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 订阅计划结果
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class SubscribePlanResultVO {

    /**
     * 是否需要立即支付
     */
    private Boolean shouldPay;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 支付 id
     */
    private List<Long> tradeNos;
}
