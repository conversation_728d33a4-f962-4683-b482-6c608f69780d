package com.alibaba.copilot.enabler.service.stripe.processor.invoice;

import com.alibaba.copilot.enabler.client.base.event.DomainEventJsonProducer;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.payment.dto.OrderDTO;
import com.alibaba.copilot.enabler.client.payment.dto.PaymentResultEventDTO;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEvent;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.service.stripe.StripeConsts;
import com.alibaba.copilot.enabler.service.stripe.processor.AbstractStripeEventProcessor;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeExecuteContext;
import com.alibaba.copilot.enabler.service.subscription.manager.adic.StripeSubManagerForADIC;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class CheckoutSessionExpired4ADIC extends AbstractStripeEventProcessor {
    @Resource
    private DomainEventJsonProducer eventPublisher;
    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;

    @Override
    protected void doProcess(StripeExecuteContext context) throws Exception {
        log.info("CheckoutSessionExpired4ADIC context={}", JSON.toJSONString(context));
        // 支付失败。修改本地的订单状态
        String orderIdStr = context.getMetaData().getSubscriptionOrderId();
        String userId = context.getMetaData().getUserId();

        if (StringUtils.isBlank(orderIdStr) || StringUtils.isBlank(userId)) {
            log.error("InvoicePaymentFailed4ADIC orderId userId is empty {}", JSON.toJSONString(context));
        }
        SubscriptionOrder subscriptionOrder = new SubscriptionOrder();
        subscriptionOrder.setId(Long.parseLong(orderIdStr));
        subscriptionOrder.setStatus(SubscriptionOrderStatus.PAY_CANCELLED);
        subscriptionOrder.setAppCode(AppEnum.ADIC.getCode());
        subscriptionOrderRepository.saveSubscriptionOrder(subscriptionOrder);

        // 发送订单支付失败事件
        PaymentResultEventDTO paymentResultEventDTO = new PaymentResultEventDTO();
        paymentResultEventDTO.setAppCode(AppEnum.ADIC.getCode());
        paymentResultEventDTO.setUserId(Long.parseLong(userId));
        OrderDTO order = new OrderDTO();
        order.setReferenceOrderId(orderIdStr);
        paymentResultEventDTO.setOrder(order);
        paymentResultEventDTO.setTradeStatus(TradeRecordStatus.FAIL.name());
        eventPublisher.publish(paymentResultEventDTO);
    }

    @Override
    public String getEventType() {
        return StripeConsts.CHECKOUT_SESSION_EXPIRED;
    }

    @Override
    public String getAppCode() {
        return AppEnum.ADIC.getCode();
    }
}
