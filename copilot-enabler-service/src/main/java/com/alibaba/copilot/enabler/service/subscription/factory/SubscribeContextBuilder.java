package com.alibaba.copilot.enabler.service.subscription.factory;

import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.marketing.dto.FirstMonthDiscountDTO;
import com.alibaba.copilot.enabler.client.marketing.dto.RestoreFirstMonthDiscountDTO;
import com.alibaba.copilot.enabler.client.marketing.service.FirstMonthDiscountService;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.payment.constant.StatusFlowReasonEnum;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.TrialDurationDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.client.user.dto.UserAppRelationDTO;
import com.alibaba.copilot.enabler.client.user.request.UserAppRelationQuery;
import com.alibaba.copilot.enabler.client.user.service.UserAppRelationService;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.payment.service.OrderDomainService;
import com.alibaba.copilot.enabler.domain.subscription.dto.FinalDiscountDTO;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.domain.subscription.service.DiscountService;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategy;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategyFactory;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeTrialContext;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.SubscribeContext;
import com.alibaba.copilot.enabler.infra.base.constants.ErrorCodes;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 订阅接口需要聚合的信息实在太多了, 这里将上下文信息类的构造逻辑, 单独抽取到一个类中<hr/>
 * 还会包含一些参数校验逻辑
 *
 * <AUTHOR>
 * @version 2024/1/18
 */
@Slf4j
@Component
public class SubscribeContextBuilder {

    @Resource
    private SubscriptionPlanRepository planRepository;

    @Resource
    private SubscriptionOrderRepository orderRepository;

    @Resource
    private TradeRecordRepository tradeRecordRepository;

    @Resource
    private UserAppRelationService userAppRelationService;

    @Resource
    private DiscountService discountService;

    @Resource
    private OrderDomainService orderDomainService;

    @Resource
    private FirstMonthDiscountService firstMonthDiscountService;

    public SubscribeContext build(SubscribePlanDTO subscribePlanDTO) {
        String appCode = subscribePlanDTO.getAppCode();
        Long userId = subscribePlanDTO.getUserId();
        Long planId = subscribePlanDTO.getPlanId();
        String shareCode = subscribePlanDTO.getShareCode();
        String discountCode = subscribePlanDTO.getDiscountCode();
        Date now = new Date();
        // 未传递支付方式时, 默认取信用卡支付, 兼容老逻辑
        PaymentMethodEnum paymentMethod = Optional.ofNullable(exportPaymentMethod(subscribePlanDTO))
                .orElse(PaymentMethodEnum.CREDIT_CARD);

        // 查询用户关联信息
        UserAppRelationDTO userAppRelationDTO = getUserAppRelation(appCode, userId);
        log.info("subscribePlan userAppRelationDTO:{}", JSON.toJSONString(userAppRelationDTO));

        // 获取待订阅计划信息
        SubscriptionPlan newPlan = queryPlanById(planId);
        Assertor.asserts(Objects.equals(newPlan.getAppCode(), appCode), "planId illegal");
        log.info("subscribePlan newPlan:{}", JSON.toJSONString(newPlan));

        // 查询该用户的历史订单
        List<SubscriptionOrder> historyOrders = orderRepository.getHistoryOrders(userId, appCode);

        // 获取用户已生效的订单
        SubscriptionOrder oldEffectOrder = getEffcOrder(historyOrders);
        log.info("subscribePlan oldEffectOrder:{}", JSON.toJSONString(oldEffectOrder));

        // 获取用户已生效的计划
        SubscriptionPlan oldPlan = queryPlanByOrder(oldEffectOrder);
        log.info("subscribePlan oldPlan:{}", JSON.toJSONString(oldPlan));

        // 检查相同套餐切换的逻辑
        checkSamePlanSwitchLogic(planId, newPlan, oldEffectOrder, oldPlan);

        // 创建订单之前，将之前未支付的订单取消掉
        // 多笔订单间关系独立, 不要取消上一笔订单, 否则会引起“支付后未回调时重复下单，老订单被取消的问题”
        // cancelPendingOrdersIfExists(historyOrders);

        // 查询上笔订单的支付记录
        TradeRecord oldPayRecord = queryPayRecordByOrder(oldEffectOrder);
        log.info("subscribePlan oldPayRecord:{}", JSON.toJSONString(oldPayRecord));

        SubscriptionPayType subscriptionPayType = exportSubscriptionPayType(subscribePlanDTO);
        if (subscriptionPayType == null) {
            subscriptionPayType = AppEnum.getAppByCode(appCode).getDefaultSubscriptionPayType();
        }

        // 查询试用期信息
        TrialDurationDTO trialDurationDTO = computeTrialDuration(appCode, userId, newPlan, historyOrders, subscriptionPayType);
        log.info("subscribePlan trialDurationDTO:{}", JSON.toJSONString(trialDurationDTO));

        // 查询折扣信息
        FinalDiscountDTO finalDiscountInfo = discountService.getFinalDiscountInfo(appCode, shareCode, newPlan, historyOrders, false);
        log.info("subscribePlan finalDiscountInfo:{}", JSON.toJSONString(finalDiscountInfo));

        // 查询首月减免折扣
        FirstMonthDiscountDTO firstMonthDiscountDTO = getFirstMonthDiscountDTO(discountCode, appCode, userId, planId);

        // 构建订阅上下文信息, 聚合所有需要用到的数据
        return new SubscribeContext(paymentMethod, userAppRelationDTO, newPlan, trialDurationDTO,
                finalDiscountInfo, oldEffectOrder, oldPlan, oldPayRecord, now, shareCode, historyOrders, firstMonthDiscountDTO, subscriptionPayType, subscribePlanDTO.getUserClientIp());
    }

    private PaymentMethodEnum exportPaymentMethod(SubscribePlanDTO subscribePlanDTO) {
        if (Objects.isNull(subscribePlanDTO)) {
            return null;
        }
        String name = subscribePlanDTO.getPaymentMethodName();
        if (StringUtils.isNotBlank(name)) {
            return PaymentMethodEnum.valueOf(PaymentMethodEnum.class, name);
        }
        // 兼容老逻辑
        return subscribePlanDTO.getPaymentMethod();
    }

    private SubscriptionPayType exportSubscriptionPayType(SubscribePlanDTO subscribePlanDTO) {
        if (Objects.isNull(subscribePlanDTO)) {
            return null;
        }
        String name = subscribePlanDTO.getSubscriptionPayTypeName();
        if (StringUtils.isNotBlank(name)) {
            return SubscriptionPayType.valueOf(SubscriptionPayType.class, name);
        }
        // 兼容老逻辑
        return subscribePlanDTO.getSubscriptionPayType();
    }

    /**
     * 查询首月减免折扣
     */
    private FirstMonthDiscountDTO getFirstMonthDiscountDTO(String discountCode, String appCode, Long userId, Long planId) {
        if (StringUtils.isEmpty(discountCode)) {
            return null;
        }

        RestoreFirstMonthDiscountDTO dto = new RestoreFirstMonthDiscountDTO()
                .setAppCode(appCode)
                .setUserId(userId)
                .setDiscountCode(discountCode)
                .setPlanId(planId);
        return firstMonthDiscountService.restoreAndValidateFirstMonthDiscount(dto);
    }

    /**
     * 取消之前未支付的订单
     */
    private void cancelPendingOrdersIfExists(List<SubscriptionOrder> historyOrders) {
        List<SubscriptionOrder> pendingPaymentOrders = historyOrders.stream()
                .filter(order -> order.getStatus() == SubscriptionOrderStatus.PENDING_PAYMENT)
                .collect(Collectors.toList());
        log.info("pending payment:{}", JSON.toJSONString(pendingPaymentOrders));
        pendingPaymentOrders.forEach(pendingPaymentOrder -> {
            orderDomainService.cancelPendingPaymentOrder(pendingPaymentOrder, StatusFlowReasonEnum.USER_CHANGE_SUBSCRIPTION);
        });
    }

    /**
     * 检查相同套餐切换的逻辑
     */
    private static void checkSamePlanSwitchLogic(Long planId, SubscriptionPlan newPlan, SubscriptionOrder oldEffectOrder, SubscriptionPlan oldPlan) {
        // 禁止免费同名套餐切换
        boolean switchSameFreePlan = oldEffectOrder != null
                && oldEffectOrder.getAttributes().isFreeOrder()
                && oldPlan != null
                && StringUtils.equals(oldPlan.getName(), newPlan.getName());
        if (switchSameFreePlan) {
            log.warn("subscribePlan with same plan by same name:{}", oldPlan.getName());
            throw new BizException(ErrorCode.INVALID_PARAM, "Not supported for switching with same plan when your order is free");
        }

        // 禁止同套餐切换
        boolean samePlan = oldPlan != null && oldPlan.getId().equals(planId);
        if (samePlan) {
            log.warn("subscribePlan with same plan by same id:{}", oldPlan.getId());
            throw new BizException(ErrorCode.INVALID_PARAM, "Not supported for switching with same plan");
        }
    }

    /**
     * 查询支付订单关联的流水信息
     */
    @Nullable
    private TradeRecord queryPayRecordByOrder(@Nullable SubscriptionOrder order) {
        return Optional.ofNullable(order)
                .map(SubscriptionOrder::getId)
                .map(tradeRecordRepository::queryPaySuccessRecordByOrderId)
                .orElse(null);
    }

    /**
     * 获取用户已生效的计划
     */
    @Nullable
    private SubscriptionPlan queryPlanByOrder(@Nullable SubscriptionOrder order) {
        return Optional.ofNullable(order)
                .map(SubscriptionOrder::getSubscriptionPlanId)
                .map(planId -> planRepository.queryByPlanId(planId, false))
                .orElse(null);
    }

    /**
     * 获取用户已生效的订单
     */
    @Nullable
    private SubscriptionOrder getEffcOrder(List<SubscriptionOrder> historyOrders) {
        if (CollectionUtils.isEmpty(historyOrders)) {
            return null;
        }
        return historyOrders.stream()
                .filter(order -> SubscriptionOrderStatus.IN_EFFECT.equals(order.getStatus()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取待订阅计划信息
     */
    @Nonnull
    private SubscriptionPlan queryPlanById(Long planId) {
        return Optional.ofNullable(planRepository.queryByPlanId(planId, true))
                .orElseThrow(() -> new BizException(ErrorCodes.PLAN_NOT_EXIST));
    }

    /**
     * 查询用户关联信息
     */
    private UserAppRelationDTO getUserAppRelation(String appCode, Long userId) {
        UserAppRelationQuery userAppRelationQuery = UserAppRelationQuery.builder()
                .appCode(appCode)
                .userId(userId)
                .build();
        UserAppRelationDTO userAppRelationDTO = userAppRelationService.queryUserAppRelation(userAppRelationQuery);
        Assertor.assertNonNull(userAppRelationDTO, ErrorCodes.USER_NOT_SUBSCRIBE_APP);
        return userAppRelationDTO;
    }

    private TrialDurationDTO computeTrialDuration(String appCode, Long userId, SubscriptionPlan newPlan, List<SubscriptionOrder> historyOrders, SubscriptionPayType subscriptionPayType) {
        SubscriptionStrategy subscriptionStrategy = SubscriptionStrategyFactory.getStrategy(appCode, subscriptionPayType);
        ComputeTrialContext computeContext = new ComputeTrialContext(appCode, userId, newPlan, historyOrders);
        return subscriptionStrategy.computeTrialDuration(computeContext);
    }
}
