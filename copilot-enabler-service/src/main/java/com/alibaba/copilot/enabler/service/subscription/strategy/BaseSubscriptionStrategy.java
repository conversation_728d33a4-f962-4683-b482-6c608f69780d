package com.alibaba.copilot.enabler.service.subscription.strategy;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.marketing.dto.FirstMonthDiscountDTO;
import com.alibaba.copilot.enabler.client.marketing.dto.RestoreFirstMonthDiscountDTO;
import com.alibaba.copilot.enabler.client.marketing.service.FirstMonthDiscountService;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.payment.constant.StatusFlowReasonEnum;
import com.alibaba.copilot.enabler.client.payment.dto.RefundDTO;
import com.alibaba.copilot.enabler.client.payment.dto.RefundResultDTO;
import com.alibaba.copilot.enabler.client.payment.service.PaymentService;
import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import com.alibaba.copilot.enabler.client.subscription.dto.NeedPayToSubscribePlanDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.TrialDurationDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.base.utils.TimeUtils;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentInfo;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.payment.service.OrderDomainService;
import com.alibaba.copilot.enabler.domain.subscription.dto.FinalDiscountDTO;
import com.alibaba.copilot.enabler.domain.subscription.event.SubscriptionPlanChangedEventProducer;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.domain.subscription.response.EmailResponse;
import com.alibaba.copilot.enabler.domain.subscription.service.DiscountService;
import com.alibaba.copilot.enabler.domain.subscription.strategy.AppEnumHolder;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategy;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeNewPlanPriceContext;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeNewPlanPriceDTO;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeOrderEndTimeResultDTO;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.SubscribeContext;
import com.alibaba.copilot.enabler.infra.base.constants.ErrorCodes;
import com.alibaba.copilot.enabler.service.payment.metaq.trade.TradeStartProducer;
import com.alibaba.copilot.enabler.service.subscription.factory.RefundRecordBuilder;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 通用处理策略 (放一些通用的处理逻辑)
 *
 * <AUTHOR>
 * @version 2023/10/12
 */
@Slf4j
public abstract class BaseSubscriptionStrategy implements SubscriptionStrategy, AppEnumHolder {

    private static final ThreadLocal<AppEnum> APP_ENUM_THREAD_LOCAL = new ThreadLocal<>();

    @Resource
    protected PaymentService paymentService;

    @Resource
    protected TradeRecordRepository tradeRecordRepository;

    @Resource
    protected SubscriptionOrderRepository subscriptionOrderRepository;

    @Resource
    protected SubscriptionPlanRepository subscriptionPlanRepository;

    @Resource
    protected SubscriptionPlanChangedEventProducer subscriptionPlanChangedEventProducer;

    @Resource
    protected OrderDomainService orderDomainService;

    @Resource
    protected DiscountService discountService;

    @Resource
    protected FirstMonthDiscountService firstMonthDiscountService;

    @Resource
    private TradeStartProducer tradeStartProducer;

    @Override
    public void refundForTrialOrder(SubscriptionOrder order) {
        log.info("refundForTrialOrder, order={}", JSON.toJSONString(order));

        // 1. 查询支付流水
        TradeRecord payRecord = tradeRecordRepository.queryPaySuccessRecordByOrderId(order.getId());

        if (payRecord != null) {
            // 2. 创建退款流水
            TradeRecord refundRecord = new RefundRecordBuilder(payRecord, getAppEnum()).build();
            tradeRecordRepository.createOrUpdateTradeRecord(refundRecord);

            // 3. 执行退款操作
            String paymentId = payRecord.getOutTradeNo();
            SingleResult<RefundResultDTO> refundResult = doRefund(paymentId, refundRecord);
            log.info("refundForTrialOrder, doRefund finished, result={}", JSON.toJSONString(refundResult));
        }

        // 4. 发送试用期退订事件
        subscriptionPlanChangedEventProducer.sendCancelWhenTrialEvent(order);
    }

    @Override
    public ComputeOrderEndTimeResultDTO computeOrderEndTime(SubscribeContext context) {
        Date now = context.getNow();
        SubscriptionPlan activatedPlan = context.getNewPlan();
        TrialDurationDTO trialDurationDTO = context.getTrialDurationDTO();
        Boolean isTrial = trialDurationDTO.getIsTrial();

        long planDays = TimeUtils.calculateDayCount(activatedPlan.getDuration(), activatedPlan.getDurationUnit());
        final long trialDays;
        final Date orderEndTime;
        if (isTrial) {
            // 试用期订单, 结束时间 = 当前时间 + 试用期剩余时间 + 套餐持续时间
            long remainTrialDays = TimeUtils.calculateDayCount(trialDurationDTO.getRemainTrialDay(), DurationUnit.DAY);
            orderEndTime = computeOrderEndTimeForTrial(now, planDays, remainTrialDays);
            trialDays = remainTrialDays;
        } else {
            // 非试用期订单, 结束时间 = 当前时间 + 套餐持续时间
            orderEndTime = DateUtils.addDays(now, (int) planDays);
            trialDays = 0;
        }
        return new ComputeOrderEndTimeResultDTO()
                .setPlanDays(planDays)
                .setTrialDays(trialDays)
                .setOrderEndTime(orderEndTime);
    }

    /**
     * 计算试用期的订单结束时间
     *
     * @param now             当前时间
     * @param planDays        套餐天数
     * @param remainTrialDays 剩余试用期天数
     * @return 订单结束时间
     */
    protected abstract Date computeOrderEndTimeForTrial(Date now, long planDays, long remainTrialDays);

    /**
     * 判断是否需要支付
     *
     * @param context 计算价格的上下文信息
     * @return true: 需要支付; false: 不需要支付;
     */
    protected Boolean adjustNeedPayByComputePriceContext(ComputeNewPlanPriceContext context) {
        NeedPayToSubscribePlanDTO needPayToSubscribePlanDTO = new NeedPayToSubscribePlanDTO()
                .setAppCode(context.getAppCode())
                .setUserId(context.getUserId())
                .setPlanId(context.getNewPlan().getId());
        return needPayToSubscribePlan(needPayToSubscribePlanDTO);
    }

    @Override
    public Boolean needPayToSubscribePlan(NeedPayToSubscribePlanDTO dto) {
        log.info("needPayToSubscribePlan, dto={}", JSON.toJSONString(dto));

        // 查询套餐信息
        Long planId = dto.getPlanId();
        SubscriptionPlan targetPlan = subscriptionPlanRepository.queryByPlanId(planId, false);
        log.info("needPayToSubscribePlan, queryByPlanId, result={}", JSON.toJSONString(targetPlan));
        Assertor.assertNonNull(targetPlan, "No plan found, planId=" + planId);

        if (targetPlan.isFree()) {
            // 切换到免费套餐, 不需要支付
            log.info("needPayToSubscribePlan, plan is free, don't need to pay");
            return false;
        }

        boolean result = needPayToSubscribePlanWhenPlanNeedCost(dto, targetPlan);
        log.info("needPayToSubscribePlan, invoke finished, result={}", result);

        return result;
    }

    /**
     * 判断订阅指定套餐需要花费时, 是否需要付款
     *
     * @param dto        参数信息
     * @param targetPlan 目标套餐
     * @return true: 需要付款; false: 无需付款;
     */
    protected abstract boolean needPayToSubscribePlanWhenPlanNeedCost(NeedPayToSubscribePlanDTO dto, SubscriptionPlan targetPlan);

    @Override
    public EmailResponse sendEmail(Long userId, Object emailInfoObj) {
        log.info("sendEmail, NOT SUPPORT to send email for {}", getAppEnum().getName());
        return EmailResponse.buildErrorRes("Not Support");
    }

    /**
     * 执行退款操作
     */
    protected SingleResult<RefundResultDTO> doRefund(String paymentId, TradeRecord refundRecord) {
        log.info("doRefund, paymentId={}, refundRecord={}", paymentId, JSON.toJSONString(refundRecord));

        // 1. 执行退款操作
        RefundDTO refundDTO = new RefundDTO()
                .setPaymentId(paymentId)
                .setRefundRequestId(refundRecord.getTradeNo())
                .setRefundAmount(refundRecord.getTradeAmount())
                .setAppCode(refundRecord.getAppCode());
        SingleResult<RefundResultDTO> refundResult = paymentService.refund(refundDTO);

        // 2. 更新外部退款单号
        updateOutTradeNoIfExists(refundResult, refundRecord);

        // 3. 如果能确保退款成功时, 直接操作退款链路的逻辑
        handleRefundLogicIfEnsureSuccess(refundResult, refundRecord);

        // 4. 投递MQ消息, 开始主动查询退款结果
        postRefundStartMQ(refundResult, refundDTO);

        return refundResult;
    }

    /**
     * 投递MQ消息, 开始主动查询退款结果
     */
    private void postRefundStartMQ(SingleResult<RefundResultDTO> refundResult, RefundDTO refundDTO) {
        Optional.ofNullable(refundResult)
                .filter(SingleResult::isSuccess)
                .map(SingleResult::getData)
                .map(RefundResultDTO::getRefundRequestId)
                .filter(StringUtils::isNotEmpty)
                .ifPresent(e -> tradeStartProducer.postRefundStartMessage(e, refundDTO.getAppCode()));
    }

    /**
     * 如果能确保退款成功时, 直接操作退款链路的逻辑 (对于Alipay场景, 返回同步方法调用成功, 即认为是退款成功)
     */
    private void handleRefundLogicIfEnsureSuccess(SingleResult<RefundResultDTO> refundResult, TradeRecord refundRecord) {
        PaymentMethodEnum paymentMethodType = PaymentMethodEnum.ofName(refundRecord.getPaymentMethod());
        RefundResultDTO resultDTO = refundResult.getData();
        String tradeNo = refundRecord.getTradeNo();
        if (!refundResult.isSuccess()
                || resultDTO == null
                || !PaymentMethodEnum.isAlipayPaymentMethod(paymentMethodType)) {
            log.info("doRefund, can't ensure refund result, tradeNo={}", tradeNo);
            return;
        }

        // 能确保退款成功时, 直接更改DB数据
        log.info("doRefund, ensure refund success, tradeNo={}", tradeNo);
        PaymentInfo paymentInfo = PaymentInfo.builder()
                .tradeTime(System.currentTimeMillis())
                .outTradeNo(resultDTO.getRefundId())
                .build();
        StatusFlowReasonEnum flowReasonEnum = StatusFlowReasonEnum.USER_HAD_DISPUTE;
        orderDomainService.refundSuccess(refundRecord, null, paymentInfo, flowReasonEnum);
    }

    /**
     * 更新外部退款单号
     */
    private void updateOutTradeNoIfExists(SingleResult<RefundResultDTO> result, TradeRecord refundRecord) {
        Optional.ofNullable(result.getData())
                .map(RefundResultDTO::getRefundId)
                .ifPresent(refundId -> {
                    log.info("updateOutTradeNoIfExists, tradeNo={}, outTradeNo={}", refundRecord.getTradeNo(), refundId);
                    refundRecord.setOutTradeNo(refundId);
                    tradeRecordRepository.createOrUpdateTradeRecord(refundRecord);
                });
    }

    /**
     * 查询该用户的历史订单
     */
    protected List<SubscriptionOrder> getHistoryOrders(Long userId, String appCode) {
        return subscriptionOrderRepository.getHistoryOrders(userId, appCode);
    }

    @Override
    public void setAppEnum(AppEnum appEnum) {
        APP_ENUM_THREAD_LOCAL.set(appEnum);
    }

    protected AppEnum getAppEnum() {
        return APP_ENUM_THREAD_LOCAL.get();
    }

    @Override
    public ComputeNewPlanPriceContext buildComputeNewPlanPriceContext(ComputeNewPlanPriceDTO dto) {
        log.info("buildComputeNewPlanPriceContext, dto={}", JSON.toJSONString(dto));
        String appCode = dto.getAppCode();
        Long userId = dto.getUserId();
        String shareCode = dto.getShareCode();
        Long planId = dto.getPlanId();
        String discountCode = dto.getDiscountCode();
        Date now = new Date();

        // 1. 查询新套餐
        SubscriptionPlan newPlan = subscriptionPlanRepository.queryByPlanId(planId, true);
        Assertor.assertNonNull(newPlan, ErrorCodes.PLAN_NOT_EXIST);
        Assertor.asserts(Objects.equals(appCode, newPlan.getAppCode()), "planId illegal");

        // 2. 查询历史订单
        List<SubscriptionOrder> historyOrders = subscriptionOrderRepository.getHistoryOrders(userId, appCode);

        // 3. 查询折扣信息
        FinalDiscountDTO finalDiscountInfo = discountService.getFinalDiscountInfo(appCode, shareCode, newPlan, historyOrders, false);


        // 4. 查询当前生效的订单
        SubscriptionOrder oldOrder = subscriptionOrderRepository.queryEffectOrder(appCode, userId);

        // 5. 查询当前生效的老套餐
        SubscriptionPlan oldPlan = queryPlanByOrder(oldOrder);

        // 6. 查询当前生效的订单对应的支付流水
        TradeRecord oldPayRecord = queryPayRecordByOrder(oldOrder);

        // 7. 查询首月减免信息
        FirstMonthDiscountDTO firstMonthDiscountDTO = queryFirstMonthDiscountDTO(discountCode, appCode, userId, planId);

        return new ComputeNewPlanPriceContext(
                appCode, userId, shareCode, now, newPlan, historyOrders, finalDiscountInfo, oldOrder, oldPlan, oldPayRecord,
                firstMonthDiscountDTO
        );
    }

    /**
     * 查询首月减免信息
     */
    private FirstMonthDiscountDTO queryFirstMonthDiscountDTO(String discountCode, String appCode, Long userId, Long planId) {
        if (StringUtils.isBlank(discountCode)) {
            return null;
        }

        RestoreFirstMonthDiscountDTO restoreFirstMonthDiscountDTO = new RestoreFirstMonthDiscountDTO()
                .setDiscountCode(discountCode)
                .setAppCode(appCode)
                .setUserId(userId)
                .setPlanId(planId);
        return firstMonthDiscountService.restoreAndValidateFirstMonthDiscount(restoreFirstMonthDiscountDTO);
    }

    /**
     * 查询订单对应的套餐信息
     */
    @Nullable
    private SubscriptionPlan queryPlanByOrder(@Nullable SubscriptionOrder order) {
        if (order == null) {
            return null;
        }
        Long planId = order.getSubscriptionPlanId();
        return subscriptionPlanRepository.queryByPlanId(planId, false);
    }

    /**
     * 查询支付订单关联的流水信息
     */
    @Nullable
    private TradeRecord queryPayRecordByOrder(@Nullable SubscriptionOrder order) {
        if (order == null) {
            return null;
        }
        return tradeRecordRepository.queryPaySuccessRecordByOrderId(order.getId());
    }

    protected String formatCycleDetailDate(LocalDate date) {
        return date.format(DateTimeFormatter.ofPattern("yyyy年M月d日"));
    }

    @NotNull
    protected RuntimeException getNotSupportedException() {
        return new RuntimeException("Not supported for " + getAppEnum().getName());
    }
}
