package com.alibaba.copilot.enabler.service.subscription.strategy.t2g;

import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.dto.CycleFeeDetail;
import com.alibaba.copilot.enabler.client.subscription.dto.NeedPayToSubscribePlanDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.TrialDurationDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.base.utils.TimeUtils;
import com.alibaba.copilot.enabler.domain.subscription.dto.FinalDiscountDTO;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionOrderQuery;
import com.alibaba.copilot.enabler.domain.subscription.response.EmailResponse;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategyConfig;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.*;
import com.alibaba.copilot.enabler.infra.base.constants.ErrorCodes;
import com.alibaba.copilot.enabler.service.subscription.strategy.basic.BasicSubscriptionStrategy;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import com.alibaba.copilot.enabler.client.subscription.service.Text2GoSubscriptionService;
import java.time.ZoneId;

/**
 * TEXT2GO应用的STRIPE支付订阅策略实现
 */
@Slf4j
@Component
@SubscriptionStrategyConfig(value = AppEnum.TEXT2GO, payType = SubscriptionPayType.STRIPE)
public class Text2GoStripeSubscriptionStrategy extends BasicSubscriptionStrategy {

    @Resource
    private SubscriptionPlanRepository subscriptionPlanRepository;

    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;

    @Resource
    private Text2GoSubscriptionService text2GoSubscriptionService;

    @Override
    public void refundForTrialOrder(SubscriptionOrder order) {
        log.info("Text2GoStripeSubscriptionStrategy.refundForTrialOrder, order={}", JSON.toJSONString(order));
        // 处理试用期订单退款逻辑 - STRIPE支付类型不需要处理
    }

    @Override
    public TrialDurationDTO computeTrialDuration(ComputeTrialContext context) {
        log.info("Text2GoStripeSubscriptionStrategy.computeTrialDuration, context={}", JSON.toJSONString(context));
        
        // Use the service layer implementation to check trial eligibility
        try {
            TrialDurationDTO trialResult = text2GoSubscriptionService.subscriptionIsTrial(context.getUserId());
            if (trialResult != null) {
                log.info("Text2GoStripeSubscriptionStrategy.computeTrialDuration, using service result={}", JSON.toJSONString(trialResult));
                return trialResult;
            }
        } catch (Exception e) {
            log.error("Text2GoStripeSubscriptionStrategy.computeTrialDuration, error checking trial eligibility, userId={}, error={}",
                context.getUserId(), e.getMessage(), e);
        }
        
        // Fallback to default behavior: no trial
        log.info("Text2GoStripeSubscriptionStrategy.computeTrialDuration, fallback to no trial");
        return new TrialDurationDTO().setIsTrial(false);
    }

    @Override
    public CreateOrderAndTradeResultDTO createOrderAndTrade(SubscribeContext context) {
        log.info("Text2GoStripeSubscriptionStrategy.createOrderAndTrade, context={}", JSON.toJSONString(context));
        // TEXT2GO应用使用StripeSubManagerForT2g来创建订单，此方法不应被直接调用
        throw new UnsupportedOperationException("TEXT2GO应用使用StripeSubManagerForT2g创建订单");
    }

    @Override
    public ComputeOrderEndTimeResultDTO computeOrderEndTime(SubscribeContext context) {
        log.info("Text2GoStripeSubscriptionStrategy.computeOrderEndTime, context={}", JSON.toJSONString(context));
        // Stripe订阅的结束时间由Stripe服务计算，这里返回空实现
        ComputeOrderEndTimeResultDTO resultDTO = new ComputeOrderEndTimeResultDTO();
        resultDTO.setOrderEndTime(new Date());
        return resultDTO;
    }

    @Override
    public Boolean needPayToSubscribePlan(NeedPayToSubscribePlanDTO dto) {
        log.info("Text2GoStripeSubscriptionStrategy.needPayToSubscribePlan, dto={}", JSON.toJSONString(dto));
        // TEXT2GO应用的所有非免费套餐都需要付费
        Long planId = dto.getPlanId();
        SubscriptionPlan targetPlan = subscriptionPlanRepository.queryByPlanId(planId, false);
        return targetPlan != null && !targetPlan.isFree();
    }

    @Override
    protected boolean needPayToSubscribePlanWhenPlanNeedCost(NeedPayToSubscribePlanDTO dto, SubscriptionPlan targetPlan) {
        // TEXT2GO应用的所有非免费套餐都需要付费
        return true;
    }

    @Override
    public void handleRefundLogicWhenOldOrderCompleted(@Nonnull SubscriptionOrder oldEffectOrder) {
        log.info("Text2GoStripeSubscriptionStrategy.handleRefundLogicWhenOldOrderCompleted, oldEffectOrder={}", JSON.toJSONString(oldEffectOrder));
        // TEXT2GO应用使用Stripe处理退款，此方法不应被直接调用
    }

    @Override
    public EmailResponse sendEmail(Long userId, Object emailInfoObj) {
        log.info("Text2GoStripeSubscriptionStrategy.sendEmail, userId={}, emailInfoObj={}", userId, JSON.toJSONString(emailInfoObj));
        // TEXT2GO应用的邮件发送逻辑通过Stripe处理
        return EmailResponse.buildErrorRes("Not Support");
    }


    @Override
    public ComputeCycleFeeResultDTO computeCycleFee(ComputeNewPlanPriceContext context) {
        log.info("Text2GoStripeSubscriptionStrategy.computeCycleFee, context={}", JSON.toJSONString(context));
        
        List<CycleFeeDetail> cycleFeeDetails = new ArrayList<>();
        LocalDate selectedPlanInfoDate = LocalDate.now();

        // 1. Check if user is eligible for trial period
        TrialDurationDTO trialDurationDTO = null;
        try {
            trialDurationDTO = text2GoSubscriptionService.subscriptionIsTrial(context.getUserId());
            log.info("Text2GoStripeSubscriptionStrategy.computeCycleFee, trial check result={}", JSON.toJSONString(trialDurationDTO));
        } catch (Exception e) {
            log.error("Text2GoStripeSubscriptionStrategy.computeCycleFee, failed to check trial eligibility, userId={}, error={}", 
                context.getUserId(), e.getMessage(), e);
        }

        // 2. Add trial period if eligible
        if (trialDurationDTO != null && trialDurationDTO.getIsTrial()) {
            // Add trial period cycle detail
            addTrialPeriodCycleDetail(selectedPlanInfoDate, cycleFeeDetails, trialDurationDTO.getRemainTrialDay());
            
            // Move next cycle date by trial period days
            selectedPlanInfoDate = selectedPlanInfoDate.plusDays(trialDurationDTO.getRemainTrialDay());
            log.info("Text2GoStripeSubscriptionStrategy.computeCycleFee, added trial period, days={}, next cycle date={}", 
                trialDurationDTO.getRemainTrialDay(), selectedPlanInfoDate);
        }

        // 3. Add final plan cycle detail
        addFinalPlanCycleDetail(selectedPlanInfoDate, cycleFeeDetails, context);

        // 4. Calculate new plan price
        ComputeNewPlanPriceResultDTO priceResult = computeNewPlanPrice(context);

        return new ComputeCycleFeeResultDTO()
            .setCycleFeeDetails(cycleFeeDetails)
            .setFinalPayAmount(priceResult.getPayAmount());
    }
    

    /**
     * 组装最终套餐信息
     */
    private LocalDate addFinalPlanCycleDetail(LocalDate date, List<CycleFeeDetail> details, ComputeNewPlanPriceContext context) {
        SubscriptionPlan newPlan = context.getNewPlan();
        BigDecimal planPrice = newPlan.getPrice();
        
        CycleFeeDetail cycleDetail = CycleFeeDetail.builder()
                .date(formatCycleDetailDate(date))
                .durationUnit(newPlan.getDurationUnit().name())
                .duration(newPlan.getDuration())
                .cycleFee(planPrice)
                .feeDescription(newPlan.getDescription() != null ? newPlan.getDescription() : "Text2Go Subscription")
                .build();
        details.add(cycleDetail);

        long planDays = TimeUtils.calculateDayCount(newPlan.getDuration(), newPlan.getDurationUnit());
        return date.plus(planDays, ChronoUnit.DAYS);
    }
    
    @Override
    public ComputeNewPlanPriceResultDTO computeNewPlanPrice(ComputeNewPlanPriceContext context) {
        log.info("Text2GoStripeSubscriptionStrategy.computeNewPlanPrice, context={}", JSON.toJSONString(context));

        // 1. 判断是否需要支付
        Boolean needPay = adjustNeedPayByComputePriceContext(context);

        // 2. 套餐金额
        BigDecimal planAmount = Optional.ofNullable(context.getNewPlan())
                .filter(plan -> !plan.isFree())
                .map(SubscriptionPlan::getPrice)
                .orElse(BigDecimal.ZERO);

        // 3. 折扣金额
        BigDecimal discountAmount = Optional.ofNullable(context.getFinalDiscountDTO())
                .filter(FinalDiscountDTO::getIsDiscount)
                .map(FinalDiscountDTO::getDiscountPrice)
                .orElse(null);

        // 4. 支付金额
        BigDecimal payAmount = Optional.ofNullable(discountAmount).orElse(planAmount);
        boolean finalNeedPay = needPay && payAmount.compareTo(BigDecimal.ZERO) > 0;
        BigDecimal finalPayAmount = finalNeedPay ? payAmount : BigDecimal.ZERO;

        ComputeNewPlanPriceResultDTO result = new ComputeNewPlanPriceResultDTO()
                .setNeedPay(finalNeedPay)
                .setPlanAmount(planAmount)
                .setDiscountAmount(discountAmount)
                .setDeductedAmountOfLastPlan(BigDecimal.ZERO)
                .setPayAmount(finalPayAmount);
        log.info("Text2GoStripeSubscriptionStrategy.computeNewPlanPrice, result={}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 使用ISO格式格式化日期（yyyy-MM-dd）
     */
    @Override
    protected String formatCycleDetailDate(LocalDate date) {
        return date.format(DateTimeFormatter.ISO_LOCAL_DATE);
    }

    /**
     * Add trial period cycle detail
     *
     * @param date current date
     * @param details list of cycle fee details
     * @param trialDays number of trial days
     * @return the date after trial period
     */
    private LocalDate addTrialPeriodCycleDetail(LocalDate date, List<CycleFeeDetail> details, Long trialDays) {
        CycleFeeDetail trialDetail = CycleFeeDetail.builder()
                .date(formatCycleDetailDate(date))
                .durationUnit(DurationUnit.DAY.name())
                .duration((long)trialDays.intValue())
                .cycleFee(BigDecimal.ZERO) // Trial period is free
                .feeDescription("Trial Period")
                .build();
        details.add(trialDetail);
        
        return date.plusDays(trialDays);
    }
}