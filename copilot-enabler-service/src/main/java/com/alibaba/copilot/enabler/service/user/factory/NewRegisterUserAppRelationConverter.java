package com.alibaba.copilot.enabler.service.user.factory;

import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.copilot.enabler.client.user.constants.UserAppBindStatusEnum;
import com.alibaba.copilot.enabler.client.user.request.UserRegisterRequest;
import com.alibaba.copilot.enabler.domain.user.model.UserAppRelation;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/5
 */
public class NewRegisterUserAppRelationConverter implements Converter<UserRegisterRequest, UserAppRelation> {
    public static final Converter<UserRegisterRequest, UserAppRelation> INSTANCE = new NewRegisterUserAppRelationConverter();

    @Override
    public UserAppRelation convertA2B(UserRegisterRequest userRegisterRequest) {
        if (userRegisterRequest == null) {
            return null;
        }

        return UserAppRelation
                .builder()
                .userId(userRegisterRequest.getUserId())
                .email(userRegisterRequest.getEmail())
                .appCode(userRegisterRequest.getAppCode())
                .appName(userRegisterRequest.getAppName())
                .appType(userRegisterRequest.getAppType())
                .bindStatus(UserAppBindStatusEnum.BINDING.value())
                .bindSource(userRegisterRequest.getBindSource())
                .build();
    }

    @Override
    public UserRegisterRequest convertB2A(UserAppRelation userAppRelation) {
        return null;
    }
}
