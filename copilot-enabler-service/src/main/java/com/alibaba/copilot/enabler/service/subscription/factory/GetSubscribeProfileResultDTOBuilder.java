package com.alibaba.copilot.enabler.service.subscription.factory;

import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.enabler.client.subscription.dto.GetSubscribeProfileResultDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.TrialDurationDTO;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2024/1/16
 */
@AllArgsConstructor
public class GetSubscribeProfileResultDTOBuilder implements Builder<GetSubscribeProfileResultDTO> {

    private final SubscriptionOrder effectOrder;
    private final SubscriptionPlan effectPlan;
    private final TrialDurationDTO trialDurationDTO;
    private final Boolean paid;
    private final boolean hasRefundRecordWithTodoStatus;

    @Override
    public GetSubscribeProfileResultDTO build() {
        Long includedTrialDay = 0L;
        if (effectOrder == null) {
            if (effectPlan != null) {
                includedTrialDay = effectPlan.getTrialDuration();
            }
        } else {
            if (effectOrder.getAttributes() != null && effectOrder.getAttributes().getTrialDays() != null) {
                includedTrialDay = effectOrder.getAttributes().getTrialDays();
            }
        }

        return new GetSubscribeProfileResultDTO()
                .setOrderId(effectOrder == null ? null : effectOrder.getId())
                .setPlanId(effectPlan == null ? null : effectPlan.getId())
                .setPlanName(effectPlan == null ? null : effectPlan.getName())
                .setIsTrial(trialDurationDTO.getIsTrial())
                .setIncludedTrialDay(includedTrialDay)
                .setIsAutoRenew(effectOrder != null && effectOrder.getAutoRenew())
                .setNextRenewTime(effectOrder == null ? null : effectOrder.getNextRenewalTime())
                .setPerformStartTime(effectOrder == null ? null : effectOrder.getPerformStartTime())
                .setPerformEndTime(effectOrder == null ? null : effectOrder.getPerformEndTime())
                .setPaid(paid)
                .setIsFreeOrder(effectOrder != null && effectOrder.getAttributes().isFreeOrder())
                .setHasRefundRecordWithTodoStatus(hasRefundRecordWithTodoStatus)
                .setSubscriptionPayType(effectOrder == null ? null : effectOrder.getSubscriptionPayType())
                .setActualFee(effectOrder == null ? new BigDecimal(0) : effectOrder.getActualFee())
                .setPlanDurationUnit(effectPlan == null ? null : effectPlan.getDurationUnit())
                .setPlanDuration(effectPlan == null ? null : effectPlan.getDuration());
    }
}
