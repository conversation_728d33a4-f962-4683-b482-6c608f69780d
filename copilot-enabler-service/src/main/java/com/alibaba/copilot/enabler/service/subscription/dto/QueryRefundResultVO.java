package com.alibaba.copilot.enabler.service.subscription.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/10/7
 */
@Data
@Accessors(chain = true)
public class QueryRefundResultVO implements Serializable {

    /**
     * 退款状态
     * SUCCESS: Indicates the refund succeeded.
     * PROCESSING: Indicates the refund is under processing.
     * FAIL: Indicates the refund failed.
     */
    private String refundStatus;
}
