package com.alibaba.copilot.enabler.service.subscription.strategy.basic.seo;

import cn.hutool.core.date.DateUtil;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.payment.dto.RefundResultDTO;
import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.client.subscription.dto.CycleFeeDetail;
import com.alibaba.copilot.enabler.client.subscription.dto.TrialDurationDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.base.utils.PaymentUtils;
import com.alibaba.copilot.enabler.domain.base.utils.TimeUtils;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.subscription.dto.FinalDiscountDTO;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.response.EmailResponse;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategyConfig;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeCycleFeeResultDTO;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeNewPlanPriceContext;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeNewPlanPriceResultDTO;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeTrialContext;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.CreateOrderAndTradeResultDTO;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.SubscribeContext;
import com.alibaba.copilot.enabler.infra.email.service.EmailService;
import com.alibaba.copilot.enabler.service.subscription.strategy.basic.BasicSubscriptionStrategy;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * SeoSite AlphaRank 独立博客网站应用的订阅处理策略
 *
 * <AUTHOR>
 * @version 2024/05/31
 */
@Slf4j
@Component
@SubscriptionStrategyConfig(value = AppEnum.SEO_COPILOT_SITE, payType = SubscriptionPayType.STRIPE)
public class SeoSiteStripeSubscriptionStrategy extends BasicSubscriptionStrategy {

    @Resource
    private EmailService emailService;

    @Override
    public TrialDurationDTO computeTrialDuration(ComputeTrialContext context) {
        String appCode = context.getAppCode();
        Long userId = context.getUserId();
        SubscriptionPlan targetPlan = context.getTargetPlan();
        List<SubscriptionOrder> historyOrders = context.getHistoryOrders();

        if (targetPlan == null) {
            // 未指定目标套餐时, 不拥有试用期 (对应场景: 没有免费套餐的应用中, 没有订阅过, 在订阅主页显示试用信息)
            return new TrialDurationDTO().setIsTrial(false);
        }

        if (!targetPlan.isFree() && !targetPlan.getIsHasTrial()) {
            // 不包含试用期的付费套餐
            log.info("SeoSiteSubscriptionStrategy computeTrialDuration, no trial plan");
            return new TrialDurationDTO().setIsTrial(false);
        }

        if (historyOrders == null) {
            // 外部未传时, 内部查询获取
            historyOrders = getHistoryOrders(userId, appCode);
        }

        // 计算历史订阅的累计的试用天数
        long triedDay = computeTotalTrialDayFromHistory(targetPlan, historyOrders);
        log.info("SeoSiteSubscriptionStrategy computeTrialDuration, compute history trial end, triedDay={}", triedDay);

        // 计算剩余试用期天数
        final long remainDay = computeRemainDay(targetPlan, triedDay);

        return new TrialDurationDTO()
                .setIsTrial(remainDay > 0L)
                .setRemainTrialDay(remainDay);
    }

    /**
     * 计算历史订阅的累计的试用天数
     */
    private long computeTotalTrialDayFromHistory(SubscriptionPlan targetPlan, List<SubscriptionOrder> historyOrders) {
        return historyOrders.stream()
                .filter(SubscriptionOrder::getIsIncludeTrial)
                .filter(order -> StringUtils.equals(order.getSubscriptionPlanName(), targetPlan.getName()))
                .map(order -> {
                    // 不考虑时分秒
                    // 2016-02-01 23:59:59订阅，2016-02-02 00:00:00取消，算一天
                    // 订单结束时间取值
                    Date orderEndTime = order.getPerformEndTime();
                    Date orderStartTime = order.getPerformStartTime();
                    Long trialDays = order.getAttributes().getTrialDays();
                    if (trialDays == null) {
                        return 0L;
                    }

                    Date trialEndTime = DateUtils.addDays(orderStartTime, Math.toIntExact(trialDays));
                    Date now = new Date();

                    Date finalEndDate = TimeUtils.min(orderEndTime, trialEndTime, now);
                    return DateUtil.betweenDay(orderStartTime, finalEndDate, true);
                })
                .reduce(0L, Long::sum);
    }

    /**
     * 计算剩余试用期天数
     */
    private long computeRemainDay(SubscriptionPlan targetPlan, long triedDay) {
        Long trialDuration = targetPlan.getTrialDuration();
        String trialDurationUnit = targetPlan.getTrialDurationUnit();
        long totalTrailDay = TimeUtils.calculateDayCount(trialDuration, trialDurationUnit);
        return totalTrailDay - triedDay;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateOrderAndTradeResultDTO createOrderAndTrade(SubscribeContext context) {
        // 创建新订单和支付流水
        return orderDomainService.createPreparePayOrder(context);
    }

    @Override
    public void handleRefundLogicWhenOldOrderCompleted(@Nonnull SubscriptionOrder oldEffectOrder) {
        log.info("SeoSiteSubscriptionStrategy handleRefundLogicWhenOldOrderCompleted, oldEffectOrder={}", JSON.toJSONString(oldEffectOrder));

        if (oldEffectOrder.currentIsInTrial()) {
            // 试用期切换, 退全款
            refundForTrialOrder(oldEffectOrder);
            return;
        }

        // 非试用期退款, 退部分款项
        TradeRecord payRecordForEffectOrder = tradeRecordRepository.queryByOrderId(oldEffectOrder.getId(), TradeDirection.FORWARD);
        log.info("SeoSiteSubscriptionStrategy handleRefundLogicWhenOldOrderCompleted, payRecordForEffectOrder={}", JSON.toJSONString(payRecordForEffectOrder));

        if (payRecordForEffectOrder == null) {
            // 之前没有支付的流水时, 不需要退款
            log.info("SeoSiteSubscriptionStrategy handleRefundLogicWhenOldOrderCompleted, no pay record found,oldOrderId:{}", oldEffectOrder.getId());
            return;
        }

        // 尝试创建退款流水
        TradeRecord refundRecord = tryCreateRefundRecord(oldEffectOrder, payRecordForEffectOrder);
        log.info("SeoSiteSubscriptionStrategy handleRefundLogicWhenOldOrderCompleted, createRefundRecord finished, result={}", JSON.toJSONString(refundRecord));
        if (refundRecord == null) {
            // 未创建退款流水时, 不需要退款
            return;
        }

        // 获取上一笔订单的支付外部交易号 (AEPay生成的)
        String paymentId = payRecordForEffectOrder.getOutTradeNo();
        // 执行退款操作
        SingleResult<RefundResultDTO> refundResult = doRefund(paymentId, refundRecord);
        log.info("SeoSiteSubscriptionStrategy handleRefundLogicWhenOldOrderCompleted, doRefund finished, result={}", JSON.toJSONString(refundResult));
    }

    @Override
    public ComputeCycleFeeResultDTO computeCycleFee(ComputeNewPlanPriceContext context) {
        List<CycleFeeDetail> cycleFeeDetails = new ArrayList<>();
        LocalDate selectedPlanInfoDate = LocalDate.now();

        // 1. 添加试用信息
        selectedPlanInfoDate = addTrialCycleDetail(selectedPlanInfoDate, cycleFeeDetails, context);

        // 2. 添加折扣信息
        selectedPlanInfoDate = addDiscountCycleDetail(selectedPlanInfoDate, cycleFeeDetails, context);

        // 3. 添加最终套餐信息
        selectedPlanInfoDate = addFinalPlanCycleDetail(selectedPlanInfoDate, cycleFeeDetails, context);

        // 4. 计算新套餐价格
//        ComputeNewPlanPriceResultDTO priceResult = computeNewPlanPrice(context);

        BigDecimal finalPayAmount = null;
        if (context.getNewPlan().getAttributes() != null && context.getNewPlan().getAttributes().containsKey("discountPrice")) {
            finalPayAmount = BigDecimal.valueOf(context.getNewPlan().getAttributes().getAsDouble("discountPrice"));
        }
        return new ComputeCycleFeeResultDTO()
                .setCycleFeeDetails(cycleFeeDetails)
                .setFinalPayAmount(finalPayAmount);
    }

    /**
     * 组装试用期信息
     */
    private LocalDate addTrialCycleDetail(LocalDate date, List<CycleFeeDetail> details, ComputeNewPlanPriceContext context) {
        TrialDurationDTO trialDurationDTO = computeTrialDuration(new ComputeTrialContext(
                context.getAppCode(),
                context.getUserId(),
                context.getNewPlan(),
                context.getHistoryOrders()
        ));

        if (!trialDurationDTO.getIsTrial()) {
            return date;
        }

        CycleFeeDetail cycleDetail = CycleFeeDetail.builder()
                .date(formatCycleDetailDate(date))
                .feeDescription("Free Trial")
                .cycleFee(BigDecimal.valueOf(0))
                .build();
        details.add(cycleDetail);
        return date.plus(trialDurationDTO.getRemainTrialDay(), ChronoUnit.DAYS);
    }

    /**
     * 组装折扣信息
     */
    private LocalDate addDiscountCycleDetail(LocalDate date, List<CycleFeeDetail> details, ComputeNewPlanPriceContext context) {
        FinalDiscountDTO finalDiscountDTO = context.getFinalDiscountDTO();
        SubscriptionPlan newPlan = context.getNewPlan();
        if (finalDiscountDTO == null || !finalDiscountDTO.getIsDiscount()) {
            return date;
        }

        CycleFeeDetail cycleDetail = CycleFeeDetail.builder()
                .date(formatCycleDetailDate(date))
                .durationUnit(finalDiscountDTO.getDiscountDurationUnit())
                .duration(finalDiscountDTO.getRemainDiscountDuration())
                .cycleFee(finalDiscountDTO.getDiscountPrice())
                .feeDescription(newPlan.getDescription())
                .discountDescription(String.valueOf((finalDiscountDTO.getDiscountInfoDTO().getDiscount()) / 10))
                .build();
        details.add(cycleDetail);

        long remainDiscountDays = TimeUtils.calculateDayCount(
                finalDiscountDTO.getRemainDiscountDuration(), finalDiscountDTO.getDiscountDurationUnit());
        return date.plus(remainDiscountDays, ChronoUnit.DAYS);
    }

    /**
     * 组装最终套餐信息
     */
    private LocalDate addFinalPlanCycleDetail(LocalDate date, List<CycleFeeDetail> details, ComputeNewPlanPriceContext context) {
        SubscriptionPlan newPlan = context.getNewPlan();
        BigDecimal discountPrice = null;
        if (newPlan.getAttributes() != null && newPlan.getAttributes().containsKey("discountPrice")) {
            discountPrice = BigDecimal.valueOf(newPlan.getAttributes().getAsDouble("discountPrice"));
        }
        CycleFeeDetail cycleDetail = CycleFeeDetail.builder()
                .date(formatCycleDetailDate(date))
                .durationUnit(newPlan.getDurationUnit().name())
                .duration(newPlan.getDuration())
                .cycleFee(discountPrice != null ? discountPrice : newPlan.getPrice())
                .feeDescription(newPlan.getDescription())
                .build();
        details.add(cycleDetail);

        long planDays = TimeUtils.calculateDayCount(newPlan.getDuration(), newPlan.getDurationUnit());
        return date.plus(planDays, ChronoUnit.DAYS);
    }

    /**
     * 尝试创建退款的交易流水
     */
    @Nullable
    private TradeRecord tryCreateRefundRecord(@Nonnull SubscriptionOrder oldEffectOrder,
                                              @Nonnull TradeRecord payRecordForEffectOrder) {

        Long userId = oldEffectOrder.getUserId();
        BigDecimal refundPrice = computeRefundPrice(oldEffectOrder, payRecordForEffectOrder);
        if (refundPrice.compareTo(BigDecimal.ZERO) <= 0) {
            // 退款金额计算非正数时, 不需要退款
            log.info("SeoSiteSubscriptionStrategy tryCreateRefundRecord, skip refund, refundPrice={}", refundPrice);
            return null;
        }

        String paymentMethod = payRecordForEffectOrder.getPaymentMethod();
        TradeRecord refundRecord = TradeRecord.builder()
                .userId(userId)
                .subscriptionOrderId(oldEffectOrder.getId())
                .appCode(oldEffectOrder.getAppCode())
                .tradeAmount(refundPrice)
                .tradeCurrency(AEPaymentConstants.CURRENCY_CODE)
                .tradeDirection(TradeDirection.REFUND)
                .status(TradeRecordStatus.TODO)
                .paymentMethod(paymentMethod)
                .taxAmount(null)
                .taxCurrency(null)
                .transactionAmount(null)
                .transactionCurrency(null)
                .tradeNo(PaymentUtils.generateTradeNo(userId))
                .deleted(false)
                .build();
        tradeRecordRepository.createOrUpdateTradeRecord(refundRecord);
        return refundRecord;
    }

    /**
     * 计算退款金额
     */
    private BigDecimal computeRefundPrice(@Nonnull SubscriptionOrder oldEffectOrder,
                                          @Nonnull TradeRecord payRecordForEffectOrder) {
        Long effectPlanId = oldEffectOrder.getSubscriptionPlanId();
        SubscriptionPlan effectPlan = subscriptionPlanRepository.queryByPlanId(effectPlanId, false);

        long usedTime = DateUtil.betweenDay(oldEffectOrder.getPerformStartTime(), new Date(), true);

        Long effcPlanDuration = effectPlan.getDuration();
        String effcPlanDurationUnit = effectPlan.getDurationUnit().name();

        if (DurationUnit.YEAR.name().equals(effcPlanDurationUnit)) {
            effcPlanDuration = effcPlanDuration * 365;
        } else if (DurationUnit.MONTH.name().equals(effcPlanDurationUnit)) {
            effcPlanDuration = effcPlanDuration * 30;
        }
        long remainTime = effcPlanDuration - usedTime;
        BigDecimal refundAmount = BigDecimal.valueOf(remainTime)
                .divide(BigDecimal.valueOf(effcPlanDuration), 10, RoundingMode.HALF_UP)
                .multiply(payRecordForEffectOrder.getTradeAmount())
                .setScale(2, RoundingMode.HALF_UP);
        log.info("SeoSiteSubscriptionStrategy computeRefundPrice, compute finished, refundAmount={}", refundAmount);
        return refundAmount;
    }

    @Override
    public EmailResponse sendEmail(Long userId, Object emailInfoObj) {
        log.info("sendEmail by seo, userId={}, emailInfoObj={}", userId, JSON.toJSONString(emailInfoObj));
        String appCode = getAppEnum().getCode();
        return emailService.sendEmail(appCode, userId, emailInfoObj);
    }

    @Override
    public ComputeNewPlanPriceResultDTO computeNewPlanPrice(ComputeNewPlanPriceContext context) {

        // 1. 判断是否需要支付
        Boolean needPay = adjustNeedPayByComputePriceContext(context);

        // 2. 套餐金额
        BigDecimal planAmount = Optional.ofNullable(context.getNewPlan())
                .filter(plan -> !plan.isFree())
                .map(SubscriptionPlan::getPrice)
                .orElse(BigDecimal.ZERO);

        // 3. 折扣金额
        BigDecimal discountAmount = null;
        if (context.getNewPlan().getAttributes() != null && context.getNewPlan().getAttributes().containsKey("discountPrice")) {
            discountAmount = BigDecimal.valueOf(context.getNewPlan().getAttributes().getAsDouble("discountPrice"));
        }

        // 4. 支付金额
        BigDecimal payAmount = Optional.ofNullable(discountAmount).orElse(planAmount);
        boolean finalNeedPay = needPay && payAmount.compareTo(BigDecimal.ZERO) > 0;
        BigDecimal finalPayAmount = finalNeedPay ? payAmount : BigDecimal.ZERO;

        ComputeNewPlanPriceResultDTO result = new ComputeNewPlanPriceResultDTO()
                .setNeedPay(finalNeedPay)
                .setPlanAmount(planAmount)
                .setDiscountAmount(discountAmount)
                .setDeductedAmountOfLastPlan(BigDecimal.ZERO)
                .setPayAmount(finalPayAmount);
        log.info("Seo SiteSubscriptionStrategy computeNewPlanPrice, result={}", JSON.toJSONString(result));
        return result;
    }
}
