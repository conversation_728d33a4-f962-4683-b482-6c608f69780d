package com.alibaba.copilot.enabler.service.user.factory;

import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.copilot.enabler.client.user.dto.UserOverviewInfoDTO;
import com.alibaba.copilot.enabler.service.user.dto.UserOverviewInfoVO;


/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/4
 */
public class UserSubscriptionInfoConverter implements Converter<UserOverviewInfoDTO, UserOverviewInfoVO> {

    public static final Converter<UserOverviewInfoDTO, UserOverviewInfoVO> INSTANCE = new UserSubscriptionInfoConverter();

    @Override
    public UserOverviewInfoVO convertA2B(UserOverviewInfoDTO userOverviewInfoDTO) {
        if (userOverviewInfoDTO == null) {
            return null;
        }

        UserOverviewInfoVO subscriptionInfoVO = UserOverviewInfoVO.builder()
                .userId(userOverviewInfoDTO.getUserId())
                .email(userOverviewInfoDTO.getEmail())
                .build();

        if (userOverviewInfoDTO.getShareInfo() == null) {
            return subscriptionInfoVO;
        }

        UserOverviewInfoVO.ShareInfo shareInfo = new UserOverviewInfoVO.ShareInfo();
        shareInfo.setUrl(userOverviewInfoDTO.getShareInfo().getUrl());
        shareInfo.setShareText(userOverviewInfoDTO.getShareInfo().getShareText());
        shareInfo.setConverted(userOverviewInfoDTO.getShareInfo().getConverted());
        shareInfo.setRegistered(userOverviewInfoDTO.getShareInfo().getRegistered());
        subscriptionInfoVO.setShareInfo(shareInfo);

        return subscriptionInfoVO;
    }

    @Override
    public UserOverviewInfoDTO convertB2A(UserOverviewInfoVO userOverviewInfoVO) {
        return null;
    }
}
