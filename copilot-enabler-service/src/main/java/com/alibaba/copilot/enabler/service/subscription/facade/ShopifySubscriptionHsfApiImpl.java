package com.alibaba.copilot.enabler.service.subscription.facade;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.constant.SeoFeatureType;
import com.alibaba.copilot.enabler.client.subscription.dto.*;
import com.alibaba.copilot.enabler.client.subscription.facade.ShopifySubscriptionHsfApi;
import com.alibaba.copilot.enabler.client.subscription.service.ShopifySubscriptionService;
import com.alibaba.copilot.enabler.infra.base.utils.ModelConvertUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2023/10/30
 */
@Slf4j
@Service
@HSFProvider(serviceInterface = ShopifySubscriptionHsfApi.class)
public class ShopifySubscriptionHsfApiImpl implements ShopifySubscriptionHsfApi {

    @Resource
    private ShopifySubscriptionService shopifySubscriptionService;

    @Override
    public SingleResult<ShopifySubscribePlanResultDTO> subscribePlan(ShopifySubscribePlanDTO dto) {
        log.info("subscribePlan, dto={}", JSON.toJSONString(dto));
        return ModelConvertUtils.wrapResult(() -> shopifySubscriptionService.subscribePlan(dto));
    }

    @Override
    public SingleResult<ShopifySubscribablePlansResultDTO> getSubscribablePlans(ShopifySubscribablePlansQueryDTO dto) {
        log.info("getSubscribablePlans, dto={}", JSON.toJSONString(dto));
        return ModelConvertUtils.wrapResult(() -> shopifySubscriptionService.getSubscribablePlans(dto));
    }

    @Override
    public SingleResult<ShopifySubscribedPlanResultDTO> getSubscribedPlan(ShopifySubscribedPlanQueryDTO dto) {
        log.info("getSubscribedPlan, dto={}", JSON.toJSONString(dto));
        return ModelConvertUtils.wrapResult(() -> shopifySubscriptionService.getSubscribedPlan(dto));
    }

    @Override
    public SingleResult<ShopifyFeatureDTO> getFeature(ShopifyFeatureQuery dto) {
        log.info("getFeature, dto={}", JSON.toJSONString(dto));
        return ModelConvertUtils.wrapResult(() -> shopifySubscriptionService.getFeature(dto));
    }

    @Override
    public SingleResult<ShopifyFeatureAllDTO> getFeatureAll(ShopifyFeatureQuery dto) {
        log.info("getFeatureAll, dto={}", JSON.toJSONString(dto));
        return ModelConvertUtils.wrapResult(() -> shopifySubscriptionService.getFeatureAll(dto));
    }

    @Override
    public SingleResult<Boolean> incFeatureUsage(ShopifyFeatureQuery dto) {
        log.info("incFeatureUsage, dto={}", JSON.toJSONString(dto));
        return ModelConvertUtils.wrapResult(() -> shopifySubscriptionService.incFeatureUsage(dto));
    }

    @Override
    public SingleResult<Boolean> rollbackFeatureUsage(ShopifyFeatureQuery dto) {
        log.info("incFeatureUsage, dto={}", JSON.toJSONString(dto));
        return ModelConvertUtils.wrapResult(() -> shopifySubscriptionService.rollbackFeatureUsage(dto));
    }


    @Override
    public SingleResult<ShopifyPlanFeatureDTO> getFeatureMapping(ShopifyFeatureQuery shopifyFeatureQuery) {
        log.info("getFeatureMapping, query={}", JSON.toJSONString(shopifyFeatureQuery));
        return ModelConvertUtils.wrapResult(() -> shopifySubscriptionService.getFeatureMapping(shopifyFeatureQuery));
    }

    @Override
    public SingleResult<ShopifyPlanFeatureDTO> getFeatureMappingNextPlan(ShopifyFeatureQuery shopifyFeatureQuery) {
        log.info("getFeatureMapping, query={}", JSON.toJSONString(shopifyFeatureQuery));
        return ModelConvertUtils.wrapResult(() -> shopifySubscriptionService.getFeatureMappingNextPlan(shopifyFeatureQuery));
    }

    @Override
    public SingleResult<ShopifySubscriptionDTO> getShopifySubscriptionDTO(ShopifySubscribablePlanQueryDTO shopifySubscribablePlanQueryDTO) {
        return ModelConvertUtils.wrapResult(() -> shopifySubscriptionService.getShopifySubscriptionDTO(shopifySubscribablePlanQueryDTO));
    }
}
