package com.alibaba.copilot.enabler.service.stripe;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
public class StripeConsts {

    // =========== metaq ===================

    public static final String METAQ_TOPIC = "enabler_inner_event";
    public static final String METAQ_TAG = "stripe_webhook";


    // ============ stripe webhook type ============

    public static final String CHECKOUT_SESSION_COMPLETED = "checkout.session.completed";
    public static final String CHECKOUT_SESSION_EXPIRED = "checkout.session.expired";

    public static final String PAYMENT_INTENT_SUCCEEDED = "payment_intent.succeeded";
    public static final String PAYMENT_INTENT_PAYMENT_FAILED = "payment_intent.payment_failed";
    public static final String PAYMENT_INTENT_CANCELED = "payment_intent.canceled";

    public static final String INVOICE_PAID = "invoice.paid";
    public static final String INVOICE_PAYMENT_FAILED = "invoice.payment_failed";
    public static final String INVOICE_UPDATED = "invoice.updated";

    public static final String CHARGE_DISPUTE_CREATED = "charge.dispute.created";
    public static final String CHARGE_DISPUTE_CLOSED = "charge.dispute.closed";

    public static final String CHARGE_REFUNDED = "charge.refunded";
    public static final String REFUND_UPDATED = "refund.updated";
    public static final String REFUND_FAILED = "refund.failed";

    public static final String SUBSCRIPTION_DELETED = "customer.subscription.deleted";
    public static final String SUBSCRIPTION_UPDATED = "customer.subscription.updated";



    public static List<String> CHECKOUT_SESSION_TYPES = Lists.newArrayList(CHECKOUT_SESSION_COMPLETED, CHECKOUT_SESSION_EXPIRED);

    public static List<String> PAYMENT_INTENT_TYPES = Lists.newArrayList(
            PAYMENT_INTENT_SUCCEEDED,
            PAYMENT_INTENT_PAYMENT_FAILED,
            PAYMENT_INTENT_CANCELED
    );

    public static List<String> REFUND_TYPES = Lists.newArrayList(
            CHARGE_REFUNDED,
            REFUND_FAILED
    );

    public static List<String> INVOICE_TYPE = Lists.newArrayList(
            INVOICE_PAID, INVOICE_PAYMENT_FAILED, INVOICE_UPDATED
    );

    public static List<String> SUB_TYPE = Lists.newArrayList(
            SUBSCRIPTION_DELETED,
            SUBSCRIPTION_UPDATED

    );

    public static List<String> CHARGE_DISPUTE_TYPES = Lists.newArrayList(CHARGE_DISPUTE_CREATED, CHARGE_DISPUTE_CLOSED);
}