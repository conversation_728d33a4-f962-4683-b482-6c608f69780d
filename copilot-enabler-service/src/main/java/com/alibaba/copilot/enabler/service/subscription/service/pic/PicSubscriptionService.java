package com.alibaba.copilot.enabler.service.subscription.service.pic;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.monitor.annotation.MonitorResult;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanResultDTO;
import com.alibaba.securitysdk.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/07
 */
@Service
@Slf4j
public class PicSubscriptionService {

    @Resource
    private PicStripeSubscriptionService picStripeSubscriptionService;


    @Monitor(name = "[Pic] 订阅-Stripe", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    @MonitorResult
    public SingleResult<SubscribePlanResultDTO> subscribePlanByStripe(SubscribePlanDTO subscribePlanDTO) {
        try {
            SubscribePlanResultDTO subscribePlanResultDTO = picStripeSubscriptionService.subscribePlanByStripe(subscribePlanDTO);
            return SingleResult.buildSuccess(subscribePlanResultDTO);
        } catch (Exception e) {
            log.error("pic subscribePlanByStripe error, input={}", JSON.toJSONString(subscribePlanDTO), e);
            return SingleResult.buildFailure(e.getMessage());
        }

    }

}