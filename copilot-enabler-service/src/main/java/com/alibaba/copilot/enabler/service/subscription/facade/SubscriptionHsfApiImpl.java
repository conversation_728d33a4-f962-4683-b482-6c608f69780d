package com.alibaba.copilot.enabler.service.subscription.facade;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.robot.dto.TradeRecordDTO;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.dto.*;
import com.alibaba.copilot.enabler.client.subscription.facade.SubscriptionHsfApi;
import com.alibaba.copilot.enabler.client.subscription.request.*;
import com.alibaba.copilot.enabler.client.subscription.service.SubscriptionManageService;
import com.alibaba.copilot.enabler.client.subscription.service.SubscriptionQueryService;
import com.alibaba.copilot.enabler.client.subscription.service.SubscriptionService;
import com.alibaba.copilot.enabler.domain.payment.service.OrderDomainService;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.infra.base.utils.ModelConvertUtils;
import com.alibaba.copilot.enabler.service.stripe.StripeService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.Invoice;
import com.stripe.model.Subscription;
import com.stripe.model.SubscriptionCollection;
import com.stripe.model.billingportal.Configuration;
import com.stripe.model.billingportal.Session;
import com.stripe.param.InvoiceListParams;
import com.stripe.param.SubscriptionListParams;
import com.stripe.param.SubscriptionUpdateParams;
import com.stripe.param.billingportal.ConfigurationCreateParams;
import com.stripe.param.billingportal.SessionCreateParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@HSFProvider(serviceInterface = SubscriptionHsfApi.class)
public class SubscriptionHsfApiImpl implements SubscriptionHsfApi {

    @Autowired
    private SubscriptionQueryService subscriptionQueryService;

    @Autowired
    private SubscriptionManageService subscriptionManageService;

    @Autowired
    private SubscriptionService subscriptionService;

    @Autowired
    private OrderDomainService orderDomainService;
    @Autowired
    private StripeService stripeService;
    @Resource
    private SubscriptionOrderRepository orderRepository;

    @Override
    public SingleResult<List<SubscribedAppDTO>> getSubscribedAppList(SubscribedAppInfoQuery query) {
        log.info("getSubscribedAppList userId:{}", query.getUserId());
        List<SubscribedAppDTO> subscribedAppList = subscriptionQueryService.getSubscribedAppList(query);
        return SingleResult.buildSuccess(subscribedAppList);
    }

    @Override
    public SingleResult<List<SubscribablePlanDTO>> getSubscribablePlanList(SubscribablePlanQuery query) {
        log.info("getSubscribablePlanList SubscribablePlanQuery:{}", JSON.toJSONString(query));
        List<SubscribablePlanDTO> subscribablePlanList = subscriptionQueryService.getSubscribablePlanList(query);
        return SingleResult.buildSuccess(subscribablePlanList);
    }

    @SuppressWarnings("unchecked")
    @Override
    public SingleResult<SelectedPlanInfoDTO> getSelectedPlanInfo(SelectedPlanInfoQuery query) {
        log.info("getSelectedPlanInfo SelectedPlanInfoQuery:{}", JSON.toJSONString(query));
        try {
            SelectedPlanInfoDTO selectedPlanInfo = subscriptionQueryService.getSelectedPlanInfo(query);
            return SingleResult.buildSuccess(selectedPlanInfo);
        } catch (Exception e) {
            log.error("getSelectedPlanInfo error, SelectedPlanInfoQuery:{}", JSON.toJSONString(query), e);
            return ModelConvertUtils.wrapExceptionResult(e);
        }
    }

    @Override
    public SingleResult<SubscribePlanResultDTO> subscribePlan(SubscribePlanDTO subscribePlanDTO) {
        log.info("subscribePlan subscribePlanDTO:{}", JSON.toJSONString(subscribePlanDTO));
        try {
            return subscriptionManageService.subscribePlan(subscribePlanDTO);
        } catch (Exception e) {
            log.error("subscribePlan error, subscribePlanDTO:{}", JSON.toJSONString(subscribePlanDTO), e);
            return ModelConvertUtils.wrapExceptionResult(e);
        }
    }

    @Override
    public SingleResult<SubscriptionPreviewResultDTO> previewSubscribe(SubscribePlanDTO subscribePlanDTO) {
        log.info("subscribePlan subscribePlanDTO:{}", JSON.toJSONString(subscribePlanDTO));
        try {
            return subscriptionManageService.previewSubscribe(subscribePlanDTO);
        } catch (Exception e) {
            log.error("subscribePlan error, subscribePlanDTO:{}", JSON.toJSONString(subscribePlanDTO), e);
            return ModelConvertUtils.wrapExceptionResult(e);
        }
    }

    @Override
    public SingleResult<Boolean> cancelSubscribedPlan(CancelSubscribedPlanRequest request, Long userId) {
        try {
            log.info("cancelSubscribedPlan request:{}, userId:{}", JSON.toJSONString(request), userId);
            Boolean result = subscriptionManageService.cancelSubscribedPlan(request, userId);
            return SingleResult.buildSuccess(result);
        } catch (Exception e) {
            log.error("cancelSubscribedPlan error, request:{}, userId={}", JSON.toJSONString(request), userId, e);
            return SingleResult.buildFailure(e.getMessage());
        }
    }

    @Override
    public SingleResult<Boolean> cancelAutoRenew(CancelAutoRenewDTO dto) {
        log.info("cancelAutoRenew, dto={}", JSON.toJSONString(dto));
        Boolean result = orderDomainService.cancelAutoRenewForCurrentEffectOrder(dto.getAppCode(), dto.getUserId());
        return SingleResult.buildSuccess(result);
    }

    @Override
    public SingleResult<TrialDurationDTO> getTrialDurationInfoByPayTradeNo(String payTradeNo) {
        log.info("getTrialDurationInfoByPayTradeNo, payTradeNo={}", payTradeNo);
        TrialDurationDTO trialDurationDTO = subscriptionQueryService.getTrialDurationInfoByPayTradeNo(payTradeNo);
        return SingleResult.buildSuccess(trialDurationDTO);
    }

    @Override
    public SingleResult<Boolean> needPayToSubscribePlan(NeedPayToSubscribePlanDTO dto) {
        log.info("needPayToSubscribePlan, dto={}", JSON.toJSONString(dto));
        boolean result = subscriptionQueryService.needPayToSubscribePlan(dto);
        return SingleResult.buildSuccess(result);
    }

    @Override
    public SingleResult<GetSubscribeProfileResultDTO> getSubscribeProfile(GetSubscribeProfileDTO dto) {
        log.info("getSubscribeProfile, dto={}", JSON.toJSONString(dto));
        GetSubscribeProfileResultDTO result = subscriptionQueryService.getSubscribeProfile(dto);
        return SingleResult.buildSuccess(result);
    }

    /**
     * 根据订单ID查询订单信息
     *
     * @param userId
     * @param id
     * @return
     */
    public SingleResult<SubscriptionOrderDTO> queryOrderById(Long userId, Long id) {
        log.info("queryOrderById, userId={}, id={}", userId, id);
        SubscriptionOrderDTO order = subscriptionQueryService.queryOrderById(userId, id);
        return SingleResult.buildSuccess(order);
    }

    /**
     * 根据外部订阅ID查询订单信息
     *
     * @param userId
     * @param outerSubscriptionId
     * @return
     */
    public SingleResult<SubscriptionOrderDTO> queryOrderByOuterSubscriptionId(Long userId, String outerSubscriptionId) {
        log.info("queryOrderByUserIds, userId={}, outerSubscriptionId={}", userId, outerSubscriptionId);
        SubscriptionOrderDTO order = subscriptionQueryService.queryOrderByOuterSubscriptionId(userId, outerSubscriptionId);
        return SingleResult.buildSuccess(order);
    }

    @Override
    public SingleResult<Map<Long, SubscriptionOrderDTO>> queryOrderByUserIds(List<Long> userIds, List<SubscriptionOrderStatus> statusList) {
        log.info("queryOrderByUserIds, userIds={}, statusList={}", JSON.toJSONString(userIds), JSON.toJSONString(statusList));
        Map<Long, SubscriptionOrderDTO> userId2Order = subscriptionQueryService.queryOrderByUserIds(userIds, statusList);
        return SingleResult.buildSuccess(userId2Order);
    }

    /**
     * 根据用户ID查询订单信息
     *
     * @param userIds    用户ID
     * @param statusList 订单状态
     * @return 订单信息
     */
    @Override
    public SingleResult<Map<Long, List<SubscriptionOrderDTO>>> queryUserId2OrderByUserIds(String appCode, List<Long> userIds, List<SubscriptionOrderStatus> statusList) {
        log.info("queryUserId2OrderByUserIds, userIds={}, statusList={}", JSON.toJSONString(userIds), JSON.toJSONString(statusList));
        Map<Long, List<SubscriptionOrderDTO>> userId2Order = subscriptionQueryService.queryUserId2OrderByUserIds(appCode, userIds, statusList);
        return SingleResult.buildSuccess(userId2Order);
    }

    @Override
    public SingleResult<SubscriptionOrderResult> querySubscribeOrder(SubscriptionOrderQueryDTO subscriptionOrderQueryDTO) {
        return SingleResult.buildSuccess(subscriptionQueryService.querySubscribeOrder(subscriptionOrderQueryDTO));
    }

    @Override
    public SingleResult<Void> createFreeOrder(CreateFreeOrderDTO dto) {
        log.info("createFreeOrder, dto={}", JSON.toJSONString(dto));
        try {
            orderDomainService.createFreeOrder(dto);
            return SingleResult.buildSuccess(null);
        } catch (Exception e) {
            return ModelConvertUtils.wrapExceptionResult(e);
        }
    }

    /**
     * 创建订单
     *
     * @param subscriptionOrderDTO
     * @return
     */
    @Override
    public SingleResult<Long> createSubscriptionOrder(SubscriptionOrderDTO subscriptionOrderDTO) {
        log.info("SubscriptionHsfApiImpl.createSubscriptionOrder, subscriptionOrderDTO={}", JSON.toJSONString(subscriptionOrderDTO));
        try {
            return SingleResult.buildSuccess(subscriptionService.createSubscriptionOrder(subscriptionOrderDTO));
        } catch (Exception e) {
            log.error("SubscriptionHsfApiImpl.createSubscriptionOrder exception, subscriptionOrderDTO={}", JSON.toJSONString(subscriptionOrderDTO), e);
            return ModelConvertUtils.wrapExceptionResult(e);
        }
    }

    /**
     * 更新订单
     *
     * @param subscriptionOrderDTO
     * @return
     */
    @Override
    public SingleResult<Boolean> updateSubscriptionOrder(SubscriptionOrderDTO subscriptionOrderDTO) {
        log.info("SubscriptionHsfApiImpl.updateSubscriptionOrder, subscriptionOrderDTO={}", JSON.toJSONString(subscriptionOrderDTO));
        try {
            return SingleResult.buildSuccess(subscriptionService.updateSubscriptionOrder(subscriptionOrderDTO));
        } catch (Exception e) {
            log.error("SubscriptionHsfApiImpl.updateSubscriptionOrder exception, subscriptionOrderDTO={}", JSON.toJSONString(subscriptionOrderDTO), e);
            return ModelConvertUtils.wrapExceptionResult(e);
        }
    }

    /**
     * 处理订阅回调
     *
     * @param webSubscriptionEventDTO
     * @return
     */
    @Override
    public SingleResult<Boolean> handleSubscriptionEvent(WebSubscriptionEventDTO webSubscriptionEventDTO) {
        log.info("SubscriptionHsfApiImpl.handleSubscriptionsUpdate, subscriptionsUpdateDTO={}", JSON.toJSONString(webSubscriptionEventDTO));

        try {
            return SingleResult.buildSuccess(subscriptionManageService.handleSubscriptionsUpdate(webSubscriptionEventDTO));
        } catch (Exception e) {
            log.error("SubscriptionHsfApiImpl.handleSubscriptionsUpdate exception, subscriptionsUpdateDTO={}", JSONObject.toJSONString(webSubscriptionEventDTO), e);
            return SingleResult.buildFailure(e.getMessage());
        }
    }

    /**
     * 更新订单
     *
     * @param subscriptionOrderDTO
     * @return
     */
    @Override
    public SingleResult<Boolean> switchSubscriptionOrder(SubscriptionOrderDTO subscriptionOrderDTO) {
        log.info("SubscriptionHsfApiImpl.switchSubscriptionOrder, subscriptionOrderDTO={}", JSON.toJSONString(subscriptionOrderDTO));
        try {
            return SingleResult.buildSuccess(subscriptionManageService.switchSubscriptionOrder(subscriptionOrderDTO));
        } catch (Exception e) {
            log.error("SubscriptionHsfApiImpl.switchSubscriptionOrder exception, subscriptionOrderDTO={}", JSON.toJSONString(subscriptionOrderDTO), e);
            return ModelConvertUtils.wrapExceptionResult(e);
        }
    }

    /**
     * 创建流水
     *
     * @param tradeRecordDTO
     * @return
     */
    @Override
    public SingleResult<Long> createTradeRecord(TradeRecordDTO tradeRecordDTO) {
        log.info("SubscriptionHsfApiImpl.createTradeRecord, tradeRecordDTO={}", JSON.toJSONString(tradeRecordDTO));
        try {
            return SingleResult.buildSuccess(subscriptionService.createTradeRecord(tradeRecordDTO));
        } catch (Exception e) {
            log.error("SubscriptionHsfApiImpl.createTradeRecord exception, tradeRecordDTO={}", JSON.toJSONString(tradeRecordDTO), e);
            return ModelConvertUtils.wrapExceptionResult(e);
        }
    }

    @Override
    public SingleResult<InvoiceResult> queryInvoice(InvoiceQuery invoiceQuery) {
        log.info("SubscriptionHsfApiImpl.queryInvoice, invoiceQuery={}", JSON.toJSONString(invoiceQuery));
        try {
            return SingleResult.buildSuccess(subscriptionQueryService.queryInvoice(invoiceQuery));
        } catch (Exception e) {
            log.error("SubscriptionHsfApiImpl.queryInvoice exception, tradeRecordDTO={}", JSON.toJSONString(invoiceQuery), e);
            return ModelConvertUtils.wrapExceptionResult(e);
        }
    }

    @Override
    public SingleResult<StripePortalResult> createPortalSession(StripePortalQuery stripePortalQuery) {
        String stripeCustomerId = stripeService.getOrCreateStripeCustomer(stripePortalQuery.getUserId());

        try {
           Stripe.apiKey = SwitchConfig.stripeApiKey;
            SubscriptionCollection subscriptionCollection = Subscription.list(SubscriptionListParams.builder()
                    .setCustomer(stripeCustomerId) // 用户的 Stripe customer id
                    .setStatus(SubscriptionListParams.Status.TRIALING)
                    .setLimit(1L)
                    .build());
            String config = SwitchConfig.stripeBpcId;
            if (CollectionUtils.isNotEmpty(subscriptionCollection.getData())) {
                config = SwitchConfig.stripeBpcCancelNowId;
            }

            SessionCreateParams params =
                   SessionCreateParams.builder()
                           .setCustomer(stripeCustomerId)
                           .setConfiguration(config)
                           .setReturnUrl(stripePortalQuery.getReturnUrl())
                           .build();
           Session session = Session.create(params);
           StripePortalResult stripePortalResult = new StripePortalResult();
           stripePortalResult.setUrl(session.getUrl());
           return SingleResult.buildSuccess(stripePortalResult);
        } catch (Exception e) {
           log.error("createPortalSession error", e);
           return SingleResult.buildFailure(e.getMessage());
        }
    }

    @Override
    public SingleResult<CheckoutSessionResult> queryCheckoutSession(String sessionId) {
        try {
            Stripe.apiKey = SwitchConfig.stripeApiKey;
            com.stripe.model.checkout.Session checkoutSession = com.stripe.model.checkout.Session.retrieve(sessionId);
            CheckoutSessionResult checkoutSessionResult = new CheckoutSessionResult();
            checkoutSessionResult.setId(sessionId);
            checkoutSessionResult.setStatus(checkoutSession.getStatus());
            return SingleResult.buildSuccess(checkoutSessionResult);
        } catch (Exception e) {
            log.error("createPortalSession error", e);
            return SingleResult.buildFailure(e.getMessage());
        }
    }

    @Override
    public SingleResult<Boolean> updateBillingCycle(Long orderId) {
        Stripe.apiKey = SwitchConfig.stripeApiKey;
        SubscriptionOrder order = orderRepository.getByOrderId(orderId);

        SubscriptionUpdateParams params = SubscriptionUpdateParams.builder()
                .setBillingCycleAnchor(SubscriptionUpdateParams.BillingCycleAnchor.NOW) // 设置为当前时间
                .setProrationBehavior(SubscriptionUpdateParams.ProrationBehavior.NONE) // 按比例调整费用
                .build();

        try {
            Subscription updatedSubscription = Subscription.retrieve(order.getAttributes().getStripeSubscriptionId()).update(params);
            Invoice invoice = Invoice.list(
                    InvoiceListParams.builder()
                            .setSubscription(updatedSubscription.getId())
                            .setLimit(1L) // 获取最新发票
                            .build()
            ).getData().get(0);
            log.info("updateBillingCycle invoice {}", JSON.toJSONString(invoice));
            if (invoice != null && !"paid".equals(invoice.getStatus())) {
                Invoice paidInvoice = invoice.pay(); // 尝试立即支付
                log.info("updateBillingCycle Invoice paid successfully: {}", paidInvoice.getId());
            }
            return SingleResult.buildSuccess(true);
        } catch (StripeException e) {
            log.error("updateBillingCycle", e);
            return SingleResult.buildFailure(e.getMessage());
        }
    }
}
