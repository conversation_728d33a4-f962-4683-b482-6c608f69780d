package com.alibaba.copilot.enabler.service.base.interceptor;

import com.alibaba.ttl.TransmittableThreadLocal;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/31
 */
public class CopilotContext {
    private static final String USER_ID = "userId";

    private static final ThreadLocal<Map<String, Object>> LOGIN_USER_INFO = new TransmittableThreadLocal<Map<String, Object>>();

    static {
        LOGIN_USER_INFO.set(new HashMap<>());
    }

    protected static void setUserId(Long userId) {
        put(USER_ID, userId);
    }

    /**
     * get user id
     *
     * @return userId
     */
    public static long getUserId() {
        Long userId = (Long) get(USER_ID);
        if (userId == null) {
            return 0L;
        }
        return userId;
    }

    /**
     * check if user has logged in
     *
     * @return logged in or not
     */
    public static boolean hasLogin() {
        return getUserId() > 0;
    }

    private static void put(String key, Object value) {
        Map<String, Object> map = LOGIN_USER_INFO.get();
        if (map == null) {
            map = new HashMap<>(4);
            LOGIN_USER_INFO.set(map);
        }
        map.put(key, value);
    }

    private static Object get(String key) {
        Map<String, Object> map = LOGIN_USER_INFO.get();
        if (map == null) {
            return null;
        }
        return LOGIN_USER_INFO.get().get(key);
    }

    public static void remove() {
        LOGIN_USER_INFO.remove();
    }
}
