package com.alibaba.copilot.enabler.service.stripe.processor.invoice;

import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.payment.dto.OrderDTO;
import com.alibaba.copilot.enabler.client.payment.dto.PaymentResultEventDTO;
import com.alibaba.copilot.enabler.client.payment.request.TradeRecordsQuery;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.base.utils.PaymentUtils;
import com.alibaba.copilot.enabler.domain.payment.gateway.geoip.GeoIpGateway;
import com.alibaba.copilot.enabler.domain.payment.gateway.geoip.dto.GetAddressInfoByIpResponse;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEvent;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEventMetadata;
import com.alibaba.copilot.enabler.domain.payment.model.*;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.service.stripe.StripeConsts;
import com.alibaba.copilot.enabler.service.stripe.processor.AbstractStripeEventProcessor;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeExecuteContext;
import com.alibaba.copilot.enabler.service.subscription.constant.SubscriptionConstants;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.alibaba.fastjson.JSONObject;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.*;
import com.stripe.param.ChargeUpdateParams;
import com.stripe.param.SubscriptionScheduleCreateParams;
import com.stripe.param.SubscriptionScheduleUpdateParams;
import com.stripe.param.SubscriptionUpdateParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.*;

@Slf4j
@Component
public class InvoicePaidProcessor4T2g extends AbstractStripeEventProcessor {
    @Resource
    private GeoIpGateway geoIpGateway;

    @Override
    protected void doProcess(StripeExecuteContext context) throws Exception {
        log.info("InvoicePaidProcessor4T2g context={}", JSON.toJSONString(context));
        StripeEventMetadata metaData = context.getMetaData();
        StripeEvent event = context.getEvent();
        String amountTotal = event.fetch("total");

        // 修改订单状态为激活
        Stripe.apiKey = SwitchConfig.stripeApiKey;
        Subscription subscription = Subscription.retrieve(event.fetch("subscription"));
        if (subscription == null) {
            log.error("com.alibaba.copilot.enabler.service.stripe.processor.invoice.InvocePaidProcessor4AT2g.doProcess subscription is null");
            return;
        }
        Map<String, String> metadata = subscription.getMetadata();
        Long subscriptionOrderId = Long.valueOf(metadata.get(SubscriptionConstants.META_SUB_ID));

        SubscriptionOrder subscriptionOrder = subscriptionOrderRepository.getByOrderId(subscriptionOrderId);
        if ("active".equals(subscription.getStatus())) {
            subscriptionOrder.setStatus(SubscriptionOrderStatus.IN_EFFECT);
            subscriptionOrder.setGmtModified(new Date());
            subscriptionOrder.setPerformStartTime(new Date(subscription.getStartDate() * 1000L));
            subscriptionOrder.setPerformEndTime(new Date(subscription.getCurrentPeriodEnd() * 1000L));
            subscriptionOrder.setOuterSubscriptionId(subscription.getId());
            subscriptionOrder.getAttributes().setStripeSubscriptionId(subscription.getId());
            subscriptionOrderRepository.saveSubscriptionOrder(subscriptionOrder);
        }

        String paymentIntent = event.fetch("payment_intent");
        String chargeId = event.fetch("charge");
        TradeRecordAttributes attributes = new TradeRecordAttributes(null);
        String tradeNo = PaymentUtils.generateTradeNo(subscriptionOrder.getUserId());
        Date tradeTime = new Date();
        if (StringUtils.isNotBlank(chargeId)) {
            try {
                Charge charge = Charge.retrieve(chargeId);
                tradeTime = new Date(charge.getCreated() * 1000);
                BillingDetail billingDetail = buildBillingDetail(charge);
                if (billingDetail != null) {
                    attributes.setBillingDetail(billingDetail);
                }
                PaymentMethodDetail paymentMethodDetail = buildPaymentMethodDetail(charge);
                if (paymentMethodDetail != null) {
                    attributes.setPaymentMethodDetail(paymentMethodDetail);
                }
                tradeTime = new Date(charge.getCreated() * 1000);
                Map<String, String> chargeMetadata = new HashMap<>(metadata);
                chargeMetadata.put(SubscriptionConstants.META_TRADE_NO, tradeNo);
                charge.update(ChargeUpdateParams.builder().setMetadata(chargeMetadata).build());
            } catch (Exception e) {
                log.error("InvoicePaidProcessor4T2g error", e);
            }
        }
        BigDecimal tradeAmount = new BigDecimal(amountTotal).divide(new BigDecimal("100"), 2, RoundingMode.HALF_DOWN).abs();
        TradeRecord tradeRecord = insertTradeRecord(subscriptionOrder, subscription, tradeTime, tradeAmount, event, subscriptionOrderId, paymentIntent, attributes, tradeNo);

        // 发送支付成功结果通知
        PaymentResultEventDTO paymentResultEventDTO = buildPaymentResultNotifyDTO(tradeRecord);
        Long currentPeriodEnd = subscription.getCurrentPeriodEnd();
        Long currentPeriodStart = subscription.getCurrentPeriodStart();
        paymentResultEventDTO.setSubscriptionPeriodEnd(currentPeriodEnd);
        paymentResultEventDTO.setSubscriptionPeriodStart(currentPeriodStart);
        paymentResultEventDTO.setBillingReason(event.fetch("billing_reason"));
        OrderDTO order = new OrderDTO();
        order.setReferenceOrderId(String.valueOf(subscriptionOrderId));
        order.setOrderAmount(tradeAmount);
        paymentResultEventDTO.setOrder(order);
        domainEventJsonProducer.publish(paymentResultEventDTO);

        if ("subscription_create".equals(event.fetch("billing_reason")) && SubscriptionConstants.SHARE_CODE_SECOND_MONTH_FREE
                .equals(subscription.getMetadata().get(SubscriptionConstants.META_SHARE_CODE))) {
            subscription = submitSecondMonthFree(subscription);
        }
    }

    private TradeRecord insertTradeRecord(SubscriptionOrder subscriptionOrder, Subscription subscription, Date tradeTime, BigDecimal amountTotal, StripeEvent event, Long subscriptionOrderId, String paymentIntent, TradeRecordAttributes attributes, String tradeNo) {
        // 插入交易流水表
        TradeRecord tradeRecord = new TradeRecord();
        tradeRecord.setAppCode(subscriptionOrder.getAppCode());
        tradeRecord.setUserId(subscriptionOrder.getUserId());
        tradeRecord.setPaymentType(PaymentTypeEnum.INITIATED_PAYMENT);
        tradeRecord.setStatus(TradeRecordStatus.SUCC);
        tradeRecord.setTradeDirection(TradeDirection.FORWARD);
        tradeRecord.setTradeAmount(amountTotal);
        String paidAt = event.fetchJsonPath("status_transitions.paid_at");
        if (StringUtils.isNotBlank(paidAt)) {
            tradeRecord.setTradeTime(new Date(Long.parseLong(paidAt) * 1000L));
        }
        tradeRecord.setTradeCurrency(event.fetch("currency"));
        tradeRecord.setSubscriptionOrderId(subscriptionOrderId);
        tradeRecord.setTradeNo(tradeNo);
        tradeRecord.setPaymentMethod(PaymentMethodEnum.STRIPE.getValue());
        tradeRecord.setOutTradeNo(paymentIntent);
        tradeRecord.setTradeTime(tradeTime);

        tradeRecord.setAttributes(attributes);
        tradeRecord.setDeleted(false);


        TradeRecordsQuery tradeRecordsQuery = new TradeRecordsQuery();
        tradeRecordsQuery.setOrderId(subscriptionOrderId);
        tradeRecordsQuery.setAppCode(subscriptionOrder.getAppCode());
        tradeRecordsQuery.setOutTradeNo(paymentIntent);
        tradeRecordsQuery.setTradeDirection(TradeDirection.FORWARD);
        List<TradeRecord> tradeRecords = tradeRecordRepository.queryTradeRecords(tradeRecordsQuery);
        if (CollectionUtils.isNotEmpty(tradeRecords)) {
            return tradeRecord;
        }
        Map<String, String> metadata = subscription.getMetadata();
        String clientIp = metadata.get(SubscriptionConstants.META_CLIENT_IP);
        if (StringUtils.isNotBlank(clientIp)) {
            GetAddressInfoByIpResponse addressInfoByIp = geoIpGateway.getAddressInfoByIp(clientIp);
            if (addressInfoByIp != null) {
                IpAddressInfo ipAddressInfo = new IpAddressInfo();
                BeanUtils.copyProperties(addressInfoByIp, ipAddressInfo);
                attributes.setIpAddressInfo(ipAddressInfo);
            }
        }
        tradeRecordRepository.createOrUpdateTradeRecord(tradeRecord);
        return tradeRecord;
    }

    public static Subscription submitSecondMonthFree(Subscription subscription) throws StripeException {
        // 如果当前订阅已经有了订阅周期，需要解绑
        if (StringUtils.isNotBlank(subscription.getSchedule())) {
            SubscriptionSchedule schedule = SubscriptionSchedule.retrieve(subscription.getSchedule());
            if (Lists.newArrayList("active", "not_started").contains(schedule.getStatus())) {
                schedule.release();
            }
        }
        if (!"month".equals(subscription.getItems().getData().get(0).getPlan().getInterval())) {
            return subscription;
        }
        SubscriptionScheduleCreateParams scheduleCreateParams = SubscriptionScheduleCreateParams.builder()
                    .setFromSubscription(subscription.getId())
                    .build();
        SubscriptionSchedule schedule = SubscriptionSchedule.create(scheduleCreateParams);
        schedule = SubscriptionSchedule.retrieve(schedule.getId());
        List<SubscriptionScheduleUpdateParams.Phase.Discount> originalDiscount = new ArrayList<>();
        for (SubscriptionSchedule.Phase.Discount discount : CollectionUtils.emptyIfNull(schedule.getPhases().get(0).getDiscounts())) {
            originalDiscount.add(SubscriptionScheduleUpdateParams.Phase.Discount.builder()
                    .setDiscount(discount.getDiscount())
                    .setCoupon(discount.getCoupon())
                    .setPromotionCode(discount.getPromotionCode())
                    .build());

        }
        SubscriptionScheduleUpdateParams paramsUpdate =
                SubscriptionScheduleUpdateParams.builder()
                        // 保持第一段周期（现在的）
                        .addPhase(
                                SubscriptionScheduleUpdateParams.Phase.builder()
                                        .addItem(
                                                SubscriptionScheduleUpdateParams.Phase.Item.builder()
                                                        .setPrice(schedule.getPhases().get(0).getItems().get(0).getPrice())
                                                        .setQuantity(schedule.getPhases().get(0).getItems().get(0).getQuantity())
                                                        .build()
                                        )
                                        .setStartDate(schedule.getPhases().get(0).getStartDate())
                                        .setEndDate(schedule.getPhases().get(0).getEndDate())
                                        .build()
                        )
                        // 第二周期（免费）
                        .addPhase(
                                SubscriptionScheduleUpdateParams.Phase.builder()
                                        .addItem(
                                                SubscriptionScheduleUpdateParams.Phase.Item.builder()
                                                        .setPrice(schedule.getPhases().get(0).getItems().get(0).getPrice())
                                                        .setQuantity(1L)
                                                        .build()
                                        )
                                        // 保留所有原有折扣（折扣码）
                                        .addAllDiscount(originalDiscount)
                                        .addDiscount(SubscriptionScheduleUpdateParams.Phase.Discount.builder().setCoupon(SwitchConfig.t2gSecondMonthFreeCoupon).build())
                                        .build()
                        )
                        .setEndBehavior(SubscriptionScheduleUpdateParams.EndBehavior.RELEASE)
                        .build();
        schedule.update(paramsUpdate);
        return Subscription.retrieve(subscription.getId());
    }



    private BillingDetail buildBillingDetail(Charge charge) {
        Charge.BillingDetails billingDetails = charge.getBillingDetails();
        if (billingDetails == null || billingDetails.getAddress() == null) {
            log.error("billing details is null, chargeId {}", charge);
            return null;
        }
        BillingDetail billingDetail = new BillingDetail();
        Address address = billingDetails.getAddress();
        billingDetail.setCountry(address.getCountry());
        billingDetail.setCity(address.getCity());
        billingDetail.setPostalCode(address.getPostalCode());
        billingDetail.setLine1(address.getLine1());
        billingDetail.setLine2(address.getLine2());
        return billingDetail;
    }

    private PaymentMethodDetail buildPaymentMethodDetail(Charge charge) {
        Charge.PaymentMethodDetails paymentMethodDetails = charge.getPaymentMethodDetails();
        if (paymentMethodDetails == null) {
            return null;
        }
        Object paymentMethodDetailJson = JSON.toJSON(paymentMethodDetails);
        if (!(paymentMethodDetailJson instanceof JSONObject)) {
            return null;
        }
        JSONObject paymentMethodDetailObj = (JSONObject) paymentMethodDetailJson;
        String type = paymentMethodDetailObj.getString("type");
        if (type == null) {
            return null;
        }
        JSONObject typeObject = paymentMethodDetailObj.getJSONObject(type);
        if (typeObject == null) {
            return null;
        }
        PaymentMethodDetail paymentMethodDetail = new PaymentMethodDetail();
        paymentMethodDetail.setCountry(typeObject.getString("country"));
        return paymentMethodDetail;
    }

    private PaymentResultEventDTO buildPaymentResultNotifyDTO(TradeRecord tradeRecord) {
        PaymentResultEventDTO notifyDTO = new PaymentResultEventDTO();
        notifyDTO.setAppCode(tradeRecord.getAppCode());
        notifyDTO.setTradeNo(tradeRecord.getTradeNo());
        notifyDTO.setOutTradeNo(tradeRecord.getOutTradeNo());
        notifyDTO.setTradeStatus(tradeRecord.getStatus().name());
        notifyDTO.setUserId(tradeRecord.getUserId());
        notifyDTO.setTradeTime(tradeRecord.getTradeTime());
        OrderDTO order = new OrderDTO();
        order.setReferenceOrderId(String.valueOf(tradeRecord.getSubscriptionOrderId()));
        notifyDTO.setOrder(order);
        return notifyDTO;
    }

    @Override
    public String getEventType() {
        return StripeConsts.INVOICE_PAID;
    }

    @Override
    public String getAppCode() {
        return AppEnum.TEXT2GO.getCode();
    }
}
