package com.alibaba.copilot.enabler.service.user.controller;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.user.service.UserRegisterService;
import com.alibaba.copilot.enabler.infra.base.constants.ErrorCodes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/1
 */
@Slf4j
@RestController
@RequestMapping("/user/register")
@Api(tags = "user", value = "user")
public class UserRegisterController {

    @Resource
    private UserRegisterService userRegisterService;

    /**
     * 检查邮箱是否注册过账号
     *
     * @param email
     * @return
     */
    @ApiOperation(value = "检查邮箱是否注册过账号", nickname = "check user email")
    @RequestMapping(value = "/checkEmail", method = RequestMethod.GET)
    @Monitor(name = "检查邮箱是否注册过账号", level = Monitor.Level.P1, layer = Monitor.Layer.WEB)
    public SingleResult<Boolean> checkEmail(@ApiParam(value = "邮箱") @RequestParam(value = "email") String email) {
        log.info("UserRegisterController#checkEmail# request email: {}", email);
        Boolean checkResult = userRegisterService.checkEmailExist(email);
        return SingleResult.buildSuccess(checkResult);
    }
}
