package com.alibaba.copilot.enabler.service.antom.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * Antom 事件
 *
 * <AUTHOR>
 * @date 2025/05/01
 */
@Data
public class AntomEvent {
    
    /**
     * 原始事件JSON对象
     */
    private JSONObject originalEvent;
    
    /**
     * 通知类型
     */
    private String notifyType;
    
    /**
     * 支付请求ID
     */
    private String paymentRequestId;
    
    /**
     * 支付ID
     */
    private String paymentId;
    
    /**
     * 结果状态
     */
    private String resultStatus;
    
    /**
     * 结果代码
     */
    private String resultCode;
    
    /**
     * 结果消息
     */
    private String resultMessage;
    
    /**
     * 授权通知类型
     */
    private String authorizationNotifyType;
    
    /**
     * 争议通知类型
     */
    private String disputeNotificationType;
    
    /**
     * 退款状态
     */
    private String refundStatus;
    
    /**
     * 从JSON对象创建事件
     *
     * @param jsonObject JSON对象
     * @return Antom事件
     */
    public static AntomEvent of(JSONObject jsonObject) {
        AntomEvent event = new AntomEvent();
        event.setOriginalEvent(jsonObject);
        
        // 设置通用字段
        if (jsonObject.containsKey("notifyType")) {
            event.setNotifyType(jsonObject.getString("notifyType"));
        }
        
        if (jsonObject.containsKey("paymentRequestId")) {
            event.setPaymentRequestId(jsonObject.getString("paymentRequestId"));
        }
        
        if (jsonObject.containsKey("paymentId")) {
            event.setPaymentId(jsonObject.getString("paymentId"));
        }
        
        // 设置结果字段
        if (jsonObject.containsKey("result")) {
            JSONObject result = jsonObject.getJSONObject("result");
            if (result != null) {
                event.setResultStatus(result.getString("resultStatus"));
                event.setResultCode(result.getString("resultCode"));
                event.setResultMessage(result.getString("resultMessage"));
            }
        }
        
        // 设置授权通知类型
        if (jsonObject.containsKey("authorizationNotifyType")) {
            event.setAuthorizationNotifyType(jsonObject.getString("authorizationNotifyType"));
        }
        
        // 设置争议通知类型
        if (jsonObject.containsKey("disputeNotificationType")) {
            event.setDisputeNotificationType(jsonObject.getString("disputeNotificationType"));
        }
        
        // 设置退款状态
        if (jsonObject.containsKey("refundStatus")) {
            event.setRefundStatus(jsonObject.getString("refundStatus"));
        }
        
        return event;
    }
}
