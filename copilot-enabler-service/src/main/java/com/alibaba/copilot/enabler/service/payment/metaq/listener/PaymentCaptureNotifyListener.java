package com.alibaba.copilot.enabler.service.payment.metaq.listener;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.aepay.fund.business.api.payment.dto.notify.CaptureNotifyDTO;
import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.infra.base.utils.MetaqUtils;
import com.alibaba.copilot.enabler.service.payment.metaq.handler.PaymentCaptureNotifyHandler;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.taobao.eagleeye.EagleEye;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 支付请款结果通知listener
 */
@Component(value = "paymentCaptureNotifyListener")
public class PaymentCaptureNotifyListener implements MessageListenerConcurrently {
    public static final Logger TASK_LOG = LoggerFactory.getLogger("taskLogger");

    @Autowired
    private PaymentCaptureNotifyHandler paymentCaptureNotifyHandler;

    @Override
    @Monitor(name = "支付请款结果通知listener", level = Monitor.Level.P1, layer = Monitor.Layer.CONSUMER)
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        MessageExt messageExt = CollectionUtil.getFirst(msgs);
        if (messageExt == null) {
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        String msgId = messageExt.getMsgId();

        JSONObject notifyContentJsonObject = MetaqUtils.getJSONObjOfMessageExtBody(messageExt);
        String notifyContentStr = notifyContentJsonObject == null ? null : notifyContentJsonObject.toJSONString();

        // 交易流水id
        String tradeNo = null;
        CaptureNotifyDTO captureNotifyDTO = null;

        try {
            EagleEye.startTrace(null, this.getClass().getName(), EagleEye.TYPE_CUSTOM_MESSAGE_SUB);
            TASK_LOG.info("paymentCaptureNotifyListener notifyContent:::{}", notifyContentStr);

            if (notifyContentJsonObject == null) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

            captureNotifyDTO = notifyContentJsonObject.toJavaObject(CaptureNotifyDTO.class);
            if (!Objects.equals(captureNotifyDTO.getNotifyType(), AEPaymentConstants.CAPTURE_PAYMENT_RESULT_NOTIFY_TYPE)) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

            // 交易流水id
            tradeNo = captureNotifyDTO.getCaptureRequestId();

            Assertor.asserts(StringUtils.isNotBlank(tradeNo), "paymentCaptureNotifyListener msg captureRequestId illegal");
            Assertor.asserts(captureNotifyDTO.getResult() != null
                            && Objects.equals(captureNotifyDTO.getResult().getResultCode(), AEPaymentConstants.PAYMENT_RESULT_NOTIFY_SUCCESS),
                    "paymentCaptureNotifyListener msg not success");

            // 支付请款处理
            paymentCaptureNotifyHandler.handle(tradeNo, msgId, notifyContentStr, captureNotifyDTO);

            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (BizException bizException) {
            TASK_LOG.error("paymentCaptureNotifyListener bizError,msg:{},notifyContent:{}",
                    bizException.getMessage(), notifyContentStr, bizException);
            // 支付请款失败处理
            paymentCaptureNotifyHandler.handleFail(tradeNo, msgId, notifyContentStr, captureNotifyDTO);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            TASK_LOG.error("paymentCaptureNotifyListener error,msg:{},notifyContent:{}",
                    e.getMessage(), notifyContentStr, e);

            // Metaq消费自动重试
            if (MetaqUtils.isResumeAutoRetry(messageExt, context, notifyContentStr)) {
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }

            // 支付请款失败处理
            paymentCaptureNotifyHandler.handleFail(tradeNo, msgId, notifyContentStr, captureNotifyDTO);

            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } finally {
            EagleEye.endTrace(null);
        }
    }
}