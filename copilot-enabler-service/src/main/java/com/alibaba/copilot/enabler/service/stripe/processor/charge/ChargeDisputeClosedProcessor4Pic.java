package com.alibaba.copilot.enabler.service.stripe.processor.charge;

import com.alibaba.copilot.enabler.client.payment.constant.dispute.DisputeResult;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEventMetadata;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.service.stripe.StripeConsts;
import com.alibaba.copilot.enabler.service.stripe.processor.AbstractStripeEventProcessor;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeExecuteContext;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 处理 charge.dispute.closed webhook 事件
 */

@Slf4j
@Component
public class ChargeDisputeClosedProcessor4Pic extends AbstractStripeEventProcessor {

    @Override
    public String getEventType() {
        return StripeConsts.CHARGE_DISPUTE_CLOSED;
    }

    @Override
    public String getAppCode() {
        return AppEnum.PIC_COPILOT.getCode();
    }

    @Override
    protected void doProcess(StripeExecuteContext context) {
        log.info("ChargeDisputeFundsWithdrawnProcessor4Pic context={}", JSON.toJSONString(context));

        StripeEventMetadata metaData = context.getMetaData();
        TradeRecord tradeRecord = paymentDomainService.queryByTradeNo(metaData.getTradeNo());
        if (tradeRecord == null) {
            log.error("ChargeDisputeFundsWithdrawnProcessor4Pic tradeRecord is null, metaData={}", JSON.toJSONString(metaData));
            return;
        }
        DisputeResult disputeResult = DisputeResult.getByValue(context.getEvent().fetchDisputeStatus());
        if (disputeResult == null) {
            log.error("ChargeDisputeFundsWithdrawnProcessor4Pic disputeResult is null, metaData={}", JSON.toJSONString(metaData));
            return;
        }

        if (metaData.whetherPaymentMode()) {
            paymentDomainService.dealDisputeRefund(tradeRecord, disputeResult);
        } else if (metaData.whetherSetupMode()) {
            picStripeSubscriptionService.dealWithDisputeRefund(metaData, tradeRecord, disputeResult);
        }

    }


}