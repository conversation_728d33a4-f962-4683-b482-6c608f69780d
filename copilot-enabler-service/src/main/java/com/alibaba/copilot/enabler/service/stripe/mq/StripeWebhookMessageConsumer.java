package com.alibaba.copilot.enabler.service.stripe.mq;

import com.alibaba.copilot.enabler.domain.base.mq.MessageHandler;
import com.alibaba.copilot.enabler.domain.base.mq.impl.BaseMessageQueueConsumer;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.alibaba.copilot.enabler.service.stripe.StripeConsts.METAQ_TAG;
import static com.alibaba.copilot.enabler.service.stripe.StripeConsts.METAQ_TOPIC;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/12
 */
@Component
public class StripeWebhookMessageConsumer extends BaseMessageQueueConsumer<JSONObject> {

    @Value("${inner.metaq.message.consumerUnitName}")
    private String unitName;
    @Resource(name = "stripeWebhookMsgHandler")
    private MessageHandler<JSONObject> messageHandler;

    @Override
    public MessageHandler<JSONObject> getMessageHandler() {
        return messageHandler;
    }

    @Override
    protected Class<JSONObject> getEventClass() {
        return JSONObject.class;
    }

    @Override
    protected String getConsumerId() {
        return "CID_stripe_consumer";
    }

    @Override
    protected String getTopic() {
        return METAQ_TOPIC;
    }

    @Override
    protected String getTags() {
        return METAQ_TAG;
    }

    @Override
    protected String getUnitName() {
        return unitName;
    }
}