package com.alibaba.copilot.enabler.service.user.factory;

import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.copilot.enabler.client.user.dto.UserAppRelationDTO;
import com.alibaba.copilot.enabler.domain.user.model.UserAppRelation;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/4
 */
public class UserAppRelationDTOConverter implements Converter<UserAppRelation, UserAppRelationDTO> {

    public static final Converter<UserAppRelation, UserAppRelationDTO> INSTANCE = new UserAppRelationDTOConverter();

    @Override
    public UserAppRelationDTO convertA2B(UserAppRelation userAppRelation) {
        if (userAppRelation == null) {
            return null;
        }
        return UserAppRelationDTO.builder()
                .id(userAppRelation.getId())
                .userId(userAppRelation.getUserId())
                .email(userAppRelation.getEmail())
                .appName(userAppRelation.getAppName())
                .appCode(userAppRelation.getAppCode())
                .appType(userAppRelation.getAppType())
                .bindSource(userAppRelation.getBindSource())
                .bindStatus(userAppRelation.getBindStatus())
                .build();
    }

    @Override
    public UserAppRelation convertB2A(UserAppRelationDTO userAppRelationDTO) {
        return null;
    }
}
