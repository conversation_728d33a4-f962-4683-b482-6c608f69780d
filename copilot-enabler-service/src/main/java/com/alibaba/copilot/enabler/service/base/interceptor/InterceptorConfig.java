package com.alibaba.copilot.enabler.service.base.interceptor;

import com.alibaba.copilot.enabler.service.bill.interceptor.BillInterceptor;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/31
 */
@Configuration
public class InterceptorConfig implements WebMvcConfigurer {
    @Resource
    private CopilotHandlerInterceptor copilotHandlerInterceptor;
    @Resource
    private BillInterceptor billInterceptor;
    @Resource
    private EdgeshopInterceptor edgeshopInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(billInterceptor).addPathPatterns("/api/bill/**").addPathPatterns("/api/alphaRankBill/**");
        registry.addInterceptor(edgeshopInterceptor).addPathPatterns("/**");

//        registry.addInterceptor(copilotHandlerInterceptor).addPathPatterns("/**");
//
//        SentinelWebMvcConfig config = new SentinelWebMvcConfig();
//        // 若设为 true，则 URL 资源名会带上 HTTP method 前缀。若之前配过规则，谨慎迁移
//        config.setHttpMethodSpecify(true);
//        config.setBlockExceptionHandler(new DefaultBlockExceptionHandler());
//        // Add to the interceptor list.
//        registry.addInterceptor(new SentinelWebInterceptor(config))
//                .order(Ordered.HIGHEST_PRECEDENCE)
//                .addPathPatterns("/**");
    }

    @Override
    public void addCorsMappings(@NotNull CorsRegistry registry) {
        registry.addMapping("/api/**")
                .allowedOrigins("https://pre-www.edgeshop.ai")
                .allowedMethods("*")
                .allowedHeaders("*")
                .allowCredentials(true);
    }
}
