package com.alibaba.copilot.enabler.service.base.interceptor;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.domain.base.utils.SpringContextUtils;
import com.alibaba.fastjson.JSON;
import com.taobao.session.ClientContext;
import com.taobao.session.TaobaoSession;
import com.taobao.session.TaobaoSessionFactory;
import com.taobao.session.authorization.AuthorizationConfig;
import com.taobao.session.authorization.DefaultAuthorizationInterceptor;
import com.taobao.unifiedsession.session.SessionFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/31
 */
@Slf4j
public class CopilotAuthorizationInterceptor extends DefaultAuthorizationInterceptor {
    private static final String USER_ID = "userId";

    @Override
    public boolean authorize(HttpServletRequest request) {
        if (HttpMethod.OPTIONS.name().equalsIgnoreCase(request.getMethod())) {
            return true;
        }
        HttpSession httpSession = request.getSession(false);
        if (httpSession instanceof SessionFacade) {
            SessionFacade session = (SessionFacade) httpSession;
            return session.hasLogin();
        }
        return false;
    }

    @Override
    public void approve(HttpServletRequest request, HttpServletResponse response, AuthorizationConfig authorizationConfig) {
        HttpSession httpSession = request.getSession(false);
        if (httpSession instanceof SessionFacade) {
            SessionFacade session = (SessionFacade) httpSession;
            try {
                ClientContext clientCtx = new ClientContext();
                clientCtx.setSessionId(session.getId());
                clientCtx.setRequestURL("https://login.edgeshop.ai");
                TaobaoSessionFactory taobaoSessionFactory = (TaobaoSessionFactory) SpringContextUtils.getBean("taobaoSessionFactoryConfig");
                TaobaoSession tbsession = taobaoSessionFactory.createSession(clientCtx);
                if (tbsession == null) {
                    log.error("CopilotAuthorizationInterceptor.approve tbsession is null");
                } else {
                    String userIdStr = (String) tbsession.getAttribute(USER_ID);
                    if (StringUtils.isNotEmpty(userIdStr)) {
                        log.info("CopilotAuthorizationInterceptor.approve set User id {}, threadId is {}", userIdStr, Thread.currentThread().getId());
                        CopilotContext.setUserId((Long.parseLong(userIdStr)));
                    }
                }
                log.info("CopilotAuthorizationInterceptor.approve,tbsessionId is {}, userId is {}", session.getId(), CopilotContext.getUserId());
            } catch (Exception e) {
                log.error("CopilotAuthorizationInterceptor.approve create Session failed, session id is {}, threadId is {}", session.getId(), Thread.currentThread().getId(), e);
            }
        }
    }

    @Override
    public void deny(HttpServletRequest request, HttpServletResponse response, AuthorizationConfig authorizationConfig) throws IOException {
        log.info("failed to auth login status");
        String requestURL = encodeRequestURL(request);
        //抛出指定异常
        SingleResult<?> result = SingleResult.buildFailure("LOGIN_EXPIRE_ERROR", authorizationConfig.getLoginUrl() + requestURL);
        PrintWriter writer = response.getWriter();
        writer.write(JSON.toJSONString(result));
        writer.flush();
        writer.close();
    }
}
