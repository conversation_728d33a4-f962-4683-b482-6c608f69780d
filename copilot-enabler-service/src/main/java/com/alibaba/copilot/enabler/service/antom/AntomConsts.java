package com.alibaba.copilot.enabler.service.antom;

import java.util.Arrays;
import java.util.List;

/**
 * Antom 常量定义
 *
 * <AUTHOR>
 * @date 2025/05/01
 */
public class AntomConsts {

    // =========== metaq ===================
    public static final String METAQ_TOPIC = "enabler_inner_event";
    public static final String METAQ_TAG = "antom_webhook";

    // ============ antom webhook type ============
    // 支付结果通知
    public static final String NOTIFY_PAYMENT_RESULT = "PAYMENT_RESULT";
    public static final String NOTIFY_PAYMENT_PENDING = "PAYMENT_PENDING";

    // 授权结果通知
    public static final String NOTIFY_AUTH_AUTHCODE_CREATED = "AUTHCODE_CREATED";
    public static final String NOTIFY_AUTH_TOKEN_CANCELED = "TOKEN_CANCELED";

    // 争议通知
    public static final String NOTIFY_DISPUTE_CREATED = "DISPUTE_CREATED";
    public static final String NOTIFY_DISPUTE_JUDGED = "DISPUTE_JUDGED";

    // 退款通知
    public static final String NOTIFY_REFUND_SUCCESS = "S";
    public static final String NOTIFY_REFUND_FAILED = "F";

    // 支付通知类型列表
    public static final List<String> PAYMENT_NOTIFY_TYPES = Arrays.asList(
            NOTIFY_PAYMENT_RESULT,
            NOTIFY_PAYMENT_PENDING
    );

    // 授权通知类型列表
    public static final List<String> AUTH_NOTIFY_TYPES = Arrays.asList(
            NOTIFY_AUTH_AUTHCODE_CREATED,
            NOTIFY_AUTH_TOKEN_CANCELED
    );

    // 争议通知类型列表
    public static final List<String> DISPUTE_NOTIFY_TYPES = Arrays.asList(
            NOTIFY_DISPUTE_CREATED,
            NOTIFY_DISPUTE_JUDGED
    );

    // 退款通知状态列表
    public static final List<String> REFUND_NOTIFY_STATUS = Arrays.asList(
            NOTIFY_REFUND_SUCCESS,
            NOTIFY_REFUND_FAILED
    );

    // Antom 签名相关常量
    public static final String ANTOM_SIGNATURE_HEADER_KEY = "Signature";
    public static final String ANTOM_CLIENT_ID_HEADER_KEY = "client-id";
    public static final String ANTOM_REQUEST_TIME_HEADER_KEY = "Request-Time";
}
