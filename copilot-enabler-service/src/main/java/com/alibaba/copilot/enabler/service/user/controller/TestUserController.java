package com.alibaba.copilot.enabler.service.user.controller;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyFeatureQuery;
import com.alibaba.copilot.enabler.domain.subscription.model.FeatureUsage;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrderAttributes;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.model.UserAttributes;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.infra.base.utils.EnvUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @version 2023/10/18
 */
@Api(tags = "测试用户接口")
@RestController
@RequestMapping("/api/test/user")
public class TestUserController {

    @Resource
    private UserRepository userRepository;

    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;

    @Resource
    private SubscriptionPlanRepository subscriptionPlanRepository;

    @ApiOperation("设置用户邮箱")
    @GetMapping("setUserEmail")
    public SingleResult<Void> setUserEmail(@ApiParam("应用标识") @RequestParam String appCode,
                                           @ApiParam("用户ID") @RequestParam Long userId,
                                           @ApiParam("邮箱") @RequestParam String email) {
        if (!SwitchConfig.testUserControllerSwitch) {
            return SingleResult.buildFailure("testUserControllerSwitch false");
        }

        EnvUtils.interceptForOnline();
        User user = queryUser(appCode, userId);
        user.setEmail(email);
        userRepository.updateUser(user);
        return SingleResult.buildSuccess(null);
    }

    @ApiOperation("清除用户卡信息")
    @GetMapping("clearUserCards")
    public SingleResult<Void> clearUserCards(@ApiParam("应用标识") @RequestParam String appCode,
                                             @ApiParam("用户ID") @RequestParam Long userId) {
        if (!SwitchConfig.testUserControllerSwitch) {
            return SingleResult.buildFailure("testUserControllerSwitch false");
        }

        EnvUtils.interceptForOnline();
        User user = queryUser(appCode, userId);
        UserAttributes attributes = user.getAttributes();
        attributes.removeAEPayInfo();
        userRepository.updateUser(user);
        return SingleResult.buildSuccess(null);
    }


    /**
     * 后门：订阅加白名单
     *
     * @param userId
     * @return
     */
    @ApiOperation("测试更新AlphaRank订阅数据")
    @GetMapping("setSubscriptionOrder")
    public SingleResult<Void> setSubscriptionOrder(@ApiParam("应用标识") @RequestParam String instruct,
                                                   @ApiParam("userId") @RequestParam Long userId,
                                                   @ApiParam("orderId") @RequestParam Long orderId) {
        if (!SwitchConfig.testUserControllerSwitch) {
            return SingleResult.buildFailure("testUserControllerSwitch false");
        }

        SubscriptionOrder subscriptionOrder = subscriptionOrderRepository.getByOrderId(orderId);
        if (subscriptionOrder != null && subscriptionOrder.getUserId().equals(userId)) {
            if (StringUtils.equals("IN_EFFECT", instruct)) {
                //update到有效状态
                subscriptionOrder.setStatus(SubscriptionOrderStatus.IN_EFFECT);
            } else if (StringUtils.equals("UNSUBSCRIBE", instruct)) {
                //update到无效状态
                subscriptionOrder.setStatus(SubscriptionOrderStatus.UNSUBSCRIBE);
            }
            subscriptionOrderRepository.saveSubscriptionOrder(subscriptionOrder);
        }
        return SingleResult.buildSuccess(null);
    }

    /**
     * 后门：订阅白名单
     *
     * @param userId
     * @return
     */
    @ApiOperation("测试新增AlphaRank订阅数据")
    @GetMapping("deleteSubscriptionOrder")
    public SingleResult<String> deleteSubscriptionOrder(@ApiParam("shopifyShopId") @RequestParam Long userId
            , @ApiParam("appCode") @RequestParam String appcode
            , @ApiParam("planId") @RequestParam Long planId
            , @ApiParam("planName") @RequestParam String planName) {
        if (!SwitchConfig.testUserControllerSwitch) {
            return SingleResult.buildFailure("testUserControllerSwitch false");
        }

        Calendar calendar = Calendar.getInstance();
        // 将当前时间增加100天
        calendar.add(Calendar.DAY_OF_YEAR, 100);
        Date futureDate = calendar.getTime();
        SubscriptionOrderAttributes subscriptionOrderAttributes = new SubscriptionOrderAttributes();
        subscriptionOrderAttributes.setPlanDays(365L);
        SubscriptionOrder order = SubscriptionOrder.builder()
                .userId(userId)
                .email("<EMAIL>")
                .appCode(appcode)
                .userAppRelationId(0L)
                .nextPlanId(null)
                .nextPlanName(null)
                .status(SubscriptionOrderStatus.IN_EFFECT)
                .autoRenew(Boolean.FALSE)
                .hadNextRenew(Boolean.FALSE)
                .performStartTime(new Date())
                .performEndTime(futureDate)
                .nextRenewalTime(futureDate)
                .planPrice(new BigDecimal("839.880"))
                .isIncludeTrial(false)
                .subscriptionPlanId(planId)
                .subscriptionPlanName(planName)
                .subscriptionDiscountTag(null)
                .shopifySubscriptionId(1L)
                .attributes(subscriptionOrderAttributes)
                .deleted(Boolean.FALSE)
                .build();
        subscriptionOrderRepository.saveSubscriptionOrder(order);
        return SingleResult.buildSuccess(JSON.toJSONString(order));
    }


    @ApiOperation("查询测试用量")
    @GetMapping("getSubscriptiontUsage")
    public SingleResult<String> getSubscriptiontUsage(@ApiParam("shopifyShopId") @RequestParam Long userId
            , @ApiParam("appCode") @RequestParam String appcode) {
        if (!SwitchConfig.testUserControllerSwitch) {
            return SingleResult.buildFailure("testUserControllerSwitch false");
        }

        JSONObject jsonObject = new JSONObject();
        List<String> typeList = new ArrayList<>();

        typeList.add("UNLIMITED_SEO_DIAGNOSIS");
        typeList.add("VIEW_SITE_WIDE_OPTIMIZATION_ADVICE");
        typeList.add("MANUAL_CONTENT_EDITING");
        typeList.add("SUPPORT_FULL_MANAGEMENT");
        typeList.add("AUTOMATIC_OPTIMIZATION_FIXING_SEO_TECHNICAL_ISSUES");
        typeList.add("KEYWORD_RECOMMENDATIONS");
        typeList.add("INTELLIGENT_GENERATION_OPTIMIZATION_SITE_WIDE_IMAGE_ALT_TAGS");
        typeList.add("INTELLIGENT_GENERATION_OPTIMIZATION_SITE_WIDE_TDK");
        typeList.add("INTERNAL_LINK_GENERATION_OPTIMIZATION");
        typeList.add("INTELLIGENT_BLOG_CONTENT");
        typeList.add("FULL_SITE_OPTIMIZATION_RECOMMENDATIONS");
        typeList.add("AUTOMATIC_FIXIING_OF_SEO_TECHNICAL_ISSUES");
        typeList.add("INTELLIGENT_BLOG_OPIMIZE_CONTENT");
        typeList.add("INTELLIGENT_PRODUCT_OPTIMIZATION");
        typeList.add("INTELLIGENT_COLLECTION_OPTIMIZATION");
        typeList.add("COMPETITOR_DATA_VIEWING");
        typeList.add("BRANK_ANALYSIS_TAILORED_CONTENT");
        typeList.add("UNLIMITED_SEO_DIAGNOSTIC_SESSIONS");
        typeList.add("OPTIMIZATION_SUGGESTIONS_FOR_EVERY_PAGE_ON_THE_SITE");
        typeList.add("GENERATION_OF_CUSTOMIZED_BLOG_POST");
        typeList.add("SITE_KEYWORD_QUERY_BLOG_CONTENT");
        typeList.add("SITE_TOPIC_QUERY_BLOG_CONTENT");
        typeList.add("SITE_COMPETITION_BLOG_CONTENT");
        typeList.add("RECOMMENDATED_SEO_KEYWORDS");
        typeList.add("AUTOMATED_BLOG_TDK");
        typeList.add("AUTOMATIC_BLOG_IMAGE_GENERATION");
        typeList.add("SUPPORT_FOR_CONTENT_ON_TRENDY_TOPIC");
        typeList.add("BRAND_AYALYSIS_AND_CONTENT_CUSTOMIZATION");
        typeList.add("INTELLIGENT_PRODUCT_CHECK");
        typeList.add("INTELLIGENT_COLLECTION_CHECK");
        typeList.add("INTELLIGENT_ARTICLE_CHECK");
        typeList.add("KEYWORD_CUSTOMISATION");
        typeList.add("INTELLIGENT_TOPIC_RECOMMENDATION");
        typeList.add("ALL_AVAILABLE_BLOG_TEMPLATES");
        typeList.add("PROVIDE_MULTILINGUAL_TRANSLATION");

        for (String type : typeList) {
            ShopifyFeatureQuery shopifyFeatureQuery = new ShopifyFeatureQuery();
            shopifyFeatureQuery.setAppCode(appcode);
            shopifyFeatureQuery.setId(userId);
            shopifyFeatureQuery.setFeatureType(type);
            FeatureUsage featureUsage = subscriptionPlanRepository.queryFeatureUsage(shopifyFeatureQuery);
            if (featureUsage != null) {
                jsonObject.put(type, "quota " + featureUsage.getQuota() + ",usageCount " + featureUsage.getUsageCount());
            }
        }
        return SingleResult.buildSuccess(jsonObject.toJSONString());
    }

    @ApiOperation("更新测试用量")
    @GetMapping("updateSubscriptiontUsage")
    public SingleResult<String> updateSubscriptiontUsage(@ApiParam("shopifyShopId") @RequestParam Long userId,
                                                         @ApiParam("type") @RequestParam String type,
                                                         @ApiParam("usage") @RequestParam Long usageCount,
                                                         @ApiParam("appCode") @RequestParam String appCode) {
        if (!SwitchConfig.testUserControllerSwitch) {
            return SingleResult.buildFailure("testUserControllerSwitch false");
        }

        ShopifyFeatureQuery shopifyFeatureQuery = new ShopifyFeatureQuery();
        shopifyFeatureQuery.setAppCode(appCode);
        shopifyFeatureQuery.setId(userId);
        shopifyFeatureQuery.setFeatureType(type);
        FeatureUsage featureUsage = subscriptionPlanRepository.queryFeatureUsage(shopifyFeatureQuery);
        if (featureUsage != null) {
            //在有效期内有用量记录 +1
            featureUsage.setUsageCount(usageCount);
            subscriptionPlanRepository.saveFeatureUsage(featureUsage);
            return SingleResult.buildSuccess("success");
        }
        return SingleResult.buildSuccess("今天没有用量记录,要先手动触发一次用量");
    }


    private User queryUser(String appCode, Long userId) {
        User user = userRepository.getUser(userId);
        Assertor.assertNonNull(user, "user is null");
        Assertor.asserts(Objects.equals(user.getAppCode(), appCode), "appCode not match");
        return user;
    }
}
