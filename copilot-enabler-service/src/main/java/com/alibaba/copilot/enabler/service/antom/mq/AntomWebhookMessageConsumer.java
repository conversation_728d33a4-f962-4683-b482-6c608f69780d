package com.alibaba.copilot.enabler.service.antom.mq;

import com.alibaba.copilot.enabler.domain.base.mq.MessageHandler;
import com.alibaba.copilot.enabler.domain.base.mq.impl.BaseMessageQueueConsumer;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.alibaba.copilot.enabler.service.antom.AntomConsts.METAQ_TAG;
import static com.alibaba.copilot.enabler.service.antom.AntomConsts.METAQ_TOPIC;

/**
 * Antom Webhook 消息消费者
 *
 * <AUTHOR>
 * @date 2025/05/01
 */
@Component
public class AntomWebhookMessageConsumer extends BaseMessageQueueConsumer<JSONObject> {

    @Value("${inner.metaq.message.consumerUnitName}")
    private String unitName;
    
    @Resource(name = "antomWebhookMsgHandler")
    private MessageHandler<JSONObject> messageHandler;

    @Override
    public MessageHandler<JSONObject> getMessageHandler() {
        return messageHandler;
    }

    @Override
    protected Class<JSONObject> getEventClass() {
        return JSONObject.class;
    }

    @Override
    protected String getConsumerId() {
        return "CID_antom_consumer";
    }

    @Override
    protected String getTopic() {
        return METAQ_TOPIC;
    }

    @Override
    protected String getTags() {
        return METAQ_TAG;
    }

    @Override
    protected String getUnitName() {
        return unitName;
    }
}
