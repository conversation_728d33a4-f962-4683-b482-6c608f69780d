package com.alibaba.copilot.enabler.service.bill.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.bill.dto.AlphaRankBillItemDTO;
import com.alibaba.copilot.enabler.client.bill.dto.BillItemDTO;
import com.alibaba.copilot.enabler.client.bill.dto.QueryBillDTO;
import com.alibaba.copilot.enabler.client.bill.dto.QueryBillResultDTO;
import com.alibaba.copilot.enabler.client.bill.service.T2gBillService;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.base.utils.TimeUtils;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.domain.payment.model.IpAddressInfo;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrderAttributes;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionOrderDO;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionOrderMapper;
import com.alibaba.copilot.enabler.service.bill.helper.TimeHelper;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.Subscription;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2024/3/21
 */
@Slf4j
@Service
public class T2gBillServiceImpl implements T2gBillService {
    private static final ZoneId ZONE_ID = ZoneId.of("Asia/Shanghai");

    private static final SimpleDateFormat FORMATTER_BILL_MONTH = new SimpleDateFormat("yyyy-MM");
    private static final SimpleDateFormat FORMATTER_BILL_TITLE = new SimpleDateFormat("yyyy-MM");
    public static final int SCALE = 6;

    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;

    @Resource
    private TradeRecordRepository tradeRecordRepository;

    @Resource
    private SubscriptionPlanRepository subscriptionPlanRepository;
    @Resource
    private SubscriptionOrderMapper subscriptionOrderMapper;

    @Override
    public void dataRepair(List<String> stripeSubId) {
        Stripe.apiKey = SwitchConfig.stripeApiKey;
        for (String id : stripeSubId) {

            Subscription subscription;
            try{
                subscription = Subscription.retrieve(id);
            } catch (StripeException e) {
                throw new RuntimeException(e);
            }
            String appCode = subscription.getMetadata().get("appCode");
            if (!AppEnum.TEXT2GO.getCode().equals(appCode)) {
                continue;
            }
            String subscriptionOrderId = subscription.getMetadata().get("subscriptionOrderId");
            Long orderId = Long.valueOf(subscriptionOrderId);
            SubscriptionOrderDO orderDO = subscriptionOrderMapper.selectById(orderId);
            boolean update = false;
            if (!Objects.equals(orderDO.getOuterSubscriptionId(), id)) {
                update = true;
                orderDO.setOuterSubscriptionId(id);
            }
            if (!Objects.equals(orderDO.getPerformStartTime(), new Date(subscription.getStartDate() * 1000))) {
                update = true;
                orderDO.setPerformStartTime(new Date(subscription.getStartDate() * 1000));
            }
            if (!Objects.equals(orderDO.getPerformEndTime(), new Date(subscription.getCurrentPeriodEnd() * 1000))) {
                update = true;
                orderDO.setPerformEndTime(new Date(subscription.getCurrentPeriodEnd() * 1000));
            }
            if (update) {
                subscriptionOrderMapper.updateById(orderDO);
            }
        }
    }

    @Override
    public QueryBillResultDTO queryBill(QueryBillDTO requestDTO) {
        log.info("requestDTO={}", requestDTO);
        List<String> appCodeList = requestDTO.getAppCodeList();
        Date targetDate = requestDTO.getTargetDate();
        Assertor.assertNotEmpty(appCodeList, "appCode can not be blank");
        Assertor.assertNonNull(targetDate, "targetDate can not be null");

        // 计算时间节点
        TimeHelper timeHelper = new TimeHelper(targetDate);

        // 查询目标月内生效过的订单
        List<SubscriptionOrder> orders;
        if (CollectionUtils.isNotEmpty(requestDTO.getPayType()) && requestDTO.getPayType().contains(SubscriptionPayType.Shoplazza.name())) {
            orders = subscriptionOrderRepository.queryListForBill(
                    appCodeList, timeHelper.getBillMonthStartTimeShoplazza(), timeHelper.getBillMonthEndTimeShoplazza()
            );
        } else {
            orders = subscriptionOrderRepository.queryListForBill(
                    appCodeList, timeHelper.getMonthStartTime(), timeHelper.getMonthEndTime()
            );
        }

        if (CollectionUtils.isNotEmpty(requestDTO.getPayType())) {
            orders = orders.stream().filter(o -> requestDTO.getPayType().contains(o.getSubscriptionPayType().name())).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(requestDTO.getExcludePayType())) {
            orders = orders.stream().filter(o -> !requestDTO.getExcludePayType().contains(o.getSubscriptionPayType().name())).collect(Collectors.toList());
        }

        List<Long> orderIds = orders.stream().map(SubscriptionOrder::getId).collect(Collectors.toList());
        log.info("found {} orders", orderIds.size());
        if (CollectionUtils.isEmpty(orderIds)) {
            String title = String.format("%s账单", FORMATTER_BILL_TITLE.format(targetDate));
            return new QueryBillResultDTO()
                    .setTitle(title)
                    .setTotalCount(0)
                    .setBillItems(new ArrayList<>());
        }

        // 查询订单对应的所有套餐
        List<Long> planIds = transform(orders, SubscriptionOrder::getSubscriptionPlanId);
        List<SubscriptionPlan> plans = subscriptionPlanRepository.queryByIds(planIds);
        Map<Long, SubscriptionPlan> planId2Plan = extractMapping(plans, SubscriptionPlan::getId);
        log.info("found {} plans", plans.size());

        // 查询成功支付账单
        List<TradeRecord> payRecords = tradeRecordRepository.queryPaySuccessRecordsForBill(orderIds);
        payRecords = payRecords.stream()
                .filter(r -> r.getTradeTime() != null)
                .filter(r -> r.getTradeTime().getTime() <= timeHelper.getMonthEndTime().getTime()).collect(Collectors.toList());

        Map<Long, List<TradeRecord>> orderIdToPayreordMap = payRecords.stream().collect(Collectors.groupingBy(TradeRecord::getSubscriptionOrderId));

        Map<Long, TradeRecord> orderId2PayRecord = extractMapping(payRecords, TradeRecord::getSubscriptionOrderId);
        log.info("found {} pay records", payRecords.size());

        // 查询对应的退款账单
        List<TradeRecord> refundRecords = tradeRecordRepository.queryRefundRecordsForBill(orderIds);
        refundRecords = refundRecords.stream().filter(r -> r.getTradeTime().getTime() >= timeHelper.getMonthStartTime().getTime())
                .filter(r -> r.getTradeTime().getTime() <= timeHelper.getMonthEndTime().getTime()).collect(Collectors.toList());
        Map<Long, TradeRecord> orderId2RefundRecord = extractMapping(refundRecords, TradeRecord::getSubscriptionOrderId);
        log.info("found {} refund records", refundRecords.size());

        List<BillItemDTO> items = new ArrayList<>();
        for (SubscriptionOrder order : orders) {
            Long orderId = order.getId();
            Long planId = order.getSubscriptionPlanId();

            SubscriptionOrderAttributes attributes = order.getAttributes();
            Long trialDays = attributes.getTrialDays();

            List<TradeRecord> payRecord = orderIdToPayreordMap.get(orderId);
            if (CollectionUtils.isEmpty(payRecord)) {
                log.warn("payRecord is null, orderId={}, orderStatus={}", orderId, order.getStatus());
                continue;
            }

            TradeRecord refundRecord = orderId2RefundRecord.get(orderId);
            SubscriptionPlan plan = planId2Plan.get(planId);


            String vipLevel = String.format("%s %s", plan.getName(), plan.getDurationUnit().name());
            for (TradeRecord tradeRecord : payRecord) {
                BigDecimal tradeAmount = tradeRecord.getTradeAmount();
                Date tradeTime = tradeRecord.getTradeTime();
                BigDecimal taxRate = BigDecimal.ZERO;
                BigDecimal taxAmount = tradeAmount.multiply(taxRate).setScale(SCALE, RoundingMode.HALF_UP);
                BigDecimal tradeAmountWithoutTax = tradeAmount.subtract(taxAmount).setScale(SCALE, RoundingMode.HALF_UP);
                Date performStartTime = order.getPerformStartTime();
                Date performEndTime = order.getPerformEndTime();
                long planDays = TimeUtils.calculateDayCount(plan.getDuration(), plan.getDurationUnit());

                // 取订单金额/套餐天数, 作为套餐平均每天的单价
                BigDecimal amountPerDay = tradeAmountWithoutTax.divide(BigDecimal.valueOf(planDays), SCALE, RoundingMode.HALF_UP);

                // 取订单的试用期天数
                long orderTrialDays = Optional.ofNullable(trialDays).orElse(0L);

                ZonedDateTime monthEndTime = timeHelper.getMonthEndTime(ZONE_ID);

                // 计算试用期结束的时间
                Date trialEndTime = DateUtils.addDays(performStartTime, (int) orderTrialDays);

                // 将 Date 转为 ZonedDateTime
                ZonedDateTime trialEndZdt = trialEndTime.toInstant().atZone(ZONE_ID);

                // 判断是否在免费退订期内
                boolean canUnsubscribeAtCurrentMonthEnd = orderTrialDays > 0 && monthEndTime.isBefore(trialEndZdt);

                // 计算上月末的时间戳
                ZonedDateTime lastMonthEndTime = timeHelper.getLastMonthEndTime(ZONE_ID);
                // 计算截止上月末累计已经使用的天数
                int usedDaysUntilLastMonthEnd = computePlanDays(performStartTime, performEndTime, lastMonthEndTime, planDays) + 1;
                if (performStartTime.toInstant().atZone(ZONE_ID).isAfter(lastMonthEndTime)) {
                    usedDaysUntilLastMonthEnd = 0;
                }

                // 用户是否在目标月退订了
                boolean unsubscribed = refundRecord != null && refundRecord.getTradeTime().toInstant().atZone(ZONE_ID).isBefore(monthEndTime.toInstant().atZone(ZONE_ID));
                Date unsubscribedTime = unsubscribed ? refundRecord.getTradeTime() : null;

                // 是否在本月末前已结束 (针对订A的当月升级B时的A的场景)
                boolean endBeforeCurrentMonthEnd =
                        !unsubscribed && performEndTime.getTime() <= Date.from(monthEndTime.toInstant()).getTime();

                // 截至上月末累计已确认收入
                BigDecimal totalTradeAmountUntilLastMonthEnd = amountPerDay.multiply(BigDecimal.valueOf(usedDaysUntilLastMonthEnd))
                        .setScale(SCALE, RoundingMode.HALF_UP);
                // 当月收款金额
                final BigDecimal totalTradeAmountAtCurrentMonth;
                if (unsubscribed) {
                    // 退订场景
                    totalTradeAmountAtCurrentMonth = DateUtil.isSameMonth(targetDate, tradeTime)
                            // 退订本月的订单, 当月收款金额取0
                            ? BigDecimal.ZERO
                            // 退订非本月的订单, 当月收款金额取交易值的负数
                            : tradeAmount.multiply(BigDecimal.valueOf(-1));
                } else {
                    // 未退订场景, 本月的交易取全额, 非本月交易取0
                    totalTradeAmountAtCurrentMonth = DateUtil.isSameMonth(targetDate, tradeTime) ? tradeAmount : BigDecimal.ZERO;
                }
                // 截止本月末累计已经使用的天数
                int usedDaysUtilCurrentMonthEnd = unsubscribed
                        ? 0 : computePlanDays(performStartTime, performEndTime, monthEndTime, planDays);
                // 本月的计费天数 = 截止本月末累计已经使用的天数 - 截止上月末累计已经使用的天数
                int usedDaysForCurrentMonth = usedDaysUtilCurrentMonthEnd - usedDaysUntilLastMonthEnd;
                // 当月收入
                BigDecimal totalTradeAmountCurrentMonth = endBeforeCurrentMonthEnd
                        ? tradeAmount.subtract(totalTradeAmountUntilLastMonthEnd)
                        : amountPerDay.multiply(BigDecimal.valueOf(usedDaysForCurrentMonth)).setScale(10, RoundingMode.HALF_UP);
                // 截止本月末累计已确认收入
                BigDecimal totalTradeAmountUntilCurrentMonthEnd = totalTradeAmountUntilLastMonthEnd.add(totalTradeAmountCurrentMonth)
                        .setScale(SCALE, RoundingMode.HALF_UP);
                if (totalTradeAmountUntilCurrentMonthEnd.compareTo(tradeAmountWithoutTax) > 0) {
                    // 兜底处理, 防止超过最大值
                    totalTradeAmountUntilCurrentMonthEnd = tradeAmountWithoutTax;
                }
                // 待确收
                BigDecimal willReceiveAmount = unsubscribed || endBeforeCurrentMonthEnd
                        ? BigDecimal.ZERO
                        : tradeAmountWithoutTax.subtract(totalTradeAmountUntilCurrentMonthEnd).setScale(SCALE, RoundingMode.HALF_UP);

                AlphaRankBillItemDTO item = (AlphaRankBillItemDTO) new AlphaRankBillItemDTO()
                        .setBillMonth(FORMATTER_BILL_MONTH.format(targetDate))
                        .setUserId(order.getUserId())
                        .setTradeNo(tradeRecord.getTradeNo())
                        .setVipLevel(vipLevel)
                        .setCurrencyUnit(AEPaymentConstants.CURRENCY_CODE)
                        .setTradeAmount(tradeAmount.setScale(3, RoundingMode.HALF_UP))
                        .setTaxRate(taxRate)
                        .setTaxAmount(taxAmount.setScale(3, RoundingMode.HALF_UP))
                        .setTradeAmountWithoutTax(tradeAmountWithoutTax.setScale(3, RoundingMode.HALF_UP))
                        .setAmountPerDay(amountPerDay.setScale(3, RoundingMode.HALF_UP))
                        .setTradeTime(tradeTime)
                        .setVipStartTime(performStartTime)
                        .setVipEndTime(performEndTime)
                        .setVipDays(planDays)
                        .setCanUnsubscribeAtCurrentMonthEnd(canUnsubscribeAtCurrentMonthEnd)
                        .setUnsubscribeEndTime(trialEndTime)
                        .setUnsubscribed(unsubscribed)
                        .setUnsubscribedTime(unsubscribedTime)
                        .setTotalTradeAmountAtCurrentMonth(totalTradeAmountAtCurrentMonth.setScale(3, RoundingMode.HALF_UP))
                        .setUsedDaysUntilLastMonthEnd(usedDaysUntilLastMonthEnd)
                        .setUsedDaysUtilCurrentMonthEnd(usedDaysUtilCurrentMonthEnd)
                        .setUsedDaysForCurrentMonth(usedDaysForCurrentMonth)
                        .setTotalTradeAmountUntilLastMonthEnd(totalTradeAmountUntilLastMonthEnd.setScale(3, RoundingMode.HALF_UP))
                        .setTotalTradeAmountCurrentMonth(totalTradeAmountCurrentMonth.setScale(3, RoundingMode.HALF_UP))
                        .setTotalTradeAmountUntilCurrentMonthEnd(totalTradeAmountUntilCurrentMonthEnd)
                        .setWillReceiveAmount(willReceiveAmount.setScale(3, RoundingMode.HALF_UP));
                item.setTrialStartTime(performStartTime);
                item.setTrialEndTime(trialEndTime);
                item.setUnsubscribedInTrial(unsubscribedTime != null && unsubscribedTime.before(trialEndTime));
                item.setOrderSource(order.getAppCode() + order.getSubscriptionPayType());

                IpAddressInfo ipAddressInfo = payRecord.get(0).getAttributes().getIpAddressInfoNotNull();
                item.setCityName(ipAddressInfo.getCityName());
                item.setCountryName(ipAddressInfo.getCountryName());

                items.add(item);
            }


            if (refundRecord != null) {

                BillItemDTO refundItem = new AlphaRankBillItemDTO()
                .setBillMonth(FORMATTER_BILL_MONTH.format(targetDate))
                        .setTradeNo(refundRecord.getTradeNo())
                        .setVipLevel(vipLevel)
                        .setCurrencyUnit(AEPaymentConstants.CURRENCY_CODE);
                refundItem.setTradeAmount(refundRecord.getTradeAmount().negate());
                refundItem.setTradeTime(refundRecord.getTradeTime());
                items.add(refundItem);
            }
        }

        String title = String.format("%s账单", FORMATTER_BILL_TITLE.format(targetDate));
        return new QueryBillResultDTO()
                .setTitle(title)
                .setTotalCount(items.size())
                .setBillItems(items);
    }


    private static int computePlanDays(Date startTime, Date endTime, ZonedDateTime monthEndTime, long totalPlanDays) {
        ZoneId zoneId = ZoneId.of("Asia/Shanghai");

        ZonedDateTime startZdt = startTime.toInstant().atZone(zoneId);
        ZonedDateTime endZdt = endTime.toInstant().atZone(zoneId);

        ZonedDateTime finalEndTime = endZdt.isBefore(monthEndTime) ? endZdt : monthEndTime;

        if (startZdt.isBefore(finalEndTime) || startZdt.isEqual(finalEndTime)) {
            long diffDays = ChronoUnit.DAYS.between(startZdt.toLocalDate(), finalEndTime.toLocalDate()) + 1; // 包含当天
            return (int) Math.min(diffDays, totalPlanDays);
        }

        return 0;
    }
    private static <T, R> List<R> transform(List<T> list, Function<T, R> converter) {
        return list.stream()
                .map(converter)
                .collect(Collectors.toList());
    }

    private static <K, V> Map<K, V> extractMapping(List<V> dataList, Function<V, K> keyConvertor) {
        return extractMapping(dataList, keyConvertor, null);
    }

    private static <K, V> Map<K, V> extractMapping(List<V> dataList, Function<V, K> keyConvertor, Consumer<V> peekConsumer) {
        return dataList.stream()
                .peek(e -> {
                    if (peekConsumer != null) {
                        peekConsumer.accept(e);
                    }
                })
                .collect(Collectors.toMap(keyConvertor, e -> e, (v1, v2) -> v1));
    }
}
