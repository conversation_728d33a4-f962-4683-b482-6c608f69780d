package com.alibaba.copilot.enabler.service.base.controller;

import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.domain.base.utils.SpringContextUtils;
import com.alibaba.copilot.enabler.infra.base.utils.EnvUtils;
import com.alibaba.fastjson.JSONArray;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Arrays;
import java.util.Objects;

/**
 * 为了便于快速enabler层能够在开发联调阶段进行快速测试
 *
 * <AUTHOR>
 * @version 2023/10/31
 */
@Slf4j
@RestController
@RequestMapping("/api/test")
@Api(tags = "enabler层的通用测试接口")
public class TestController {

    @PostMapping("/invoke")
    public Object invoke(@RequestBody InvokeDTO dto) {
        EnvUtils.interceptForOnline();
        String beanName = dto.getBeanName();
        String methodName = dto.getMethodName();
        JSONArray params = dto.getParams();

        Object bean = getBean(beanName);
        Class<?> beanType = bean.getClass();
        Method method = Arrays.stream(beanType.getMethods())
                .filter(it -> Objects.equals(it.getName(), methodName))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("找不到方法: " + methodName));
        method.setAccessible(true);
        Parameter[] parameters = method.getParameters();
        Assertor.asserts(parameters.length == params.size(), "方法长度不匹配");

        Object[] args = new Object[params.size()];
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            Class<?> paramType = parameter.getType();
            args[i] = params.getObject(i, paramType);
        }

        try {
            return method.invoke(bean, args);
        } catch (Exception e) {
            log.error("TestController#invoke, invoke error", e);
            return "Error: " + e.getMessage();
        }
    }

    @NotNull
    private static Object getBean(String beanName) {
        try {
            beanName = beanName.substring(0, 1).toLowerCase() + beanName.substring(1);
            return SpringContextUtils.getBean(beanName);
        } catch (Exception ignored) {
            try {
                return SpringContextUtils.getBean(beanName + "Impl");
            } catch (Exception ignored1) {
            }
        }
        throw new RuntimeException("找不到对应的Bean: " + beanName);
    }

    @Data
    @ApiModel(description = "入参信息的结构体")
    public static class InvokeDTO implements Serializable {

        @ApiModelProperty(notes = "对应Spring中Bean的名称")
        private String beanName;

        @ApiModelProperty(notes = "对应Bean中的方法名称")
        private String methodName;

        @ApiModelProperty(notes = "对应方法中的参数信息")
        private JSONArray params;
    }
}
