package com.alibaba.copilot.enabler.service.user.service;

import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.user.dto.UserAppRelationDTO;
import com.alibaba.copilot.enabler.client.user.request.AppBindingRequest;
import com.alibaba.copilot.enabler.client.user.request.UserAppRelationQuery;
import com.alibaba.copilot.enabler.client.user.service.UserAppRelationService;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.model.UserAppRelation;
import com.alibaba.copilot.enabler.domain.user.repository.UserAppRelationRepository;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import com.alibaba.copilot.enabler.infra.base.constants.ErrorCodes;
import com.alibaba.copilot.enabler.service.user.factory.UserAppRelationDTOConverter;
import com.alibaba.copilot.enabler.service.user.factory.UserLoginBindAppRelationBuilder;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/4
 */
@Slf4j
@Service
public class UserAppRelationServiceImpl implements UserAppRelationService {

    @Resource
    private UserAppRelationRepository userAppRelationRepository;

    @Resource
    private UserRepository userRepository;

    @Override
    public UserAppRelationDTO queryUserAppRelation(UserAppRelationQuery userAppRelationQuery) {
        if (userAppRelationQuery == null) {
            return null;
        }

        UserAppRelation userAppRelation = userAppRelationRepository.getUserAppRelation(
                userAppRelationQuery.getUserId(),
                userAppRelationQuery.getAppCode(),
                userAppRelationQuery.getEmail());
        if (userAppRelation == null) {
            return null;
        }

        return UserAppRelationDTOConverter.INSTANCE.convertA2B(userAppRelation);
    }

    @Override
    public List<UserAppRelationDTO> queryUserAppRelationList(UserAppRelationQuery userAppRelationQuery) {
        Assertor.asserts(userAppRelationQuery != null && userAppRelationQuery.getUserId() != null, ErrorCodes.NULL_USER_ID);

        List<UserAppRelation> userAppRelations = userAppRelationRepository.queryUserAppRelationList(userAppRelationQuery);
        if (CollectionUtils.isEmpty(userAppRelations)) {
            return Lists.newArrayList();
        }

        return UserAppRelationDTOConverter.INSTANCE.convertA2B(userAppRelations);
    }

    @Override
    public List<UserAppRelationDTO> queryListByAppCodeAndUserId(String appCode, Long userId) {
        Assertor.assertNotBlank(appCode, "appCode is blank");
        Assertor.assertNonNull(userId, "userId is null");

        UserAppRelationQuery userAppRelationQuery = UserAppRelationQuery.builder()
                .appCode(appCode)
                .userId(userId)
                .build();
        return queryUserAppRelationList(userAppRelationQuery);
    }

    @Override
    public UserAppRelationDTO createUserAppRelation(Long userId, AppBindingRequest appBindingRequest) {
        log.info("UserAppRelationServiceImpl.createUserAppRelation, userId={}, appBindingRequest={}", userId, JSON.toJSONString(appBindingRequest));

        User user = userRepository.getUser(userId);
        if (user == null) {
            log.warn("UserAppRelationServiceImpl#createUserAppRelation error, user {} is not exist", userId);
            throw new RuntimeException(ErrorCodes.USER_NOT_EXIST.getDisplayMessage());
        }

        UserLoginBindAppRelationBuilder builder = new UserLoginBindAppRelationBuilder();
        builder.setUser(user);
        builder.setAppBindingRequest(appBindingRequest);
        UserAppRelation userAppRelation = builder.build();

        UserAppRelation saveUserAppRelation = userAppRelationRepository.saveUserAppRelation(userAppRelation);

        return UserAppRelationDTOConverter.INSTANCE.convertA2B(saveUserAppRelation);
    }

    /**
     * updateUserAppRelation
     *
     * @param id
     * @param userAppRelationDTO
     * @return
     */
    @Override
    public Boolean updateUserAppRelation(Long id, UserAppRelationDTO userAppRelationDTO) {
        log.info("UserAppRelationServiceImpl.updateUserAppRelation, id={}, userAppRelationDTO={}", id, JSON.toJSONString(userAppRelationDTO));

        UserAppRelation userAppRelation = userAppRelationRepository.getUserAppRelationById(id);
        if (userAppRelation == null) {
            return false;
        }

        if (userAppRelationDTO.getUserId() != null) {
            userAppRelation.setUserId(userAppRelationDTO.getUserId());
        }
        if (StringUtils.isNotBlank(userAppRelationDTO.getEmail())) {
            userAppRelation.setEmail(userAppRelationDTO.getEmail());
        }
        if (StringUtils.isNotBlank(userAppRelationDTO.getAppName())) {
            userAppRelation.setAppName(userAppRelationDTO.getAppName());
        }
        if (StringUtils.isNotBlank(userAppRelationDTO.getAppCode())) {
            userAppRelation.setAppCode(userAppRelationDTO.getAppCode());
        }
        if (StringUtils.isNotBlank(userAppRelationDTO.getAppType())) {
            userAppRelation.setAppType(userAppRelationDTO.getAppType());
        }
        if (StringUtils.isNotBlank(userAppRelationDTO.getBindStatus())) {
            userAppRelation.setBindStatus(userAppRelationDTO.getBindStatus());
        }
        if (StringUtils.isNotBlank(userAppRelationDTO.getBindSource())) {
            userAppRelation.setBindSource(userAppRelationDTO.getBindSource());
        }

        return userAppRelationRepository.updateUserAppRelation(userAppRelation);
    }
}
