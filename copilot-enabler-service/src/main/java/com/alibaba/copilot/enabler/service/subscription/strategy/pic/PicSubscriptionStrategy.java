package com.alibaba.copilot.enabler.service.subscription.strategy.pic;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.copilot.enabler.client.email.dto.DeductSuccessEmailDTO;
import com.alibaba.copilot.enabler.client.email.dto.NotifyBeforeDeductEmailDTO;
import com.alibaba.copilot.enabler.client.email.dto.SubscribeWithAutoRenewEmailDTO;
import com.alibaba.copilot.enabler.client.marketing.dto.FirstMonthDiscountDTO;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.payment.request.TradeRecordsQuery;
import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.dto.CycleFeeDetail;
import com.alibaba.copilot.enabler.client.subscription.dto.NeedPayToSubscribePlanDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.TrialDurationDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.base.dingtalk.DingTalkRequest;
import com.alibaba.copilot.enabler.domain.base.dingtalk.DingTalkService;
import com.alibaba.copilot.enabler.domain.base.utils.TimeUtils;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.response.EmailResponse;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategyConfig;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.*;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.infra.base.utils.EnvUtils;
import com.alibaba.copilot.enabler.infra.email.service.EmailService;
import com.alibaba.copilot.enabler.service.subscription.strategy.BaseSubscriptionStrategy;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.taobao.eagleeye.EagleEye;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * PicCopilot应用的订阅处理策略
 *
 * <AUTHOR>
 * @version 2023/10/12
 */
@Slf4j
@Component
@SubscriptionStrategyConfig(value = AppEnum.PIC_COPILOT, payType = SubscriptionPayType.AEPay)
public class PicSubscriptionStrategy extends BaseSubscriptionStrategy {

    @Resource
    private EmailService emailService;

    @Override
    public TrialDurationDTO computeTrialDuration(ComputeTrialContext context) {
        String appCode = context.getAppCode();
        Long userId = context.getUserId();

        // 有零元购订单时, 就没有试用期了
        List<SubscriptionOrder> historyOrders = Optional.ofNullable(context.getHistoryOrders())
                .orElseGet(() -> subscriptionOrderRepository.getHistoryOrders(userId, appCode));
        boolean hasFreeOrder = historyOrders.stream().anyMatch(order -> order.getAttributes().isFreeOrder());
        if (hasFreeOrder) {
            return new TrialDurationDTO().setIsTrial(false);
        }

        // 查询所有成功的交易
        List<TradeRecord> tradeRecords = queryAllSuccessTradeRecords(appCode, userId);
        tradeRecords = Optional.ofNullable(tradeRecords).orElseGet(Lists::newArrayList)
                .stream().filter(Objects::nonNull)
                .filter(record -> record.getPaymentType() == null || PaymentTypeEnum.INITIATED_PAYMENT.equals(record.getPaymentType()) || PaymentTypeEnum.PERIODIC_DEDUCT.equals(record.getPaymentType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tradeRecords) && tradeRecords.size() > 1) {
            // 交易成功的记录不止一笔时, 就没有试用期了
            return new TrialDurationDTO().setIsTrial(false);
        }

        // 获取试用期开始的时间
        Date trialStartTime = queryTrailStartTime(tradeRecords);

        Long planTrialDays = SwitchConfig.planTrialDaysForPicCopilot;
        if (trialStartTime == null) {
            // 之前没付过钱, 则当前还有满额试用期
            return new TrialDurationDTO()
                    .setIsTrial(true)
                    .setRemainTrialDay(planTrialDays);
        } else {
            // 之前已付过钱, 则先计算试用过的天数
            long triedDay = DateUtil.betweenDay(trialStartTime, new Date(), false);
            // 计算试用期剩余的天数
            long remainDay = planTrialDays - triedDay;
            return new TrialDurationDTO()
                    .setIsTrial(remainDay > 0L)
                    .setRemainTrialDay(remainDay);
        }
    }

    /**
     * 获取试用期开始的时间
     */
    private Date queryTrailStartTime(List<TradeRecord> tradeRecords) {
        log.info("queryTrailStartTime, tradeRecords={}", JSON.toJSONString(tradeRecords));
        if (CollectionUtil.isEmpty(tradeRecords)) {
            // 从未付款
            return null;
        } else {
            // 付款过, 取首个付款流水的时间作为试用期开始时间
            return tradeRecords.stream()
                    .map(TradeRecord::getTradeTime)
                    .filter(Objects::nonNull)
                    .min(Date::compareTo)
                    .orElse(null);
        }
    }

    /**
     * 查询所有成功的交易 (含支付和退款)
     */
    private List<TradeRecord> queryAllSuccessTradeRecords(String appCode, Long userId) {
        TradeRecordsQuery recordsQuery = TradeRecordsQuery.builder()
                .appCode(appCode)
                .userId(userId)
                .status(TradeRecordStatus.SUCC)
                .build();
        return tradeRecordRepository.queryTradeRecords(recordsQuery);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateOrderAndTradeResultDTO createOrderAndTrade(SubscribeContext context) {
        SubscriptionOrder effcOrder = context.getOldOrder();
        SubscriptionPlan effcPlan = context.getOldPlan();
        SubscriptionPlan activatedPlan = context.getNewPlan();

        // 判断是否要立即生效
        boolean effectRightNow = isEffectRightNow(effcPlan, activatedPlan);
        if (effectRightNow) {
            // 立即生效, 创建新订单和新流水, 但不退款
            return orderDomainService.createPreparePayOrder(context);
        } else {
            // 当前套餐结束再生效, 设置下个更换的套餐
            if (effcOrder != null) {
                effcOrder.setNextPlanId(activatedPlan.getId());
                effcOrder.setNextPlanName(activatedPlan.getName());
                // 下个套餐是免费时, 不需要自动续费; 收费时, 需要自动续费(主要针对订阅之前取消自动续费的场景);
                effcOrder.setAutoRenew(!activatedPlan.isFree());
                subscriptionOrderRepository.saveSubscriptionOrder(effcOrder);
            }
            // 不会产生流水和新订单
            return new CreateOrderAndTradeResultDTO();
        }
    }

    @Override
    protected Date computeOrderEndTimeForTrial(Date now, long planDays, long remainTrialDays) {
        // 试用期订单, 结束时间 = 当前时间 + 套餐持续时间 (其中包含试用期的时间, 如: 7 + 23 = 30)
        return DateUtils.addDays(now, (int) planDays);
    }

    @Override
    public void handleRefundLogicWhenOldOrderCompleted(@Nonnull SubscriptionOrder oldEffectOrder) {
        log.info("handleRefundLogicWhenOldOrderCompleted, DO NOT need to refund for PicCopilot");
    }

    @Override
    protected boolean needPayToSubscribePlanWhenPlanNeedCost(NeedPayToSubscribePlanDTO dto, SubscriptionPlan targetPlan) {
        // PicCopilot场景下, 要根据套餐切换情况来决定, 最终的订单是否需要支付
        String appCode = dto.getAppCode();
        Long userId = dto.getUserId();

        SubscriptionOrder effectOrder = subscriptionOrderRepository.queryEffectOrder(appCode, userId);
        log.info("needPayToSubscribePlanWhenPlanNeedCost, queryEffectOrder finished, result={}", JSON.toJSONString(effectOrder));

        if (effectOrder == null) {
            // 当前没有生效订单, 且新订单收费时, 需要支付
            return true;
        }

        // 查询当前生效的套餐
        Long oldPlanId = effectOrder.getSubscriptionPlanId();
        SubscriptionPlan oldPlan = subscriptionPlanRepository.queryByPlanId(oldPlanId, false);
        log.info("needPayToSubscribePlanWhenPlanNeedCost, oldPlan={}", JSON.toJSONString(oldPlan));

        // 判断是否立即生效
        boolean effectRightNow = isEffectRightNow(oldPlan, targetPlan);
        log.info("needPayToSubscribePlanWhenPlanNeedCost, effectRightNow={}", effectRightNow);

        // 需要立即生效时, 需要支付; 否则不需要支付(如, 高套餐切换低套餐的场景);
        return effectRightNow;
    }

    /**
     * 判断是否要立即生效
     */
    private boolean isEffectRightNow(@Nullable SubscriptionPlan oldPlan, SubscriptionPlan newPlan) {
        boolean isNewSubscribe = oldPlan == null || oldPlan.isFree();
        if (isNewSubscribe) {
            // 新订的套餐, 是立即生效的
            log.info("isEffectRightNow, isNewSubscribe");
            return true;
        }

        // 老套餐
        BigDecimal oldPlanPrice = oldPlan.getPrice();
        Long oldPlanDuration = oldPlan.getDuration();
        DurationUnit oldPlanDurationUnit = oldPlan.getDurationUnit();
        long oldPlanDays = TimeUtils.calculateDayCount(oldPlanDuration, oldPlanDurationUnit);
        BigDecimal oldPlanPricePerDay = oldPlanPrice.divide(BigDecimal.valueOf(oldPlanDays), RoundingMode.HALF_UP);
        log.info("isEffectRightNow, oldPlanPrice={}, oldPlanDays={}, oldPlanPricePerDay={}",
                oldPlanPrice, oldPlanDays, oldPlanPricePerDay);

        // 新套餐
        BigDecimal newPlanPrice = newPlan.getPrice();
        Long newPlanDuration = newPlan.getDuration();
        DurationUnit newPlanDurationUnit = newPlan.getDurationUnit();
        long newPlanDays = TimeUtils.calculateDayCount(newPlanDuration, newPlanDurationUnit);
        BigDecimal newPlanPricePerDay = newPlanPrice.divide(BigDecimal.valueOf(newPlanDays), RoundingMode.HALF_UP);
        log.info("isEffectRightNow, newPlanPrice={}, newPlanDays={}, newPlanPricePerDay={}",
                newPlanPrice, newPlanDays, newPlanPricePerDay);

        // 计算是否是低套餐到到高套餐
        boolean low2High = oldPlanPricePerDay.compareTo(newPlanPricePerDay) < 0;
        log.info("isEffectRightNow, low2High={}", low2High);
        if (low2High) {
            // 月度低级 => 月度高级
            boolean monthLow2MonthHigh = oldPlanDurationUnit == DurationUnit.MONTH && newPlanDurationUnit == DurationUnit.MONTH;
            // 月度低级 => 年度高级
            boolean monthLow2YearHigh = oldPlanDurationUnit == DurationUnit.MONTH && newPlanDurationUnit == DurationUnit.YEAR;
            // 年度低级 => 年度高级
            boolean yearLow2YearHigh = oldPlanDurationUnit == DurationUnit.YEAR && newPlanDurationUnit == DurationUnit.YEAR;
            log.info("isEffectRightNow, monthLow2MonthHigh={}, monthLow2YearHigh={}, yearLow2YearHigh={}",
                    monthLow2MonthHigh, monthLow2YearHigh, yearLow2YearHigh);

            // 是以上三种情况的任一种时, 才会立即生效; 否则, 当前套餐结束再生效;
            return monthLow2MonthHigh || monthLow2YearHigh || yearLow2YearHigh;
        } else {
            // 不是低到高套餐时, 当前套餐结束再生效
            return false;
        }
    }

    @Resource(name = "picEmailDingTalkService")
    private DingTalkService dingTalkService;

    @Override
    public EmailResponse sendEmail(Long userId, Object emailInfoObj) {
        // 目前仅PicCopilot场景支持邮件发送, 拓展其他场景下, 需要改造EmailTemplate及其解析的逻辑
        String appCode = getAppEnum().getCode();
        EmailResponse emailResponse = emailService.sendEmail(appCode, userId, emailInfoObj);

        // 发送钉钉消息
        sendDingTalkMessage(userId, emailInfoObj, emailResponse);
        return emailResponse;
    }


    private void sendDingTalkMessage(Long userId, Object emailInfoObj, EmailResponse emailResponse) {
        try {
//            if (!EnvUtils.isOnline()) {
//                // 不是线上环境，不发送钉钉消息
//                return;
//            }
            String scene;
            if (emailInfoObj instanceof NotifyBeforeDeductEmailDTO) {
                scene = "Pic 代扣提前通知邮件";
            } else if (emailInfoObj instanceof DeductSuccessEmailDTO) {
                scene = "Pic 支付成功通知邮件";
            } else if (emailInfoObj instanceof SubscribeWithAutoRenewEmailDTO) {
                scene = "Pic 连续订阅通知邮件";
            } else {
                scene = "Pic 场景 " + emailInfoObj.getClass().getSimpleName();
            }
            String content = "[场景] " + EnvUtils.getEnv() + " - " + scene
                    + "\n[结果] " + (Objects.equals(true, emailResponse.getSuccess()) ? "成功" : "失败，" + emailResponse.getErrorMessage())
                    + "\n[信息] userId=" + userId + ", traceId=" + EagleEye.getTraceId();
            DingTalkRequest request = new DingTalkRequest();
            request.setContent(content);
            dingTalkService.sendMessage(request);
        } catch (Exception e) {
            log.error("pic send dingTalk Message error, userId={}", userId, e);
        }
    }


    @Override
    public ComputeNewPlanPriceResultDTO computeNewPlanPrice(ComputeNewPlanPriceContext context) {
        // 1. 判断是否需要支付
        Boolean needPay = adjustNeedPayByComputePriceContext(context);
        log.info("computeNewPlanPrice, needPay={}", needPay);

        // 2. 套餐金额
        BigDecimal planAmount = Optional.ofNullable(context.getNewPlan())
                .filter(plan -> !plan.isFree())
                .map(SubscriptionPlan::getPrice)
                .orElse(BigDecimal.ZERO);
        log.info("computeNewPlanPrice, planAmount={}", planAmount);

        // 3. 折扣金额 (注: PicCopilot场景下, 只有首月减免的折扣)
        BigDecimal discountAmount = Optional.ofNullable(context.getFirstMonthDiscountDTO())
                .map(FirstMonthDiscountDTO::getDiscountPrice)
                .orElse(null);
        log.info("computeNewPlanPrice, discountAmount={}", discountAmount);

        // 4. 计算上个套餐还能抵扣的钱
        BigDecimal deductedAmountOfLastPlan = computeDeductedAmountOfLastPlan(context);
        log.info("computeNewPlanPrice, deductedAmountOfLastPlan={}", deductedAmountOfLastPlan);

        // 5. 计算最终需要支付的钱
        BigDecimal newOrderAmount = Optional.ofNullable(discountAmount).orElse(planAmount);
        BigDecimal payAmount = newOrderAmount.compareTo(deductedAmountOfLastPlan) <= 0 ?
                BigDecimal.ZERO : newOrderAmount.subtract(deductedAmountOfLastPlan);
        boolean finalNeedPay = needPay && payAmount.compareTo(BigDecimal.ZERO) > 0;
        BigDecimal finalPayAmount = finalNeedPay ? payAmount : BigDecimal.ZERO;

        ComputeNewPlanPriceResultDTO result = new ComputeNewPlanPriceResultDTO()
                .setNeedPay(finalNeedPay)
                .setPlanAmount(planAmount)
                .setDiscountAmount(discountAmount)
                .setDeductedAmountOfLastPlan(deductedAmountOfLastPlan)
                .setPayAmount(finalPayAmount);
        log.info("computeNewPlanPrice, result={}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 计算上个套餐还能抵扣的钱
     */
    private BigDecimal computeDeductedAmountOfLastPlan(ComputeNewPlanPriceContext context) {
        SubscriptionPlan newPlan = context.getNewPlan();
        SubscriptionOrder oldOrder = context.getOldOrder();
        SubscriptionPlan oldPlan = context.getOldPlan();
        TradeRecord oldPayRecord = context.getOldPayRecord();
        Date now = context.getNow();

        // 1. 之前没有支付过, 或支付过但订单已到期时, 不能抵扣
        if (oldPayRecord == null
                || oldPlan == null
                || oldPlan.isFree()
                || oldOrder == null
                || oldOrder.getPerformEndTime().compareTo(now) < 0) {
            String oldOrderEndTime = Optional.ofNullable(oldOrder)
                    .map(SubscriptionOrder::getPerformEndTime)
                    .map(date -> DateUtil.format(date, DatePattern.NORM_DATETIME_PATTERN))
                    .orElse("");
            log.info("computeDeductedAmountOfLastPlan, oldOrderEndTime={}", oldOrderEndTime);
            return BigDecimal.ZERO;
        }

        // 2. 之前支付过, 且订单还没到期时, 根据使用时间进行抵扣计算
        DurationUnit oldPlanDurationUnit = oldPlan.getDurationUnit();
        DurationUnit newPlanDurationUnit = newPlan.getDurationUnit();
        BigDecimal payAmount = oldPayRecord.getTradeAmount();
        if (oldPlanDurationUnit != DurationUnit.YEAR || newPlanDurationUnit != DurationUnit.YEAR) {
            // 2.1 新老套餐, 有任意一个不是年度套餐时, 都全额抵扣
            log.info("computeDeductedAmountOfLastPlan, oldPlanDurationUnit={}, newPlanDurationUnit={}", oldPlanDurationUnit, newPlanDurationUnit);
            return payAmount;
        }

        // 2.2 年度低级 => 年度高级 时, 计算逻辑为 Price(Old) * (1 -  usedMonth / 12f)
        long usedMills = now.getTime() - oldPayRecord.getTradeTime().getTime();
        int usedMonth = (int) Math.floor(usedMills * 1f / Duration.ofDays(30).toMillis());
        log.info("computeDeductedAmountOfLastPlan, usedMonth={}", usedMonth);
        if (usedMonth >= 12) {
            // 用满一年, 算全额
            return BigDecimal.valueOf(0);
        } else if (usedMonth <= 0) {
            // 未满一月, 算没用
            return payAmount;
        } else {
            // 其他情况, 按月份比例折算
            return payAmount.multiply(BigDecimal.valueOf(1 - usedMonth / 12f))
                    .setScale(2, RoundingMode.HALF_UP);
        }
    }

    @Override
    public ComputeCycleFeeResultDTO computeCycleFee(ComputeNewPlanPriceContext context) {
        List<CycleFeeDetail> cycleFeeDetails = new ArrayList<>();
        LocalDate selectedPlanInfoDate = LocalDate.now();

        // 1. 计算价格信息
        ComputeNewPlanPriceResultDTO priceResult = computeNewPlanPrice(context);
        BigDecimal deductedAmountOfLastPlan = priceResult.getDeductedAmountOfLastPlan();

        // 2. 添加折扣或抵扣信息
        if (deductedAmountOfLastPlan.compareTo(BigDecimal.ZERO) <= 0) {
            // 2.1 没有抵扣时, 添加折扣信息
            selectedPlanInfoDate = addDiscountCycleDetail(selectedPlanInfoDate, cycleFeeDetails, context);
        } else {
            // 2.2 有抵扣时, 添加抵扣信息
            selectedPlanInfoDate = addDeductCycleDetail(selectedPlanInfoDate, cycleFeeDetails, context, priceResult);
        }

        // 3. 添加最终套餐信息
        selectedPlanInfoDate = addFinalPlanCycleDetail(selectedPlanInfoDate, cycleFeeDetails, context);

        return new ComputeCycleFeeResultDTO()
                .setCycleFeeDetails(cycleFeeDetails)
                .setFinalPayAmount(priceResult.getPayAmount());
    }

    /**
     * 组装折扣信息
     */
    private LocalDate addDiscountCycleDetail(LocalDate date, List<CycleFeeDetail> details, ComputeNewPlanPriceContext context) {
        FirstMonthDiscountDTO firstMonthDiscountDTO = context.getFirstMonthDiscountDTO();
        SubscriptionPlan newPlan = context.getNewPlan();
        if (firstMonthDiscountDTO == null || firstMonthDiscountDTO.getDiscountRuleId() == null) {
            return date;
        }

        // PicCopilot的周期单位需要转化为天
        long remainDiscountDays = 30;
        LocalDate endTime = date.plusDays(remainDiscountDays);
        String dateText = String.format("%s - %s", formatCycleDetailDate(date), formatCycleDetailDate(endTime));

        CycleFeeDetail cycleDetail = CycleFeeDetail.builder()
                .date(dateText)
                .cycleFee(firstMonthDiscountDTO.getDiscountPrice())
                .feeDescription(null)
                .durationUnit(null)
                .duration(null)
                .discountDescription(null)
                .build();
        details.add(cycleDetail);


        return date.plusDays(remainDiscountDays);
    }

    /**
     * 组装抵扣信息
     */
    private LocalDate addDeductCycleDetail(LocalDate date, List<CycleFeeDetail> details, ComputeNewPlanPriceContext context, ComputeNewPlanPriceResultDTO priceResult) {
        BigDecimal planAmount = priceResult.getPlanAmount();
        BigDecimal discountAmount = priceResult.getDiscountAmount();
        BigDecimal newOrderAmount = Optional.ofNullable(discountAmount).orElse(planAmount);
        BigDecimal deductedAmountOfLastPlan = priceResult.getDeductedAmountOfLastPlan();
        BigDecimal payAmount = priceResult.getPayAmount();
        SubscriptionPlan newPlan = context.getNewPlan();

//        String description = String.format(
//                "%s - %s = %s",
//                formatAmount(newOrderAmount),
//                formatAmount(deductedAmountOfLastPlan),
//                formatAmount(payAmount)
//        );

        // PicCopilot的周期单位需要转化为天
        long planDays = TimeUtils.calculateDayCount(newPlan.getDuration(), newPlan.getDurationUnit());

        CycleFeeDetail cycleDetail = CycleFeeDetail.builder()
                .date(formatCycleDetailDate(date))
                .durationUnit(DurationUnit.DAY.name())
                .duration(planDays)
                .cycleFee(payAmount)
                .feeDescription(SwitchConfig.deductDescriptionForPicCopilot)
                .build();
        details.add(cycleDetail);

        return date.plusDays(planDays);
    }

    /**
     * 金额格式化
     */
    private BigDecimal formatAmount(BigDecimal amount) {
        return amount.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 组装最终套餐信息
     */
    private LocalDate addFinalPlanCycleDetail(LocalDate date, List<CycleFeeDetail> details, ComputeNewPlanPriceContext context) {
        SubscriptionPlan newPlan = context.getNewPlan();

        // PicCopilot的周期单位需要转化为天
        long planDays = TimeUtils.calculateDayCount(newPlan.getDuration(), newPlan.getDurationUnit());

        CycleFeeDetail cycleDetail = CycleFeeDetail.builder()
                .date(formatCycleDetailDate(date))
                .durationUnit(DurationUnit.DAY.name())
                .duration(planDays)
                .cycleFee(newPlan.getPrice())
                .feeDescription(newPlan.getDescription())
                .build();
        details.add(cycleDetail);

        return date.plusDays(planDays);
    }

    @Override
    protected String formatCycleDetailDate(LocalDate date) {
        return date.format(DateTimeFormatter.ofPattern("yyyy-M-d"));
    }
}
