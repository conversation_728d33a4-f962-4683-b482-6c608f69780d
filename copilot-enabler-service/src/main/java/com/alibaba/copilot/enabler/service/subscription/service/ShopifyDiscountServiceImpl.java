package com.alibaba.copilot.enabler.service.subscription.service;

import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.subscription.dto.ShopifyDiscountDTO;
import com.alibaba.copilot.enabler.domain.subscription.dto.ShopifyDiscountResultDTO;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.domain.subscription.service.ShopifyDiscountService;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.infra.subscription.vo.SubsciptionShareCodeVO;
import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/10/30
 */
@Slf4j
@Service
public class ShopifyDiscountServiceImpl implements ShopifyDiscountService {

    @Resource
    private SubscriptionPlanRepository subscriptionPlanRepository;

    @Override
    public ShopifyDiscountResultDTO computeDiscount(ShopifyDiscountDTO dto) {
        log.info("computeDiscount, dto={}", JSON.toJSONString(dto));

        SubscriptionPlan subscriptionPlan = Optional.ofNullable(dto.getPlan())
                .orElseGet(() -> subscriptionPlanRepository.queryByPlanId(dto.getPlanId(), false));
        if (subscriptionPlan.isFree()) {
            // 免费套餐不享有折扣信息
            return new ShopifyDiscountResultDTO().setHasDiscount(false);
        }

        AppEnum appEnum = AppEnum.getAppByCode(dto.getAppCode());
        if ((appEnum != AppEnum.SEO_COPILOT) && (appEnum != AppEnum.SEO_COPILOT_SITE)) {
            // Shopify订阅中, 仅对SEO应用做折扣处理
            return new ShopifyDiscountResultDTO().setHasDiscount(false);
        }

        // 获取SEO应用的折扣信息
        DiscountRateInfo discountInfoForSeo = getDiscountInfoForSeo(subscriptionPlan.getDurationUnit());
        if (discountInfoForSeo == null) {
            // 获取不到折扣信息的场景
            return new ShopifyDiscountResultDTO().setHasDiscount(false);
        }

        // 折扣已过期
        Date currentDate = new Date();
        if (discountInfoForSeo.discountEndTime != null && currentDate.after(discountInfoForSeo.discountEndTime)) {
            return new ShopifyDiscountResultDTO()
                    .setHasDiscount(false)
                    .setDiscountEndTime(discountInfoForSeo.discountEndTime);
        }
        //所有用户统一折扣
        BigDecimal planPrice = subscriptionPlan.getPrice();
        BigDecimal discountRate = discountInfoForSeo.discountRate;
        // 折扣价 = 原价 * 折扣率, 再四舍五入保留两位小数
        BigDecimal discountPrice = planPrice.multiply(discountRate)
                .setScale(2, RoundingMode.HALF_UP);

        //Attributes中的优惠价格,使用折扣码时的原价,折扣周期之后需要变成原价也就是这个价格
        BigDecimal originDiscountPrice = null;
        //有效的折扣码
        String effectiveShareCode = null;

        // 如果折扣价是已经指定好的，替换成指定价，并且反算一个折扣rate
        if (subscriptionPlan.getAttributes() != null && subscriptionPlan.getAttributes().containsKey("discountPrice")) {
            discountPrice = BigDecimal.valueOf(subscriptionPlan.getAttributes().getAsDouble("discountPrice"));
            originDiscountPrice = discountPrice;
            discountRate = discountPrice.divide(planPrice, 5, RoundingMode.HALF_UP);
        }

        if (StringUtils.isNotBlank(dto.getShareCode()) && StringUtils.equals(dto.getAppCode(), "SEO_COPILOT")) {
            //SEO_COPILOT 有折扣码的用户(临时)
            String durationUnit = subscriptionPlan.getDurationUnit() == DurationUnit.YEAR ? "YEAR" : subscriptionPlan.getDurationUnit() == DurationUnit.MONTH ? "MONTH" : "OTHER";
            String discountPriceKey = subscriptionPlan.getName() + "_" + durationUnit + "_" + dto.getShareCode();
            log.info("discountPriceKey={}", discountPriceKey);
            if (SwitchConfig.shareCodeSubscriptionConfig.get(discountPriceKey) != null) {
                SubsciptionShareCodeVO subsciptionShareCodeVO = SwitchConfig.shareCodeSubscriptionConfig.get(discountPriceKey);
                discountPrice = BigDecimal.valueOf(subsciptionShareCodeVO.getSharePrice());
                discountRate = discountPrice.divide(originDiscountPrice != null ? originDiscountPrice : planPrice, 5, RoundingMode.HALF_UP);
                effectiveShareCode = dto.getShareCode();
            }
        }

        return new ShopifyDiscountResultDTO()
                .setHasDiscount(true)
                .setDiscountRate(discountRate)
                .setDiscountPrice(discountPrice)
                .setEffectiveShareCode(effectiveShareCode)
                .setOriginDiscountPrice(originDiscountPrice)
                .setDiscountEndTime(discountInfoForSeo.discountEndTime);
    }

    /**
     * 获取SEO应用的折扣信息
     */
    @Nullable
    private static DiscountRateInfo getDiscountInfoForSeo(DurationUnit planDurationUnit) {
        //这个折扣用法暂时舍弃，实际上都是在用一口价的方式，应该所有的折扣都走这里，不需要订阅计划表里的attributes，下次再改
        switch (planDurationUnit) {
            case MONTH:
                BigDecimal monthDiscountRate = BigDecimal.valueOf(SwitchConfig.shopifySubscriptionMonthDiscountRateForSeo);
//                Date discountEndTime = SwitchConfig.shopifySubscriptionMonthDiscountEndTimeForSeo;
                return new DiscountRateInfo(monthDiscountRate, null);
            case YEAR:
                BigDecimal yearDiscountRate = BigDecimal.valueOf(SwitchConfig.shopifySubscriptionYearDiscountRateForSeo);
                return new DiscountRateInfo(yearDiscountRate, null);
            default:
                // 只对月年套餐做折扣处理
                return null;
        }
    }

    @AllArgsConstructor
    private static class DiscountRateInfo {
        /**
         * 折扣率
         */
        private final BigDecimal discountRate;

        /**
         * 折扣结束时间 (为空时, 长期享有折扣)
         */
        private final Date discountEndTime;
    }
}
