package com.alibaba.copilot.enabler.service.payment.metaq.handler;

import com.alibaba.aepay.fund.business.api.payment.dto.notify.AuthorizationNotifyDTO;
import com.alibaba.aepay.fund.business.api.payment.enums.AuthorizationNotifyTypeEnum;
import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageStatusEnum;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageTypeEnum;
import com.alibaba.copilot.enabler.domain.payment.model.MessageInfo;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentToken;
import com.alibaba.copilot.enabler.domain.payment.repository.PaymentTokenRepository;
import com.alibaba.copilot.enabler.domain.payment.service.OrderDomainService;
import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * Alipay签约授权结果结果通知handler
 *
 * <AUTHOR>
 * @version 2024/1/11
 */
@Slf4j
@Component
public class AlipayAuthNotifyHandler {

    @Resource
    private PaymentTokenRepository paymentTokenRepository;

    @Resource
    private OrderDomainService orderDomainService;

    @Transactional(rollbackFor = Exception.class, noRollbackFor = BizException.class)
    public void handle(String msgId, String notifyContentStr, AuthorizationNotifyDTO notifyDTO) {
        String authorizationNotifyType = notifyDTO.getAuthorizationNotifyType();
        Assertor.assertNotBlank(authorizationNotifyType, "notifyDTO is null");

        AuthorizationNotifyTypeEnum authorizationNotifyTypeEnum = AuthorizationNotifyTypeEnum.valueOf(authorizationNotifyType);
        final PaymentToken paymentToken;
        switch (authorizationNotifyTypeEnum) {


            case TOKEN_CREATED:
                // 授权成功
                String authState = notifyDTO.getAuthState();
                String authedToken = notifyDTO.getAccessToken();
                log.info("payAlarm-alipayAuthNotifyHandler, TOKEN_CREATED, msgId={}, authState={}", msgId, authState);

                Assertor.assertNotBlank(authState, "authState is blank");
                Assertor.assertNotBlank(authedToken, "authedToken is blank");

                paymentToken = paymentTokenRepository.queryByAuthState(authState);
                if (paymentToken == null) {
                    log.warn("payAlarm-alipayAuthN;otifyHandler, TOKEN_CREATED, msgId={}, authState={}, paymentToken is null", msgId, authState);
                } else {
                    boolean updateSuccess = paymentTokenRepository.updateToken(paymentToken.getId(), authedToken);
                    log.info("payAlarm-alipayAuthNotifyHandler, TOKEN_CREATED, msgId={}, authState={}, updateSuccess={}", msgId, authState, updateSuccess);
                }
                break;
            case TOKEN_CANCELED:
                // 取消授权
                String canceledToken = notifyDTO.getAccessToken();
                log.info("payAlarm-alipayAuthNotifyHandler, TOKEN_CANCELED, msgId={}", msgId);
                Assertor.assertNotBlank(canceledToken, "canceledToken is blank");

                paymentToken = paymentTokenRepository.queryByToken(canceledToken);
                if (paymentToken == null) {
                    log.warn("payAlarm-alipayAuthNotifyHandler, TOKEN_CANCELED, msgId={}, delete is null", msgId);
                } else {
                    boolean deleteSuccess = paymentTokenRepository.deleteById(paymentToken.getId());
                    log.info("payAlarm-alipayAuthNotifyHandler, TOKEN_CANCELED, msgId={}, deleteSuccess={}", msgId, deleteSuccess);
                }
                break;
            default:
                return;
        }


        if (paymentToken == null) {
            return;
        }

        // 保存消息记录
        String appCode = paymentToken.getAppCode().getCode();
        Long userId = paymentToken.getUserId();
        MessageInfo messageInfo = MessageInfo.builder()
                .msgId(msgId)
                .notifyContentOrRequestParam(notifyContentStr)
                .response(ConsumeConcurrentlyStatus.CONSUME_SUCCESS.name())
                .typeEnum(MessageTypeEnum.ALIPAY_AUTH_NOTIFY)
                .statusEnum(MessageStatusEnum.SUCCESS)
                .entityId(String.valueOf(userId))
                .build();
        orderDomainService.saveMessageRecord(messageInfo, appCode, userId);
    }
}
