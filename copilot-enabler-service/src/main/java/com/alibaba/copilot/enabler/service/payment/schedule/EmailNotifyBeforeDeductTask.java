package com.alibaba.copilot.enabler.service.payment.schedule;

import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.enabler.client.email.dto.NotifyBeforeDeductEmailDTO;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.base.dingtalk.DingTalkRequest;
import com.alibaba.copilot.enabler.domain.base.dingtalk.DingTalkService;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrderAttributes;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionOrderQuery;
import com.alibaba.copilot.enabler.domain.subscription.response.EmailResponse;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategyFactory;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.infra.base.utils.EnvUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.google.common.collect.Sets;
import com.taobao.eagleeye.EagleEye;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 代扣前发邮件通知的定时任务
 *
 * <AUTHOR>
 * @version 2023/10/25
 */
@Component
public class EmailNotifyBeforeDeductTask extends MapJobProcessor {

    private static final Logger TASK_LOG = LoggerFactory.getLogger("taskLogger");

    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;
    @Resource(name = "picEmailDingTalkService")
    private DingTalkService dingTalkService;

    @Override
    @Monitor(name = "代扣前发邮件通知的定时任务", level = Monitor.Level.P1, layer = Monitor.Layer.OTHER)
    public ProcessResult process(JobContext context) {

        try {
            EagleEye.startTrace(null, this.getClass().getName(), EagleEye.TYPE_CUSTOM_MESSAGE_SUB);

            TASK_LOG.info("EmailNotifyBeforeDeductTask start……");

            // 扫描需要扣款的订单（扫描范围为3天之内扣款的订单）
            Date queryStartTime = new Date();
            Date queryEndTime = DateUtils.addDays(queryStartTime, SwitchConfig.scheduledEmailNotifyForDeductionAdvanceDays);

            SubscriptionOrderQuery subscriptionOrderQuery = SubscriptionOrderQuery.builder()
                    .status(Collections.singletonList(SubscriptionOrderStatus.IN_EFFECT))
                    .hadNextRenew(false)
                    .autoRenew(true)
                    .nextRenewalStartTime(queryStartTime)
                    .nextRenewalEndTime(queryEndTime)
                    .build();

            List<SubscriptionOrder> subscriptionOrders = subscriptionOrderRepository.scanSubscriptionOrders(subscriptionOrderQuery);
            Set<Long> orderIdSet = Sets.newHashSet();
            for (SubscriptionOrder oldOrder : subscriptionOrders) {
                if (oldOrder == null || oldOrder.getSubscriptionPayType() != SubscriptionPayType.AEPay) {
                    continue;
                }
                execute(oldOrder);
                orderIdSet.add(oldOrder.getId());
            }

            TASK_LOG.info("EmailNotifyBeforeDeductTask completed-ordersCount:{},orderIds:{}", orderIdSet.size(), JSON.toJSONString(orderIdSet));
            return new ProcessResult(true);
        } catch (Throwable e) {
            TASK_LOG.error("EmailNotifyBeforeDeductTask error", e);
            return new ProcessResult(false);
        } finally {
            EagleEye.endTrace(null);
        }
    }

    /**
     * 处理发邮件逻辑
     */
    private void execute(SubscriptionOrder oldOrder) {
        Long orderId = oldOrder.getId();
        String appCode = oldOrder.getAppCode();
        AppEnum appEnum = AppEnum.getAppByCode(appCode);
        Long userId = oldOrder.getUserId();

        TASK_LOG.info("EmailNotifyBeforeDeductTask.execute, userId={}, orderId={}", userId, orderId);

        SubscriptionOrderAttributes attributes = oldOrder.getAttributes();
        if (attributes.getWhetherSentDeductionNotifyEmail()) {
            // 已发过邮件不处理
            return;
        }

        try {
            // 只对未发送过邮件的场景做处理
            TASK_LOG.info("EmailNotifyBeforeDeductTask.execute, whetherSentDeductionNotifyEmail is 0, userId={}, orderId={}", userId, orderId);

            // 发送通知邮件
            String nextPlanName = Optional.ofNullable(oldOrder.getNextPlanName())
                    .orElseGet(oldOrder::getSubscriptionPlanName);
            NotifyBeforeDeductEmailDTO emailDTO = new NotifyBeforeDeductEmailDTO()
                    .setAppName(appEnum.getName())
                    .setPlanName(nextPlanName);
            EmailResponse emailResponse = SubscriptionStrategyFactory.getStrategy(appCode, oldOrder.getSubscriptionPayType())
                    .sendEmail(userId, emailDTO);
            if (!Objects.equals(true, emailResponse.getSuccess())) {
                TASK_LOG.error("EmailNotifyBeforeDeductTask send email error,appCode:{},userId:{},orderId:{},errorMsg={}",
                        appCode, userId, orderId, emailResponse.getErrorMessage());
                return;
            }

            attributes.setWhetherSentDeductionNotifyEmail(SubscriptionOrderAttributes.ATTR_WHETHER_SENT_DEDUCTION_NOTIFY_EMAIL_VALUE);
            subscriptionOrderRepository.saveSubscriptionOrder(oldOrder);
        } catch (Exception e) {
            TASK_LOG.error("EmailNotifyBeforeDeductTask error,appCode:{},userId:{},orderId:{}",
                    appCode, userId, orderId, e);
            String content = "[场景] " + EnvUtils.getEnv() + " - Pic 代扣前邮件通知"
                    + "\n[结果] " + "失败，" + e.getMessage()
                    + "\n[信息] userId=" + userId + ", orderId=" + orderId + ", traceId=" + EagleEye.getTraceId()
                    ;
            DingTalkRequest request = new DingTalkRequest();
            request.setContent(content);
            dingTalkService.sendMessage(request);
        }
    }
}
