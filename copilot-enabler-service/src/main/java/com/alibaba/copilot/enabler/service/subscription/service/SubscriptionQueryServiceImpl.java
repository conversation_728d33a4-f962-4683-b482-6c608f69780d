package com.alibaba.copilot.enabler.service.subscription.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.monitor.annotation.MonitorResult;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.marketing.dto.FirstMonthDiscountDTO;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.payment.request.TradeRecordsQuery;
import com.alibaba.copilot.enabler.client.subscription.constant.*;
import com.alibaba.copilot.enabler.client.subscription.dto.*;
import com.alibaba.copilot.enabler.client.subscription.request.*;
import com.alibaba.copilot.enabler.client.subscription.service.SubscriptionQueryService;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.client.user.dto.UserAppRelationDTO;
import com.alibaba.copilot.enabler.client.user.service.UserAppRelationService;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentToken;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.repository.PaymentTokenRepository;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.subscription.dto.FinalDiscountDTO;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionFeature;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionOrderQuery;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionPlanQuery;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionPlanQueryInfo;
import com.alibaba.copilot.enabler.domain.subscription.service.DiscountService;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategy;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategyFactory;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeCycleFeeResultDTO;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeNewPlanPriceContext;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeNewPlanPriceDTO;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeTrialContext;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.infra.base.utils.ModelConvertUtils;
import com.alibaba.copilot.enabler.service.subscription.factory.GetSubscribeProfileResultDTOBuilder;
import com.alibaba.copilot.enabler.service.subscription.factory.SubscribablePlanDTOBuilder;
import com.alibaba.copilot.enabler.service.subscription.factory.SubscribedUserRelationConverter;
import com.alibaba.copilot.enabler.service.subscription.utils.SubscriptionIdUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.Invoice;
import com.stripe.model.InvoiceCollection;
import com.stripe.param.InvoiceListParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订阅服务
 */
@Service
@Slf4j
public class SubscriptionQueryServiceImpl implements SubscriptionQueryService {

    @Autowired
    private SubscriptionPlanRepository planRepository;
    @Autowired
    private SubscriptionOrderRepository orderRepository;
    @Autowired
    private TradeRecordRepository tradeRecordRepository;
    @Autowired
    private UserAppRelationService userAppRelationService;
    @Autowired
    private DiscountService discountService;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private PaymentTokenRepository paymentTokenRepository;

    @Monitor(name = "[Subscription Query Service] 订阅域-已订阅App列表", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    @MonitorResult
    @Override
    public List<SubscribedAppDTO> getSubscribedAppList(SubscribedAppInfoQuery query) {
        String appCode = query.getAppCode();
        Long userId = query.getUserId();

        if (Objects.isNull(userId)) {
            return new ArrayList<>();
        }

        List<UserAppRelationDTO> userAppRelations = userAppRelationService.queryListByAppCodeAndUserId(appCode, userId);
        log.info("getSubscribedAppList userAppRelations:{}", JSON.toJSONString(userAppRelations));

        List<SubscribedAppDTO> result = userAppRelations.stream()
                .map(dto -> convertUserRelationToSubscribedApp(dto, userId))
                .collect(Collectors.toList());
        log.info("getSubscribedAppList result:{}", JSON.toJSONString(result));

        return result;
    }

    private SubscribedAppDTO convertUserRelationToSubscribedApp(UserAppRelationDTO userAppRelationDto, Long userId) {
        SubscribedAppDTO subscribedAppDto = SubscribedUserRelationConverter.INSTANCE.convertB2A(userAppRelationDto);
        List<SubscriptionOrderStatus> subscriptionOrderStatuses = Arrays.asList(
                SubscriptionOrderStatus.IN_EFFECT,
                SubscriptionOrderStatus.UNSUBSCRIBE
        );
        SubscriptionOrderQuery subscriptionOrderQuery = SubscriptionOrderQuery.builder()
                .userId(userId)
                .appCode(subscribedAppDto.getAppCode())
                .status(subscriptionOrderStatuses)
                .build();
        List<SubscriptionOrder> subscriptionOrders = orderRepository.querySubscriptionOrders(subscriptionOrderQuery);
        log.info("getSubscribedAppList subscriptionOrders:{}", JSON.toJSONString(subscriptionOrders));

        if (CollectionUtils.isNotEmpty(subscriptionOrders)) {
            setSubscribedAppDetailsFromOrder(subscribedAppDto, subscriptionOrders.get(0));
        } else {
            setSubscribedAppDetailsFromPlan(subscribedAppDto, userAppRelationDto);
        }
        log.info("getSubscribedAppList subscribedAppDto:{}", JSON.toJSONString(subscribedAppDto));

        subscribedAppDto.setAppType(SubscriptionTypeName.CHROME.name());
        subscribedAppDto.setAppLink("https://www.dscopilot.ai");

        return subscribedAppDto;
    }

    private void setSubscribedAppDetailsFromOrder(SubscribedAppDTO subscribedAppDto, SubscriptionOrder singleOrder) {
        subscribedAppDto.setCurrentPlan(singleOrder.getSubscriptionPlanName());
        subscribedAppDto.setEndTime(singleOrder.getPerformEndTime());
        subscribedAppDto.setStatus(SubscriptionOrderStatus.IN_EFFECT.name());
        subscribedAppDto.setOrdeId(singleOrder.getId());
    }

    private void setSubscribedAppDetailsFromPlan(SubscribedAppDTO subscribedAppDto, UserAppRelationDTO userAppRelationDto) {
        SubscriptionPlan freePlan = planRepository.queryFreePlan(userAppRelationDto.getAppCode(), false);
        if (freePlan != null) {
            subscribedAppDto.setCurrentPlan(freePlan.getName());
            subscribedAppDto.setStatus(SubscriptionOrderStatus.IN_EFFECT.name());
        }
    }

    @Monitor(name = "[Subscription Query Service] 订阅域-订阅计划列表", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    @MonitorResult
    @Override
    public List<SubscribablePlanDTO> getSubscribablePlanList(SubscribablePlanQuery query) {
        String appCode = query.getAppCode();
        String shareCode = query.getShareCode();

        // 1. 查询DB的套餐信息
        SubscriptionPlanQuery subscriptionPlanQuery = SubscriptionPlanQuery.builder()
                .appCode(appCode)
                .queryInfos(Lists.newArrayList(SubscriptionPlanQueryInfo.FEATURE))
                .build();
        List<SubscriptionPlan> subscriptionPlans = planRepository.querySubscriptionPlans(subscriptionPlanQuery);

        // 2. 查询用户的历史订单
        List<SubscriptionOrder> historyOrders = Optional.ofNullable(query.getUserId())
                .map(userId -> orderRepository.getHistoryOrders(userId, appCode))
                .orElse(null);

        // 3. 组装信息
        List<SubscribablePlanDTO> subscribablePlanDTOS = subscriptionPlans.stream()
                .map(plan -> {
                    // 查询折扣信息 (此处逻辑不合理, 串行调用IO阻塞方法会增加RT, 待优化)
                    FinalDiscountDTO discountDTO = queryFinalDiscountDTO(appCode, shareCode, historyOrders, plan);
                    return new SubscribablePlanDTOBuilder(plan, discountDTO, subscriptionPlans, historyOrders).build();
                })
                .sorted()
                .collect(Collectors.toList());
        log.info("getSubscribablePlanList subscribablePlanDTOS:{}", JSON.toJSONString(subscribablePlanDTOS));

        return subscribablePlanDTOS;
    }

    @Nullable
    private FinalDiscountDTO queryFinalDiscountDTO(String appCode, String shareCode, List<SubscriptionOrder> historyOrders, SubscriptionPlan plan) {
        if (plan.isFree() || historyOrders == null) {
            return null;
        }
        return discountService.getFinalDiscountInfo(appCode, shareCode, plan, historyOrders, false);
    }

    @Monitor(name = "[Subscription Query Service] 订阅域-待订阅计划信息", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    @MonitorResult
    @Override
    public SelectedPlanInfoDTO getSelectedPlanInfo(SelectedPlanInfoQuery query) {
        Long userId = query.getUserId();
        String appCode = query.getAppCode();
        Long planId = query.getPlanId();
        String shareCode = query.getShareCode();
        String discountCode = query.getDiscountCode();
        Assertor.assertNonNull(userId, "userId is null");
        Assertor.assertNonNull(appCode, "appCode is null");
        Assertor.assertNonNull(planId, "planId is null");

        // 1. 获取当前应用对应的订阅策略类
        SubscriptionStrategy strategy;
        if ("TEXT2GO".equals(appCode)) {
            // TEXT2GO应用使用STRIPE支付
            strategy = SubscriptionStrategyFactory.getStrategy(appCode, SubscriptionPayType.STRIPE);
        } else {
            // 保持原有逻辑，使用AEPay
            strategy = SubscriptionStrategyFactory.getStrategy(appCode, SubscriptionPayType.AEPay);
        }

        // 2. 构建计算价格的上下文信息
        ComputeNewPlanPriceDTO computePriceDTO = new ComputeNewPlanPriceDTO()
                .setAppCode(appCode)
                .setUserId(userId)
                .setPlanId(planId)
                .setShareCode(shareCode)
                .setDiscountCode(discountCode);
        ComputeNewPlanPriceContext computePriceContext = strategy.buildComputeNewPlanPriceContext(computePriceDTO);
        SubscriptionPlan newPlan = computePriceContext.getNewPlan();
        FinalDiscountDTO finalDiscountDTO = computePriceContext.getFinalDiscountDTO();
        FirstMonthDiscountDTO firstMonthDiscountDTO = computePriceContext.getFirstMonthDiscountDTO();

        // 3. 构建选择套餐的结果信息
        SelectedPlanInfoDTO selectedPlanInfoDTO = buildSelectedPlanInfo(shareCode, newPlan, finalDiscountDTO, firstMonthDiscountDTO);

        // 4. 计算账单周期和费用
        ComputeCycleFeeResultDTO cycleFeeResultDTO = strategy.computeCycleFee(computePriceContext);
        selectedPlanInfoDTO.setCycleFeeDetails(cycleFeeResultDTO.getCycleFeeDetails());
        selectedPlanInfoDTO.setCurrentPayFee(cycleFeeResultDTO.getFinalPayAmount());

        // 5. 查询卡片列表信息
        List<UserPaymentCardDTO> cardList = queryCardList(userId);
        selectedPlanInfoDTO.setCardList(cardList);

        // 6. 支付方式和支付TokenId的映射关系
        selectedPlanInfoDTO.setPaymentMethodToTokenId(queryPaymentMethodToTokenId(appCode, userId));

        log.info("getSelectedPlanInfo selectedPlanInfoDTO:{}", JSON.toJSONString(selectedPlanInfoDTO));
        return selectedPlanInfoDTO;
    }

    private Map<String, String> queryPaymentMethodToTokenId(String appCode, Long userId) {
        Map<String, String> result = new HashMap<>();
        Map<PaymentMethodEnum, PaymentToken> method2Token = paymentTokenRepository.queryAuthedTokensByUser(appCode, userId);
        method2Token.forEach((methodEnum, paymentToken) -> result.put(methodEnum.name(), paymentToken.getTokenId()));
        return result;
    }

    /**
     * 查询卡片列表信息
     */
    private List<UserPaymentCardDTO> queryCardList(Long userId) {
        return Optional.ofNullable(userRepository.getUser(userId))
                .map(User::getUserPaymentCardList)
                .map(cardList -> ModelConvertUtils.copyListByReflect(cardList, UserPaymentCardDTO::new))
                .orElseGet(Lists::newArrayList);
    }

    /**
     * 构建选择套餐的结果信息
     */
    private SelectedPlanInfoDTO buildSelectedPlanInfo(String shareCode, SubscriptionPlan plan, FinalDiscountDTO discountDTO, FirstMonthDiscountDTO firstMonthDiscountDTO) {
        // 1. 准备信息
        AppEnum appEnum = AppEnum.getAppByCode(plan.getAppCode());
        List<String> featureNames = Optional.ofNullable(plan.getFeatures())
                .map(features -> features.stream()
                        .map(SubscriptionFeature::getName)
                        .collect(Collectors.toList())
                )
                .orElseGet(Lists::newArrayList);

        String planDescription = StringUtils.equals(AppEnum.SEO_COPILOT_SITE.getCode(), plan.getAppCode()) ? "The Top Rated SEO Tool. One Step, Surge Traffic & Rankings by 50%+" : plan.getDescription();

        //SEO_COPILOT_SITE 的价格
        BigDecimal originPlanPrice = SubscriptionIdUtil.getPrice(plan, shareCode);

        // 2. 设置基础信息
        SelectedPlanInfoDTO result = new SelectedPlanInfoDTO()
                .setAppName(appEnum.getName())
                .setPlanDescription(planDescription)
                .setPlanName(plan.getName())
                .setOriginPlanPrice(originPlanPrice != null ? originPlanPrice : plan.getPrice())
                .setDuration(plan.getDuration())
                .setDurationUnit(plan.getDurationUnit().name())
                .setFeatureNames(featureNames);

        // 3. 设置折扣信息
        if (firstMonthDiscountDTO != null) {
            // 首月折扣
            result
                    .setDiscountDuration(1L)
                    .setDiscountDurationUnit(DurationUnit.MONTH.name())
                    .setDiscountPlanPrice(firstMonthDiscountDTO.getDiscountPrice());
        } else if (discountDTO != null && discountDTO.getIsDiscount()) {
            // 其他折扣
            result
                    .setDiscountDuration(discountDTO.getRemainDiscountDuration())
                    .setDiscountDurationUnit(discountDTO.getDiscountDurationUnit())
                    .setDiscountPlanPrice(discountDTO.getDiscountPrice());
        }

        return result;
    }

    @Override
    @Monitor(name = "[Subscription Query Service] 订阅域-通过支付的交易号查询试用期信息", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    public TrialDurationDTO getTrialDurationInfoByPayTradeNo(String payTradeNo) {
        // 1. 查询关联流水
        TradeRecordsQuery recordsQuery = TradeRecordsQuery.builder()
                .tradeDirection(TradeDirection.FORWARD)
                .tradeNo(payTradeNo)
                .build();
        List<TradeRecord> tradeRecords = tradeRecordRepository.queryTradeRecords(recordsQuery);
        TradeRecord record = CollectionUtil.getFirst(tradeRecords);
        log.info("getTrialDurationInfoByPayTradeNo, queryTradeRecords, result={}", JSON.toJSONString(record));

        // 2. 查询关联订单
        Long orderId = record.getSubscriptionOrderId();
        SubscriptionOrder order = orderRepository.getByOrderId(orderId);
        log.info("getTrialDurationInfoByPayTradeNo, getByOrderId, result={}", JSON.toJSONString(order));

        // 3. 查询套餐信息
        Long planId = order.getSubscriptionPlanId();
        SubscriptionPlan subscriptionPlan = planRepository.queryByPlanId(planId, false);
        log.info("getTrialDurationInfoByPayTradeNo, queryByPlanId, result={}", JSON.toJSONString(subscriptionPlan));

        // 4. 计算试用期信息
        SubscriptionStrategy strategy = SubscriptionStrategyFactory.getStrategy(subscriptionPlan.getAppCode(), null);
        TrialDurationDTO trialDurationDTO = strategy.computeTrialDuration(new ComputeTrialContext(
                record.getAppCode(), record.getUserId(), subscriptionPlan, null
        ));
        log.info("getTrialDurationInfoByPayTradeNo, computeTrialDuration, result={}", JSON.toJSONString(trialDurationDTO));

        return trialDurationDTO;
    }

    @Override
    @Monitor(name = "[Subscription Query Service] 订阅域-判断订阅指定套餐是否需要付款", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    public Boolean needPayToSubscribePlan(NeedPayToSubscribePlanDTO dto) {
        SubscriptionStrategy subscriptionStrategy = SubscriptionStrategyFactory.getStrategy(dto.getAppCode(), null);
        return subscriptionStrategy.needPayToSubscribePlan(dto);
    }

    @Override
    @Monitor(name = "[Subscription Query Service] 订阅域-查询订阅主页的信息", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    public GetSubscribeProfileResultDTO getSubscribeProfile(GetSubscribeProfileDTO dto) {
        log.info("getSubscribeProfile, dto={}", JSON.toJSONString(dto));
        String appCode = dto.getAppCode();
        Long userId = dto.getUserId();

        // 1. 查询当前生效的订单
        SubscriptionOrder effectOrder = orderRepository.queryEffectOrder(appCode, userId);
        log.info("getSubscribeProfile, getEffectOrder, result={}", JSON.toJSONString(effectOrder));

        // 2. 查询当前生效的套餐
        SubscriptionPlan effectPlan = Optional.ofNullable(effectOrder)
                .map(SubscriptionOrder::getSubscriptionPlanId)
                .map(planId -> planRepository.queryByPlanId(planId, false))
                // 查不到当前生效的订单时, 默认取免费套餐使用
                .orElseGet(() -> planRepository.queryFreePlan(appCode, false));
        log.info("getSubscribeProfile, query plan finished, result={}", JSON.toJSONString(effectPlan));

        // 3. 计算试用期信息
        SubscriptionStrategy strategy = SubscriptionStrategyFactory.getStrategy(appCode, null);
        ComputeTrialContext trialContext = new ComputeTrialContext(appCode, userId, effectPlan, null);
        TrialDurationDTO trialDurationDTO = strategy.computeTrialDuration(trialContext);
        log.info("getSubscribeProfile, computeTrialDuration finished, result={}", JSON.toJSONString(trialDurationDTO));

        // 4. 判断是否支付过
        Boolean paid = tradeRecordRepository.isPaid(appCode, userId);

        // 5. 查询当前是否有待退款状态的流水
        boolean hasRefundRecordWithTodoStatus = tradeRecordRepository.hasRefundRecordWithTodoStatus(appCode, userId);

        // 6. 组装最终结果
        GetSubscribeProfileResultDTO result = new GetSubscribeProfileResultDTOBuilder(
                effectOrder, effectPlan, trialDurationDTO, paid, hasRefundRecordWithTodoStatus
        ).build();
        log.info("getSubscribeProfile, invoke finished, result={}", JSON.toJSONString(result));

        return result;
    }

    /**
     * 根据订单ID查询订单信息
     *
     * @param userId
     * @param id
     * @return
     */
    @Override
    public SubscriptionOrderDTO queryOrderById(Long userId, Long id) {
        SubscriptionOrder order = orderRepository.getByOrderId(userId, id);
        SubscriptionOrderDTO dto = new SubscriptionOrderDTO();
        BeanUtils.copyProperties(order, dto);

        return dto;
    }

    /**
     * 根据外部订阅ID查询订单信息
     *
     * @param userId
     * @param outerSubscriptionId
     * @return
     */
    @Override
    public SubscriptionOrderDTO queryOrderByOuterSubscriptionId(Long userId, String outerSubscriptionId) {
        SubscriptionOrder order = orderRepository.getByOuterSubscriptionId(userId, outerSubscriptionId);
        SubscriptionOrderDTO dto = new SubscriptionOrderDTO();
        BeanUtils.copyProperties(order, dto);

        return dto;
    }

    @Override
    public Map<Long, SubscriptionOrderDTO> queryOrderByUserIds(List<Long> userIds, List<SubscriptionOrderStatus> statusList) {
        log.info("queryOrderByUserIds, userIds={}, statusList={}", JSON.toJSONString(userIds), JSON.toJSONString(statusList));
        Map<Long, SubscriptionOrder> userId2Order = orderRepository.queryOrderByUserIds(userIds, statusList);

        Map<Long, SubscriptionOrderDTO> result = new HashMap<>();
        userId2Order.forEach((userId, order) -> {
            SubscriptionOrderDTO dto = new SubscriptionOrderDTO();
            BeanUtils.copyProperties(order, dto);
            result.put(userId, dto);
        });

        return result;
    }

    /**
     * 根据用户ID查询订单信息
     *
     * @param userIds    用户ID
     * @param statusList 订单状态
     * @return 订单信息
     */
    @Override
    public Map<Long, List<SubscriptionOrderDTO>> queryUserId2OrderByUserIds(String appCode, List<Long> userIds, List<SubscriptionOrderStatus> statusList) {
        log.info("queryUserId2OrderByUserIds, userIds={}, statusList={}", JSON.toJSONString(userIds), JSON.toJSONString(statusList));
        Map<Long, List<SubscriptionOrder>> userId2Order = orderRepository.queryUserId2OrderByUserIds(appCode, userIds, statusList);

        Map<Long, List<SubscriptionOrderDTO>> result = new HashMap<>();
        for (Map.Entry<Long, List<SubscriptionOrder>> entry : userId2Order.entrySet()) {
            List<SubscriptionOrderDTO> subscriptionOrderDTOList = new ArrayList<>();

            for (SubscriptionOrder subscriptionOrder : entry.getValue()) {
                SubscriptionOrderDTO dto = new SubscriptionOrderDTO();
                BeanUtils.copyProperties(subscriptionOrder, dto);
                if (subscriptionOrder.getAttributes() != null && StringUtils.isNotBlank(subscriptionOrder.getAttributes().getStripeSubscriptionId())) {
                    dto.setStripeSubscriptionId(subscriptionOrder.getAttributes().getStripeSubscriptionId());
                }
                subscriptionOrderDTOList.add(dto);
            }

            result.put(entry.getKey(), subscriptionOrderDTOList);
        }

        return result;
    }

    @Override
    public SubscriptionOrderResult querySubscribeOrder(SubscriptionOrderQueryDTO subscriptionOrderQueryDTO) {
        SubscriptionOrderQuery subscriptionOrderQuery = SubscriptionOrderQuery.builder()
                .userId(subscriptionOrderQueryDTO.getUserId())
                .appCode(subscriptionOrderQueryDTO.getAppCode())
                .status(subscriptionOrderQueryDTO.getStatusList())
                .orderIds(subscriptionOrderQueryDTO.getOrderIdList())
                .build();
        List<SubscriptionOrder> subscriptionOrders = orderRepository.querySubscriptionOrders(subscriptionOrderQuery);
        log.info("getSubscribedAppList subscriptionOrders:{}", JSON.toJSONString(subscriptionOrders));
        List<SubscriptionOrderDTO> subscriptionOrderDTOList = new ArrayList<>();
        for (SubscriptionOrder subscriptionOrder : subscriptionOrders) {
            SubscriptionOrderDTO dto = new SubscriptionOrderDTO();
            BeanUtils.copyProperties(subscriptionOrder, dto);
            if (subscriptionOrder.getAttributes() != null && StringUtils.isNotBlank(subscriptionOrder.getAttributes().getStripeSubscriptionId())) {
                dto.setStripeSubscriptionId(subscriptionOrder.getAttributes().getStripeSubscriptionId());
            }
            subscriptionOrderDTOList.add(dto);
        }
        SubscriptionOrderResult result = new SubscriptionOrderResult();
        result.setOrderList(subscriptionOrderDTOList);
        return result;
    }

    @Override
    public InvoiceResult queryInvoice(InvoiceQuery query) {
        Stripe.apiKey = SwitchConfig.stripeApiKey;
        SubscriptionOrder order = orderRepository.getByOrderId(query.getSubscriptionOrderId());
        InvoiceResult invoiceResult = new InvoiceResult();
        List<InvoiceDTO> invoiceDTOList = new ArrayList<>();
        invoiceResult.setInvoiceList(invoiceDTOList);
        String stripeSubscriptionId = order.getAttributes().getStripeSubscriptionId();
        if (StringUtils.isBlank(stripeSubscriptionId)) {
            return invoiceResult;
        }
        try {
            InvoiceCollection invoiceCollection = Invoice.list(InvoiceListParams.builder().setSubscription(stripeSubscriptionId).build());
            List<Invoice> invoiceList = invoiceCollection.getData();
            for (Invoice invoice : invoiceList) {
                InvoiceDTO dto = new InvoiceDTO();
                dto.setId(invoice.getId());
                dto.setInvoicePdf(invoice.getInvoicePdf());
                dto.setStatus(invoice.getStatus());
                dto.setAmountPaid(new BigDecimal(invoice.getAmountPaid()).divide(new BigDecimal(100), RoundingMode.HALF_UP));
                dto.setCreated(invoice.getCreated());
                invoiceDTOList.add(dto);
            }

        } catch (StripeException e) {
                throw new RuntimeException(e);
        }
        return invoiceResult;
    }
}
