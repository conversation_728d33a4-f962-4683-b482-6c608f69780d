package com.alibaba.copilot.enabler.service.user.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.copilot.enabler.client.user.service.UserTokenService;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2024/1/7
 */
@Slf4j
@Service
public class UserTokenServiceImpl implements UserTokenService {
    @Resource
    private UserRepository userRepository;

    @Override
    public String genToken(Long userId, String shopDomain) {
        if (userId == null || StringUtils.isBlank(shopDomain)) {
            return null;
        }

        // 查询是否存在 Token，存在则返回成功
        User user = userRepository.getUser(userId);
        if (user == null) {
            log.warn("one shop user bind is not exist, userId: {}", userId);
            return null;
        }

        String oneShopUserToken = user.getAttributes().getOneShopUserToken();
        if (StringUtils.isBlank(oneShopUserToken)) {
            // 生成 Token, 回调 AE
            return genAeOneShopImportProducts(user, shopDomain);
        } else {
            return oneShopUserToken;
        }
    }

    public String genAeOneShopImportProducts(User user, String shopDomain) {
        // 生成 Token, 写入数据库回调 AE
        String token = "tkoneshop_" + shopDomain + "_" + DateUtil.current();
        user.getAttributes().setOneShopUserToken(token);
        userRepository.updateUser(user);
        return token;
    }
}
