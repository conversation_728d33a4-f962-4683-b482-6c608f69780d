package com.alibaba.copilot.enabler.service.subscription.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.boot.shopify.web.api.ShopifyBillingGrapgQLApi;
import com.alibaba.copilot.boot.shopify.web.data.graphql.exception.ShopifyException;
import com.alibaba.copilot.boot.shopify.web.data.graphql.result.GraphQLBaseResult;
import com.alibaba.copilot.boot.shopify.web.data.graphql.subscription.*;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import com.alibaba.copilot.enabler.client.subscription.constant.SeoFeatureType;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.dto.*;
import com.alibaba.copilot.enabler.client.subscription.service.ShopifySubscriptionService;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.base.utils.TimeUtils;
import com.alibaba.copilot.enabler.domain.subscription.dto.ShopifyDiscountDTO;
import com.alibaba.copilot.enabler.domain.subscription.dto.ShopifyDiscountResultDTO;
import com.alibaba.copilot.enabler.domain.subscription.model.*;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionOrderQuery;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionPlanQuery;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionPlanQueryInfo;
import com.alibaba.copilot.enabler.domain.subscription.service.ShopifyDiscountService;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategy;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategyFactory;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeNewPlanPriceContext;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeNewPlanPriceDTO;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeNewPlanPriceResultDTO;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeTrialContext;
import com.alibaba.copilot.enabler.infra.base.constants.ErrorCodes;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.infra.base.utils.EnvUtils;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionFeatureDO;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionPlanFeatureDO;
import com.alibaba.copilot.enabler.infra.subscription.factory.SubscriptionFeatureConverter;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionFeatureMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionPlanFeatureMapper;
import com.alibaba.copilot.enabler.service.subscription.constant.ShopifySubscriptionConstant;
import com.alibaba.copilot.enabler.service.subscription.constant.ShopifySubscriptionSwitchType;
import com.alibaba.copilot.enabler.service.subscription.dto.DiscountVO;
import com.alibaba.copilot.enabler.service.subscription.dto.SubscriptionRequestDTO;
import com.alibaba.copilot.enabler.service.subscription.factory.ShopifySubscribablePlanDTOBuilder;
import com.alibaba.copilot.enabler.service.subscription.holder.ShopifySubscriptionHolder;
import com.alibaba.copilot.enabler.service.subscription.holder.ShopifySubscriptionHolderDTO;
import com.alibaba.copilot.enabler.service.subscription.utils.ShopifySubscriptionIdUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.security.SecurityUtil;
import com.alibaba.security.url.SSRFUnsafeConnectionError;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2023/10/30
 */
@Slf4j
@Service
public class ShopifySubscriptionServiceImpl implements ShopifySubscriptionService {

    @Resource
    private SubscriptionPlanRepository subscriptionPlanRepository;

    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;

    @Resource
    private ShopifyDiscountService shopifyDiscountService;

    @Resource
    private SubscriptionOrderRepository orderRepository;

    @Resource
    private SubscriptionPlanFeatureMapper subscriptionPlanFeatureMapper;

    @Resource
    private SubscriptionFeatureMapper subscriptionFeatureMapper;

    @Override
    public ShopifySubscribePlanResultDTO subscribePlan(ShopifySubscribePlanDTO subscribePlanDTO) {
        log.info("subscribePlan, subscribePlanDTO={}", JSON.toJSONString(subscribePlanDTO));

        // 对订阅参数进行前置校验
        checkSubscribePlanParams(subscribePlanDTO);

        // 构建计算价格的上下文
        ComputeNewPlanPriceContext priceContext = buildComputePriceContext(subscribePlanDTO);

        // 检查同套餐切换逻辑
        checkSwitchWithSamePlan(priceContext);

        // 计算支付价格
        SubscriptionStrategy strategy = SubscriptionStrategyFactory.getStrategy(subscribePlanDTO.getAppCode(), SubscriptionPayType.Shopify);
        ComputeNewPlanPriceResultDTO priceResultDTO = strategy.computeNewPlanPrice(priceContext);

        if (priceResultDTO.getNeedPay()) {
            // 需要支付的场景, 对应创建/更新订阅
            return createOrUpdateSubscription(subscribePlanDTO, priceContext, priceResultDTO);
        } else {
            // 不需要支付的场景, 对应取消订阅
            return cancelSubscription(subscribePlanDTO, priceContext, priceResultDTO);
        }

    }

    /**
     * 检查同套餐切换逻辑
     */
    private void checkSwitchWithSamePlan(ComputeNewPlanPriceContext priceContext) {
        SubscriptionPlan newPlan = priceContext.getNewPlan();
        SubscriptionPlan oldPlan = Optional.ofNullable(priceContext.getOldPlan())
                .orElseGet(() -> subscriptionPlanRepository.queryFreePlan(priceContext.getAppCode(), false));
        if (newPlan != null && oldPlan != null && Objects.equals(newPlan.getId(), oldPlan.getId())) {
            // 不允许相同套餐的切换
            Long planId = newPlan.getId();
            log.warn("checkSwitchWithSamePlan, planId={}", planId);
            throw new BizException(ErrorCodes.SWITCH_WITH_SAME_PLAN);
        }
    }

    /**
     * 取消订阅
     */
    private ShopifySubscribePlanResultDTO cancelSubscription(ShopifySubscribePlanDTO dto, ComputeNewPlanPriceContext priceContext, ComputeNewPlanPriceResultDTO priceResultDTO) {
        // 取出待取消的订单
        SubscriptionOrder oldOrder = priceContext.getOldOrder();
        Assertor.assertNonNull(oldOrder, "No effect order found.");

        // 取出待取消订单的Shopify订阅ID
        Long shopifySubscriptionId = oldOrder.getShopifySubscriptionId();
        Assertor.assertNonNull(shopifySubscriptionId, "shopifySubscriptionId is null");

        // 构建Shopify的订阅请求信息
        AppSubscriptionCancelGraphQLMutation qlMutation = buildCancelSubscriptionRequest(shopifySubscriptionId);

        try {
            log.info("ShopifyBillingGrapgQLApi appSubscriptionCancel -> {}", JSON.toJSONString(qlMutation));
            // 调用Shopify的订阅接口
            SecurityUtil.startSSRFNetHookChecking();
            AppSubscriptionGraphQLResult qlResult = ShopifyBillingGrapgQLApi.appSubscriptionCancel(dto.getShopDomain(), dto.getAccessToken(), qlMutation);
            checkShopifyResult(qlResult, "appSubscriptionCancel");

            // 存入订阅相关的上下文信息
            putSubscriptionInfoToHolder(ShopifySubscriptionSwitchType.CHARGE_TO_FREE, shopifySubscriptionId, priceContext, null, null, null);

            // 构建结果并返回
            return new ShopifySubscribePlanResultDTO()
                    .setConfirmationUrl(qlResult.getConfirmationUrl());
        } catch (ShopifyException | SSRFUnsafeConnectionError e) {
            log.error("cancelSubscription, invoke appSubscriptionCancel error", e);
            throw new BizException(ErrorCodes.ORDER_CALL_SHOPIFY_ERROR, e.getMessage());
        } finally {
            SecurityUtil.stopSSRFNetHookChecking();
        }
    }

    /**
     * 创建/更新订阅
     */
    private ShopifySubscribePlanResultDTO createOrUpdateSubscription(ShopifySubscribePlanDTO dto, ComputeNewPlanPriceContext priceContext, ComputeNewPlanPriceResultDTO priceResultDTO) {
        // 计算试用期天数
        Integer trialDays = computeTrialDays(priceContext);

        // 构建Shopify的订阅请求信息

        SubscriptionRequestDTO subscriptionRequestDTO = buildCreateOrUpdateSubscriptionRequest(dto, priceContext, trialDays, priceResultDTO);
        AppSubscriptionGraphQLMutation qlMutation = subscriptionRequestDTO.getAppSubscriptionGraphQLMutation();

        try {
            // 调用Shopify的订阅接口
            SecurityUtil.startSSRFNetHookChecking();
            AppSubscriptionGraphQLResult qlResult = ShopifyBillingGrapgQLApi.appSubscriptionCreate(dto.getShopDomain(), dto.getAccessToken(), qlMutation);
            checkShopifyResult(qlResult, "appSubscriptionCreate");

            // 存入订阅相关的上下文信息
            Long apiId = getApiIdFromShopifySubscriptionResult(qlResult);
            ShopifySubscriptionSwitchType switchType = priceContext.getOldOrder() == null
                    ? ShopifySubscriptionSwitchType.FREE_TO_CHARGE : ShopifySubscriptionSwitchType.CHARGE_TO_CHARGE;
            putSubscriptionInfoToHolder(switchType, apiId, priceContext, trialDays, subscriptionRequestDTO.getActualPaymentAmount(), subscriptionRequestDTO.getClientIp());

            // 构建结果并返回
            return new ShopifySubscribePlanResultDTO()
                    .setConfirmationUrl(qlResult.getConfirmationUrl())
                    .setShopifySubscriptionId(apiId);
        } catch (ShopifyException | SSRFUnsafeConnectionError e) {
            log.error("createOrUpdateSubscription, invoke error", e);
            throw new BizException(ErrorCodes.SYS_ERROR, e.getMessage());
        } finally {
            SecurityUtil.stopSSRFNetHookChecking();
        }
    }

    /**
     * 检查Shopify的调用结果
     */
    private static void checkShopifyResult(AppSubscriptionGraphQLResult qlResult, String domain) {
        GraphQLBaseResult.UserError userError = CollectionUtil.getFirst(qlResult.getUserErrors());
        if (userError != null) {
            String errorMessage = userError.getMessage();
            log.error("checkShopifyResult, invoke " + domain + " error: " + errorMessage);
            throw new BizException(ErrorCodes.ORDER_CALL_SHOPIFY_ERROR, errorMessage);
        }
    }

    /**
     * 对订阅参数进行前置校验
     */
    private void checkSubscribePlanParams(ShopifySubscribePlanDTO dto) {
        Assertor.assertNotBlank(dto.getShopDomain(), "shopDomain is blank");
        Assertor.assertNotBlank(dto.getAccessToken(), "accessToken is blank");
        Assertor.assertNotBlank(dto.getAppCode(), "appCode is blank");
        Assertor.assertNotBlank(dto.getRedirectUrl(), "redirectUrl is blank");
        Assertor.assertNonNull(dto.getUserId(), "userId is null");
        Assertor.assertNonNull(dto.getPlanId(), "planId is null");
    }

    /**
     * 构建计算价格的上下文
     */
    private static ComputeNewPlanPriceContext buildComputePriceContext(ShopifySubscribePlanDTO dto) {
        log.info("buildComputePriceContext, dto={}", JSON.toJSONString(dto));
        ComputeNewPlanPriceDTO computeNewPlanPriceDTO = new ComputeNewPlanPriceDTO()
                .setAppCode(dto.getAppCode())
                .setUserId(dto.getUserId())
                .setPlanId(dto.getPlanId())
                .setShareCode(StringUtils.equals(dto.getAppCode(), "SEO_COPILOT") ? null : dto.getShareCode());
//                .setShareCode(null);//shareCode暂时不走这个逻辑
        SubscriptionStrategy strategy = SubscriptionStrategyFactory.getStrategy(dto.getAppCode(), SubscriptionPayType.Shopify);
        return strategy.buildComputeNewPlanPriceContext(computeNewPlanPriceDTO);
    }

    /**
     * 计算试用期天数
     */
    @Nullable
    private static Integer computeTrialDays(ComputeNewPlanPriceContext context) {
        SubscriptionPlan newPlan = context.getNewPlan();
        List<SubscriptionOrder> historyOrders = context.getHistoryOrders();
        String appCode = context.getAppCode();

        ComputeTrialContext trialContext = new ComputeTrialContext(appCode, context.getUserId(), newPlan, historyOrders);
        SubscriptionStrategy strategy = SubscriptionStrategyFactory.getStrategy(appCode, SubscriptionPayType.Shopify);
        TrialDurationDTO trialDurationDTO = strategy.computeTrialDuration(trialContext);

        boolean hasTrial = trialDurationDTO != null && trialDurationDTO.getIsTrial();
        return hasTrial ? Math.toIntExact(trialDurationDTO.getRemainTrialDay()) : null;
    }

    /**
     * 构建Shopify的取消请求信息
     *
     * @param shopifySubscriptionId 当前生效订单对应的Shopify订阅ID
     * @return 订阅Shopify的请求信息
     */
    @NotNull
    private AppSubscriptionCancelGraphQLMutation buildCancelSubscriptionRequest(Long shopifySubscriptionId) {
        AppSubscriptionCancelGraphQLMutation qlMutation = new AppSubscriptionCancelGraphQLMutation();
        String graphQLApiId = ShopifySubscriptionIdUtils.getGraphQLApiId(shopifySubscriptionId);
        qlMutation.setId(graphQLApiId);
        qlMutation.setProrate(true);
        return qlMutation;
    }

    /**
     * 构建Shopify的订阅请求信息
     *
     * @param dto            客户订阅参数
     * @param priceContext   计算价格上下文
     * @param trialDays      试用期天数
     * @param priceResultDTO 计算价格的结果
     * @return 订阅Shopify的请求信息
     */
    @NotNull
    private SubscriptionRequestDTO buildCreateOrUpdateSubscriptionRequest(
            ShopifySubscribePlanDTO dto, ComputeNewPlanPriceContext priceContext, Integer trialDays, ComputeNewPlanPriceResultDTO priceResultDTO) {

        log.info("buildCreateOrUpdateSubscriptionRequest shopifySubscribePlanDTO={}", JSON.toJSONString(dto));

        SubscriptionRequestDTO subscriptionRequestDTO = new SubscriptionRequestDTO();
        SubscriptionPlan newPlan = priceContext.getNewPlan();
        AppSubscriptionGraphQLMutation qlMutation = new AppSubscriptionGraphQLMutation();
        qlMutation.setName(newPlan.getName());
        qlMutation.setReplacementBehavior(
                AppSubscriptionReplacementBehavior.valueOf(SwitchConfig.shopifySubscriptionBehavior)
        );
        qlMutation.setReturnUrl(dto.getRedirectUrl());
        // 注: Shopify的Api中trialDays不能为null, 否则会报错
        qlMutation.setTrialDays(Optional.ofNullable(trialDays).orElse(0));

        // 获取支付环境
        qlMutation.setTest(SwitchConfig.shopifySubscriptionUseTestEnv);

        // 获取支付价格
        BigDecimal payAmount = getPaymentPrice(priceResultDTO);

        //计算Shopify的折扣信息
        DiscountVO discountVO = computeShopifyDiscount(priceContext, newPlan, dto.getShareCode());
        Discount discount = discountVO != null ? discountVO.getDiscount() : null;
        if (discountVO != null && discountVO.getOriginDiscountPrice() != null) {
            //使用了用效折扣码，更新价格
            payAmount = discountVO.getOriginDiscountPrice();
        }
        // 构建订阅请求信息
        Price price = Price.builder()
                .amount(payAmount)
                .currencyCode(ShopifySubscriptionConstant.CURRENCY_CODE)
                .build();
        AppSubscriptionGraphQLMutation.AppRecurringPricingInput pricingInput = AppSubscriptionGraphQLMutation.AppRecurringPricingInput.builder()
                .price(price)
                .interval(getShopifyInterval(newPlan))
                .discount(discount)
                .build();
        AppSubscriptionGraphQLMutation.Plan shopifyPlan = AppSubscriptionGraphQLMutation.Plan.builder()
                .appRecurringPricingDetails(pricingInput)
                .build();
        AppSubscriptionGraphQLMutation.LineItem lineItem = AppSubscriptionGraphQLMutation.LineItem.builder()
                .plan(shopifyPlan)
                .build();
        qlMutation.setLineItems(Lists.newArrayList(lineItem));

        subscriptionRequestDTO.setAppSubscriptionGraphQLMutation(qlMutation);

        log.info("buildCreateOrUpdateSubscriptionRequest discount={},payAmount={}", JSON.toJSONString(discount), payAmount);
        if (discount != null && discount.getValue() != null && discount.getValue().getPercentage() != null
                && discount.getValue().getPercentage().compareTo(BigDecimal.ZERO) != 0) {
            log.info("buildCreateOrUpdateSubscriptionRequest discount_is_not_null");
            // 创建表示数字1的BigDecimal
            BigDecimal one = BigDecimal.ONE;
            // 使用subtract方法来执行减法操作
            BigDecimal discountResult = one.subtract(discount.getValue().getPercentage());
            subscriptionRequestDTO.setActualPaymentAmount(payAmount.multiply(discountResult));
        } else {
            subscriptionRequestDTO.setActualPaymentAmount(payAmount);
        }
        log.info("buildCreateOrUpdateSubscriptionRequest subscriptionRequestDTO={}", JSON.toJSONString(subscriptionRequestDTO));
        subscriptionRequestDTO.setClientIp(dto.getClientIp());
        return subscriptionRequestDTO;
    }

    /**
     * 获取支付价格
     */
    private static BigDecimal getPaymentPrice(ComputeNewPlanPriceResultDTO priceResultDTO) {
        BigDecimal payAmount = priceResultDTO.getPayAmount();
        Double testPayPrice = SwitchConfig.shopifySubscriptionTestPayPrice;
        if (EnvUtils.isOnline() || testPayPrice <= 0) {
            // 更改支付价格属于高危操作, 此处添加拦截, 仅对非线上环境生效
            return payAmount;
        }

        return BigDecimal.valueOf(testPayPrice);
    }

    /**
     * 从Shopify的订阅结果中获取到对应的订阅ID
     */
    private Long getApiIdFromShopifySubscriptionResult(AppSubscriptionGraphQLResult qlResult) {
        return Optional.ofNullable(qlResult.getAppSubscription())
                .map(AppSubscriptionGraphQLResult.AppSubscription::getId)
                .map(ShopifySubscriptionIdUtils::getShopifySubscriptionId)
                .orElseThrow(() -> new BizException(ErrorCode.SYS_ERROR, "No apiId found."));
    }

    /**
     * 将订阅信息存入Holder中
     */
    private void putSubscriptionInfoToHolder(ShopifySubscriptionSwitchType switchType, Long apiId,
                                             ComputeNewPlanPriceContext priceContext, Integer trialDays, BigDecimal actualPaymentAmount, String clientIp) {
        Long oldEffectOrderId = Optional.ofNullable(priceContext.getOldOrder())
                .map(SubscriptionOrder::getId)
                .orElse(null);
        if (actualPaymentAmount == null) {
            actualPaymentAmount = BigDecimal.ZERO;
        }
        ShopifySubscriptionHolderDTO holderDTO = new ShopifySubscriptionHolderDTO()
                .setClientIp(clientIp)
                .setApiId(apiId)
                .setSwitchType(switchType)
                .setPlanId(priceContext.getNewPlan().getId())
                .setAppCode(priceContext.getAppCode())
                .setUserId(priceContext.getUserId())
                .setTrialDays(trialDays)
                .setOldEffectOrderId(oldEffectOrderId)
                .setActualPaymentAmount(actualPaymentAmount);
        log.info("putSubscriptionInfoToHolder apiId={},holerDTO={}", apiId, JSON.toJSONString(holderDTO));
        ShopifySubscriptionHolder.putToShopifySubscriptionHolder(apiId, holderDTO);
    }

    /**
     * 计算Shopify的折扣信息
     */
    private DiscountVO computeShopifyDiscount(ComputeNewPlanPriceContext priceContext, SubscriptionPlan plan, String shareCode) {

        log.info("computeShopifyDiscount shareCode={}", shareCode);

        ShopifyDiscountDTO discountDTO = new ShopifyDiscountDTO()
                .setAppCode(priceContext.getAppCode())
                .setUserId(priceContext.getUserId())
                .setShareCode(shareCode)
                .setPlan(plan);
        ShopifyDiscountResultDTO resultDTO = shopifyDiscountService.computeDiscount(discountDTO);

        log.info("computeShopifyDiscount resultDTO={}", JSON.toJSONString(resultDTO));

        if (resultDTO == null || !resultDTO.getHasDiscount()) {
            return null;
        }
        DiscountVO discountVO = new DiscountVO();
        if (StringUtils.isNotBlank(resultDTO.getEffectiveShareCode())) {
            // 注意, 此处8折对应discountRate是0.8, 传给Shopify是0.2
            //设置折扣周期
            discountVO.setDiscount(Discount.builder()
                    .value(new DiscountValue(1 - resultDTO.getDiscountRate().doubleValue()))
                    .durationLimitInIntervals(BigDecimal.valueOf(1))
                    .build());
            discountVO.setOriginDiscountPrice(resultDTO.getOriginDiscountPrice());
        } else {
            discountVO.setDiscount(Discount.builder()
                    .value(new DiscountValue(1 - resultDTO.getDiscountRate().doubleValue()))
                    .build());
        }
        return discountVO;
    }

    /**
     * 查询套餐的interval单位
     */
    private String getShopifyInterval(SubscriptionPlan plan) {
        // TODO: 2023/10/30 @籽轩 确认是否有现成枚举使用
        DurationUnit durationUnit = plan.getDurationUnit();
        switch (durationUnit) {
            case YEAR:
                return "ANNUAL";
            case MONTH:
                return "EVERY_30_DAYS";
            default:
                throw new RuntimeException("Not supported " + durationUnit);
        }
    }


    @Override
    public ShopifySubscribablePlansResultDTO getSubscribablePlans(ShopifySubscribablePlansQueryDTO dto) {
        SubscriptionPlanQuery subscriptionPlanQuery = SubscriptionPlanQuery.builder()
                .appCode(dto.getAppCode())
                .queryInfos(Lists.newArrayList(SubscriptionPlanQueryInfo.FEATURE))
                .build();
        List<SubscriptionPlan> subscriptionPlans = subscriptionPlanRepository.querySubscriptionPlans(subscriptionPlanQuery);
        if (CollectionUtils.isEmpty(subscriptionPlans)) {
            return null;
        }

        // 查询折扣
        List<ShopifySubscribablePlansResultDTO.PlanDTO> collect = subscriptionPlans.stream()
                .filter(Objects::nonNull)
                .map(subscriptionPlan -> {
                    ShopifyDiscountDTO shopifyDiscountDTO = new ShopifyDiscountDTO()
                            .setUserId(dto.getUserId())
                            .setAppCode(dto.getAppCode())
                            .setShareCode(dto.getShareCode())
                            .setPlan(subscriptionPlan);
                    ShopifyDiscountResultDTO discountResultDTO = shopifyDiscountService.computeDiscount(shopifyDiscountDTO);

                    ShopifySubscribablePlanDTOBuilder builder = new ShopifySubscribablePlanDTOBuilder();
                    builder.setDiscountResultDTO(discountResultDTO);
                    builder.setSubscriptionPlan(subscriptionPlan);
                    builder.setPlans(subscriptionPlans);
                    return builder.build();
                })
                .collect(Collectors.toList());

        ShopifySubscribablePlansResultDTO resultDTO = new ShopifySubscribablePlansResultDTO();
        resultDTO.setList(collect);
        return resultDTO;
    }

    @Override
    public ShopifySubscribedPlanResultDTO getSubscribedPlan(ShopifySubscribedPlanQueryDTO dto) {
        log.info("ShopifySubscriptionServiceImpl#getSubscribeProfile, dto={}", JSON.toJSONString(dto));
        String appCode = dto.getAppCode();
        Long userId = dto.getUserId();
        if (StringUtils.isBlank(appCode) || userId == null) {
            return null;
        }

        // 查询当前生效的订单
        SubscriptionOrder effectOrder = getEffectOrder(appCode, userId);
        log.info("ShopifySubscriptionServiceImpl#getSubscribeProfile, getEffectOrder, result={}", JSON.toJSONString(effectOrder));

        // 查询当前生效的套餐
        SubscriptionPlan effectPlan = Optional.ofNullable(effectOrder)
                .map(SubscriptionOrder::getSubscriptionPlanId)
                .map(planId ->
                        subscriptionPlanRepository.queryByPlanId(planId, false))
                // 查不到当前生效的订单时, 默认取免费套餐使用
                .orElseGet(() ->
                        subscriptionPlanRepository.queryFreePlan(appCode, false));
        log.info("ShopifySubscriptionServiceImpl#getSubscribeProfile, query plan finished, result={}", JSON.toJSONString(effectPlan));

        return new ShopifySubscribedPlanResultDTO()
                .setAppCode(appCode)
                .setOrderId(effectOrder == null ? null : effectOrder.getId())
                .setPlanId(effectPlan.getId())
                .setPlanName(effectPlan.getName())
                .setPrice(effectPlan.getPrice())
                .setIsAutoRenew(effectOrder != null && effectOrder.getAutoRenew())
                .setNextRenewTime(effectOrder == null ? null : effectOrder.getNextRenewalTime())
                .setPerformStartTime(effectOrder == null ? null : effectOrder.getPerformStartTime())
                .setPerformEndTime(effectOrder == null ? null : effectOrder.getPerformEndTime());
    }

    /**
     * 查询当前生效的订单
     *
     * @param appCode
     * @param userId
     * @return
     */
    private SubscriptionOrder getEffectOrder(String appCode, Long userId) {
        SubscriptionOrderQuery orderQuery = SubscriptionOrderQuery.builder()
                .status(Lists.newArrayList(SubscriptionOrderStatus.IN_EFFECT))
                .appCode(appCode)
                .userId(userId)
                .build();
        List<SubscriptionOrder> orders = subscriptionOrderRepository.querySubscriptionOrders(orderQuery);
        if (CollectionUtil.size(orders) > 1) {
            log.warn("ShopifySubscriptionServiceImpl#getEffectOrder, more than one effect order found, orders={}", JSON.toJSONString(orders));
        }
        return CollectionUtil.getFirst(orders);
    }

    @Override
    public ShopifyFeatureDTO getFeature(ShopifyFeatureQuery shopifyFeatureQuery) {
        if (shopifyFeatureQuery == null || StringUtils.isBlank(shopifyFeatureQuery.getFeatureType())
                || StringUtils.isBlank(shopifyFeatureQuery.getAppCode())
                || shopifyFeatureQuery.getId() == null) {
            return null;
        }
        ShopifyFeatureDTO shopifyFeatureDTO = new ShopifyFeatureDTO();
        try {

            SubscriptionPlan plan = getSubscriptionPlan(shopifyFeatureQuery);
            if (plan == null || CollectionUtil.isEmpty(plan.getFeatures())) {
                return null;
            }
            List<SubscriptionFeature> effectPlanFeatures = plan.getFeatures();
            if (CollectionUtil.isEmpty(effectPlanFeatures)) {
                return null;
            }

            shopifyFeatureDTO.setHasAuthorize(false);
            for (SubscriptionFeature feature : effectPlanFeatures) {
                if (StringUtils.equals(feature.getType(), shopifyFeatureQuery.getFeatureType())) {
                    shopifyFeatureDTO.setHasAuthorize(true);
                    if (feature.getIsDepletion()) {
                        //消耗型选择性
                        shopifyFeatureDTO.setDepletion(true);
                        FeatureUsage featureUsage = subscriptionPlanRepository.queryFeatureUsage(shopifyFeatureQuery);
                        if (featureUsage != null) {
                            //在有效期内有用量记录
//                            shopifyFeatureDTO.setRemainQuota(featureUsage.getQuota() - featureUsage.getUsageCount());
                            // 当用户升级或降级套餐，让消费者能用，但用一次之后featureUsage表会更新为准确的值
                            if (StringUtils.isNotBlank(featureUsage.getAttributes().getPlanName())
                                    && !Objects.equals(plan.getName(), featureUsage.getAttributes().getPlanName())) {
                                shopifyFeatureDTO.setRemainQuota(1L);
                            } else {
//                                featureUsage.setQuota(feature.getBenefit().getAsLong("quota"));
//                                subscriptionPlanRepository.saveFeatureUsage(featureUsage);
                                shopifyFeatureDTO.setRemainQuota(feature.getBenefit().getAsLong("quota") - featureUsage.getUsageCount());
                            }
                        } else {
                            //在有效期内无用量记录
                            subscriptionPlanRepository.saveFeatureUsage(buildFeatureUsage(feature, shopifyFeatureQuery));
                            shopifyFeatureDTO.setRemainQuota(feature.getBenefit().getAsLong("quota"));
                        }
                    } else {
                        shopifyFeatureDTO.setDepletion(false);
                        if (feature.getBenefit() != null && feature.getBenefit().getAsLong("quota") != null) {
                            shopifyFeatureDTO.setRemainQuota(feature.getBenefit().getAsLong("quota"));
                        }
                    }
                    break;
                }
            }
        } catch (Exception e) {
            log.error("ShopifySubscriptionServiceImpl#getFeature exp,shopifyFeatureQuery={}", JSON.toJSONString(shopifyFeatureQuery), e);
        }
        log.info("getFeature shopifyFeatureDTO={}", JSON.toJSONString(shopifyFeatureDTO));
        return shopifyFeatureDTO;
    }

    @Override
    public ShopifyFeatureAllDTO getFeatureAll(ShopifyFeatureQuery shopifyFeatureQuery) {
        if (shopifyFeatureQuery == null || StringUtils.isBlank(shopifyFeatureQuery.getFeatureType())
                || StringUtils.isBlank(shopifyFeatureQuery.getAppCode())
                || shopifyFeatureQuery.getId() == null) {
            return null;
        }
        ShopifyFeatureAllDTO shopifyFeatureAllDTO = new ShopifyFeatureAllDTO();
        try {
            SubscriptionPlan plan = getSubscriptionPlan(shopifyFeatureQuery);
            if (plan == null || CollectionUtil.isEmpty(plan.getFeatures())) {
                return null;
            }
            List<SubscriptionFeature> effectPlanFeatures = plan.getFeatures();
            if (CollectionUtil.isEmpty(effectPlanFeatures)) {
                return null;
            }
            shopifyFeatureAllDTO.setHasAuthorize(false);
            for (SubscriptionFeature feature : effectPlanFeatures) {
                if (StringUtils.equals(feature.getType(), shopifyFeatureQuery.getFeatureType())) {
                    shopifyFeatureAllDTO.setHasAuthorize(true);
                    if (feature.getIsDepletion()) {
                        //消耗型选择性
                        shopifyFeatureAllDTO.setDepletion(true);
                        FeatureUsage featureUsage = subscriptionPlanRepository.queryFeatureUsage(shopifyFeatureQuery);
                        if (featureUsage != null) {
                            //在有效期内有用量记录
//                            shopifyFeatureDTO.setRemainQuota(featureUsage.getQuota() - featureUsage.getUsageCount());
                            // 当用户升级或降级套餐，让消费者能用，但用一次之后featureUsage表会更新为准确的值
                            if (StringUtils.isNotBlank(featureUsage.getAttributes().getPlanName())
                                    && !Objects.equals(plan.getName(), featureUsage.getAttributes().getPlanName())) {
                                shopifyFeatureAllDTO.setRemainQuota(1L);
                            } else {
//                                featureUsage.setQuota(feature.getBenefit().getAsLong("quota"));
//                                subscriptionPlanRepository.saveFeatureUsage(featureUsage);
                                shopifyFeatureAllDTO.setRemainQuota(feature.getBenefit().getAsLong("quota") - featureUsage.getUsageCount());
                            }
                        } else {
                            //在有效期内无用量记录
                            subscriptionPlanRepository.saveFeatureUsage(buildFeatureUsage(feature, shopifyFeatureQuery));
                            shopifyFeatureAllDTO.setRemainQuota(feature.getBenefit().getAsLong("quota"));
                        }
                    } else {
                        shopifyFeatureAllDTO.setDepletion(false);
                        if (feature.getBenefit() != null && feature.getBenefit().getAsLong("quota") != null) {
                            shopifyFeatureAllDTO.setRemainQuota(feature.getBenefit().getAsLong("quota"));
                        }
                    }
                    shopifyFeatureAllDTO.setAllOverQuota(feature.getBenefit().getAsLong("quota"));
                    break;
                }
            }
        } catch (Exception e) {
            log.error("ShopifySubscriptionServiceImpl#getFeatureAll exp,shopifyFeatureQuery={}", JSON.toJSONString(shopifyFeatureQuery), e);
        }
        shopifyFeatureAllDTO.setMaxQuota(getMaxQuotaByFeatureType(shopifyFeatureQuery.getFeatureType()));
        log.info("getFeatureAll shopifyFeatureAllDTO={}", JSON.toJSONString(shopifyFeatureAllDTO));
        return shopifyFeatureAllDTO;
    }

    /**
     * 根据 featureType 返回该type所有套餐最大用量
     *
     * @param featureType
     * @return
     */
    private Long getMaxQuotaByFeatureType(String featureType) {
        QueryWrapper<SubscriptionPlanFeatureDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("subscription_plan_id", 60L);
        List<SubscriptionPlanFeatureDO> subscriptionPlanFeatureDOS = subscriptionPlanFeatureMapper.selectList(queryWrapper);
        for (SubscriptionPlanFeatureDO subscriptionPlanFeatureDO : subscriptionPlanFeatureDOS) {
            SubscriptionFeatureDO subscriptionFeatureDO = subscriptionFeatureMapper.selectById(subscriptionPlanFeatureDO.getSubscriptionFeatureId());
            if (subscriptionFeatureDO != null) {
                SubscriptionFeature subscriptionFeature = SubscriptionFeatureConverter.INSTANCE.convertA2B(subscriptionFeatureDO);
                if (subscriptionFeature != null) {
                    if (StringUtils.equals(subscriptionFeature.getType(), featureType)) {
                        return subscriptionFeature.getBenefit().getAsLong("quota");
                    }
                }
            }
        }
        return 0L;
    }

    @Override
    public ShopifyPlanFeatureDTO getFeatureMappingNextPlan(ShopifyFeatureQuery shopifyFeatureQuery) {
        log.info("ShopifySubscriptionServiceImpl#getFeatureMappingNextPlan, dto={}", JSON.toJSONString(shopifyFeatureQuery));
        String appCode = shopifyFeatureQuery.getAppCode();
        Long userId = shopifyFeatureQuery.getId();
        if (StringUtils.isBlank(appCode) || userId == null) {
            return null;
        }

        try {

            // 查询当前生效的订单
            SubscriptionOrder effectOrder = getEffectOrder(appCode, userId);
            log.info("ShopifySubscriptionServiceImpl#getSubscribeProfile, getEffectOrder, result={}", JSON.toJSONString(effectOrder));
            ShopifyPlanFeatureDTO planFeatureDTO = new ShopifyPlanFeatureDTO();

            // 查询当前生效的套餐
            SubscriptionPlan effectPlan = Optional.ofNullable(effectOrder)
                    .map(SubscriptionOrder::getSubscriptionPlanId)
                    .map(planId ->
                            subscriptionPlanRepository.queryByPlanId(planId, false))
                    // 查不到当前生效的订单时, 默认取免费套餐使用
                    .orElseGet(() ->
                            subscriptionPlanRepository.queryFreePlan(appCode, false));
            log.info("ShopifySubscriptionServiceImpl#getSubscribeProfile, query plan finished, result={}", JSON.toJSONString(effectPlan));

            // 查询下一个套餐
            SubscriptionPlan nextPlan = subscriptionPlanRepository.queryNextPlan(appCode, true, effectPlan.getDurationUnit().name(), effectPlan.getPrice());
            planFeatureDTO.setPlanId(Optional.ofNullable(nextPlan).map(SubscriptionPlan::getId).orElse(null));
            if (nextPlan != null) {
                ShopifyPlanFeatureDTO.ShopifyPlanSimpleDTO simpleDTO = new ShopifyPlanFeatureDTO.ShopifyPlanSimpleDTO();
                BeanUtils.copyProperties(nextPlan, simpleDTO);
                planFeatureDTO.setPlanDTO(simpleDTO);
                Map<SeoFeatureType, ShopifyFeatureDTO> featureDTOMap = new HashMap<>();
                for (SubscriptionFeature feature : nextPlan.getFeatures()) {
                    ShopifyFeatureDTO shopifyFeatureDTO = new ShopifyFeatureDTO();
                    shopifyFeatureDTO.setHasAuthorize(true);
                    shopifyFeatureDTO.setDepletion(feature.getIsDepletion());
                    if (feature.getBenefit() != null && feature.getBenefit().getAttributes() != null) {
                        int quota = Integer.parseInt(String.valueOf(feature.getBenefit().getAttributes().get("quota")));
                        shopifyFeatureDTO.setRemainQuota((long) quota);
                    }
                    featureDTOMap.put(SeoFeatureType.valueOf(feature.getType()), shopifyFeatureDTO);
                }
                planFeatureDTO.setFeatureDTOMap(featureDTOMap);
                return planFeatureDTO;
            }
            return null;
        } catch (Exception e) {
            log.error("ShopifySubscriptionServiceImpl#getFeatureMapping exp,shopifyFeatureQuery={}", JSON.toJSONString(shopifyFeatureQuery), e);
        }
        return null;
    }


    @Override
    public ShopifyPlanFeatureDTO getFeatureMapping(ShopifyFeatureQuery shopifyFeatureQuery) {
        log.info("ShopifySubscriptionServiceImpl#getFeatureMapping,shopifyFeatureQuery={}", JSON.toJSONString(shopifyFeatureQuery));
        ShopifyPlanFeatureDTO planFeatureDTO = new ShopifyPlanFeatureDTO();
        Map<SeoFeatureType, ShopifyFeatureDTO> featureDTOMap = new HashMap<>();
        if (shopifyFeatureQuery == null || StringUtils.isBlank(shopifyFeatureQuery.getAppCode())
                || shopifyFeatureQuery.getId() == null) {
            return null;
        }
        try {
            SubscriptionPlan plan = getSubscriptionPlan(shopifyFeatureQuery);
            planFeatureDTO.setPlanId(Optional.ofNullable(plan).map(SubscriptionPlan::getId).orElse(null));
            if (plan != null) {
                ShopifyPlanFeatureDTO.ShopifyPlanSimpleDTO simpleDTO = new ShopifyPlanFeatureDTO.ShopifyPlanSimpleDTO();
                BeanUtils.copyProperties(plan, simpleDTO);
                planFeatureDTO.setPlanDTO(simpleDTO);
            }
//            List<SubscriptionFeature> effectPlanFeatures = getEffectiveFeatures(shopifyFeatureQuery);
            List<SubscriptionFeature> effectPlanFeatures = Optional.ofNullable(plan).map(SubscriptionPlan::getFeatures).orElse(null);
            if (CollectionUtil.isEmpty(effectPlanFeatures)) {
                return null;
            }
            for (SubscriptionFeature feature : effectPlanFeatures) {
                ShopifyFeatureDTO shopifyFeatureDTO = new ShopifyFeatureDTO();
                shopifyFeatureDTO.setHasAuthorize(true);
                if (feature.getIsDepletion()) {
                    shopifyFeatureDTO.setDepletion(true);
                    shopifyFeatureQuery.setFeatureType(feature.getType());
                    FeatureUsage featureUsage = subscriptionPlanRepository.queryFeatureUsage(shopifyFeatureQuery);
                    if (featureUsage != null) {
                        //在有效期内有用量记录
//                        shopifyFeatureDTO.setRemainQuota(featureUsage.getQuota() - featureUsage.getUsageCount());
                        shopifyFeatureDTO.setRemainQuota(feature.getBenefit().getAsLong("quota") - featureUsage.getUsageCount());
                    } else {
                        shopifyFeatureDTO.setRemainQuota(feature.getBenefit().getAsLong("quota"));
                    }
                } else {
                    shopifyFeatureDTO.setDepletion(false);
                    if (feature.getBenefit() != null && feature.getBenefit().containsKey("quota")) {
                        shopifyFeatureDTO.setRemainQuota(feature.getBenefit().getAsLong("quota"));
                    }
                }
                featureDTOMap.put(SeoFeatureType.valueOf(feature.getType()), shopifyFeatureDTO);
            }
            planFeatureDTO.setFeatureDTOMap(featureDTOMap);
            return planFeatureDTO;
        } catch (Exception e) {
            log.error("ShopifySubscriptionServiceImpl#getFeatureMapping exp,shopifyFeatureQuery={}", JSON.toJSONString(shopifyFeatureQuery), e);
        }
        return null;
    }

    @Override
    public Boolean incFeatureUsage(ShopifyFeatureQuery shopifyFeatureQuery) {
        if (shopifyFeatureQuery == null || StringUtils.isBlank(shopifyFeatureQuery.getFeatureType())
                || StringUtils.isBlank(shopifyFeatureQuery.getAppCode())
                || shopifyFeatureQuery.getId() == null) {
            return null;
        }
        SubscriptionPlan plan = getSubscriptionPlan(shopifyFeatureQuery);
        if (plan == null || CollectionUtil.isEmpty(plan.getFeatures())) {
            return null;
        }

        for (SubscriptionFeature feature : plan.getFeatures()) {
            if (StringUtils.equals(feature.getType(), shopifyFeatureQuery.getFeatureType())) {
                if (feature.getIsDepletion()) {
                    //消耗型选择性
                    FeatureUsage featureUsage = subscriptionPlanRepository.queryFeatureUsage(shopifyFeatureQuery);

                    // 当用户升级了计划后，将旧计划作废
                    if (featureUsage != null && featureUsage.getAttributes() != null &&
                            StringUtils.isNotBlank(featureUsage.getAttributes().getPlanName())
                            && !Objects.equals(plan.getName(), featureUsage.getAttributes().getPlanName())) {
                        featureUsage.setEndTime(new Date());
                        subscriptionPlanRepository.saveFeatureUsage(featureUsage);
                        featureUsage = null;
                    }

                    if (featureUsage != null) {
                        //在有效期内有用量记录 +1
                        featureUsage.setUsageCount(featureUsage.getUsageCount() + 1);
                    } else {
                        //在有效期内无用量记录
                        featureUsage = buildFeatureUsage(feature, shopifyFeatureQuery);
                        featureUsage.setUsageCount(1L);
                    }

                    if (Objects.equals(shopifyFeatureQuery.getAppCode(), AppEnum.DS_COPILOT.getCode())) {
                        featureUsage.getAttributes().setPlanName(plan.getName());
                    }
                    subscriptionPlanRepository.saveFeatureUsage(featureUsage);
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public Boolean rollbackFeatureUsage(ShopifyFeatureQuery shopifyFeatureQuery) {
        if (shopifyFeatureQuery == null || StringUtils.isBlank(shopifyFeatureQuery.getFeatureType())
                || StringUtils.isBlank(shopifyFeatureQuery.getAppCode())
                || shopifyFeatureQuery.getId() == null) {
            return null;
        }
        List<SubscriptionFeature> effectPlanFeatures = getEffectiveFeatures(shopifyFeatureQuery);
        if (CollectionUtil.isEmpty(effectPlanFeatures)) {
            return null;
        }
        for (SubscriptionFeature feature : effectPlanFeatures) {
            if (StringUtils.equals(feature.getType(), shopifyFeatureQuery.getFeatureType())) {
                if (feature.getIsDepletion()) {
                    //消耗型选择性
                    FeatureUsage featureUsage = subscriptionPlanRepository.queryFeatureUsage(shopifyFeatureQuery);
                    if (featureUsage != null && featureUsage.getUsageCount() > 0) {
                        //在有效期内有用量记录 +1
                        featureUsage.setUsageCount(featureUsage.getUsageCount() - 1);
                    }
                    subscriptionPlanRepository.saveFeatureUsage(featureUsage);
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public Boolean refreshFeatureUsage(ShopifyFeatureQuery shopifyFeatureQuery) {
        if (shopifyFeatureQuery == null || StringUtils.isBlank(shopifyFeatureQuery.getFeatureType())
                || StringUtils.isBlank(shopifyFeatureQuery.getAppCode())
                || shopifyFeatureQuery.getId() == null) {
            return null;
        }
        List<SubscriptionFeature> effectPlanFeatures = getEffectiveFeatures(shopifyFeatureQuery);
        if (CollectionUtil.isEmpty(effectPlanFeatures)) {
            return null;
        }
        for (SubscriptionFeature feature : effectPlanFeatures) {
            if (StringUtils.equals(feature.getType(), shopifyFeatureQuery.getFeatureType())) {
                if (feature.getIsDepletion()) {
                    //消耗型选择性
                    FeatureUsage featureUsage = subscriptionPlanRepository.queryFeatureUsage(shopifyFeatureQuery);
                    if (featureUsage != null) {
                        featureUsage.setUsageCount(0L);
                        subscriptionPlanRepository.saveFeatureUsage(featureUsage);
                    }
                }
            }
        }
        return true;
    }

    @Override
    public ShopifySubscriptionDTO getShopifySubscriptionDTO(ShopifySubscribablePlanQueryDTO shopifySubscribablePlanQueryDTO) {

        ShopifySubscriptionDTO shopifySubscriptionDTO = new ShopifySubscriptionDTO();
        SubscriptionPlan subscriptionPlan = subscriptionPlanRepository.queryByPlanId(shopifySubscribablePlanQueryDTO.getPlanId(), false);
        shopifySubscriptionDTO.setPlanId(subscriptionPlan.getId());
        shopifySubscriptionDTO.setAppCode(subscriptionPlan.getAppCode());
        shopifySubscriptionDTO.setName(subscriptionPlan.getName());
        shopifySubscriptionDTO.setPrice(subscriptionPlan.getPrice());
        shopifySubscriptionDTO.setDuration(subscriptionPlan.getDuration());
        shopifySubscriptionDTO.setDurationUnit(subscriptionPlan.getDurationUnit());
        return shopifySubscriptionDTO;
    }

    /**
     * build FeatureUsage
     *
     * @param feature
     * @param shopifyFeatureQuery
     * @return
     */
    private FeatureUsage buildFeatureUsage(SubscriptionFeature feature, ShopifyFeatureQuery shopifyFeatureQuery) {
        Date now = new Date();
        Date endTime = null;

        try {
            // 注释迁移：DSC过来的请求结束时间为订阅计划对应的结束时间，否则保持原逻辑不动
            if (StringUtils.equals(shopifyFeatureQuery.getAppCode(), AppEnum.DS_COPILOT.getCode())) {
                endTime = TimeUtils.getEndTime(now, Long.parseLong(String.valueOf(feature.getBenefit().getDuration())), feature.getBenefit().getDurationUnit());
            } else if (StringUtils.equals(shopifyFeatureQuery.getAppCode(), AppEnum.SEO_COPILOT.getCode()) || StringUtils.equals(shopifyFeatureQuery.getAppCode(), AppEnum.SEO_COPILOT_SITE.getCode())) {
                if (feature.getBenefit().getDuration() == 1) {
                    endTime = TimeUtils.getEndOfDay();
                } else {
                    endTime = TimeUtils.getEndTime(now, Long.parseLong(String.valueOf(feature.getBenefit().getDuration())), feature.getBenefit().getDurationUnit());
                }
            } else {
                endTime = TimeUtils.getEndOfDay();
            }
        } catch (Exception e) {
            log.error("ShopifySubscriptionServiceImpl.buildFeatureUsage exception, shopifyFeatureQuery={}, feature={}", JSON.toJSONString(shopifyFeatureQuery), feature, e);
            endTime = TimeUtils.getEndOfDay();
        }

        FeatureUsage featureUsage = FeatureUsage.builder()
                .gmtCreate(now)
                .gmtCreate(now)
                .appCode(feature.getAppCode())
                .userId(shopifyFeatureQuery.getId())
                .featureType(feature.getType())
                .quota(feature.getBenefit().getAsLong("quota"))
                .usageCount(0L)
                .startTime(TimeUtils.getStartOfDay())
                .endTime(endTime)
                .deleted(false)
                .attributes(new FeatureUsageAttributes("{}"))
                .build();
        return featureUsage;
    }

    private SubscriptionPlan getSubscriptionPlan(ShopifyFeatureQuery shopifyFeatureQuery) {
        if (shopifyFeatureQuery == null
//                || StringUtils.isBlank(shopifyFeatureQuery.getFeatureType())
                || StringUtils.isBlank(shopifyFeatureQuery.getAppCode())
                || shopifyFeatureQuery.getId() == null) {
            return null;
        }
        try {
            // 查询当前生效的订单
            SubscriptionOrder effectOrder = orderRepository.queryEffectOrder(shopifyFeatureQuery.getAppCode(), shopifyFeatureQuery.getId());
            log.info("orderRepository.queryEffectOrder, getEffectOrder, result={}", JSON.toJSONString(effectOrder));
            // 查询当前生效的套餐
            SubscriptionPlan effectPlan = Optional.ofNullable(effectOrder)
                    .map(SubscriptionOrder::getSubscriptionPlanId)
                    .map(planId ->
                            subscriptionPlanRepository.queryByPlanId(planId, true))
                    // 查不到当前生效的订单时, 默认取免费套餐使用
                    .orElseGet(() ->
                            subscriptionPlanRepository.queryFreePlan(shopifyFeatureQuery.getAppCode(), true));
            log.info("ShopifySubscriptionServiceImpl#getFeature, query plan finished, result={}", JSON.toJSONString(effectPlan));

            if (effectPlan == null || CollectionUtils.isEmpty(effectPlan.getFeatures())) {
                return null;
            }
            return effectPlan;
        } catch (Exception e) {
            log.error("getEffectiveFeatures exp,shopifyFeatureQuery={}", shopifyFeatureQuery, e);
        }
        return null;
    }

    /**
     * 获取当前用户在appcode下的所有有效features
     *
     * @param shopifyFeatureQuery
     * @return
     */
    private List<SubscriptionFeature> getEffectiveFeatures(ShopifyFeatureQuery shopifyFeatureQuery) {
        SubscriptionPlan plan = getSubscriptionPlan(shopifyFeatureQuery);
        if (plan == null) {
            return null;
        }
        return plan.getFeatures();
    }
}
