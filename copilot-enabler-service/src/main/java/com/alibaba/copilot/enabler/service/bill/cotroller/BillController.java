package com.alibaba.copilot.enabler.service.bill.cotroller;


import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.bill.dto.BillItemDTO;
import com.alibaba.copilot.enabler.client.bill.dto.QueryBillDTO;
import com.alibaba.copilot.enabler.client.bill.dto.QueryBillResultDTO;
import com.alibaba.copilot.enabler.client.bill.service.BillService;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.service.bill.helper.BillItemFieldParser;
import com.alibaba.copilot.enabler.service.bill.manager.BillTokenManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 账单接口<hr/>
 * 注：账单是高风险查询，因此该类的所有接口, 均需要令牌验证才能调用<br/>
 * 如需生成新令牌，请 <a href="https://pre-copilot-enabler.alibaba-inc.com/api/bill/new/generateToken">点击此处</a>
 *
 * <AUTHOR>
 * @version 2024/3/26
 */
@SuppressWarnings("MVCPathVariableInspection")
@Slf4j
@Api(tags = "对账接口")
@RestController
@RequestMapping("/api/bill/{token}")
public class BillController {

    private static final String FORMATTER_TARGET_DATE = "yyyy-MM";

    @javax.annotation.Resource
    private BillService billService;

    @ApiOperation("生成令牌")
    @GetMapping("/generateToken")
    public ModelAndView generateToken() {
        String privateToken = UUID.randomUUID().toString().toLowerCase().replace("-", "");
        String publicToken = BillTokenManager.convertPrivateToken2PublicToken(privateToken);

        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("bill-token-generator");
        modelAndView.addObject("privateToken", privateToken);
        modelAndView.addObject("publicToken", publicToken);

        return modelAndView;
    }

    @ApiOperation("查询账单信息")
    @GetMapping("/query")
    public SingleResult<QueryBillResultDTO> queryBill(
            @ApiParam(value = "应用标识") @RequestParam AppEnum appCode,
            @ApiParam(value = "目标日期") @RequestParam String targetDate,
            @ApiParam(value = "支付类型") @RequestParam(required = false) String paymentType
    ) {
        log.info("start, appCode={}, targetDate={}", appCode, targetDate);

        // 查询账单结果
        QueryBillResultDTO billResultDTO = queryBillResultDTO(appCode, targetDate, paymentType);

        // 返回结果
        return SingleResult.buildSuccess(billResultDTO);
    }

    @ApiOperation("下载账单信息")
    @GetMapping("/download")
    public ResponseEntity<Resource> downloadBill(
            @ApiParam(value = "应用标识") @RequestParam AppEnum appCode,
            @ApiParam(value = "目标日期") @RequestParam String targetDate,
            @ApiParam(value = "支付类型") @RequestParam(required = false) String paymentType
    ) {
        log.info("start, appCode={}, targetDate={}", appCode, targetDate);

        // 查询账单结果
        QueryBillResultDTO billResultDTO = queryBillResultDTO(appCode, targetDate, paymentType);

        // 生成 Excel 文件
        byte[] excelBytes = generateExcel(billResultDTO);

        // 包装为 Resource 对象
        InputStreamResource resource = new InputStreamResource(new ByteArrayInputStream(excelBytes));

        // 设置响应头，指定文件名
        String now = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        String filename = String.format("%s_%s.xlsx", now, targetDate);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, String.format("attachment; filename=%s", filename));

        // 返回 ResponseEntity
        return ResponseEntity
                .ok()
                .headers(headers)
                .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
                .contentLength(excelBytes.length)
                .body(resource);
    }

    private byte[] generateExcel(QueryBillResultDTO billResultDTO) {
        try (
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                Workbook workbook = new XSSFWorkbook();
        ) {
            Sheet sheet = workbook.createSheet(billResultDTO.getTitle());

            // Create header row
            Row headerRow = sheet.createRow(0);
            List<BillItemFieldParser.FieldItem> fieldItems = BillItemFieldParser.getFieldItems();
            for (int i = 0; i < fieldItems.size(); i++) {
                headerRow.createCell(i).setCellValue(fieldItems.get(i).getFieldAlias());
            }

            // Fill data rows
            List<BillItemDTO> items = billResultDTO.getBillItems();
            int rowNum = 1;
            for (BillItemDTO item : items) {
                Row row = sheet.createRow(rowNum++);

                for (int i = 0; i < fieldItems.size(); i++) {
                    String fieldName = fieldItems.get(i).getFieldName();
                    Object value = BillItemFieldParser.getValue(item, fieldName);
                    row.createCell(i).setCellValue(formatCellValue(value));
                }
            }

            workbook.write(outputStream);
            return outputStream.toByteArray();
        } catch (IOException e) {
            log.error("Error generating Excel: {}", e.getMessage());
            return new byte[0];
        }
    }

    private static String formatCellValue(Object value) {
        if (value == null) {
            return "";
        }
        if (value instanceof Date) {
            return DateUtil.format((Date) value, DatePattern.NORM_DATETIME_PATTERN);
        } else if (value instanceof BigDecimal) {
            // 保留两位小数
            return ((BigDecimal) value).setScale(10, RoundingMode.HALF_UP).toString();
        } else if (value instanceof Boolean) {
            return ((Boolean) value) ? "1" : "0";
        }
        return String.valueOf(value);
    }

    private QueryBillResultDTO queryBillResultDTO(AppEnum appCode, String targetDateStr, String paymentType) {
        SimpleDateFormat formatter = new SimpleDateFormat(FORMATTER_TARGET_DATE);
        final Date targetDate;
        try {
            targetDate = formatter.parse(targetDateStr);
        } catch (ParseException e) {
            throw new RuntimeException(String.format("targetDate's format muse be \"%s\"", FORMATTER_TARGET_DATE), e);
        }

        QueryBillDTO requestDTO = new QueryBillDTO()
                .setAppCode(appCode.getCode())
                .setPaymentType(paymentType)
                .setTargetDate(targetDate);
        return billService.queryBill(requestDTO);
    }


}
