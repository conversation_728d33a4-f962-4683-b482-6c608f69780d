package com.alibaba.copilot.enabler.service.payment.metaq.handler;

import com.alibaba.aepay.fund.business.api.payment.dto.notify.RefundNotifyDTO;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.payment.constant.StatusFlowReasonEnum;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageStatusEnum;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageTypeEnum;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.domain.payment.model.MessageInfo;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentInfo;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.service.OrderDomainService;
import com.alibaba.copilot.enabler.infra.base.utils.MoneyUtils;
import com.alibaba.global.money.Money;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindException;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 退款通知handler
 */
@Slf4j
@Component
public class PaymentRefundNotifyHandler {

    @Autowired
    private OrderDomainService orderDomainService;

    /**
     * 支付退款处理
     */
    @Transactional(rollbackFor = Exception.class, noRollbackFor = BindException.class)
    public void handle(String tradeNo, String msgId, String notifyContentStr, RefundNotifyDTO refundNotifyDTO) {

        TradeRecord tradeRecord = orderDomainService.getTradeRecord(tradeNo);
        if (tradeRecord == null) {
            return;
        }

        BigDecimal tradeAmount = tradeRecord.getTradeAmount();
        Money refundAmount = refundNotifyDTO.getRefundAmount();

        Assertor.asserts(refundAmount != null
                        && refundAmount.getCurrency() != null
                        && Objects.equals(refundAmount.getCurrency().getCurrencyCode(), AEPaymentConstants.CURRENCY_CODE)
                        && MoneyUtils.isEqualBigDecimal(refundAmount.getAmount(), tradeAmount),
                "refundAmount incorrect"
        );

        MessageInfo messageInfo = MessageInfo.builder()
                .msgId(msgId)
                .notifyContentOrRequestParam(notifyContentStr)
                .response(ConsumeConcurrentlyStatus.CONSUME_SUCCESS.name())
                .typeEnum(MessageTypeEnum.PAYMENT_REFUND_NOTIFY)
                .statusEnum(MessageStatusEnum.SUCCESS)
                .entityId(tradeNo)
                .build();

        PaymentInfo paymentInfo = PaymentInfo.builder()
                .outTradeNo(refundNotifyDTO.getRefundId())
                .tradeTime(refundNotifyDTO.getRefundTime())
                .build();

        orderDomainService.refundSuccess(tradeRecord, messageInfo, paymentInfo, StatusFlowReasonEnum.USER_CANCEL_SUBSCRIBE);
    }

    /**
     * 支付退款处理
     */
    @Transactional(rollbackFor = Exception.class, noRollbackFor = BindException.class)
    public void handleFail(String tradeNo, String msgId, String notifyContentStr) {

        TradeRecord refundRecord = orderDomainService.getTradeRecord(tradeNo);
        if (refundRecord == null) {
            return;
        }

        // 告警通知
        log.error("payAlarm-支付退款通知失败,tradeNo:{},msgId:{},appCode:{},userId:{},notifyContent:{}",
                tradeNo, msgId, refundRecord.getAppCode(), refundRecord.getUserId(), notifyContentStr);

        // 保存消息记录
        MessageInfo messageInfo = MessageInfo.builder()
                .msgId(msgId)
                .notifyContentOrRequestParam(notifyContentStr)
                .response(ConsumeConcurrentlyStatus.CONSUME_SUCCESS.name())
                .typeEnum(MessageTypeEnum.PAYMENT_REFUND_NOTIFY)
                .statusEnum(MessageStatusEnum.FAIL)
                .entityId(tradeNo)
                .build();
        orderDomainService.saveMessageRecord(messageInfo, refundRecord.getAppCode(), refundRecord.getUserId());
    }
}
