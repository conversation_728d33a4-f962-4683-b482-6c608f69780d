package com.alibaba.copilot.enabler.service.bill.interceptor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.copilot.enabler.infra.base.utils.EnvUtils;
import com.alibaba.copilot.enabler.service.bill.manager.BillTokenManager;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 2024/3/28
 */
@Slf4j
@Component
public class BillInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response,
                             @NotNull Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            // 线上环境禁止调用
            if (EnvUtils.isOnline()) {
                log.warn("online env, do not allow access");
                response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                return false;
            }

            // generateToken方法不校验
            String methodName = ((HandlerMethod) handler).getMethod().getName();
            if ("generateToken".equals(methodName)) {
                return true;
            }

            if (!isAuthed(request)) {
                response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                return false;
            }
        }
        return true;
    }

    private boolean isAuthed(HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.warn("start, requestURI={}", requestURI);

        // 解析token
        String token = parseToken(requestURI);
        log.info("token: {}", token);

        // token为空时拦截
        if (StrUtil.isEmpty(token)) {
            log.warn("token is empty");
            return false;
        }

        // token不存在时拦截
        String nickname = BillTokenManager.parseNickname(token);
        if (StrUtil.isEmpty(nickname)) {
            log.warn("token is illegal, token={}", token);
            return false;
        }

        log.info("validate pass, nickname: {}", nickname);
        return true;
    }

    private static String parseToken(String requestURI) {
        try {
            return requestURI.replace("/api/bill/", "")
                    .replace("/api/alphaRankBill/", "")
                    .split("/")[0];
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public void afterCompletion(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response,
                                @NotNull Object handler, Exception ex) throws Exception {
        if (ex != null) {
            log.error("interceptor invoke error", ex);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
}
