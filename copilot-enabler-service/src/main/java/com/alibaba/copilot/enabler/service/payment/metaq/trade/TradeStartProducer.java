package com.alibaba.copilot.enabler.service.payment.metaq.trade;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.common.message.Message;
import com.taobao.metaq.client.MetaProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import java.time.Duration;

/**
 * <AUTHOR>
 * @version 2023/10/16
 */
@Slf4j
@Component
public class TradeStartProducer {

    /**
     * MQ消息的投递延时 (为了解决MQ消费早于数据落库导致的查询不到数据问题)
     */
    private static final Duration MQ_POST_DELAY_DURATION = Duration.ofSeconds(3);

    private MetaProducer producer;

    @PostConstruct
    private void init() throws MQClientException {
        producer = new MetaProducer(TradeConst.PRODUCER_GROUP);
        producer.start();
    }

    /**
     * 投递支付开始的消息
     *
     * @param payTradeNo 支付交易号
     */
    public void postPayStartMessage(String payTradeNo, String appCode) {
        log.info("postPayStartMessage, payTradeNo={}", payTradeNo);
        TradeStartDTO tradeStartDTO = new TradeStartDTO()
                .setTradeNo(payTradeNo)
                .setAppCode(appCode);
        Message message = new Message(TradeConst.TOPIC, TradeConst.TAG_PAY, JSON.toJSONBytes(tradeStartDTO));
        postMQWithDelay(() -> {
            try {
                SendResult result = producer.send(message);
                log.info("postPayStartMessage, send finished, result={}", JSON.toJSONString(result));
            } catch (Exception e) {
                log.error("postPayStartMessage, send error", e);
            }
        });
    }

    /**
     * 投递退款开始的消息
     *
     * @param refundTradeNo 退款交易号
     */
    public void postRefundStartMessage(String refundTradeNo, String appCode) {
        log.info("postRefundStartMessage, refundTradeNo={}", refundTradeNo);
        TradeStartDTO tradeStartDTO = new TradeStartDTO()
                .setTradeNo(refundTradeNo)
                .setAppCode(appCode);
        Message message = new Message(TradeConst.TOPIC, TradeConst.TAG_REFUND, JSON.toJSONBytes(tradeStartDTO));

        postMQWithDelay(() -> {
            try {
                SendResult result = producer.send(message);
                log.info("postRefundStartMessage, send finished, result={}", JSON.toJSONString(result));
            } catch (Exception e) {
                log.error("postRefundStartMessage, send error", e);
            }
        });
    }

    private void postMQWithDelay(@Nonnull Runnable runnable) {
        ThreadUtil.execute(() -> {
            ThreadUtil.sleep(MQ_POST_DELAY_DURATION.toMillis());
            runnable.run();
        });
    }
}
