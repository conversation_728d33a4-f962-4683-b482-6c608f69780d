package com.alibaba.copilot.enabler.service.subscription.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 可选择的订阅计划
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class SubscribablePlanVO {

    /**
     * app name
     */
    String appName;

    /**
     * 订阅计划ID
     */
    Long planId;

    /**
     * 计划周期（按年/按月）
     */
    String planDurationUnit;

    /**
     * 计划名称
     */
    String planName;

    /**
     * 计划描述
     */
    String planDescription;

    /**
     * 划线价（年度才有，取值为月度X12)
     */
    BigDecimal crossedPrice;

    /**
     * 折扣信息（折扣数据）
     */
    BigDecimal discount;

    /**
     * 当前计划ID
     */
    Long currentPlanId;

    /**
     * 计划原价（subscription_plan price字段）
     */
    BigDecimal originPlanPrice;

    /**
     * 计划折扣价 （考虑推广折扣）
     */
    BigDecimal discountPlanPrice;

    /**
     * 折扣周期（区别推广域返回的周期）
     */
    Long discountDuration;

    /**
     * 折扣周期单位
     */
    String discountDurationUnit;

    /**
     * 特性名称集合
     */
    List<String> featureNames;
}
