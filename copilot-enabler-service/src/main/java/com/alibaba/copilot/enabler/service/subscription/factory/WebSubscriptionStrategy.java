package com.alibaba.copilot.enabler.service.subscription.factory;

import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanResultDTO;

/**
 * 订阅策略
 *
 * <AUTHOR>
 * @date 2024/9/27 上午11:00
 */
public interface WebSubscriptionStrategy {

    /**
     * 订阅
     *
     * @param context
     * @return
     */
    SubscribePlanResultDTO subscribe(WebSubscriptionContext context);

    /**
     * 取消订阅
     *
     * @param context
     * @return
     */
    Boolean cancelSubscribedPlan(WebSubscriptionContext context);
}
