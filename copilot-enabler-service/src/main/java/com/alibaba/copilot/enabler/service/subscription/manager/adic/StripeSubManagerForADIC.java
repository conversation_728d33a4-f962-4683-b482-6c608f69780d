package com.alibaba.copilot.enabler.service.subscription.manager.adic;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanResultDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscriptionOrderDTO;
import com.alibaba.copilot.enabler.client.subscription.service.SubscriptionService;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.base.exception.BizException;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.service.stripe.StripeService;
import com.alibaba.copilot.enabler.service.subscription.constant.SubscriptionConstants;
import com.alibaba.copilot.enabler.service.subscription.manager.AbstractSubscriptionManager;
import com.alibaba.copilot.enabler.service.subscription.manager.BizSubscriptionContext;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.Price;
import com.stripe.model.PriceCollection;
import com.stripe.model.Subscription;
import com.stripe.model.SubscriptionCollection;
import com.stripe.model.checkout.Session;
import com.stripe.model.checkout.SessionCollection;
import com.stripe.param.PriceListParams;
import com.stripe.param.SubscriptionListParams;
import com.stripe.param.SubscriptionUpdateParams;
import com.stripe.param.checkout.SessionCreateParams;
import com.stripe.param.checkout.SessionListParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class StripeSubManagerForADIC  extends AbstractSubscriptionManager {

    @Resource
    private SubscriptionService subscriptionService;
    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;
    @Resource
    private StripeService stripeService;
    @Resource
    private TradeRecordRepository tradeRecordRepository;

    @Override
    protected SingleResult<SubscribePlanResultDTO> doSubscribe(BizSubscriptionContext context) {
        try {
            SubscribePlanResultDTO stripeOrder = createStripeOrder(context.getSubscribePlanDTO());
            return  SingleResult.buildSuccess(stripeOrder);
        } catch (Exception e) {
            log.error("StripeSubManagerForADIC error ", e);
            return SingleResult.buildFailure(e.getMessage());
        }
    }

    public static void main(String[] args) throws StripeException {
        SubscribePlanDTO subscribePlanDTO = new SubscribePlanDTO();
        subscribePlanDTO.setPaymentPlanKey("ADIC#20250212#7900");
        subscribePlanDTO.setEmail("<EMAIL>");
        subscribePlanDTO.setUserId(3L);
        subscribePlanDTO.setRedirectUrl("https://qq.com");
        new StripeSubManagerForADIC().createStripeOrder(subscribePlanDTO);

//        Stripe.apiKey = SwitchConfig.stripeApiKey;
//        Subscription subscription = Subscription.retrieve("sub_1QrtPwBUuFYymNc8wF9Fvfh7");
//        log.info(JSON.toJSONString(subscription));
    }

    public Boolean cancelOrderForADIC(Long userId){
        try {
            SubscriptionOrder subscriptionOrder = subscriptionOrderRepository.queryEffectOrder(AppEnum.ADIC.getCode(), userId);
            String stripeSubscriptionId = subscriptionOrder.getAttributes().getStripeSubscriptionId();
            Subscription subscription = Subscription.retrieve(stripeSubscriptionId);
            // 取消订阅，并设置为当前周期结束后取消
            subscription.update(SubscriptionUpdateParams.builder().setCancelAtPeriodEnd(true).build());
            return true;
        } catch (StripeException e) {
            log.error("cancelOrderForADIC error", e);
            return false;
        }
    }



    private SubscribePlanResultDTO createStripeOrder(SubscribePlanDTO subscribePlanDTO) throws StripeException {
        Stripe.apiKey = SwitchConfig.stripeApiKey;
        PriceListParams priceParams = PriceListParams.builder()
                .addLookupKey(subscribePlanDTO.getPaymentPlanKey())
                .build();
        PriceCollection prices = Price.list(priceParams);
        String stripeCustomerId = stripeService.getOrCreateStripeCustomer(subscribePlanDTO.getAppCode(), subscribePlanDTO.getUserId());

        // 获取该客户的所有订阅
        SubscriptionListParams subscritionListParam = SubscriptionListParams.builder()
                .setCustomer(stripeCustomerId) // 根据 customerId 查询
                .setStatus(SubscriptionListParams.Status.ACTIVE) // 只获取有效的订阅
                .build();

        SubscriptionCollection subscriptions = Subscription.list(subscritionListParam);
        if (CollectionUtils.isNotEmpty(subscriptions.getData())) {
            throw new RuntimeException("User has a activate subscription");
        }

        SubscriptionOrderDTO subscriptionOrderDTO = new SubscriptionOrderDTO();
        subscriptionOrderDTO.setUserId(subscribePlanDTO.getUserId());
        subscriptionOrderDTO.setSubscriptionPlanId(0L);
        subscriptionOrderDTO.setSubscriptionPlanName(subscribePlanDTO.getPlanName());
        subscriptionOrderDTO.setAppCode(subscribePlanDTO.getAppCode());
        subscriptionOrderDTO.setEmail(subscriptionOrderDTO.getEmail());
        subscriptionOrderDTO.setPlanPrice(BigDecimal.ZERO);
        subscriptionOrderDTO.setPerformStartTime(new Date());
        subscriptionOrderDTO.setPerformEndTime(new Date());
        Long subscriptionOrderId = subscriptionService.createSubscriptionOrderNoPlan(subscriptionOrderDTO);

        // 添加订阅的元数据信息。后续状态变化时从这里取数据
        Map<String, String> metaDataMap = new HashMap<>();
        metaDataMap.put(SubscriptionConstants.META_APP_CODE, String.valueOf(subscribePlanDTO.getAppCode()));
        metaDataMap.put(SubscriptionConstants.META_USER_ID, String.valueOf(subscribePlanDTO.getUserId()));
        metaDataMap.put(SubscriptionConstants.META_SUB_ID, String.valueOf(subscriptionOrderId));


        // 获取当前时间戳
        Instant now = Instant.now();
        // 加上1小时
        Instant oneHourLater = now.plusSeconds(3600);
        // 获取1小时后的秒级时间戳
        long timestampOneHourLater = oneHourLater.getEpochSecond();

        // 将 stripe 侧的未付款session设置为取消
        SessionCollection sessions = Session.list(
                SessionListParams.builder()
                        .setCustomer(stripeCustomerId) // 用户的客户 ID
                        .setStatus(SessionListParams.Status.OPEN) // 获取未支付的 Session
                        .build()
        );
        for (Session session : sessions.getData()) {
            session.expire();
        }


        // 创建新的 stripe 订单。
        SessionCreateParams params = SessionCreateParams.builder()
                .setUiMode(SessionCreateParams.UiMode.EMBEDDED)
                .addLineItem(SessionCreateParams.LineItem.builder()
                        .setPrice(prices.getData().get(0).getId())
                        .setQuantity(1L)
                        .build())
                .setCustomer(stripeCustomerId)
                .setSubscriptionData(SessionCreateParams.SubscriptionData.builder()
                        .putAllMetadata(metaDataMap)
                        .build())
                .putAllMetadata(metaDataMap)
                .setAllowPromotionCodes(true)
                .setExpiresAt(timestampOneHourLater)
                .setMode(SessionCreateParams.Mode.SUBSCRIPTION)
                .setReturnUrl(subscribePlanDTO.getRedirectUrl())
                .setRedirectOnCompletion(StringUtils.isBlank(subscribePlanDTO.getRedirectUrl()) ?
                        SessionCreateParams.RedirectOnCompletion.NEVER : SessionCreateParams.RedirectOnCompletion.ALWAYS)
                .build();
        Session session = Session.create(params);

        SubscribePlanResultDTO subscribePlanResultDTO = new SubscribePlanResultDTO();
        subscribePlanResultDTO.setClientSecret(session.getClientSecret());
        subscribePlanResultDTO.setOrderId(subscriptionOrderId);

        log.info("StripeSubscriptionStrategy.doSubscribeByStripe, userId={}, input={}, session={}", subscribePlanDTO.getUserId(), JSONObject.toJSONString(subscribePlanDTO), JSON.toJSONString(session));

        return subscribePlanResultDTO;
    }

    @Override
    public SubscriptionPayType getSubscriptionPayType() {
        return SubscriptionPayType.STRIPE;
    }

    @Override
    public String getAppCode() {
        return AppEnum.ADIC.getCode();
    }
}
