package com.alibaba.copilot.enabler.service.antom.mq;

import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentStatusEnum;
import com.alibaba.copilot.enabler.client.payment.dto.PaymentResultDTO;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.domain.base.exception.BizNeedRetryException;
import com.alibaba.copilot.enabler.domain.base.mq.MessageHandler;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.service.PaymentDomainService;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.service.antom.processor.AntomEventProcessor;
import com.alibaba.copilot.enabler.service.antom.processor.AntomEventProcessorFactory;
import com.alibaba.copilot.enabler.service.antom.processor.AntomExecuteContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.alibaba.copilot.enabler.service.antom.AntomConsts.*;

/**
 * Antom Webhook 消息处理器
 *
 * <AUTHOR>
 * @date 2025/05/01
 */
@Component
@Slf4j
public class AntomWebhookMsgHandler implements MessageHandler<JSONObject> {

    @Autowired
    private PaymentDomainService paymentDomainService;

    @Autowired
    private SubscriptionOrderRepository subscriptionOrderRepository;

    @Override
    public void handle(JSONObject eventJsonObj) {
        try {
            Assertor.assertNonNull(eventJsonObj, "event obj is null");
            log.info("AntomWebhookMsgHandler handle event: {}", eventJsonObj);

            // 根据不同的通知类型处理
            if (eventJsonObj.containsKey("notifyType")) {
                // 支付通知
                handlePaymentNotification(eventJsonObj);
            } else if (eventJsonObj.containsKey("authorizationNotifyType")) {
                // 授权通知
                handleAuthorizationNotification(eventJsonObj);
            } else if (eventJsonObj.containsKey("disputeNotificationType")) {
                // 争议通知
                handleDisputeNotification(eventJsonObj);
            } else if (eventJsonObj.containsKey("refundStatus")) {
                // 退款通知
                handleRefundNotification(eventJsonObj);
            } else {
                log.warn("Unknown antom event type, event={}", eventJsonObj);
            }
        } catch (BizNeedRetryException e) {
            log.error("AntomWebhookMsgHandler error, will retry, event={}", eventJsonObj, e);
            // 异常继续向上抛，mq 消息会重试
            throw e;
        } catch (Exception e) {
            // 仅打印日志，不处理异常
            log.error("AntomWebhookMsgHandler error, no retry, event={}", eventJsonObj, e);
        }
    }

    /**
     * 处理支付通知
     */
    private void handlePaymentNotification(JSONObject eventJsonObj) {
        String notifyType = eventJsonObj.getString("notifyType");
        Assertor.assertNotBlank(notifyType, "notifyType is blank");

        if (!PAYMENT_NOTIFY_TYPES.contains(notifyType)) {
            log.warn("Unknown payment notify type: {}, event={}", notifyType, eventJsonObj);
            return;
        }

        // 获取支付请求ID
        String paymentRequestId = eventJsonObj.getString("paymentRequestId");
        if (StringUtils.isBlank(paymentRequestId)) {
            log.warn("Payment request ID is blank, event={}", eventJsonObj);
            return;
        }

        // 获取支付结果
        JSONObject resultObj = eventJsonObj.getJSONObject("result");
        if (resultObj == null) {
            log.warn("Result object is null, event={}", eventJsonObj);
            return;
        }

        String resultStatus = resultObj.getString("resultStatus");
        if (StringUtils.isBlank(resultStatus)) {
            log.warn("Result status is blank, event={}", eventJsonObj);
            return;
        }

        // 查询交易记录
        TradeRecord tradeRecord = paymentDomainService.queryByOutTradeNoAndTradeDirection(paymentRequestId, TradeDirection.FORWARD);
        if (tradeRecord == null) {
            // 如果交易记录不存在，尝试通过 paymentRequestId 查询订阅订单
            // 在某些情况下，paymentRequestId 可能是 subscriptionOrderId
            try {
                Long subscriptionOrderId = Long.parseLong(paymentRequestId);
                SubscriptionOrder subscriptionOrder = subscriptionOrderRepository.getByOrderId(subscriptionOrderId);
                if (subscriptionOrder != null) {
                    // 找到订阅订单，设置上下文并处理
                    processWithSubscriptionOrder(notifyType, eventJsonObj, subscriptionOrder);
                    return;
                }
            } catch (NumberFormatException e) {
                log.warn("Failed to parse paymentRequestId as subscriptionOrderId: {}", paymentRequestId);
            }

            log.warn("Trade record not found for payment request ID: {}", paymentRequestId);
            return;
        }
        // 如果有订阅订单ID，则设置上下文并处理
        if (tradeRecord.getSubscriptionOrderId() != null) {
            SubscriptionOrder subscriptionOrder = subscriptionOrderRepository.getByOrderId(tradeRecord.getSubscriptionOrderId());
            if (subscriptionOrder != null) {
                processWithSubscriptionOrder(notifyType, eventJsonObj, subscriptionOrder);
            }
        }
    }

    /**
     * 使用订阅订单处理通知
     */
    private void processWithSubscriptionOrder(String notifyType, JSONObject eventJsonObj, SubscriptionOrder subscriptionOrder) {
        String appCode = subscriptionOrder.getAppCode();

        // 设置上下文
        AntomExecuteContext context = new AntomExecuteContext();
        context.setNotifyType(notifyType);
        context.setEvent(eventJsonObj);
        context.setAppCode(appCode);
        context.setSubscriptionOrderId(subscriptionOrder.getId());
        context.setUserId(subscriptionOrder.getUserId());

        // 获取处理器进行处理
        try {
            AntomEventProcessor processor = AntomEventProcessorFactory.getProcessorNotNull(context);
            processor.process(context);
        } catch (RuntimeException e) {
            log.warn("No processor found for notifyType={}, appCode={}", notifyType, appCode);
        }
    }

    /**
     * 处理授权通知
     */
    private void handleAuthorizationNotification(JSONObject eventJsonObj) {
        String authNotifyType = eventJsonObj.getString("authorizationNotifyType");
        Assertor.assertNotBlank(authNotifyType, "authorizationNotifyType is blank");

        if (!AUTH_NOTIFY_TYPES.contains(authNotifyType)) {
            log.warn("Unknown auth notify type: {}, event={}", authNotifyType, eventJsonObj);
            return;
        }

        // 获取授权码
        String authCode = eventJsonObj.getString("authCode");
        if (StringUtils.isBlank(authCode)) {
            log.warn("Auth code is blank, event={}", eventJsonObj);
            return;
        }

        // 获取用户ID
        String userId = eventJsonObj.getString("userId");
        if (StringUtils.isBlank(userId)) {
            log.warn("User ID is blank, event={}", eventJsonObj);
            return;
        }

        // TODO: 根据实际业务需求处理授权通知
        log.info("Received authorization notification: type={}, authCode={}, userId={}",
                authNotifyType, authCode, userId);
    }

    /**
     * 处理争议通知
     */
    private void handleDisputeNotification(JSONObject eventJsonObj) {
        String disputeNotifyType = eventJsonObj.getString("disputeNotificationType");
        Assertor.assertNotBlank(disputeNotifyType, "disputeNotificationType is blank");

        if (!DISPUTE_NOTIFY_TYPES.contains(disputeNotifyType)) {
            log.warn("Unknown dispute notify type: {}, event={}", disputeNotifyType, eventJsonObj);
            return;
        }

        // 获取支付ID
        String paymentId = eventJsonObj.getString("paymentId");
        if (StringUtils.isBlank(paymentId)) {
            log.warn("Payment ID is blank, event={}", eventJsonObj);
            return;
        }

        // TODO: 根据实际业务需求处理争议通知
        log.info("Received dispute notification: type={}, paymentId={}",
                disputeNotifyType, paymentId);
    }

    /**
     * 处理退款通知
     */
    private void handleRefundNotification(JSONObject eventJsonObj) {
        String refundStatus = eventJsonObj.getString("refundStatus");
        Assertor.assertNotBlank(refundStatus, "refundStatus is blank");

        if (!REFUND_NOTIFY_STATUS.contains(refundStatus)) {
            log.warn("Unknown refund status: {}, event={}", refundStatus, eventJsonObj);
            return;
        }

        // 获取退款ID
        String refundId = eventJsonObj.getString("refundId");
        if (StringUtils.isBlank(refundId)) {
            log.warn("Refund ID is blank, event={}", eventJsonObj);
            return;
        }

        // 获取支付ID
        String paymentId = eventJsonObj.getString("paymentId");
        if (StringUtils.isBlank(paymentId)) {
            log.warn("Payment ID is blank, event={}", eventJsonObj);
            return;
        }

        // TODO: 根据实际业务需求处理退款通知
        log.info("Received refund notification: status={}, refundId={}, paymentId={}",
                refundStatus, refundId, paymentId);
    }
}
