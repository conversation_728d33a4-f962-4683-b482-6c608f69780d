package com.alibaba.copilot.enabler.service.stripe.processor.refund;

import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.base.utils.PaymentUtils;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEvent;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.service.stripe.processor.AbstractStripeEventProcessor;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeExecuteContext;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

import static com.alibaba.copilot.enabler.service.stripe.StripeConsts.REFUND_UPDATED;

@Slf4j
@Component
public class RefundUpdatedProcessor4T2g extends AbstractStripeEventProcessor {
    public String getEventType() {
        return REFUND_UPDATED;
    }

    @Override
    public String getAppCode() {
        return AppEnum.TEXT2GO.getCode();
    }

    @Override
    protected void doProcess(StripeExecuteContext context) throws Exception {
        log.info("RefundSucceededProcessor4T2g, context = {}", JSON.toJSONString(context));
        StripeEvent event = context.getEvent();
        String status = event.fetch("status");
        if (!"succeeded".equals(status)) {
            return;
        }
        // 插入交易流水表
        TradeRecord tradeRecord = new TradeRecord();
        tradeRecord.setAppCode(context.getMetaData().getAppCode());
        tradeRecord.setUserId(Long.parseLong(context.getMetaData().getUserId()));
        tradeRecord.setPaymentType(PaymentTypeEnum.INITIATED_PAYMENT);
        tradeRecord.setStatus(TradeRecordStatus.SUCC);
        tradeRecord.setTradeDirection(TradeDirection.REFUND);
        tradeRecord.setTradeAmount(new BigDecimal(event.fetch("amount")).divide(new BigDecimal("100")).abs());
        tradeRecord.setTradeCurrency(event.fetch("currency"));
        tradeRecord.setSubscriptionOrderId(Long.parseLong(context.getMetaData().getSubscriptionOrderId()));
        tradeRecord.setTradeNo(PaymentUtils.generateTradeNo(context.getMetaData().getUserId()));
        tradeRecord.setPaymentMethod(PaymentMethodEnum.STRIPE.getValue());
        tradeRecord.setOutTradeNo(context.getEvent().fetch("id"));
        String refundCreated = context.getEvent().fetch("created");
        if (StringUtils.isNotBlank(refundCreated)) {
            tradeRecord.setTradeTime(new Date(Long.parseLong(refundCreated) * 1000L));
        }
        tradeRecord.setDeleted(false);
        tradeRecordRepository.createOrUpdateTradeRecord(tradeRecord);
    }
}
