package com.alibaba.copilot.enabler.service.marketing.facade;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.marketing.dto.FirstMonthDiscountDTO;
import com.alibaba.copilot.enabler.client.marketing.dto.QueryFirstMonthDiscountDTO;
import com.alibaba.copilot.enabler.client.marketing.facade.FirstMonthDiscountHsfApi;
import com.alibaba.copilot.enabler.client.marketing.service.FirstMonthDiscountService;
import com.alibaba.copilot.enabler.infra.base.utils.ModelConvertUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2024/2/27
 */
@Slf4j
@Service
@HSFProvider(serviceInterface = FirstMonthDiscountHsfApi.class)
public class FirstMonthDiscountHsfApiImpl implements FirstMonthDiscountHsfApi {

    @Resource
    private FirstMonthDiscountService firstMonthDiscountService;

    @Override
    public SingleResult<Map<Long, FirstMonthDiscountDTO>> queryFirstMonthDiscount(QueryFirstMonthDiscountDTO discountDTO) {
        log.info("queryFirstMonthDiscount discountDTO={}", JSON.toJSONString(discountDTO));

        try {
            Map<Long, FirstMonthDiscountDTO> planId2Discount = firstMonthDiscountService.queryFirstMonthDiscount(discountDTO);
            return SingleResult.buildSuccess(planId2Discount);
        } catch (Exception e) {
            return ModelConvertUtils.wrapExceptionResult(e);
        }
    }
}
