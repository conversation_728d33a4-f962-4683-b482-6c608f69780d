package com.alibaba.copilot.enabler.service.stripe.processor.paymentintent;

import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEventMetadata;
import com.alibaba.copilot.enabler.service.stripe.StripeConsts;
import com.alibaba.copilot.enabler.service.stripe.processor.AbstractStripeEventProcessor;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeExecuteContext;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 处理 payment_intent.payment_failed webhook 事件
 */
@Slf4j
@Component
public class PaymentIntentPaymentFailedProcessor4Pic extends AbstractStripeEventProcessor {

    @Override
    public String getEventType() {
        return StripeConsts.PAYMENT_INTENT_PAYMENT_FAILED;
    }

    @Override
    public String getAppCode() {
        return AppEnum.PIC_COPILOT.getCode();
    }

    @Override
    protected void doProcess(StripeExecuteContext context) {
        log.info("PaymentIntentPaymentFailedProcessor4Pic context={}", JSON.toJSONString(context));

        StripeEventMetadata metaData = context.getMetaData();

        if (metaData.whetherPaymentMode()) {
            // PAYMENT 模式
            processFailed(metaData);
        } else if(metaData.whetherSetupMode()) {
            picStripeSubscriptionService.handlePaymentIntentFailed4SetupMode(context);
        }

    }

}