package com.alibaba.copilot.enabler.service.user.factory;

import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.client.user.constants.UserAppBindStatusEnum;
import com.alibaba.copilot.enabler.client.user.request.AppBindingRequest;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.model.UserAppRelation;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/5
 */
@Setter
public class UserLoginBindAppRelationBuilder implements Builder<UserAppRelation> {
    private User user;
    private AppBindingRequest appBindingRequest;

    @Override
    public UserAppRelation build() {
        return UserAppRelation.builder()
                .userId(user.getUserId())
                .email(appBindingRequest.getEmail())
                .appCode(appBindingRequest.getAppCode())
                .appName(AppEnum.getAppByCode(appBindingRequest.getAppCode()).getName())
                .appType(AppEnum.getAppByCode(appBindingRequest.getAppCode()).getType())
                .bindSource(appBindingRequest.getBindSource())
                .bindStatus(UserAppBindStatusEnum.BINDING.value())
                .build();
    }
}
