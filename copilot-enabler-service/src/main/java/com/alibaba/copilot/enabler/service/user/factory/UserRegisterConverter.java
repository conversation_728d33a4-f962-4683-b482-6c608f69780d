package com.alibaba.copilot.enabler.service.user.factory;

import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.copilot.enabler.client.user.request.UserRegisterRequest;
import com.alibaba.copilot.enabler.service.user.dto.UicUserRegisterDTO;


/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/31
 */
public class UserRegisterConverter implements Converter<UicUserRegisterDTO, UserRegisterRequest> {
    public static final Converter<UicUserRegisterDTO, UserRegisterRequest> INSTANCE = new UserRegisterConverter();

    @Override
    public UserRegisterRequest convertA2B(UicUserRegisterDTO uicUserRegisterDTO) {
        UserRegisterRequest registerRequest = new UserRegisterRequest();
        if (uicUserRegisterDTO == null) {
            return registerRequest;
        }
        registerRequest.setUserId(uicUserRegisterDTO.getUserId());
        registerRequest.setEmail(uicUserRegisterDTO.getEmail());
        registerRequest.setBindSource(uicUserRegisterDTO.getBindSource());
        registerRequest.setBindStatus(uicUserRegisterDTO.getBindStatus());
        registerRequest.setAppCode(uicUserRegisterDTO.getAppCode());
        registerRequest.setAppName(uicUserRegisterDTO.getAppName());
        registerRequest.setAppType(uicUserRegisterDTO.getAppType());
        registerRequest.setSignUpChannel(uicUserRegisterDTO.getSignUpChannel());
        registerRequest.setSignUpSource(uicUserRegisterDTO.getSignUpSource());
        registerRequest.setShareCode(uicUserRegisterDTO.getShareCode());
        return registerRequest;
    }

    @Override
    public UicUserRegisterDTO convertB2A(UserRegisterRequest userRegisterRequest) {
        UicUserRegisterDTO uicUserRegisterDTO = new UicUserRegisterDTO();
        if (userRegisterRequest == null) {
            return uicUserRegisterDTO;
        }
        uicUserRegisterDTO.setUserId(userRegisterRequest.getUserId());
        uicUserRegisterDTO.setEmail(userRegisterRequest.getEmail());
        uicUserRegisterDTO.setBindSource(userRegisterRequest.getBindSource());
        uicUserRegisterDTO.setBindStatus(userRegisterRequest.getBindStatus());
        uicUserRegisterDTO.setAppCode(userRegisterRequest.getAppCode());
        uicUserRegisterDTO.setAppName(userRegisterRequest.getAppName());
        uicUserRegisterDTO.setAppType(userRegisterRequest.getAppType());
        uicUserRegisterDTO.setSignUpChannel(userRegisterRequest.getSignUpChannel());
        uicUserRegisterDTO.setSignUpSource(userRegisterRequest.getSignUpSource());
        return uicUserRegisterDTO;
    }
}
