package com.alibaba.copilot.enabler.service.user.factory;

import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.client.user.constants.UserAppBindStatusEnum;
import com.alibaba.copilot.enabler.service.user.dto.UicUserRegisterDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/30
 */
@Setter
public class UicUserRegisterBuilder implements Builder<UicUserRegisterDTO> {
    /**
     * UIC 注册消息体
     */
    private String registerMsgContent;

    @Override
    public UicUserRegisterDTO build() {
        UicRegisterInfo uicRegisterInfo = JSON.parseObject(registerMsgContent, UicRegisterInfo.class);
        if (uicRegisterInfo == null || uicRegisterInfo.getUserId() == null || uicRegisterInfo.getUserDTO() == null) {
            return null;
        }

        UicRegisterInfo.UicContext uicContext = uicRegisterInfo.getUicContext();
        if (uicContext == null || uicContext.getAttributesMap() == null) {
            return null;
        }

        UicRegisterInfo.RegisterUserInfo registerUserInfo = uicRegisterInfo.getUserDTO();
        return UicUserRegisterDTO.builder()
                .userId(uicRegisterInfo.getUserId())
                .email(registerUserInfo.getEmail())
                .signUpSource(uicContext.getAttributesMap().get("tag"))
                .signUpChannel(uicRegisterInfo.getBusinessType())
                .appCode(uicContext.getAttributesMap().get("tag"))
                .appName(AppEnum.getAppByCode(uicContext.getAttributesMap().get("tag")).getName())
                .appType(AppEnum.getAppByCode(uicContext.getAttributesMap().get("tag")).getType())
                .bindStatus(UserAppBindStatusEnum.BINDING.value())
                .bindSource(uicContext.getAttributesMap().get("tag"))
                .shareCode(uicContext.getAttributesMap().get("shareCode"))
                .build();
    }

    @Data
    public static class UicRegisterInfo {
        private String siteId;
        private String platform;

        @JSONField(name = "uicContext")
        private UicContext uicContext;

        private String messageId;
        private Long userId;
        private String businessType;
        private String bizUniqueId;

        @JSONField(name = "userDTO")
        private RegisterUserInfo userDTO;

        private boolean success;

        @Data
        public static class UicContext {
            private String clientIp;
            private String platform;
            private String language;
            private String websiteLanguage;
            private String sid;
            private String umidToken;
            private Map<String, String> attributesMap;
        }

        @Data
        public static class RegisterUserInfo {
            private long userId;
            private boolean isAdminUser;
            private String email;
            private String loginId;
            private String firstName;
            private String fullName;
            private int userStatus;
            private String userType;
            private boolean enableEwallet;
            private boolean liveUpUser;
        }
    }
}
