package com.alibaba.copilot.enabler.service.payment.facade;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.payment.dto.CashierPayResultDTO;
import com.alibaba.copilot.enabler.client.payment.facade.PaymentHsfApi;
import com.alibaba.copilot.enabler.client.payment.request.CashierPayRequest;
import com.alibaba.copilot.enabler.client.payment.service.PaymentService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.alibaba.copilot.enabler.infra.base.utils.ModelConvertUtils.wrapExceptionResult;

/**
 * 支付HSF接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@HSFProvider(serviceInterface = PaymentHsfApi.class)
public class PaymentHsfApiImpl implements PaymentHsfApi {

    @Resource
    private PaymentService paymentService;

    @Override
    public SingleResult<CashierPayResultDTO> cashierPay(CashierPayRequest request) {
        try {
            return paymentService.cashierPay(request);
        } catch (Exception e) {
            log.error("PaymentHsfApi#cashierPay Exception, request={}", JSON.toJSONString(request), e);
            return wrapExceptionResult(e);
        }
    }
}
