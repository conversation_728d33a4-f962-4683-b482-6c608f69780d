package com.alibaba.copilot.enabler.service.stripe.processor.paymentintent;

import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.constant.PaymentConst;
import com.alibaba.copilot.enabler.domain.payment.dto.PaymentAsyncResultDTO;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEventMetadata;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.service.stripe.StripeConsts;
import com.alibaba.copilot.enabler.service.stripe.processor.AbstractStripeEventProcessor;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeExecuteContext;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 处理 payment_intent.succeeded webhook 事件
 */

@Slf4j
@Component
public class PaymentIntentSucceededProcessor4Pic extends AbstractStripeEventProcessor {

    @Override
    public String getEventType() {
        return StripeConsts.PAYMENT_INTENT_SUCCEEDED;
    }

    @Override
    public String getAppCode() {
        return AppEnum.PIC_COPILOT.getCode();
    }

    @Override
    protected void doProcess(StripeExecuteContext context) {
        log.info("PaymentIntentSucceededProcessor4Pic context={}", JSON.toJSONString(context));

        StripeEventMetadata metaData = context.getMetaData();

        if (metaData.whetherPaymentMode()) {
            // PAYMENT 模式
            PaymentAsyncResultDTO result = buildPaymentResult(metaData, PaymentConst.PAYMENT_SUCCESS);
            paymentDomainService.updateCashierPayStatus(result);
        } else if (metaData.whetherSetupMode()) {
            picStripeSubscriptionService.handlePaymentIntentSucceed4SetupMode(context);
        }

        // out trace no
        String paymentIntentId = context.getEvent().fetchPaymentIntentId();
        paymentDomainService.updateOutTradeNo(metaData.getTradeNo(), paymentIntentId);

        // card info
        String pmId = context.getEvent().fetchPaymentMethodId();
        Long pId = saveCardInfo2PaymentToken(pmId, metaData);

        if (metaData.whetherSetupMode()) {
            String subscriptionOrderId = metaData.getSubscriptionOrderId();
            SubscriptionOrder subscriptionOrder = subscriptionOrderRepository.getByOrderId(Long.valueOf(subscriptionOrderId));
            savePaymentToken2SubOrder(pId, pmId, subscriptionOrder);
        }

    }

}