package com.alibaba.copilot.enabler.service.user.facade;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.payment.dto.UserAccountDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.UserPaymentCardDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.client.user.dto.UserDetailDTO;
import com.alibaba.copilot.enabler.client.user.facade.UserTokenHsfApi;
import com.alibaba.copilot.enabler.client.user.service.UserTokenService;
import com.alibaba.copilot.enabler.domain.base.utils.Md5Utils;
import com.alibaba.copilot.enabler.domain.base.utils.RedisUtils;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentToken;
import com.alibaba.copilot.enabler.domain.payment.repository.PaymentTokenRepository;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.model.UserAttributes;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.infra.base.utils.ModelConvertUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2024/1/7
 */
@Service
@Slf4j
@HSFProvider(serviceInterface = UserTokenHsfApi.class)
public class UserTokenHsfApiImpl implements UserTokenHsfApi {
    @Resource
    private UserTokenService userTokenService;
    @Resource
    private UserRepository userRepository;
    @Resource
    private PaymentTokenRepository paymentTokenRepository;

    /**
     * redis中存储用户的token对应的key
     */
    private static final String PAYMENT_USER_TOKEN_FORMAT_REDIS_KEY = "PAYMENT_TOKEN_%s_USER_ID_%s";

    @Override
    public SingleResult<String> genToken(Long userId, String shopDomain) {
        String token = userTokenService.genToken(userId, shopDomain);
        return SingleResult.buildSuccess(token);
    }

    @Override
    public SingleResult<UserDetailDTO> checkToken(String token) {
        if (StringUtils.isBlank(token)) {
            return SingleResult.buildFailure(ErrorCode.TOKEN_INVALID.getCode(), ErrorCode.TOKEN_INVALID.getMessage());
        }

        User oneShopUser = null;
        List<User> users = userRepository.allUsers();
        if (!CollectionUtils.isEmpty(users)) {
            oneShopUser = users.stream()
                    .filter(user -> {
                        UserAttributes attributes = user.getAttributes();
                        if (attributes != null && StringUtils.isNotBlank(attributes.getOneShopUserToken())) {
                            return attributes.getOneShopUserToken().equals(token);
                        }
                        return false;
                    })
                    .findAny().orElse(null);
            if (oneShopUser == null) {
                return SingleResult.buildFailure(ErrorCode.TOKEN_INVALID.getCode(), ErrorCode.TOKEN_INVALID.getMessage());
            }

            return SingleResult.buildSuccess(
                    UserDetailDTO.builder()
                            .token(oneShopUser.getAttributes().getOneShopUserToken())
                            .userId(oneShopUser.getUserId())
                            .build());
        }

        return SingleResult.buildSuccess(null);
    }

    @Override
    public SingleResult<String> generatePayUserToken(AppEnum appEnum, Long userId, String email) {
        if (userId == null) {
            return SingleResult.buildFailure("userId illegal");
        }

        if (appEnum == null) {
            return SingleResult.buildFailure("appEnum illegal");
        }

        String userToken = Md5Utils.getPayUserToken(userId, appEnum);
        String key = String.format(PAYMENT_USER_TOKEN_FORMAT_REDIS_KEY, appEnum.getCode(), userToken);

        if (RedisUtils.get(key) != null) {
            return SingleResult.buildSuccess(userToken);
        }

        UserAccountDTO userAccountDTO = new UserAccountDTO();
        userAccountDTO.setUserId(userId);
        userAccountDTO.setEmail(email);
        RedisUtils.setex(key, SwitchConfig.payUserTokenEffectiveSeconds, JSON.toJSONString(userAccountDTO));
        return SingleResult.buildSuccess(userToken);
    }

    @Override
    public SingleResult<UserAccountDTO> queryPayUserToken(AppEnum appEnum, String userToken) {
        String key = String.format(PAYMENT_USER_TOKEN_FORMAT_REDIS_KEY, appEnum.getCode(), userToken);

        String userTairAccountStr = RedisUtils.get(key);
        if (StringUtils.isBlank(userTairAccountStr)) {
            return null;
        }

        UserAccountDTO userAccountDTO = new UserAccountDTO();
        if (NumberUtils.isCreatable(userTairAccountStr)) {
            userAccountDTO.setUserId(NumberUtils.createLong(userTairAccountStr));
        } else {
            userAccountDTO = JSON.parseObject(userTairAccountStr, UserAccountDTO.class);
        }

        return SingleResult.buildSuccess(userAccountDTO);
    }

    @Override
    public SingleResult<UserAccountDTO> getUserToken(Long userId, String appCode) {
        UserAccountDTO user = new UserAccountDTO();
        user.setAppCode(appCode);
        user.setUserId(userId);

        // 1. 查询卡片列表信息
        List<UserPaymentCardDTO> cardList = Optional.ofNullable(userRepository.getUser(userId))
                .map(User::getUserPaymentCardList)
                .map(cards -> ModelConvertUtils.copyListByReflect(cards, UserPaymentCardDTO::new))
                .orElseGet(Lists::newArrayList);
        user.setCardList(cardList);

        // 2. 支付方式和支付TokenId的映射关系
        Map<String, String> result = new HashMap<>();
        Map<PaymentMethodEnum, PaymentToken> method2Token = paymentTokenRepository.queryAuthedTokensByUser(appCode, userId);
        method2Token.forEach((methodEnum, paymentToken) -> result.put(methodEnum.name(), paymentToken.getTokenId()));
        user.setPaymentMethodToTokenId(result);

        // 3. 查询Stripe一次性支付信息
        List<PaymentToken> paymentTokens = paymentTokenRepository.queryTokenByPaymentMethod(appCode, userId, PaymentMethodEnum.STRIPE);

        if (!CollectionUtils.isEmpty(paymentTokens)) {
            Optional<PaymentToken> paymentTokenOptional = paymentTokens.stream().filter(paymentToken ->
                            paymentToken.getAttributes() != null)
                    .findFirst();
            paymentTokenOptional.ifPresent(paymentToken -> {
                UserPaymentCardDTO userPaymentCardDTO = new UserPaymentCardDTO();
                userPaymentCardDTO.setFunding(paymentToken.getAttributes().getStripeInfoNotNull().getFunding());
                userPaymentCardDTO.setCardNo(paymentToken.getAttributes().getStripeInfoNotNull().getCardLast4());
                userPaymentCardDTO.setEncryptedCardToken(null);
                userPaymentCardDTO.setCardBrand(paymentToken.getAttributes().getStripeInfoNotNull().getCardBrand());
                user.setStripeOneTimePaymentCard(userPaymentCardDTO);
            });
        }
        return SingleResult.buildSuccess(user);
    }
}
