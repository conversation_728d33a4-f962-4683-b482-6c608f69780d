package com.alibaba.copilot.enabler.service.bill.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.bill.dto.AlphaRankBillItemDTO;
import com.alibaba.copilot.enabler.client.bill.dto.BillItemDTO;
import com.alibaba.copilot.enabler.client.bill.dto.QueryBillDTO;
import com.alibaba.copilot.enabler.client.bill.dto.QueryBillResultDTO;
import com.alibaba.copilot.enabler.client.bill.service.AlphaRankBillService;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.domain.base.utils.TimeUtils;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.domain.payment.model.IpAddressInfo;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrderAttributes;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.service.bill.helper.TimeHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2024/3/21
 */
@Slf4j
@Service
public class AlphaRankBillServiceImpl implements AlphaRankBillService {

    private static final SimpleDateFormat FORMATTER_BILL_MONTH = new SimpleDateFormat("yyyy-MM");
    private static final SimpleDateFormat FORMATTER_BILL_TITLE = new SimpleDateFormat("yyyy-MM");
    public static final int SCALE = 6;

    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;

    @Resource
    private TradeRecordRepository tradeRecordRepository;

    @Resource
    private SubscriptionPlanRepository subscriptionPlanRepository;

    @Override
    public QueryBillResultDTO queryBill(QueryBillDTO requestDTO) {
        log.info("requestDTO={}", requestDTO);
        List<String> appCodeList = requestDTO.getAppCodeList();
        Date targetDate = requestDTO.getTargetDate();
        Assertor.assertNotEmpty(appCodeList, "appCode can not be blank");
        Assertor.assertNonNull(targetDate, "targetDate can not be null");

        // 计算时间节点
        TimeHelper timeHelper = new TimeHelper(targetDate);

        // 查询目标月内生效过的订单
        List<SubscriptionOrder> orders;
        if (CollectionUtils.isNotEmpty(requestDTO.getPayType()) && requestDTO.getPayType().contains(SubscriptionPayType.Shoplazza.name())) {
            orders = subscriptionOrderRepository.queryListForBill(
                    appCodeList, timeHelper.getBillMonthStartTimeShoplazza(), timeHelper.getBillMonthEndTimeShoplazza()
            );
        } else {
            orders = subscriptionOrderRepository.queryListForBill(
                    appCodeList, timeHelper.getMonthStartTime(), timeHelper.getMonthEndTime()
            );
        }

        if (CollectionUtils.isNotEmpty(requestDTO.getPayType())) {
            orders = orders.stream().filter(o -> requestDTO.getPayType().contains(o.getSubscriptionPayType().name())).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(requestDTO.getExcludePayType())) {
            orders = orders.stream().filter(o -> !requestDTO.getExcludePayType().contains(o.getSubscriptionPayType().name())).collect(Collectors.toList());
        }

        List<Long> orderIds = orders.stream().map(SubscriptionOrder::getId).collect(Collectors.toList());
        log.info("found {} orders", orderIds.size());
       if (CollectionUtils.isEmpty(orderIds)) {
           String title = String.format("%s账单", FORMATTER_BILL_TITLE.format(targetDate));
           return new QueryBillResultDTO()
                   .setTitle(title)
                   .setTotalCount(0)
                   .setBillItems(new ArrayList<>());
       }

        // 查询订单对应的所有套餐
        List<Long> planIds = transform(orders, SubscriptionOrder::getSubscriptionPlanId);
        List<SubscriptionPlan> plans = subscriptionPlanRepository.queryByIds(planIds);
        Map<Long, SubscriptionPlan> planId2Plan = extractMapping(plans, SubscriptionPlan::getId);
        log.info("found {} plans", plans.size());

        // 查询成功支付账单
        List<TradeRecord> payRecords = tradeRecordRepository.queryPaySuccessRecordsForBill(orderIds);
        Map<Long, TradeRecord> orderId2PayRecord = extractMapping(payRecords, TradeRecord::getSubscriptionOrderId);
        log.info("found {} pay records", payRecords.size());

        // 查询对应的退款账单
        List<TradeRecord> refundRecords = tradeRecordRepository.queryRefundRecordsForBill(orderIds);
        Map<Long, TradeRecord> orderId2RefundRecord = extractMapping(refundRecords, TradeRecord::getSubscriptionOrderId);
        log.info("found {} refund records", refundRecords.size());

        List<BillItemDTO> items = new ArrayList<>();
        for (SubscriptionOrder order : orders) {
            Long orderId = order.getId();
            Long planId = order.getSubscriptionPlanId();

            SubscriptionOrderAttributes attributes = order.getAttributes();
            Long trialDays = attributes.getTrialDays();

            TradeRecord payRecord = orderId2PayRecord.get(orderId);
            if (payRecord == null) {
                log.warn("payRecord is null, orderId={}, orderStatus={}", orderId, order.getStatus());
                continue;
            }

            TradeRecord refundRecord = orderId2RefundRecord.get(orderId);
            SubscriptionPlan plan = planId2Plan.get(planId);

            Date tradeTime = payRecord.getTradeTime();
            String vipLevel = String.format("%s %s", plan.getName(), plan.getDurationUnit().name());
            BigDecimal tradeAmount = payRecord.getTradeAmount();
            BigDecimal taxRate = BigDecimal.ZERO;
            BigDecimal taxAmount = tradeAmount.multiply(taxRate).setScale(SCALE, RoundingMode.HALF_UP);
            BigDecimal tradeAmountWithoutTax = tradeAmount.subtract(taxAmount).setScale(SCALE, RoundingMode.HALF_UP);
            Date performStartTime = order.getPerformStartTime();
            Date performEndTime = order.getPerformEndTime();
            long planDays = TimeUtils.calculateDayCount(plan.getDuration(), plan.getDurationUnit());

            // 取订单金额/套餐天数, 作为套餐平均每天的单价
            BigDecimal amountPerDay = tradeAmountWithoutTax.divide(BigDecimal.valueOf(planDays), SCALE, RoundingMode.HALF_UP);

            // 取订单的试用期天数
            long orderTrialDays = Optional.ofNullable(trialDays).orElse(0L);

            // 计算当月末的时间戳
            Date monthEndTime = timeHelper.getMonthEndTime();
            // 计算试用期结束的时间
            Date trialEndTime = DateUtils.addDays(performStartTime, (int) orderTrialDays);
            // 有试用期, 且试用期到本月末结束时还在, 则认为是"截止本月末仍在免费退订期内"
            boolean canUnsubscribeAtCurrentMonthEnd = orderTrialDays > 0 && monthEndTime.before(trialEndTime);

            // 计算上月末的时间戳
            Date lastMonthEndTime = timeHelper.getLastMonthEndTime();
            // 计算截止上月末累计已经使用的天数
            int usedDaysUntilLastMonthEnd = computePlanDays(performStartTime, performEndTime, lastMonthEndTime, planDays);

            // 用户是否在目标月退订了
            boolean unsubscribed = refundRecord != null && refundRecord.getGmtCreate().before(monthEndTime);
            Date unsubscribedTime = unsubscribed ? refundRecord.getGmtCreate() : null;

            // 是否在本月末前已结束 (针对订A的当月升级B时的A的场景)
            boolean endBeforeCurrentMonthEnd = !unsubscribed && performEndTime.getTime() <= monthEndTime.getTime();

            // 截至上月末累计已确认收入
            BigDecimal totalTradeAmountUntilLastMonthEnd = amountPerDay.multiply(BigDecimal.valueOf(usedDaysUntilLastMonthEnd))
                    .setScale(SCALE, RoundingMode.HALF_UP);
            // 当月收款金额
            final BigDecimal totalTradeAmountAtCurrentMonth;
            if (unsubscribed) {
                // 退订场景
                totalTradeAmountAtCurrentMonth = DateUtil.isSameMonth(targetDate, tradeTime)
                        // 退订本月的订单, 当月收款金额取0
                        ? BigDecimal.ZERO
                        // 退订非本月的订单, 当月收款金额取交易值的负数
                        : tradeAmount.multiply(BigDecimal.valueOf(-1));
            } else {
                // 未退订场景, 本月的交易取全额, 非本月交易取0
                totalTradeAmountAtCurrentMonth = DateUtil.isSameMonth(targetDate, tradeTime) ? tradeAmount : BigDecimal.ZERO;
            }
            // 截止本月末累计已经使用的天数
            int usedDaysUtilCurrentMonthEnd = unsubscribed
                    ? 0 : computePlanDays(performStartTime, performEndTime, monthEndTime, planDays);
            // 本月的计费天数 = 截止本月末累计已经使用的天数 - 截止上月末累计已经使用的天数
            int usedDaysForCurrentMonth = usedDaysUtilCurrentMonthEnd - usedDaysUntilLastMonthEnd;
            // 当月收入
            BigDecimal totalTradeAmountCurrentMonth = endBeforeCurrentMonthEnd
                    ? tradeAmount.subtract(totalTradeAmountUntilLastMonthEnd)
                    : amountPerDay.multiply(BigDecimal.valueOf(usedDaysForCurrentMonth)).setScale(10, RoundingMode.HALF_UP);
            // 截止本月末累计已确认收入
            BigDecimal totalTradeAmountUntilCurrentMonthEnd = totalTradeAmountUntilLastMonthEnd.add(totalTradeAmountCurrentMonth)
                    .setScale(SCALE, RoundingMode.HALF_UP);
            if (totalTradeAmountUntilCurrentMonthEnd.compareTo(tradeAmountWithoutTax) > 0) {
                // 兜底处理, 防止超过最大值
                totalTradeAmountUntilCurrentMonthEnd = tradeAmountWithoutTax;
            }
            // 待确收
            BigDecimal willReceiveAmount = unsubscribed || endBeforeCurrentMonthEnd
                    ? BigDecimal.ZERO
                    : tradeAmountWithoutTax.subtract(totalTradeAmountUntilCurrentMonthEnd).setScale(SCALE, RoundingMode.HALF_UP);

            AlphaRankBillItemDTO item = (AlphaRankBillItemDTO) new AlphaRankBillItemDTO()
                    .setBillMonth(FORMATTER_BILL_MONTH.format(targetDate))
                    .setUserId(order.getUserId())
                    .setTradeNo(payRecord.getTradeNo())
                    .setVipLevel(vipLevel)
                    .setCurrencyUnit(AEPaymentConstants.CURRENCY_CODE)
                    .setTradeAmount(tradeAmount.setScale(3, RoundingMode.HALF_UP))
                    .setTaxRate(taxRate)
                    .setTaxAmount(taxAmount.setScale(3, RoundingMode.HALF_UP))
                    .setTradeAmountWithoutTax(tradeAmountWithoutTax.setScale(3, RoundingMode.HALF_UP))
                    .setAmountPerDay(amountPerDay.setScale(3, RoundingMode.HALF_UP))
                    .setTradeTime(tradeTime)
                    .setVipStartTime(performStartTime)
                    .setVipEndTime(performEndTime)
                    .setVipDays(planDays)
                    .setCanUnsubscribeAtCurrentMonthEnd(canUnsubscribeAtCurrentMonthEnd)
                    .setUnsubscribeEndTime(trialEndTime)
                    .setUnsubscribed(unsubscribed)
                    .setUnsubscribedTime(unsubscribedTime)
                    .setTotalTradeAmountAtCurrentMonth(totalTradeAmountAtCurrentMonth.setScale(3, RoundingMode.HALF_UP))
                    .setUsedDaysUntilLastMonthEnd(usedDaysUntilLastMonthEnd)
                    .setUsedDaysUtilCurrentMonthEnd(usedDaysUtilCurrentMonthEnd)
                    .setUsedDaysForCurrentMonth(usedDaysForCurrentMonth)
                    .setTotalTradeAmountUntilLastMonthEnd(totalTradeAmountUntilLastMonthEnd.setScale(3, RoundingMode.HALF_UP))
                    .setTotalTradeAmountCurrentMonth(totalTradeAmountCurrentMonth.setScale(3, RoundingMode.HALF_UP))
                    .setTotalTradeAmountUntilCurrentMonthEnd(totalTradeAmountUntilCurrentMonthEnd)
                    .setWillReceiveAmount(willReceiveAmount.setScale(3, RoundingMode.HALF_UP));
            item.setTrialStartTime(performStartTime);
            item.setTrialEndTime(trialEndTime);
            item.setUnsubscribedInTrial(unsubscribedTime != null && unsubscribedTime.before(trialEndTime));
            item.setOrderSource(order.getAppCode() + order.getSubscriptionPayType());

            IpAddressInfo ipAddressInfo = payRecord.getAttributes().getIpAddressInfoNotNull();
            item.setCityName(ipAddressInfo.getCityName());
            item.setCountryName(ipAddressInfo.getCountryName());

            items.add(item);
        }

        String title = String.format("%s账单", FORMATTER_BILL_TITLE.format(targetDate));
        return new QueryBillResultDTO()
                .setTitle(title)
                .setTotalCount(items.size())
                .setBillItems(items);
    }

    private static int computePlanDays(Date startTime, Date endTime, Date monthEndTime, long totalPlanDays) {
        // 取最新到达的时间作为参与计算的结束时间
        Date finalEndTime = endTime.before(monthEndTime) ? endTime : monthEndTime;
        if (startTime.before(finalEndTime)) {
            int diffDays = (int) DateUtil.betweenDay(startTime, finalEndTime, false);
            return Math.min(diffDays, (int) totalPlanDays);
        }
        return 0;
    }

    private static <T, R> List<R> transform(List<T> list, Function<T, R> converter) {
        return list.stream()
                .map(converter)
                .collect(Collectors.toList());
    }

    private static <K, V> Map<K, V> extractMapping(List<V> dataList, Function<V, K> keyConvertor) {
        return extractMapping(dataList, keyConvertor, null);
    }

    private static <K, V> Map<K, V> extractMapping(List<V> dataList, Function<V, K> keyConvertor, Consumer<V> peekConsumer) {
        return dataList.stream()
                .peek(e -> {
                    if (peekConsumer != null) {
                        peekConsumer.accept(e);
                    }
                })
                .collect(Collectors.toMap(keyConvertor, e -> e, (v1, v2) -> v1));
    }
}
