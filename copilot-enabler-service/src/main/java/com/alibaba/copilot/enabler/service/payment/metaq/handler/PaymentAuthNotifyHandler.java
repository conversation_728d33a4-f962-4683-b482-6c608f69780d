package com.alibaba.copilot.enabler.service.payment.metaq.handler;

import com.alibaba.aepay.fund.business.api.payment.dto.PaymentResultInfo;
import com.alibaba.aepay.fund.business.api.payment.dto.notify.PaymentNotifyDTO;
import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.constant.StatusFlowReasonEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageStatusEnum;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageTypeEnum;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionCompleteReason;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.domain.payment.dto.PaymentAsyncResultDTO;
import com.alibaba.copilot.enabler.domain.payment.model.MessageInfo;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentInfo;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.service.OrderDomainService;
import com.alibaba.copilot.enabler.domain.payment.service.PaymentDomainService;
import com.alibaba.copilot.enabler.domain.subscription.event.SubscriptionPlanChangedEventProducer;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.user.model.AEPayInfo;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import com.alibaba.copilot.enabler.infra.base.utils.MoneyUtils;
import com.alibaba.copilot.enabler.service.payment.schedule.handler.InitiateDeductionHandler;
import com.alibaba.fastjson.JSON;
import com.alibaba.global.money.Money;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

/**
 * 支付授权通知handler
 */
@Slf4j
@Component
public class PaymentAuthNotifyHandler {

    @Autowired
    private UserRepository userRepository;
    @Autowired
    private OrderDomainService orderDomainService;
    @Resource
    private PaymentDomainService paymentDomainService;
    @Autowired
    private InitiateDeductionHandler initiateDeductionHandler;
    @Autowired
    private SubscriptionPlanChangedEventProducer subscriptionPlanChangedEventProducer;

    /**
     * 支付授权成功处理
     */
    @Transactional(rollbackFor = Exception.class, noRollbackFor = BizException.class)
    public void handle(String tradeNo, String msgId, String notifyContentStr, PaymentNotifyDTO paymentNotifyDTO) {
        log.info("paymentAuthNotify tradeNo:{},msgId:{},paymentNotifyNo:{}", tradeNo, msgId, JSON.toJSONString(paymentNotifyDTO));
        // 获取交易流水记录
        TradeRecord payRecord = orderDomainService.getTradeRecord(tradeNo);
        if (payRecord == null) {
            log.error("paymentAuthNotify payRecord empty,tradeNo:{},msgId:{}",
                    tradeNo, msgId);
            return;
        }

        // 保存消息记录
        saveNMessageRecord(tradeNo, msgId, notifyContentStr, payRecord);

        PaymentTypeEnum paymentType = payRecord.getPaymentType();
        if (PaymentTypeEnum.ONE_TIME_PAYMENT.equals(paymentType)) {
            PaymentAsyncResultDTO result = buildPaymentResult(paymentNotifyDTO);
            paymentDomainService.updateCashierPayStatus(result);

            PaymentMethodEnum paymentMethod = PaymentMethodEnum.ofName(payRecord.getPaymentMethod());
            if (PaymentMethodEnum.CREDIT_CARD.equals(paymentMethod)) {
                handleCardLogic(msgId, paymentNotifyDTO, payRecord);
            }
        } else {
            // 根据支付方式区分处理
            PaymentMethodEnum paymentMethod = PaymentMethodEnum.ofName(payRecord.getPaymentMethod());
            switch (paymentMethod) {
                case CREDIT_CARD:
                    // 针对信用卡支付时, 需要处理卡token逻辑 (注: Alipay的token逻辑其实也需要处理, 但不在支付结果的信息中, 是在单独的授权消息中)
                    handleCardLogic(msgId, paymentNotifyDTO, payRecord);
                    break;
                case ALIPAY_CN:
                case ALIPAY_HK:
                    // 针对Alipay支付时, 支付结果是在该消息中的
                    handleAlipayLogic(paymentNotifyDTO, payRecord);
                    break;
            }
        }

    }

    private PaymentAsyncResultDTO buildPaymentResult(PaymentNotifyDTO paymentNotifyDTO) {
        PaymentAsyncResultDTO result = new PaymentAsyncResultDTO();
        result.setResultCode(paymentNotifyDTO.getResult().getResultCode());
        result.setResultStatus(paymentNotifyDTO.getResult().getResultStatus());
        result.setResultMessage(paymentNotifyDTO.getResult().getResultMessage());
        result.setPaymentRequestId(paymentNotifyDTO.getPaymentRequestId());
        result.setPaymentId(paymentNotifyDTO.getPaymentId());
        return result;
    }

    private void handleAlipayLogic(PaymentNotifyDTO paymentNotifyDTO, TradeRecord payRecord) {
        if (payRecord.getStatus() != TradeRecordStatus.TODO) {
            return;
        }

        BigDecimal tradeAmount = payRecord.getTradeAmount();
        Money paymentAmount = paymentNotifyDTO.getPaymentAmount();

        Assertor.asserts(paymentAmount != null
                        && paymentAmount.getCurrency() != null
                        && Objects.equals(paymentAmount.getCurrency().getCurrencyCode(), AEPaymentConstants.CURRENCY_CODE)
                        && MoneyUtils.isEqualBigDecimal(paymentAmount.getAmount(), tradeAmount),
                "paymentAmount incorrect"
        );

        PaymentInfo paymentInfo = PaymentInfo.builder()
                .outTradeNo(paymentNotifyDTO.getPaymentId())
                .tradeTime(paymentNotifyDTO.getPaymentTime())
                .build();
        orderDomainService.paymentSuccess(payRecord, null, paymentInfo);
    }

    private void saveNMessageRecord(String tradeNo, String msgId, String notifyContentStr, TradeRecord payRecord) {
        MessageInfo messageInfo = MessageInfo.builder()
                .msgId(msgId)
                .notifyContentOrRequestParam(notifyContentStr)
                .response(ConsumeConcurrentlyStatus.CONSUME_SUCCESS.name())
                .typeEnum(MessageTypeEnum.PAYMENT_AUTH_NOTIFY)
                .statusEnum(MessageStatusEnum.SUCCESS)
                .entityId(tradeNo)
                .build();
        orderDomainService.saveMessageRecord(messageInfo, payRecord.getAppCode(), payRecord.getUserId());
    }

    private void handleCardLogic(String msgId, PaymentNotifyDTO paymentNotifyDTO, TradeRecord payRecord) {
        // 保存用户支付信息(仅当cardToken和networkTransactionId均存在时才保存)
        String tradeNo = payRecord.getTradeNo();
        PaymentResultInfo paymentResultInfo = paymentNotifyDTO.getPaymentResultInfo();
        if (paymentResultInfo != null
                && StringUtils.isNotBlank(paymentResultInfo.getCardToken())
                && StringUtils.isNotBlank(paymentResultInfo.getNetworkTransactionId())) {
            saveUserPayInfo(paymentNotifyDTO.getPaymentResultInfo(), payRecord);
            return;
        }

        // 告警通知（用户主动付款如果没有token进行告警）
        if (payRecord.getPaymentType() == PaymentTypeEnum.INITIATED_PAYMENT) {
            log.error("payAlarm-支付授权通知告警:用户主动付款没有保存卡token,tradeNo:{},msgId:{},appCode:{},userId:{}",
                    tradeNo, msgId, payRecord.getAppCode(), payRecord.getUserId());
        }
    }

    /**
     * 支付授权失败处理
     */
    public void handleFail(String tradeNo, String msgId, String notifyContentStr, PaymentNotifyDTO paymentNotifyDTO) {
        // 获取交易流水
        TradeRecord payRecord = orderDomainService.getTradeRecord(tradeNo);
        if (payRecord == null) {
            return;
        }

        // 告警通知
        log.error("payAlarm-支付授权通知失败,tradeNo:{},msgId:{},appCode:{},userId:{},notifyContent:{}",
                tradeNo, msgId, payRecord.getAppCode(), payRecord.getUserId(), notifyContentStr);

        if (payRecord.getPaymentType().equals(PaymentTypeEnum.ONE_TIME_PAYMENT)) {
            PaymentAsyncResultDTO result = buildPaymentResult(paymentNotifyDTO);
            paymentDomainService.updateCashierPayStatus(result);
        } else {
            // 保存消息记录
            MessageInfo messageInfo = MessageInfo.builder()
                    .msgId(msgId)
                    .notifyContentOrRequestParam(notifyContentStr)
                    .response(ConsumeConcurrentlyStatus.CONSUME_SUCCESS.name())
                    .typeEnum(MessageTypeEnum.PAYMENT_AUTH_NOTIFY)
                    .statusEnum(MessageStatusEnum.FAIL)
                    .entityId(tradeNo)
                    .build();

            // 设置该次支付失败的外部交易信息
            PaymentInfo paymentInfo = Optional.ofNullable(paymentNotifyDTO)
                    .map(notifyDTO -> PaymentInfo.builder()
                            .outTradeNo(notifyDTO.getPaymentId())
                            .tradeTime(notifyDTO.getPaymentCreateTime())
                            .build()
                    )
                    .orElse(null);

            // 取消正在支付状态的支付订单 (注意此时原因选择为拒付, 针对支付完成后收到支付机构的确认电话后, 又拒付的场景)
            StatusFlowReasonEnum reasonEnum = StatusFlowReasonEnum.USER_HAD_DISPUTE;
            orderDomainService.cancelPendingPaymentOrder(payRecord, messageInfo, paymentInfo, reasonEnum);

            if (PaymentTypeEnum.PERIODIC_DEDUCT.equals(payRecord.getPaymentType())) {
                log.info("periodic_deduct fail, payRecord={}", JSON.toJSONString(payRecord));

                // 根据支付方式区分处理
                PaymentMethodEnum paymentMethod = PaymentMethodEnum.ofName(payRecord.getPaymentMethod());
                switch (paymentMethod) {
                    case ALIPAY_CN:
                    case ALIPAY_HK:
                        // 完结当前生效的订单
                        SubscriptionOrder oldEffectOrder = initiateDeductionHandler.completeCurrentEffectOrderIfNeed(payRecord.getAppCode(), payRecord.getUserId());

                        // 发送老套餐完结事件
                        if (oldEffectOrder != null) {
                            subscriptionPlanChangedEventProducer.sendCompleteEvent(oldEffectOrder, SubscriptionCompleteReason.FAIL_TO_RENEW);
                        }

                        // 发送「扣费失败」的邮件
                        initiateDeductionHandler.sendDeductFailureEmail(payRecord);
                        break;
                }
            }
        }
    }

    /**
     * 保存user支付信息
     */
    private void saveUserPayInfo(PaymentResultInfo paymentResultInfo, TradeRecord tradeRecord) {
        User user = userRepository.getUser(tradeRecord.getUserId());
        AEPayInfo aePayInfo = AEPayInfo.builder()
                .funding(paymentResultInfo.getFunding())
                .cardNo(paymentResultInfo.getCardNo())
                .cardToken(paymentResultInfo.getCardToken())
                .cardBrand(paymentResultInfo.getCardBrand())
                .issuingCountry(paymentResultInfo.getIssuingCountry())
                .networkTransactionId(paymentResultInfo.getNetworkTransactionId())
                .updateTime(System.currentTimeMillis())
                .build();
        user.savePaymentCardInfo(userRepository, aePayInfo);
    }
}
