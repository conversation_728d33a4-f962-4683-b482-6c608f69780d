package com.alibaba.copilot.enabler.service.subscription.dto;

import java.math.BigDecimal;
import java.util.List;

import com.alibaba.copilot.enabler.client.subscription.dto.CycleFeeDetail;

import lombok.Data;

/**
 * @ClassName SelectedPlanInfoVO
 * <AUTHOR>
 * @Date 2023/8/31 15:37
 */
@Data
public class SelectedPlanInfoVO {

    /**
     * 计划名称
     */
    String planName;
    /**
     * 计划描述
     */
    String planDescription;
    /**
     * 特性名称集合
     */
    List<String> featureNames;
    /**
     * 第一期费用
     */
    BigDecimal currentPayFee;
    /**
     * 周期费用详情
     */
    List<CycleFeeDetail> cycleFeeDetails;
}
