package com.alibaba.copilot.enabler.service.subscription.log;

/**
 * @des 日志场景统一枚举类
 * <AUTHOR>
 */
public enum LogSceneCodeEnum {

    /**
     * 日志场景：用户新安装应用或卸载后重新安装
     */
    SCENE_INSTALL("scene_install"),

    /**
     * 日志场景：订阅事件 (含新订、变更和取消套餐)
     */
    SUBSCRIPTION_EVETN("subscription_event");


    private String sceneCode;

    LogSceneCodeEnum(String sceneCode) {
        this.sceneCode = sceneCode;
    }

    public String getSceneCode() {
        return sceneCode;
    }

}
