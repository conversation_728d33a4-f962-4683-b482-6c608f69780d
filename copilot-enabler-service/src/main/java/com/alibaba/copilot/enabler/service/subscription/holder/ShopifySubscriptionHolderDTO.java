package com.alibaba.copilot.enabler.service.subscription.holder;

import com.alibaba.copilot.enabler.service.subscription.constant.ShopifySubscriptionSwitchType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2023/10/30
 */
@Data
@Accessors(chain = true)
public class ShopifySubscriptionHolderDTO implements Serializable {

    /**
     * Shopify订阅的apiId
     */
    private Long apiId;

    /**
     * 套餐切换类型
     */
    private ShopifySubscriptionSwitchType switchType;

    /**
     * 计划 id
     */
    private Long planId;

    /**
     * App Code
     */
    private String appCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 试用期天数
     */
    private Integer trialDays;

    /**
     * 执行订阅操作时, 生效的订单ID
     */
    private Long oldEffectOrderId;
    /**
     * 真实下单付的价格
     */
    private BigDecimal actualPaymentAmount;

    private String clientIp;
}
