package com.alibaba.copilot.enabler.service.atom;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionOrderDO;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionOrderMapper;
import com.alipay.global.api.model.ams.SubscriptionStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import com.alipay.global.api.request.ams.notify.AlipaySubscriptionNotify;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

/**
 * 支付结果处理器
 * https://docs.antom.com/ac/ams_zh-cn/paymentrn_online
 */
@Slf4j
@Component
public class PaymentResultProcessor4T2g {
    @Resource
    private SubscriptionOrderMapper subscriptionOrderMapper;
    
    public void processPaymentResult(AlipaySubscriptionNotify notify) {
        log.info("Payment result: {}", notify);
        updateSubscriptionStatus(notify);

    }

    private void updateSubscriptionStatus(AlipaySubscriptionNotify notify) {
        log.info("Update subscription status: {}", notify);
        Long orderId = Long.parseLong(notify.getSubscriptionRequestId());
        SubscriptionOrderDO subscriptionOrderDO = new SubscriptionOrderDO();
        subscriptionOrderDO.setId(orderId);
        subscriptionOrderDO.setPerformStartTime(parseIsoDate(notify.getSubscriptionStartTime()));
        subscriptionOrderDO.setPerformEndTime(parseIsoDate(notify.getSubscriptionEndTime()));
        if (SubscriptionStatus.ACTIVE.equals(notify.getSubscriptionStatus())) {
            subscriptionOrderDO.setStatus(SubscriptionOrderStatus.IN_EFFECT.name());
        } else if (SubscriptionStatus.TERMINATED.equals(notify.getSubscriptionStatus())) {
            subscriptionOrderDO.setStatus(SubscriptionOrderStatus.COMPLETED.name());
        } else {
            log.error("Unsupported subscription status: {}", notify.getSubscriptionStatus());   
            return;
        }
        subscriptionOrderMapper.updateById(subscriptionOrderDO);
    }
    
    /**
     * 解析ISO 8601格式的时间字符串为Date对象
     * 例如："2019-11-27T12:01:01+08:00"
     *
     * @param iso8601DateString ISO 8601格式的时间字符串
     * @return Date对象，解析失败时返回null
     */
    public Date parseIsoDate(String iso8601DateString) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            return sdf.parse(iso8601DateString);
        } catch (ParseException e) {
            log.error("Failed to parse ISO 8601 date: {}", iso8601DateString, e);
            return null;
        }
    }
}
