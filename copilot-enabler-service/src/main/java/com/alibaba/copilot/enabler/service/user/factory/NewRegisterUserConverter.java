package com.alibaba.copilot.enabler.service.user.factory;

import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.copilot.enabler.client.user.request.UserRegisterRequest;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.model.UserAttributes;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/5
 */
public class NewRegisterUserConverter implements Converter<UserRegisterRequest, User> {
    public static final Converter<UserRegisterRequest, User> INSTANCE = new NewRegisterUserConverter();

    @Override
    public User convertA2B(UserRegisterRequest userRegisterRequest) {
        if (userRegisterRequest == null) {
            return null;
        }

        User user = User.builder()
                .userId(userRegisterRequest.getUserId())
                .email(userRegisterRequest.getEmail())
                .appCode(userRegisterRequest.getSignUpSource())
                .signUpChannel(userRegisterRequest.getSignUpChannel())
                .build();

        if (StringUtils.isNotBlank(userRegisterRequest.getFirstChannel())) {
            user.getAttributes().setFirstChannel(userRegisterRequest.getFirstChannel());
        }
        if (StringUtils.isNotBlank(userRegisterRequest.getLastChannel())) {
            user.getAttributes().setLastChannel(userRegisterRequest.getLastChannel());
        }

        return user;
    }

    @Override
    public UserRegisterRequest convertB2A(User user) {
        return null;
    }
}

