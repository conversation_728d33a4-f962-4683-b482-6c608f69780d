package com.alibaba.copilot.enabler.service.subscription.factory;

import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.base.utils.PaymentUtils;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecordAttributes;
import lombok.AllArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 2024/1/16
 */
@AllArgsConstructor
public class RefundRecordBuilder implements Builder<TradeRecord> {

    private final TradeRecord payRecord;

    private final AppEnum appEnum;

    @Override
    public TradeRecord build() {
        Date now = new Date();
        Long userId = payRecord.getUserId();
        String paymentMethod = payRecord.getPaymentMethod();
        return TradeRecord.builder()
                .gmtCreate(now)
                .gmtModified(now)
                .userId(userId)
                .subscriptionOrderId(payRecord.getSubscriptionOrderId())
                .appCode(appEnum.getCode())
                .tradeAmount(payRecord.getTradeAmount())
                .tradeCurrency(AEPaymentConstants.CURRENCY_CODE)
                .tradeDirection(TradeDirection.REFUND)
                .paymentMethod(paymentMethod)
                .status(TradeRecordStatus.TODO)
                .taxAmount(null)
                .taxCurrency(null)
                .transactionAmount(null)
                .transactionCurrency(null)
                .hadInitiatePay(false)
                .deleted(false)
                .attributes(new TradeRecordAttributes(null))
                .tradeNo(PaymentUtils.generateTradeNo(userId))
                .build();
    }
}
