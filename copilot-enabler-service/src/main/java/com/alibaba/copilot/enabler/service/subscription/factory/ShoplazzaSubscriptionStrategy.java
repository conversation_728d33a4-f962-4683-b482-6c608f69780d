package com.alibaba.copilot.enabler.service.subscription.factory;

import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionSource;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanResultDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscriptionOrderDTO;
import com.alibaba.copilot.enabler.domain.payment.dto.ShoplazzaSubscriptionResponse;
import com.alibaba.copilot.enabler.domain.payment.gateway.ShoplazzaGateway;
import com.alibaba.copilot.enabler.domain.payment.request.ShoplazzaSubscriptionRequest;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionOrderQuery;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.infra.base.utils.EnvUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * Shoplazza订阅
 *
 * <AUTHOR>
 * @date 2024/9/27 上午11:08
 */
@Component
@Slf4j
public class ShoplazzaSubscriptionStrategy extends AbstractWebSubscriptionStrategy {

    private final static String SHOPLAZZA_SUB_NAME = "shoplazzaSubName";

    @Autowired
    private ShoplazzaGateway shoplazzaGateway;

    /**
     * 订阅来源
     *
     * @return
     */
    @Override
    protected SubscriptionSource getSubscriptionSource() {
        return SubscriptionSource.Shoplazza;
    }

    /**
     * 订阅
     *
     * @param context
     * @return
     */
    @Override
    public SubscribePlanResultDTO subscribe(WebSubscriptionContext context) {
        log.info("ShoplazzaSubscriptionStrategy.subscribe, context={}", JSONObject.toJSONString(context));

        SubscribePlanDTO subscribePlanDTO = context.getSubscribePlanDTO();

        SubscriptionOrderQuery query = new SubscriptionOrderQuery();
        query.setUserId(subscribePlanDTO.getUserId());
        query.setAppCode(subscribePlanDTO.getAppCode());
        query.setStatus(Lists.newArrayList(SubscriptionOrderStatus.IN_EFFECT));
        List<SubscriptionOrder> inEffectOrders = subscriptionOrderRepository.querySubscriptionOrders(query);
        log.info("ShoplazzaSubscriptionStrategy.subscribe, inEffectOrders={}", JSONObject.toJSONString(inEffectOrders));

        int size = CollectionUtils.size(inEffectOrders);
        if (size > 1) {
            throw new BizException(ErrorCode.SYS_ERROR, "order count more than 1");
        } else if (size == 1) {
            return doSwitchByShoplazza(subscribePlanDTO);
        } else {
            return doSubscribeByShoplazza(subscribePlanDTO);
        }
    }

    /**
     * 套餐订阅
     *
     * @param subscribePlanDTO
     * @return
     */
    private SubscribePlanResultDTO doSubscribeByShoplazza(SubscribePlanDTO subscribePlanDTO) {
        log.info("ShoplazzaSubscriptionStrategy.doSubscribeByShoplazza, subscribePlanDTO={}", JSONObject.toJSONString(subscribePlanDTO));

        cancelPendingPaymentOrders(subscribePlanDTO);

        ShoplazzaSubscriptionResponse shoplazzaSubscriptionResponse = shoplazzaSubscribePlan(subscribePlanDTO);

        Long orderId = createSubscriptionOrder(subscribePlanDTO, shoplazzaSubscriptionResponse.getId());

        log.info("ShoplazzaSubscriptionStrategy.doSubscribeByShoplazza, userId={}, subscribePlanDTO={}, orderId={}, url={}", subscribePlanDTO.getUserId(), JSONObject.toJSONString(subscribePlanDTO), orderId, shoplazzaSubscriptionResponse.getConfirmationUrl());

        SubscribePlanResultDTO subscribePlanResultDTO = new SubscribePlanResultDTO();
        subscribePlanResultDTO.setSubscriptionUrl(shoplazzaSubscriptionResponse.getConfirmationUrl());
        return subscribePlanResultDTO;
    }

    private void cancelPendingPaymentOrders(SubscribePlanDTO subscribePlanDTO) {
        SubscriptionOrderQuery query = new SubscriptionOrderQuery();
        query.setUserId(subscribePlanDTO.getUserId());
        query.setAppCode(subscribePlanDTO.getAppCode());
        query.setStatus(Lists.newArrayList(SubscriptionOrderStatus.PENDING_PAYMENT));
        List<SubscriptionOrder> pendingPaymentOrders = subscriptionOrderRepository.querySubscriptionOrders(query);
        log.info("ShoplazzaSubscriptionStrategy.doSubscribeByShoplazza, pendingPaymentOrders={}", JSONObject.toJSONString(pendingPaymentOrders));
        if (CollectionUtils.isNotEmpty(pendingPaymentOrders)) {
            for (SubscriptionOrder subscriptionOrder : pendingPaymentOrders) {
                subscriptionOrder.setGmtModified(new Date());
                subscriptionOrder.setStatus(SubscriptionOrderStatus.PAY_CANCELLED);
                subscriptionOrderRepository.saveSubscriptionOrder(subscriptionOrder);
            }
        }
    }

    private ShoplazzaSubscriptionResponse shoplazzaSubscribePlan(SubscribePlanDTO subscribePlanDTO) {
        ShoplazzaSubscriptionRequest shoplazzaSubscriptionRequest = new ShoplazzaSubscriptionRequest();

        SubscriptionPlan subscriptionPlan = subscriptionPlanRepository.queryByPlanId(subscribePlanDTO.getPlanId(), false);
        if (subscriptionPlan.getAttributes().containsKey(SHOPLAZZA_SUB_NAME)) {
            shoplazzaSubscriptionRequest.setName(subscriptionPlan.getAttributes().getAsString(SHOPLAZZA_SUB_NAME));
        }

        shoplazzaSubscriptionRequest.setPrice(subscriptionPlan.getPrice().toPlainString());
        shoplazzaSubscriptionRequest.setReturnUrl(SwitchConfig.pricingRedirectUrl);
        shoplazzaSubscriptionRequest.setTrialDays(1);

        if (EnvUtils.isPre()) {
            shoplazzaSubscriptionRequest.setTest(true);
        }

        log.info("ShoplazzaSubscriptionStrategy.shoplazzaSubscribePlan, shoplazzaDomain={}, shoplazzaSubscriptionRequest={}", subscribePlanDTO.getShopDomain(), JSON.toJSONString(shoplazzaSubscriptionRequest));
        ShoplazzaSubscriptionResponse shoplazzaSubscriptionResponse = shoplazzaGateway.subscribePlan(subscribePlanDTO.getShopDomain(), subscribePlanDTO.getAccessToken(), shoplazzaSubscriptionRequest);
        log.info("ShoplazzaSubscriptionStrategy.shoplazzaSubscribePlan, shoplazzaSubscriptionResponse={}", JSON.toJSONString(shoplazzaSubscriptionResponse));

        return shoplazzaSubscriptionResponse;
    }

    private Long createSubscriptionOrder(SubscribePlanDTO subscribePlanDTO, String outerSubscriptionId) {
        SubscriptionOrderDTO subscriptionOrderDTO = new SubscriptionOrderDTO();
        subscriptionOrderDTO.setUserId(subscribePlanDTO.getUserId());
        subscriptionOrderDTO.setSubscriptionPlanId(subscribePlanDTO.getPlanId());
        subscriptionOrderDTO.setAppCode(subscribePlanDTO.getAppCode());
        subscriptionOrderDTO.setEmail(subscribePlanDTO.getEmail());
        subscriptionOrderDTO.setOuterSubscriptionId(outerSubscriptionId);
        Long orderId = subscriptionService.createSubscriptionOrder(subscriptionOrderDTO);
        if (orderId == null) {
            throw new BizException(ErrorCode.SYS_ERROR, "subscribePlan error");
        }

        return orderId;
    }

    /**
     * 套餐变更
     *
     * @param subscribePlanDTO
     * @return
     */
    private SubscribePlanResultDTO doSwitchByShoplazza(SubscribePlanDTO subscribePlanDTO) {
        log.info("ShoplazzaSubscriptionStrategy.doSwitchByShoplazza, subscribePlanDTO={}", JSONObject.toJSONString(subscribePlanDTO));

        throw new BizException(ErrorCode.SYS_ERROR, "plan change not supported");
    }

    /**
     * 取消订阅
     *
     * @param context
     * @return
     */
    @Override
    public Boolean cancelSubscribedPlan(WebSubscriptionContext context) {
        log.info("ShoplazzaSubscriptionStrategy.cancelSubscribedPlanByShoplazza, context={}", JSONObject.toJSONString(context));

        SubscribePlanDTO subscribePlanDTO = context.getSubscribePlanDTO();

        SubscriptionOrderQuery query = new SubscriptionOrderQuery();
        query.setUserId(subscribePlanDTO.getUserId());
        query.setAppCode(subscribePlanDTO.getAppCode());
        query.setStatus(Lists.newArrayList(SubscriptionOrderStatus.IN_EFFECT));
        List<SubscriptionOrder> inEffectOrders = subscriptionOrderRepository.querySubscriptionOrders(query);
        log.info("ShoplazzaSubscriptionStrategy.cancelSubscribedPlanByShoplazza, inEffectOrders={}", JSONObject.toJSONString(inEffectOrders));

        if (CollectionUtils.isNotEmpty(inEffectOrders) && inEffectOrders.size() > 1) {
            throw new BizException(ErrorCode.SYS_ERROR, "wrong order count");
        }

        SubscriptionOrder subscriptionOrder = inEffectOrders.get(0);
        log.info("ShoplazzaSubscriptionStrategy.cancelSubscribedPlanByShoplazza, subscriptionOrder={}", JSON.toJSONString(subscriptionOrder));
        if (StringUtils.isBlank(subscriptionOrder.getOuterSubscriptionId())) {
            throw new BizException(ErrorCode.SYS_ERROR, "param invalid");
        }

        shoplazzaGateway.cancelSubscribedPlan(subscribePlanDTO.getShopDomain(), subscribePlanDTO.getAccessToken(), subscriptionOrder.getOuterSubscriptionId());

        return true;
    }
}
