package com.alibaba.copilot.enabler.service.stripe.processor.invoice;

import com.alibaba.aepay.fund.business.api.payment.dto.Order;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.payment.dto.DisputeEventDTO;
import com.alibaba.copilot.enabler.client.payment.dto.OrderDTO;
import com.alibaba.copilot.enabler.client.payment.dto.PaymentResultEventDTO;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.domain.base.utils.PaymentUtils;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEvent;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEventMetadata;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecordAttributes;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.service.stripe.StripeConsts;
import com.alibaba.copilot.enabler.service.stripe.processor.AbstractStripeEventProcessor;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeExecuteContext;
import com.alibaba.copilot.enabler.service.subscription.constant.SubscriptionConstants;
import com.alibaba.copilot.enabler.service.subscription.manager.adic.StripeSubManagerForADIC;
import com.alibaba.fastjson.JSON;
import com.stripe.Stripe;
import com.stripe.model.Subscription;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.weaver.ast.Or;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

@Slf4j
@Component
public class InvoicePaidProcessor4ADIC extends AbstractStripeEventProcessor {
    @Override
    protected void doProcess(StripeExecuteContext context) throws Exception {
        log.info("InvoicePaidProcessor4ADIC context={}", JSON.toJSONString(context));
        StripeEventMetadata metaData = context.getMetaData();
        StripeEvent event = context.getEvent();
        String amountTotal = event.fetch("total");

        // 修改订单状态为激活
        Stripe.apiKey = SwitchConfig.stripeApiKey;
        Subscription subscription = Subscription.retrieve(event.fetch("subscription"));
        if (subscription == null) {
            log.error("com.alibaba.copilot.enabler.service.stripe.processor.invoice.InvocePaidProcessor4ADIC.doProcess subscription is null");
            return;
        }
        Map<String, String> metadata = subscription.getMetadata();
        Long subscriptionOrderId = Long.valueOf(metadata.get(SubscriptionConstants.META_SUB_ID));

        SubscriptionOrder subscriptionOrder = subscriptionOrderRepository.getByOrderId(subscriptionOrderId);
        if ("active".equals(subscription.getStatus())) {
            subscriptionOrder.setStatus(SubscriptionOrderStatus.IN_EFFECT);
            subscriptionOrder.setGmtModified(new Date());
            subscriptionOrder.getAttributes().setStripeSubscriptionId(subscription.getId());
            subscriptionOrderRepository.saveSubscriptionOrder(subscriptionOrder);
        }

        String paymentIntent = event.fetch("payment_intent");

        // 插入交易流水表
        TradeRecord tradeRecord = new TradeRecord();
        tradeRecord.setAppCode(subscriptionOrder.getAppCode());
        tradeRecord.setUserId(subscriptionOrder.getUserId());
        tradeRecord.setPaymentType(PaymentTypeEnum.INITIATED_PAYMENT);
        tradeRecord.setStatus(TradeRecordStatus.SUCC);
        tradeRecord.setTradeDirection(TradeDirection.FORWARD);
        tradeRecord.setTradeAmount(new BigDecimal(amountTotal).divide(new BigDecimal("100")).abs());
        tradeRecord.setTradeCurrency(event.fetch("currency"));
        tradeRecord.setSubscriptionOrderId(subscriptionOrderId);
        tradeRecord.setTradeNo(PaymentUtils.generateTradeNo(subscriptionOrder.getUserId()));
        tradeRecord.setPaymentMethod(PaymentMethodEnum.STRIPE.getValue());
        tradeRecord.setOutTradeNo(paymentIntent);
        tradeRecord.setDeleted(false);

        tradeRecordRepository.createOrUpdateTradeRecord(tradeRecord);

        // 发送支付成功结果通知
        PaymentResultEventDTO paymentResultEventDTO = buildPaymentResultNotifyDTO(tradeRecord);
        Long currentPeriodEnd = subscription.getCurrentPeriodEnd();
        Long currentPeriodStart = subscription.getCurrentPeriodStart();
        paymentResultEventDTO.setSubscriptionPeriodEnd(currentPeriodEnd);
        paymentResultEventDTO.setSubscriptionPeriodStart(currentPeriodStart);
        paymentResultEventDTO.setBillingReason(event.fetch("billing_reason"));
        domainEventJsonProducer.publish(paymentResultEventDTO);
    }

    private PaymentResultEventDTO buildPaymentResultNotifyDTO(TradeRecord tradeRecord) {
        PaymentResultEventDTO notifyDTO = new PaymentResultEventDTO();
        notifyDTO.setAppCode(tradeRecord.getAppCode());
        notifyDTO.setTradeNo(tradeRecord.getTradeNo());
        notifyDTO.setOutTradeNo(tradeRecord.getOutTradeNo());
        notifyDTO.setTradeStatus(tradeRecord.getStatus().name());
        notifyDTO.setUserId(tradeRecord.getUserId());
        notifyDTO.setTradeTime(tradeRecord.getTradeTime());
        OrderDTO order = new OrderDTO();
        order.setReferenceOrderId(String.valueOf(tradeRecord.getSubscriptionOrderId()));
        notifyDTO.setOrder(order);
        return notifyDTO;
    }

    @Override
    public String getEventType() {
        return StripeConsts.INVOICE_PAID;
    }

    @Override
    public String getAppCode() {
        return "ADIC";
    }
}
