package com.alibaba.copilot.enabler.service.subscription.holder;

import com.alibaba.copilot.enabler.domain.base.utils.RedisUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Nullable;
import java.time.Duration;

/**
 * <AUTHOR>
 * @version 2023/10/30
 */
@Slf4j
public class ShopifySubscriptionHolder {

    private static final String REDIS_KEY_PREFIX = "shopify_subscribe_holder_";
    private static final Duration DATA_EXISTS_DURATION = Duration.ofDays(3);

    /**
     * 向Redis中存储订阅上下文信息
     *
     * @param shopifySubscriptionId 订阅唯一ID (由Shopify返回)
     * @param dto                   订阅信息
     */
    public static void putToShopifySubscriptionHolder(Long shopifySubscriptionId, ShopifySubscriptionHolderDTO dto) {
        String redisKey = getRedisKey(shopifySubscriptionId);
        String jsonStr = JSON.toJSONString(dto);
        RedisUtils.setex(redisKey, DATA_EXISTS_DURATION.getSeconds(), jsonStr);
    }

    /**
     * 清除Redis中存储的订阅上下文信息
     *
     * @param shopifySubscriptionId 订阅唯一ID (由Shopify返回)
     */
    public static void clearShopifySubscriptionHolder(Long shopifySubscriptionId) {
        String redisKey = getRedisKey(shopifySubscriptionId);
        RedisUtils.delete(redisKey);
    }

    /**
     * 从Redis中读取订阅上下文信息
     *
     * @param shopifySubscriptionId 订阅唯一ID (由Shopify返回)
     * @return 订阅信息
     */
    @Nullable
    public static ShopifySubscriptionHolderDTO getFromShopifySubscriptionHolder(Long shopifySubscriptionId) {
        String redisKey = getRedisKey(shopifySubscriptionId);
        String jsonStr = RedisUtils.get(redisKey);
        if (StringUtils.isEmpty(jsonStr)) {
            return null;
        }
        return JSON.parseObject(jsonStr, ShopifySubscriptionHolderDTO.class);
    }

    private static String getRedisKey(Long subscriptionApiId) {
        return String.format("%s%s", REDIS_KEY_PREFIX, subscriptionApiId);
    }
}
