package com.alibaba.copilot.enabler.service.stripe.mq;

import com.alibaba.copilot.enabler.domain.base.mq.impl.BaseMessageQueueProducer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/12
 */
@Component
public class StripeMessageProducer extends BaseMessageQueueProducer {



    @Value("${inner.metaq.message.producerUnitName}")
    private String unitName;



    @Override
    protected String getProducerGroup() {
        return "PID_stripe_producer";
    }

    @Override
    protected String getProducerUnitName() {
        return unitName;
    }
}