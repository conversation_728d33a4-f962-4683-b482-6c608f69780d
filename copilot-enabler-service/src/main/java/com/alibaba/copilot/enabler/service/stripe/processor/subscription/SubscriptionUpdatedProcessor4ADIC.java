package com.alibaba.copilot.enabler.service.stripe.processor.subscription;

import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.service.stripe.StripeConsts;
import com.alibaba.copilot.enabler.service.stripe.processor.AbstractStripeEventProcessor;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeExecuteContext;
import com.alibaba.fastjson.JSON;
import com.stripe.model.Subscription;
import com.stripe.param.SubscriptionCancelParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SubscriptionUpdatedProcessor4ADIC  extends AbstractStripeEventProcessor {
    @Override
    protected void doProcess(StripeExecuteContext context) throws Exception {
        log.info("PaymentFailedProcessor4ADIC context={}", JSON.toJSONString(context));

    }

    @Override
    public String getEventType() {
        return StripeConsts.SUBSCRIPTION_UPDATED;
    }

    @Override
    public String getAppCode() {
        return AppEnum.ADIC.getCode();
    }

}
