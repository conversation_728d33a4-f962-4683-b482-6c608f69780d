package com.alibaba.copilot.enabler.service.subscription.log;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * 统一业务日志埋点
 */
public class SubsciptionBizLogger {
    /**
     * 订阅统一业务埋点日志，对应日志文件  subscription-unify-biz.log
     */
    private static final Logger unifyBizLogger = LoggerFactory.getLogger("unifybiz");

    /**
     * @param unifyLog 日志对象
     */
    public static void unifyBizLog(UnifyLog unifyLog) {
        unifyBizLogger.info(buildParams(unifyLog.getShopDomain(), unifyLog.getShopifyShopId(),
                unifyLog.getSceneCode(), JSON.toJSONString(unifyLog.getUnifyBizLog()), System.currentTimeMillis()));
    }

    private static String buildParams(String op, Object... params) {
        StringBuffer paramSb = new StringBuffer();

        paramSb.append("|");
        paramSb.append(op);
        paramSb.append("|");
        // 组织 value
        if (params != null) {
            for (Object p : params) {
                paramSb.append(p);
                paramSb.append('|');
            }
        }
        return StringUtils.substring(paramSb.toString(), 0, StringUtils.length(paramSb.toString()) - 1);
    }

    public static UnifyLog buildLogEvent(String shopDomain, Long shopifyShopId, String sceneCode, Object unifyBizLog) {
        return UnifyLog.builder()
                .shopDomain(shopDomain)
                .shopifyShopId(shopifyShopId)
                .sceneCode(sceneCode)
                .unifyBizLog(unifyBizLog)
                .build();
    }

}
