package com.alibaba.copilot.enabler.service.payment.schedule;

import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.constant.StatusFlowReasonEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.payment.request.TradeRecordsQuery;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.payment.service.OrderDomainService;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategyFactory;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.client.email.dto.CancelSubscribeForPayTimeoutEmailDTO;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.taobao.eagleeye.EagleEye;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 超期订单未支付自动取消的定时任务
 */
@Component
public class TimeOutOrderAutoCancelledTask extends MapJobProcessor {
    public static final Logger TASK_LOG = LoggerFactory.getLogger("taskLogger");

    @Autowired
    private TradeRecordRepository tradeRecordRepository;
    @Autowired
    private SubscriptionOrderRepository subscriptionOrderRepository;
    @Autowired
    private OrderDomainService orderDomainService;

    /**
     * 扫描未支付的订单的扫描范围（过去24小时创建的未支付的订单）
     */
    private final static Long unpaidOrderScanDuration = 24 * 60 * 60 * 1000L;

    @Override
    @Monitor(name = "超期订单未支付自动取消的定时任务", level = Monitor.Level.P1, layer = Monitor.Layer.OTHER)
    public ProcessResult process(JobContext context) {

        try {
            EagleEye.startTrace(null, this.getClass().getName(), EagleEye.TYPE_CUSTOM_MESSAGE_SUB);
            TASK_LOG.info("TimeOutOrderAutoCancelledTask start……");

            long currentTimeMillis = System.currentTimeMillis();

            TradeRecordsQuery paymentRecordsQuery = TradeRecordsQuery.builder()
                    .tradeDirection(TradeDirection.FORWARD)
                    .status(TradeRecordStatus.TODO)
                    // 过去24小时未支付的订单
                    .startCreateTime(new Date(currentTimeMillis - unpaidOrderScanDuration))
                    // 超过1小时未支付的订单
                    .endCreateTime(new Date(currentTimeMillis - SwitchConfig.unpaidOrderOverdueSeconds * 1000))
                    .hadInitiatePay(true)
                    // 用户主动付款
                    .paymentType(PaymentTypeEnum.INITIATED_PAYMENT)
                    .build();

            List<TradeRecord> tradeRecords = tradeRecordRepository.scanTradeRecords(paymentRecordsQuery);
            for (TradeRecord tradeRecord : tradeRecords) {
                // 目前产生待支付的流水均为AE支付产生的，shopify支付的不会产生待支付的流水
                if (tradeRecord == null) {
                    continue;
                }

                String appCode = tradeRecord.getAppCode();
                Long userId = tradeRecord.getUserId();
                String tradeNo = tradeRecord.getTradeNo();

                // 取消等待支付的订单
                orderDomainService.cancelPendingPaymentOrder(tradeRecord, null, null,
                        StatusFlowReasonEnum.ORDER_OVERDUE_4_PAY);

                // 发送「由于超时未支付导致取消订单」的邮件
                sendCancelSubscribeForPayTimeoutEmail(tradeRecord);

                TASK_LOG.info("TimeOutOrderAutoCancelledTask appCode:{},userId:{},tradeNo:{}", appCode, userId, tradeNo);
            }

            TASK_LOG.info("TimeOutOrderAutoCancelledTask completed-tradeRecordCount:{}", tradeRecords.size());
            return new ProcessResult(true);
        } catch (Throwable e) {
            TASK_LOG.error("payAlarm-超期订单未支付自动取消的定时任务失败,msg:{}", e.getMessage(), e);
            return new ProcessResult(false);
        } finally {
            EagleEye.endTrace(null);
        }
    }

    /**
     * 发送「由于超时未支付导致取消订单」的邮件
     */
    private void sendCancelSubscribeForPayTimeoutEmail(TradeRecord payRecord) {
        String appCode = payRecord.getAppCode();
        Long userId = payRecord.getUserId();
        AppEnum appEnum = AppEnum.getAppByCode(appCode);
        SubscriptionOrder order = subscriptionOrderRepository.getByOrderId(payRecord.getSubscriptionOrderId());
        CancelSubscribeForPayTimeoutEmailDTO emailDTO = new CancelSubscribeForPayTimeoutEmailDTO()
                .setOrderNo(payRecord.getTradeNo())
                .setAppName(appEnum.getName())
                .setPlanName(order.getSubscriptionPlanName());
        SubscriptionStrategyFactory.getStrategy(appEnum, order.getSubscriptionPayType()).sendEmail(userId, emailDTO);
    }
}
