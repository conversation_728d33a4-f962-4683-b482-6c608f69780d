package com.alibaba.copilot.enabler.service.base.config;

import com.aliexpress.geoip.GeoipService;
import com.google.common.collect.Lists;
import com.taobao.hsf.app.spring.util.HSFSpringConsumerBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 2023/9/26
 */
@Configuration
public class GeoIpHsfConfiguration {

    @Value("${hsf.ae.geoip.unit}")
    private String aeGeoipUnit;

    @Bean
    public HSFSpringConsumerBean geoipService() {
        HSFSpringConsumerBean consumer = new HSFSpringConsumerBean();
        consumer.setInterfaceClass(GeoipService.class);
        consumer.setGroup("HSF");
        consumer.setClientTimeout(15000);
        consumer.setVersion("1.0.0");
        consumer.setConfigserverCenter(Lists.newArrayList(aeGeoipUnit));
        return consumer;
    }
}
