package com.alibaba.copilot.enabler.service.stripe.processor.charge;

import com.alibaba.copilot.enabler.client.payment.constant.dispute.DisputeStatus;
import com.alibaba.copilot.enabler.client.payment.dto.DisputeEventDTO;
import com.alibaba.copilot.enabler.client.payment.dto.OrderDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEventMetadata;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.service.stripe.StripeConsts;
import com.alibaba.copilot.enabler.service.stripe.processor.AbstractStripeEventProcessor;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeExecuteContext;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ChargeDisputeCreatedProcessor4ADIC  extends AbstractStripeEventProcessor {
    @Override
    protected void doProcess(StripeExecuteContext context) throws Exception {
        log.info("ChargeDisputeCreatedProcessor4ADIC context={}", JSON.toJSONString(context));

        StripeEventMetadata metaData = context.getMetaData();

        DisputeEventDTO disputeEventDTO = new DisputeEventDTO();
        disputeEventDTO.setTradeNo(metaData.getTradeNo());
        disputeEventDTO.setUserId(Long.valueOf(metaData.getUserId()));
        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setReferenceOrderId(metaData.getSubscriptionOrderId());
        disputeEventDTO.setOrder(orderDTO);
        disputeEventDTO.setStatus(DisputeStatus.CREATED.name());
        disputeEventDTO.setResult(null);

        domainEventJsonProducer.publish(disputeEventDTO);
    }

    @Override
    public String getEventType() {
        return StripeConsts.CHARGE_DISPUTE_CREATED;
    }

    @Override
    public String getAppCode() {
        return AppEnum.ADIC.getCode();
    }
}
