package com.alibaba.copilot.enabler.service.payment.strategy;

import com.alibaba.aepay.fund.business.api.payment.dto.Buyer;
import com.alibaba.aepay.fund.business.api.payment.dto.Goods;
import com.alibaba.aepay.fund.business.api.payment.dto.Order;
import com.alibaba.aepay.fund.business.api.payment.enums.ProductCodeEnum;
import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.monitor.annotation.MonitorResult;
import com.alibaba.copilot.enabler.client.payment.constant.ClientType;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.dto.*;
import com.alibaba.copilot.enabler.client.payment.request.CashierPayRequest;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.domain.payment.constant.PaymentConst;
import com.alibaba.copilot.enabler.domain.payment.constant.StripeCheckoutSessionMode;
import com.alibaba.copilot.enabler.domain.payment.dto.PaymentAsyncResultDTO;
import com.alibaba.copilot.enabler.domain.payment.factory.PaymentChannelFactory;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.StripeGateway;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeCreateSessionRequest;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEventMetadata;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.subscription.request.InitiatePaymentRequest;
import com.alibaba.copilot.enabler.domain.user.model.AEPayInfo;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import com.alibaba.copilot.enabler.service.stripe.StripeService;
import com.alibaba.fastjson.JSON;
import com.alibaba.global.money.Money;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Pic Copilot业务创建支付
 */

@Service
@Slf4j
public class PicCopilotCreateCashierPayStrategy extends AbstractCreateCashierPayStrategy {

    @Resource
    private UserRepository userRepository;

    @Resource
    private StripeGateway stripeGateway;

    @Resource
    private StripeService stripeService;

    @Override
    public AppEnum app() {
        return AppEnum.PIC_COPILOT;
    }

    @Monitor(name = "[Pic] 加油包", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    @MonitorResult
    @Override
    public TradeRecord create(CashierPayRequest request) {

        log.info("PicCopilotCreateCashierPayStrategy.create, request={}", JSON.toJSONString(request));

        PaymentMethodEnum paymentMethod = PaymentMethodEnum.ofName(request.getPaymentMethod());

        switch (paymentMethod) {
            case STRIPE:
                // stripe
                return doCreateByStripe(request);
            case ALIPAY_CN:
            case ALIPAY_HK:
            case CREDIT_CARD:
                // AE pay
                return doCreateByAEPay(request);
            default:
                throw new BizException(ErrorCode.SYS_ERROR, "unsupported paymentMethod.");
        }
    }

    /**
     * 走Stripe相关支付逻辑
     *
     * @param request
     * @return
     */
    private TradeRecord doCreateByStripe(CashierPayRequest request) {
        // 1. 生成TradeRecord记录
        TradeRecord tradeRecord = createCashierPayRecord(request, PaymentConst.CURRENCY_USD, ProductCodeEnum.CASHIER_PAYMENT.name());

        // 2. 调用渠道接口
        ClientType clientType = ClientType.valueOf(request.getClientType());
        if (!ClientType.WEB_SDK.equals(clientType)) {
            log.error("PicCopilotCreateCashierPayStrategy.create, unsupported clientType, request={}", JSON.toJSONString(request));
            throw new BizException(ErrorCode.SYS_ERROR, "unsupported clientType.");
        }
        doCreateByStripeWithWebSDK(request, tradeRecord);

        // 3. 更新TradeRecord记录
        updateCashierPayRecord(tradeRecord);
        return tradeRecord;
    }


    /**
     * 走AE金融支付相关逻辑
     *
     * @param request
     * @return
     */
    private TradeRecord doCreateByAEPay(CashierPayRequest request) {
        // 1. 生成TradeRecord记录
        TradeRecord tradeRecord = createCashierPayRecord(request, AEPaymentConstants.CURRENCY_CODE, ProductCodeEnum.CASHIER_PAYMENT.name());

        // 2. 调用渠道接口
        ClientType clientType = ClientType.valueOf(request.getClientType());
        if (ClientType.WEB_SDK.equals(clientType)) {
            cashierPaymentByWebSDK(request, tradeRecord);
        } else if (ClientType.WEB_DIRECT.equals(clientType)) {
            cashierPaymentByWebDirect(request, tradeRecord);
        } else {
            log.error("PicCopilotCreateCashierPayStrategy.create, unsupported clientType, request={}", JSON.toJSONString(request));
            throw new BizException(ErrorCode.SYS_ERROR, "unsupported clientType.");
        }

        // 3. 更新TradeRecord记录
        updateCashierPayRecord(tradeRecord);
        return tradeRecord;
    }

    private void cashierPaymentByWebDirect(CashierPayRequest request, TradeRecord tradeRecord) {
        InitiatePaymentRequest paymentRequest = buildInitiatePaymentRequest(request, tradeRecord);
        SingleResult<PaymentAsyncResultDTO> paymentResult = PaymentChannelFactory.getPaymentByApp(request.getAppCode())
                .cashierPay(paymentRequest);
        if (!paymentResult.isSuccess()) {
            log.error("PicCopilotCreateCashierPayStrategy.cashierPaymentByWebDirect, cashierPay failed, request={}, result={}", JSON.toJSONString(request), JSON.toJSONString(paymentResult));
            throw new BizException(ErrorCode.SYS_ERROR, "cashier payment failed.");
        }
        PaymentAsyncResultDTO cashierPayResult = paymentResult.getData();

        tradeRecord.setOutTradeNo(cashierPayResult.getPaymentId());
        tradeRecord.getAttributes().setCashierPayRedirectUrl(cashierPayResult.getRedirectActionForm().getRedirectUrl());
        tradeRecord.getAttributes().setCashierPayNormalUrl(cashierPayResult.getNormalUrl());
    }

    private void cashierPaymentByWebSDK(CashierPayRequest request, TradeRecord tradeRecord) {
        CreatePaymentSessionDTO sessionDTO = buildCreatePaymentSession(request, tradeRecord);
        SingleResult<PaymentSessionDTO> paymentSessionResult = PaymentChannelFactory.getPaymentByApp(request.getAppCode())
                .createPaymentSession(sessionDTO);
        if (!paymentSessionResult.isSuccess()) {
            log.error("PicCopilotCreateCashierPayStrategy.cashierPaymentByWebSDK, cashierPay failed, request={}, result={}", JSON.toJSONString(request), JSON.toJSONString(paymentSessionResult));
            throw new BizException(ErrorCode.SYS_ERROR, "cashier payment failed.");
        }
        PaymentSessionDTO paymentSession = paymentSessionResult.getData();

        tradeRecord.getAttributes().setPaymentSessionId(paymentSession.getPaymentSessionId());
        tradeRecord.getAttributes().setPaymentSessionData(paymentSession.getPaymentSessionData());
        tradeRecord.getAttributes().setEnv(paymentSession.getEnvironment());
        tradeRecord.getAttributes().setPaymentSessionExpiryTime(paymentSession.getPaymentSessionExpiryTime());
    }

    private InitiatePaymentRequest buildInitiatePaymentRequest(CashierPayRequest request, TradeRecord tradeRecord) {
        InitiatePaymentRequest initiatePaymentRequest = new InitiatePaymentRequest();
        initiatePaymentRequest.setAppEnum(AppEnum.getAppByCode(request.getAppCode()));
        initiatePaymentRequest.setPaymentMethod(PaymentMethodEnum.ofName(request.getPaymentMethod()));
        initiatePaymentRequest.setPaymentRequestId(tradeRecord.getTradeNo());
        initiatePaymentRequest.setOrder(buildOrder(request));
        initiatePaymentRequest.setPaymentAmount(Money.of(request.getPaymentAmount(), AEPaymentConstants.CURRENCY_CODE));

        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromHttpUrl(request.getPaymentRedirectUrl())
                .queryParam(AEPaymentConstants.KEY_PAYMENT_REQUEST_ID, tradeRecord.getTradeNo());
        if (MapUtils.isNotEmpty(request.getMetadata())) {
            request.getMetadata().entrySet().forEach(entry -> {
                if (AEPaymentConstants.KEY_PAYMENT_REQUEST_ID.equals(entry.getKey())) {
                    return;
                }
                uriComponentsBuilder.queryParam(entry.getKey(), entry.getValue());
            });
        }
        initiatePaymentRequest.setPaymentRedirectUrl(uriComponentsBuilder.toUriString());
        return initiatePaymentRequest;
    }

    private CreatePaymentSessionDTO buildCreatePaymentSession(CashierPayRequest request, TradeRecord tradeRecord) {
        CreatePaymentSessionDTO sessionDTO = new CreatePaymentSessionDTO();
        sessionDTO.setAppEnum(AppEnum.getAppByCode(request.getAppCode()));
        sessionDTO.setPaymentMethod(PaymentMethodEnum.ofName(request.getPaymentMethod()));
        sessionDTO.setPaymentRequestId(tradeRecord.getTradeNo());
        sessionDTO.setActualPayAmount(request.getPaymentAmount());

        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromHttpUrl(request.getPaymentRedirectUrl())
                .queryParam(AEPaymentConstants.KEY_PAYMENT_REQUEST_ID, tradeRecord.getTradeNo());
        if (MapUtils.isNotEmpty(request.getMetadata())) {
            request.getMetadata().forEach((key, value) -> {
                if (AEPaymentConstants.KEY_PAYMENT_REQUEST_ID.equals(key)) {
                    return;
                }
                uriComponentsBuilder.queryParam(key, value);
            });
        }
        sessionDTO.setRedirectUrl(uriComponentsBuilder.toUriString());

        sessionDTO.setPaymentType(PaymentTypeEnum.ONE_TIME_PAYMENT);
        sessionDTO.setClientIp(request.getClientIp());
        sessionDTO.setOrder(new OrderDTO()
                .setOrderAmount(request.getPaymentAmount())
                .setReferenceOrderId(String.valueOf(request.getOrder().getReferenceOrderId()))
                .setOrderDescription(request.getOrder().getOrderDescription())
                .setBuyer(new BuyerDTO()
                        .setBuyerId(String.valueOf(request.getOrder().getBuyer().getBuyerId()))
                        .setBuyerRegistrationTime(request.getOrder().getBuyer().getBuyerRegistrationTime())
                )
                .setGoodsList(Optional.ofNullable(request.getOrder().getGoodsList())
                        .orElseGet(Lists::newArrayList)
                        .stream().map(goodsDTO -> new GoodsDTO()
                                .setGoodsId(String.valueOf(goodsDTO.getGoodsId()))
                                .setGoodsName(goodsDTO.getGoodsName())
                        ).collect(Collectors.toList())
                )
        );

        if (StringUtils.isNotBlank(request.getCardId())) {
            buildCardInfo(request, sessionDTO);
        }

        return sessionDTO;
    }

    private void buildCardInfo(CashierPayRequest request, CreatePaymentSessionDTO sessionRequest) {
        if (request.getCardId() == null) {
            log.error("PicCopilotCreateCashierPayStrategy.buildCardInfo, cardId is null, request={}", JSON.toJSONString(request));
            return;
        }
        User user = userRepository.getUser(request.getUserId());
        if (user == null) {
            log.error("PicCopilotCreateCashierPayStrategy.buildCardInfo, user not found, userId={}", request.getUserId());
            return;
        }
        AEPayInfo aePayInfo = user.getPayCardInfoByCardId(request.getCardId());
        if (aePayInfo == null) {
            log.error("PicCopilotCreateCashierPayStrategy.buildCardInfo, card not found, cardId={}", request.getCardId());
            return;
        }
        // 刷新最近使用的对应卡的时间
        aePayInfo.setUpdateTime(System.currentTimeMillis());
        user.savePaymentCardInfo(userRepository, aePayInfo);

        sessionRequest.setCardToken(aePayInfo.getCardToken()).setNetworkTransactionId(aePayInfo.getNetworkTransactionId());
    }

    private Order buildOrder(CashierPayRequest request) {
        Order order = new Order();
        order.setOrderAmount(Money.of(request.getOrder().getOrderAmount(), AEPaymentConstants.CURRENCY_CODE));
        order.setReferenceOrderId(request.getOrder().getReferenceOrderId());
        order.setOrderDescription(request.getOrder().getOrderDescription());

        List<GoodsDTO> goodsList = request.getOrder().getGoodsList();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(goodsList)) {
            List<Goods> goods = goodsList.stream().map(goodsDTO -> {
                Goods goods1 = new Goods();
                goods1.setGoodsName(goodsDTO.getGoodsName());
                goods1.setReferenceGoodsId(goodsDTO.getGoodsId());
                return goods1;
            }).collect(Collectors.toList());
            order.setGoods(goods);
        }

        Buyer buyer = new Buyer();
        buyer.setReferenceBuyerId(request.getOrder().getBuyer().getBuyerId());
        buyer.setBuyerRegistrationTime(request.getOrder().getBuyer().getBuyerRegistrationTime());
        order.setBuyer(buyer);

        return order;
    }

    @Override
    protected StripeCreateSessionRequest buildCreateCheckoutSession(CashierPayRequest request, TradeRecord tradeRecord, String stripeCustomerId) {
        // stripe metadata
        StripeEventMetadata eventMetadata = StripeEventMetadata.of(
                StripeCheckoutSessionMode.PAYMENT,
                request.getAppCode(),
                tradeRecord.getTradeNo());
        eventMetadata.setUserId(request.getUserId().toString());
        eventMetadata.setGoodsName(request.getOrder().getGoodsList().get(0).getGoodsName());

        // return url
        String returnUrl = buildReturnUrl(request, tradeRecord);

        // goods
        List<GoodsDTO> goodsList = request.getOrder().getGoodsList();
        GoodsDTO goods = new GoodsDTO();
        goods.setGoodsName(goodsList.get(0).getGoodsName());
        goods.setDescription(goodsList.get(0).getDescription());
        goods.setUnitAmount(request.getPaymentAmount().multiply(new BigDecimal(100L)).longValue());
        goods.setQuantity(1L);

        // request
        StripeCreateSessionRequest sessionRequest = new StripeCreateSessionRequest();
        sessionRequest.setMode(StripeCheckoutSessionMode.PAYMENT);
        sessionRequest.setReturnUrl(returnUrl);
        sessionRequest.setMetadata(eventMetadata);
        sessionRequest.setGoodsList(Lists.newArrayList(goods));
        sessionRequest.setStripeCustomerId(stripeCustomerId);
        sessionRequest.setTimeout(3600L);
        sessionRequest.setClientReferenceId(request.getClientReferenceId());
        return sessionRequest;
    }
}