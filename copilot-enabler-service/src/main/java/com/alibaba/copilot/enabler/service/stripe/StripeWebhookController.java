package com.alibaba.copilot.enabler.service.stripe;

import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.monitor.annotation.MonitorResult;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.StripeGateway;
import com.alibaba.copilot.enabler.service.base.constant.Constants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/05
 */
@Slf4j
@Api(tags = "Stripe Callback")
@RestController
@RequestMapping(Constants.EXTERNAL_URI_PREFIX + "/stripe/callback")
public class StripeWebhookController {

    private static final String STRIPE_SIG_HEADER_KEY = "Stripe-Signature";

    @Resource
    private StripeService stripeService;
    @Resource
    private StripeGateway stripeGateway;

    @Monitor(name = "[Stripe] Webhook", layer = Monitor.Layer.WEB, level = Monitor.Level.P1)
    @MonitorResult
    @ApiOperation("Webhook回调地址")
    @RequestMapping(value = {"/webhook"}, method = {RequestMethod.GET, RequestMethod.POST})
    public ResponseEntity<String> listener(HttpEntity<String> entity) {
        String webhookBody = entity.getBody();
        try {
            String sig = entity.getHeaders().getFirst(STRIPE_SIG_HEADER_KEY);
            // todo chenxiang check sig
            log.info("StripeCallbackController listener, strList={}, webhookBody={}", sig, webhookBody);
            stripeService.sendWebhookEvent(webhookBody);
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (Exception e) {
            log.error("stripe listener error, webhookBody={}", webhookBody, e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
    }



    @ApiOperation("预发Webhook")
    @RequestMapping(value = {"/pre/webhook"}, method = {RequestMethod.GET, RequestMethod.POST})
    public ResponseEntity<String> preListener(HttpEntity<String> entity) {
        String webhookBody = entity.getBody();
        try {
            String sig = entity.getHeaders().getFirst("Stripe-Signature");
            // todo chenxiang check sig
            log.info("StripeCallbackController listener pre, sig={}, webhookBody={}", sig, webhookBody);
            if (StringUtils.isBlank(webhookBody)) {
                throw new RuntimeException("empty webhook body");
            }
            String targetUrl = "https://pre-copilot-enabler.alibaba-inc.com/external/stripe/callback/webhook";
            HttpURLConnection connection = (HttpURLConnection)(new URL(targetUrl)).openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty(STRIPE_SIG_HEADER_KEY, sig);
            connection.setDoOutput(true);
            connection.getOutputStream().write(webhookBody.getBytes());
            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            StringBuilder response = new StringBuilder();

            String line;
            while((line = reader.readLine()) != null) {
                response.append(line);
            }

            reader.close();
            System.out.println(response.toString());
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (Exception e) {
            log.error("stripe pre listener error, webhookBody={}", webhookBody, e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }


    }



}