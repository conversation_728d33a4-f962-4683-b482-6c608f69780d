package com.alibaba.copilot.enabler.service.subscription.utils;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;

import java.util.Objects;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/08
 */
public class BizUtils {


    public static boolean isPicApp(SubscribePlanDTO subscribePlanDTO) {
        return Objects.nonNull(subscribePlanDTO)
                && Objects.equals(subscribePlanDTO.getAppCode(), AppEnum.PIC_COPILOT.getCode());
    }

    public static boolean isPicApp(TradeRecord tradeRecord) {
        return Objects.nonNull(tradeRecord)
                && Objects.equals(tradeRecord.getAppCode(), AppEnum.PIC_COPILOT.getCode());
    }

    public static boolean isStripePay4Subscription(SubscribePlanDTO subscribePlanDTO) {
        return Objects.nonNull(subscribePlanDTO)
                && Objects.equals(subscribePlanDTO.getSubscriptionPayTypeName(), SubscriptionPayType.STRIPE.name());
    }

    public static boolean isPicOrderByStripe(SubscriptionOrder order) {
        return Objects.nonNull(order)
                && Objects.equals(order.getAppCode(), AppEnum.PIC_COPILOT.getCode())
                && Objects.equals(order.getSubscriptionPayType(), SubscriptionPayType.STRIPE);
    }

    public static boolean isAePayForSubscription(SubscriptionOrder order) {
        return Objects.nonNull(order)
                && SubscriptionPayType.AEPay.equals(order.getSubscriptionPayType());
    }

}