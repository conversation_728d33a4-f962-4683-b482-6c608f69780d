package com.alibaba.copilot.enabler.service.subscription.facade;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.copilot.enabler.client.subscription.callback.ShopifyCallback;
import com.alibaba.copilot.enabler.client.subscription.dto.AuthorizeEventDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyAppUninstalledEventDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscriptionEventDTO;
import com.alibaba.copilot.enabler.client.subscription.facade.ShopifyCallbackHsfApi;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2023/10/30
 */
@Slf4j
@Service
@HSFProvider(serviceInterface = ShopifyCallbackHsfApi.class)
public class ShopifyCallbackHsfApiImpl implements ShopifyCallbackHsfApi {

    @Resource
    private ShopifyCallback shopifyCallback;

    @Override
    public void onSubscriptionEvent(SubscriptionEventDTO event) {
        log.info("onSubscriptionEvent, event={}", JSON.toJSONString(event));
        shopifyCallback.onSubscriptionEvent(event, "");
    }

    @Override
    public void onSubscriptionEvent(SubscriptionEventDTO event, String shareCode) {
        log.info("onSubscriptionEvent, event={},shareCode={}", JSON.toJSONString(event), shareCode);
        shopifyCallback.onSubscriptionEvent(event, shareCode);
    }

    @Override
    public void onAppUninstalledEvent(ShopifyAppUninstalledEventDTO event) {
        log.info("onAppUninstalledEvent, event={}", JSON.toJSONString(event));
        shopifyCallback.onAppUninstalledEvent(event);
    }

    @Override
    public void onAuthorizeEvent(AuthorizeEventDTO event) {
        log.info("onAuthorizeEvent, event={}", JSON.toJSONString(event));
        shopifyCallback.onAuthorizeEvent(event);
    }
}
