package com.alibaba.copilot.enabler.service.bill.helper;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.Getter;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2024/3/26
 */
@Getter
public class TimeHelper {

    /**
     * 目标日期
     */
    private final Date targetDate;

    /**
     * 当月开始时间
     */
    private final DateTime monthStartTime;

    /**
     * 当月结束时间
     */
    private final DateTime monthEndTime;

    /**
     * 上月开始时间
     */
    private final DateTime lastMonthStartTime;

    /**
     * 上月结束时间
     */
    private final DateTime lastMonthEndTime;

    /**
     * Shoplazza 的账单月开始时间(上月16-本月15）
     */
    private final DateTime billMonthStartTimeShoplazza;
    /**
     * Shoplazza 的账单月结束时间(上月16-本月15）
     */
    private final DateTime billMonthEndTimeShoplazza;


    public ZonedDateTime getLastMonthEndTime(ZoneId zoneId) {
        return this.lastMonthEndTime.toInstant().atZone(zoneId);
    }

    public ZonedDateTime getMonthEndTime(ZoneId zoneId) {
        return this.monthEndTime.toInstant().atZone(zoneId);
    }

    public TimeHelper(Date targetDate) {
        this.targetDate = targetDate;

        // 计算当月开始和结束时间点
        this.monthStartTime = DateUtil.beginOfMonth(targetDate);
        this.monthEndTime = DateUtil.endOfMonth(targetDate);

        // 计算上月开始和结束时间点
        Date lastMonthTime = DateUtil.offsetMonth(targetDate, -1);
        this.lastMonthStartTime = DateUtil.beginOfMonth(lastMonthTime);
        this.lastMonthEndTime = DateUtil.endOfMonth(lastMonthTime);

        // 计算 shopify 的账单月时间

        this.billMonthStartTimeShoplazza = DateUtil.beginOfDay(DateUtil.offsetMonth(targetDate, -1).setField(DateField.DAY_OF_MONTH, 16));;
        this.billMonthEndTimeShoplazza = DateUtil.endOfDay(DateUtil.date(targetDate).setField(DateField.DAY_OF_MONTH, 15));

    }
}
