package com.alibaba.copilot.enabler.service.stripe;

import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.domain.base.mq.MessageQueueProducer;
import com.alibaba.copilot.enabler.domain.payment.constant.PaymentConst;
import com.alibaba.copilot.enabler.domain.payment.constant.StripeCheckoutSessionMode;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.StripeGateway;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeCreateCustomerRequest;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeCreateCustomerResponse;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeCreatePaymentIntentRequest;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEventMetadata;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.user.model.StripeInfo;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

import static com.alibaba.copilot.enabler.service.stripe.StripeConsts.METAQ_TAG;
import static com.alibaba.copilot.enabler.service.stripe.StripeConsts.METAQ_TOPIC;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Service
@Slf4j
public class StripeService {

    @Resource(name = "stripeMessageProducer")
    private MessageQueueProducer messageQueueProducer;

    @Resource
    private UserRepository userRepository;

    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;

    @Resource
    private StripeGateway stripeGateway;


    public void sendWebhookEvent(String webhookBody) {
        messageQueueProducer.send(METAQ_TOPIC, METAQ_TAG, webhookBody);
    }

    public String getOrCreateStripeCustomer(Long userId) {
        User user = userRepository.getUser(userId);
        if (user == null) {
            log.error("StripeService.getCustomer, user not found, userId={}", userId);
            throw new BizException(ErrorCode.SYS_ERROR, "user not found.");
        }
        StripeInfo stripeInfo = user.getAttributes().getStripeInfoNotNull();
        if (StringUtils.isNotBlank(stripeInfo.getCustomerId())) {
            return stripeInfo.getCustomerId();
        }

        // create
        StripeCreateCustomerResponse customer = createStripeUser(user, stripeInfo);
        return customer.getId();
    }

    public String getOrCreateStripeCustomer(String appCode, Long userId) {
        User user = userRepository.getUser(appCode, userId);
        if (user == null) {
            log.error("StripeService.getCustomer, user not found, userId={}", userId);
            throw new BizException(ErrorCode.SYS_ERROR, "user not found.");
        }
        StripeInfo stripeInfo = user.getAttributes().getStripeInfoNotNull();
        if (StringUtils.isNotBlank(stripeInfo.getCustomerId())) {
            return stripeInfo.getCustomerId();
        }

        StripeCreateCustomerResponse customer = createStripeUser(user, stripeInfo);
        return customer.getId();
    }

    private StripeCreateCustomerResponse createStripeUser(User user, StripeInfo stripeInfo) {
        // create
        StripeCreateCustomerRequest stripeCreateCustomerRequest = new StripeCreateCustomerRequest();
        stripeCreateCustomerRequest.setEmail(user.getEmail());
        stripeCreateCustomerRequest.setDescription(user.getAppCode());
        stripeCreateCustomerRequest.add("enablerUserId", user.getId().toString());
        stripeCreateCustomerRequest.add("bizUserId", user.getUserId().toString());
        stripeCreateCustomerRequest.add("appCode", user.getAppCode());
        StripeCreateCustomerResponse customer = stripeGateway.createCustomer(stripeCreateCustomerRequest);

        stripeInfo.setCustomerId(customer.getId());
        user.getAttributes().setStripeInfo(stripeInfo);

        userRepository.updateUser(user);
        return customer;
    }

    private String queryStripeCustomerId(Long userId) {
        User user = userRepository.getUser(userId);
        if (user == null) {
            log.error("StripeService.getCustomer, user not found, userId={}", userId);
            throw new BizException(ErrorCode.SYS_ERROR, "user not found.");
        }
        StripeInfo stripeInfo = user.getAttributes().getStripeInfoNotNull();
        return stripeInfo.getCustomerId();
    }

    private String queryStripeCustomerId(String appCode, Long userId) {
        User user = userRepository.getUser(appCode, userId);
        if (user == null) {
            log.error("StripeService.getCustomer, user not found, userId={}", userId);
            throw new BizException(ErrorCode.SYS_ERROR, "user not found.");
        }
        StripeInfo stripeInfo = user.getAttributes().getStripeInfoNotNull();
        return stripeInfo.getCustomerId();
    }


    public void createPaymentIntentForSubInSetupMode(SubscriptionOrder order, TradeRecord tradeRecord) {
        Assertor.assertNonNull(order, "order should not be null");
        Assertor.assertNonNull(tradeRecord, "tradeRecord should not be null");
        Long userId = order.getUserId();
        String customerId = queryStripeCustomerId(userId);
        Assertor.assertNotBlank(customerId, "stripe customerId should not be blank");

        // tradeNo
        String tradeNo = tradeRecord.getTradeNo();
        // payment method id
        String stripePaymentMethodId = order.getAttributes().getStripePaymentMethodId();
        // price
        BigDecimal planPrice = tradeRecord.getTradeAmount();
        long cent = planPrice.multiply(new BigDecimal(100)).longValue();
        // meta data
        StripeEventMetadata metadata = StripeEventMetadata.of(
                StripeCheckoutSessionMode.SETUP,
                order.getAppCode(),
                tradeNo);
        metadata.setUserId(order.getUserId().toString());
        metadata.setSubscriptionOrderId(order.getId().toString());
        metadata.setGoodsName(order.getSubscriptionPlanName());

        StripeCreatePaymentIntentRequest request = StripeCreatePaymentIntentRequest.builder()
                .amount(cent)
                .currency(PaymentConst.CURRENCY_USD)
                .customerId(customerId)
                .paymentMethodId(stripePaymentMethodId)
                .offSession(true)
                .confirm(true)
                .metadata(metadata)
                .build();
        stripeGateway.createPaymentIntent(request);
    }

}