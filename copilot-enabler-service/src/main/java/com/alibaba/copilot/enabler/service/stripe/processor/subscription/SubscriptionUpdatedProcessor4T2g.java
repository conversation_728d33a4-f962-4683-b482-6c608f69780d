package com.alibaba.copilot.enabler.service.stripe.processor.subscription;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.service.stripe.StripeConsts;
import com.alibaba.copilot.enabler.service.stripe.processor.AbstractStripeEventProcessor;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeExecuteContext;
import com.alibaba.copilot.enabler.service.subscription.constant.SubscriptionConstants;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.stripe.model.Subscription;
import com.stripe.param.SubscriptionCancelParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

@Component
@Slf4j
public class SubscriptionUpdatedProcessor4T2g extends AbstractStripeEventProcessor {
    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;

    @Override
    protected void doProcess(StripeExecuteContext context) throws Exception {
        log.info("SubscriptionUpdatedProcessor4T2g context={}", JSON.toJSONString(context));
        Subscription subscription = Subscription.retrieve(context.getEvent().fetch("id"));
        if (Lists.newArrayList("active", "trialing").contains(subscription.getStatus())) {
            String lookupKey = subscription.getItems().getData().get(0).getPrice().getLookupKey();
            Long planId = SwitchConfig.t2gPlanId2LookupKey.entrySet().stream()
                    .filter(e -> StringUtils.equals(lookupKey, e.getValue())).findFirst()
                    .map(Map.Entry::getKey).map(Long::parseLong).orElse(null);
            if (planId != null) {
                SubscriptionOrder subscriptionOrder = new SubscriptionOrder();
                subscriptionOrder.setId(Long.parseLong(subscription.getMetadata().get(SubscriptionConstants.META_SUB_ID)));
                subscriptionOrder.setSubscriptionPlanId(planId);
                subscriptionOrder.setPerformStartTime(new Date(subscription.getStartDate() * 1000L));
                subscriptionOrder.setPerformEndTime(new Date(subscription.getCurrentPeriodEnd() * 1000L));
                subscriptionOrder.setAppCode(subscription.getMetadata().get(SubscriptionConstants.META_APP_CODE));
                subscriptionOrder.setStatus(SubscriptionOrderStatus.IN_EFFECT);
                subscriptionOrderRepository.saveSubscriptionOrder(subscriptionOrder);
            }
        }
    }

    @Override
    public String getEventType() {
        return StripeConsts.SUBSCRIPTION_UPDATED;
    }

    @Override
    public String getAppCode() {
        return AppEnum.TEXT2GO.getCode();
    }

}
