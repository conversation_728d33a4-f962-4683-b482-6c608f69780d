package com.alibaba.copilot.enabler.service.stripe.processor.session;

import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEvent;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEventMetadata;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.service.stripe.StripeConsts;
import com.alibaba.copilot.enabler.service.stripe.StripeService;
import com.alibaba.copilot.enabler.service.stripe.processor.AbstractStripeEventProcessor;
import com.alibaba.copilot.enabler.service.stripe.processor.StripeExecuteContext;
import com.alibaba.fastjson.JSON;
import com.stripe.exception.StripeException;
import com.stripe.model.SetupIntent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Component
@Slf4j
public class SessionCompletedProcessor4Pic extends AbstractStripeEventProcessor {

    @Resource
    private StripeService stripeService;

    @Override
    public String getEventType() {
        return StripeConsts.CHECKOUT_SESSION_COMPLETED;
    }

    @Override
    public String getAppCode() {
        return AppEnum.PIC_COPILOT.getCode();
    }

    @Override
    protected void doProcess(StripeExecuteContext context) throws Exception {
        StripeEvent event = context.getEvent();
        StripeEventMetadata metaData = context.getMetaData();

        String setupIntentId = event.fetchSetupIntentId();

        // setup 模式
        if (metaData.whetherSetupMode() && StringUtils.isNotBlank(setupIntentId)) {
            doChargeForSetupMode(setupIntentId, metaData);
        } else {
            log.warn("not setup mode, skip, context={}", JSON.toJSONString(context));
        }

    }


    private void doChargeForSetupMode(String setupIntentId, StripeEventMetadata metaData) throws StripeException {
        TradeRecord tradeRecord = tradeRecordRepository.queryByTradeNo(metaData.getTradeNo());
        Assertor.assertNonNull(tradeRecord, "tradeRecord should not be null");

        SetupIntent setupIntent = SetupIntent.retrieve(setupIntentId);
        String paymentMethodId = setupIntent.getPaymentMethod();

        String subscriptionOrderId = metaData.getSubscriptionOrderId();

        // 保存 payment token
        Long pId = saveCardInfo2PaymentToken(paymentMethodId, metaData);
        // 保存到 order 表
        SubscriptionOrder subscriptionOrder = subscriptionOrderRepository.getByOrderId(Long.valueOf(subscriptionOrderId));
        savePaymentToken2SubOrder(pId, paymentMethodId, subscriptionOrder);

        // 发起扣款
        stripeService.createPaymentIntentForSubInSetupMode(subscriptionOrder, tradeRecord);

    }


}