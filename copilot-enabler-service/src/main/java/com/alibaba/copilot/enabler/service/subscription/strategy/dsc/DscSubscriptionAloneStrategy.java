package com.alibaba.copilot.enabler.service.subscription.strategy.dsc;

import cn.hutool.core.date.DateUtil;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.payment.dto.RefundResultDTO;
import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.client.subscription.dto.CycleFeeDetail;
import com.alibaba.copilot.enabler.client.subscription.dto.TrialDurationDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.base.utils.PaymentUtils;
import com.alibaba.copilot.enabler.domain.base.utils.TimeUtils;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.subscription.dto.FinalDiscountDTO;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategyConfig;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.*;
import com.alibaba.copilot.enabler.service.subscription.strategy.basic.BasicSubscriptionStrategy;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * DscCopilot应用的订阅处理独立支付策略
 */
@Slf4j
@Component
@SubscriptionStrategyConfig(value = AppEnum.DS_COPILOT, payType = SubscriptionPayType.AEPay)
public class DscSubscriptionAloneStrategy extends BasicSubscriptionStrategy {

    @Override
    public TrialDurationDTO computeTrialDuration(ComputeTrialContext context) {
        String appCode = context.getAppCode();
        Long userId = context.getUserId();
        SubscriptionPlan targetPlan = context.getTargetPlan();
        List<SubscriptionOrder> historyOrders = context.getHistoryOrders();

        if (targetPlan == null) {
            // 未指定目标套餐时, 不拥有试用期 (对应场景: 没有免费套餐的应用中, 没有订阅过, 在订阅主页显示试用信息)
            return new TrialDurationDTO().setIsTrial(false);
        }

        if (!targetPlan.isFree() && !targetPlan.getIsHasTrial()) {
            // 不包含试用期的付费套餐
            log.info("computeTrialDuration, no trial plan");
            return new TrialDurationDTO().setIsTrial(false);
        }

        if (historyOrders == null) {
            // 外部未传时, 内部查询获取
            historyOrders = getHistoryOrders(userId, appCode);
        }

        // 计算历史订阅的累计的试用天数
        long triedDay = computeTotalTrialDayFromHistory(targetPlan, historyOrders);
        log.info("computeTrialDuration, compute history trial end, triedDay={}", triedDay);

        // 计算剩余试用期天数
        final long remainDay = computeRemainDay(targetPlan, triedDay);

        return new TrialDurationDTO()
                .setIsTrial(remainDay > 0L)
                .setRemainTrialDay(remainDay);
    }

    /**
     * 计算历史订阅的累计的试用天数
     */
    private long computeTotalTrialDayFromHistory(SubscriptionPlan targetPlan, List<SubscriptionOrder> historyOrders) {
        return historyOrders.stream()
                .filter(SubscriptionOrder::getIsIncludeTrial)
                .filter(order -> StringUtils.equals(order.getSubscriptionPlanName(), targetPlan.getName()))
                .map(order -> {
                    // 不考虑时分秒
                    // 2016-02-01 23:59:59订阅，2016-02-02 00:00:00取消，算一天
                    // 订单结束时间取值
                    Date orderEndTime = order.getPerformEndTime();
                    Date orderStartTime = order.getPerformStartTime();
                    Long trialDays = order.getAttributes().getTrialDays();
                    if (trialDays == null) {
                        return 0L;
                    }

                    Date trialEndTime = DateUtils.addDays(orderStartTime, Math.toIntExact(trialDays));
                    Date now = new Date();

                    Date finalEndDate = TimeUtils.min(orderEndTime, trialEndTime, now);
                    return DateUtil.betweenDay(orderStartTime, finalEndDate, true);
                })
                .reduce(0L, Long::sum);
    }

    /**
     * 计算剩余试用期天数
     */
    private long computeRemainDay(SubscriptionPlan targetPlan, long triedDay) {
        Long trialDuration = targetPlan.getTrialDuration();
        String trialDurationUnit = targetPlan.getTrialDurationUnit();
        long totalTrailDay = TimeUtils.calculateDayCount(trialDuration, trialDurationUnit);
        return totalTrailDay - triedDay;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateOrderAndTradeResultDTO createOrderAndTrade(SubscribeContext context) {
        // 创建新订单和支付流水
        return orderDomainService.createPreparePayOrder(context);
    }

    @Override
    public void handleRefundLogicWhenOldOrderCompleted(@Nonnull SubscriptionOrder oldEffectOrder) {
        log.info("handleRefundLogicWhenOldOrderCompleted, oldEffectOrder={}", JSON.toJSONString(oldEffectOrder));

        if (oldEffectOrder.currentIsInTrial()) {
            // 试用期切换, 退全款
            refundForTrialOrder(oldEffectOrder);
            return;
        }

        // 非试用期退款, 退部分款项
        TradeRecord payRecordForEffectOrder = tradeRecordRepository.queryByOrderId(oldEffectOrder.getId(), TradeDirection.FORWARD);
        log.info("handleRefundLogicWhenOldOrderCompleted, payRecordForEffectOrder={}", JSON.toJSONString(payRecordForEffectOrder));

        if (payRecordForEffectOrder == null) {
            // 之前没有支付的流水时, 不需要退款
            log.info("handleRefundLogicWhenOldOrderCompleted, no pay record found,oldOrderId:{}", oldEffectOrder.getId());
            return;
        }

        // 尝试创建退款流水
        TradeRecord refundRecord = tryCreateRefundRecord(oldEffectOrder, payRecordForEffectOrder);
        log.info("handleRefundLogicWhenOldOrderCompleted, createRefundRecord finished, result={}", JSON.toJSONString(refundRecord));
        if (refundRecord == null) {
            // 未创建退款流水时, 不需要退款
            return;
        }

        // 获取上一笔订单的支付外部交易号 (AEPay生成的)
        String paymentId = payRecordForEffectOrder.getOutTradeNo();
        // 执行退款操作
        SingleResult<RefundResultDTO> refundResult = doRefund(paymentId, refundRecord);
        log.info("handleRefundLogicWhenOldOrderCompleted, doRefund finished, result={}", JSON.toJSONString(refundResult));
    }

    @Override
    public ComputeCycleFeeResultDTO computeCycleFee(ComputeNewPlanPriceContext context) {
        List<CycleFeeDetail> cycleFeeDetails = new ArrayList<>();
        LocalDate selectedPlanInfoDate = LocalDate.now();

        // 1. 组装首次扣款信息
        selectedPlanInfoDate = addFirstPlanCycleDetail(selectedPlanInfoDate, cycleFeeDetails, context);

        // 2. 组装二次扣款信息
        selectedPlanInfoDate = addNextPlanCycleDetail(selectedPlanInfoDate, cycleFeeDetails, context);

        // 3. 计算新套餐价格
        ComputeNewPlanPriceResultDTO priceResult = computeNewPlanPrice(context);

        return new ComputeCycleFeeResultDTO()
                .setCycleFeeDetails(cycleFeeDetails)
                .setFinalPayAmount(priceResult.getPayAmount());
    }

    /**
     * 组装首次扣款信息
     */
    private LocalDate addFirstPlanCycleDetail(LocalDate date, List<CycleFeeDetail> details, ComputeNewPlanPriceContext context) {
        FinalDiscountDTO finalDiscountDTO = context.getFinalDiscountDTO();
        SubscriptionPlan newPlan = context.getNewPlan();

        CycleFeeDetail cycleDetail = CycleFeeDetail.builder()
                .date(formatCycleDetailDate(date))
                .durationUnit(newPlan.getDurationUnit().name())
                .duration(newPlan.getDuration())
                .cycleFee((finalDiscountDTO == null || !finalDiscountDTO.getIsDiscount()) ? newPlan.getPrice() : finalDiscountDTO.getDiscountPrice())
                .feeDescription(newPlan.getDescription())
                // DSC显示的是减免比例（百分数）
                .discountDescription(getDiscountDescription(finalDiscountDTO, newPlan))
                .build();
        details.add(cycleDetail);

        // 试用期信息
        TrialDurationDTO trialDurationDTO = computeTrialDuration(new ComputeTrialContext(
                context.getAppCode(),
                context.getUserId(),
                context.getNewPlan(),
                context.getHistoryOrders()
        ));

        if (trialDurationDTO.getIsTrial()) {
            date = date.plusDays(trialDurationDTO.getRemainTrialDay());
        }

        long remainDiscountDays = TimeUtils.calculateDayCount(
                newPlan.getDuration(), newPlan.getDurationUnit());
        return date.plusDays(remainDiscountDays);
    }

    /**
     * 组装二次扣款信息
     */
    private LocalDate addNextPlanCycleDetail(LocalDate date, List<CycleFeeDetail> details, ComputeNewPlanPriceContext context) {
        FinalDiscountDTO finalDiscountDTO = context.getFinalDiscountDTO();
        SubscriptionPlan newPlan = context.getNewPlan();

        CycleFeeDetail cycleDetail = CycleFeeDetail.builder()
                .date(formatCycleDetailDate(date))
                .durationUnit(newPlan.getDurationUnit().name())
                .duration(newPlan.getDuration())
                .cycleFee((finalDiscountDTO == null || !finalDiscountDTO.getIsDiscount()) ? newPlan.getPrice() : finalDiscountDTO.getDiscountPrice())
                .feeDescription(newPlan.getDescription())
                // DSC显示的是减免比例（百分数）
                .discountDescription(getDiscountDescription(finalDiscountDTO, newPlan))
                .build();
        details.add(cycleDetail);

        long planDays = TimeUtils.calculateDayCount(newPlan.getDuration(), newPlan.getDurationUnit());
        return date.plusDays(planDays);
    }

    /**
     * 尝试创建退款的交易流水
     */
    @Nullable
    private TradeRecord tryCreateRefundRecord(@Nonnull SubscriptionOrder oldEffectOrder,
                                              @Nonnull TradeRecord payRecordForEffectOrder) {

        Long userId = oldEffectOrder.getUserId();
        BigDecimal refundPrice = computeRefundPrice(oldEffectOrder, payRecordForEffectOrder);
        if (refundPrice.compareTo(BigDecimal.ZERO) <= 0) {
            // 退款金额计算非正数时, 不需要退款
            log.info("tryCreateRefundRecord, skip refund, refundPrice={}", refundPrice);
            return null;
        }

        String paymentMethod = payRecordForEffectOrder.getPaymentMethod();
        TradeRecord refundRecord = TradeRecord.builder()
                .userId(userId)
                .subscriptionOrderId(oldEffectOrder.getId())
                .appCode(oldEffectOrder.getAppCode())
                .tradeAmount(refundPrice)
                .tradeCurrency(AEPaymentConstants.CURRENCY_CODE)
                .tradeDirection(TradeDirection.REFUND)
                .status(TradeRecordStatus.TODO)
                .paymentMethod(paymentMethod)
                .taxAmount(null)
                .taxCurrency(null)
                .transactionAmount(null)
                .transactionCurrency(null)
                .tradeNo(PaymentUtils.generateTradeNo(userId))
                .deleted(false)
                .build();
        tradeRecordRepository.createOrUpdateTradeRecord(refundRecord);
        return refundRecord;
    }

    /**
     * 计算退款金额
     */
    private BigDecimal computeRefundPrice(@Nonnull SubscriptionOrder oldEffectOrder,
                                          @Nonnull TradeRecord payRecordForEffectOrder) {
        Long effectPlanId = oldEffectOrder.getSubscriptionPlanId();
        SubscriptionPlan effectPlan = subscriptionPlanRepository.queryByPlanId(effectPlanId, false);

        long usedTime = DateUtil.betweenDay(oldEffectOrder.getPerformStartTime(), new Date(), true);
        // 扣除试用期天数
        if (oldEffectOrder.getAttributes() != null && oldEffectOrder.getAttributes().getTrialDays() != null) {
            usedTime = usedTime - oldEffectOrder.getAttributes().getTrialDays();
        }
        if (usedTime < 0L) {
            usedTime = 0L;
        }

        Long effcPlanDuration = effectPlan.getDuration();
        String effcPlanDurationUnit = effectPlan.getDurationUnit().name();

        if (DurationUnit.YEAR.name().equals(effcPlanDurationUnit)) {
            effcPlanDuration = effcPlanDuration * 365;
        } else if (DurationUnit.MONTH.name().equals(effcPlanDurationUnit)) {
            effcPlanDuration = effcPlanDuration * 30;
        }
        long remainTime = effcPlanDuration - usedTime;
        BigDecimal refundAmount = BigDecimal.valueOf(remainTime)
                .divide(BigDecimal.valueOf(effcPlanDuration), 10, RoundingMode.HALF_UP)
                .multiply(payRecordForEffectOrder.getTradeAmount())
                .setScale(2, RoundingMode.HALF_UP);
        log.info("computeRefundPrice, compute finished, refundAmount={}", refundAmount);
        return refundAmount;
    }

    @Nullable
    private static String getDiscountDescription(FinalDiscountDTO finalDiscountDTO, SubscriptionPlan newPlan) {
        if (finalDiscountDTO == null || !finalDiscountDTO.getIsDiscount()) {
            // 年套餐默认是减免40%，如果折扣码优惠减免小于40%，则不使用折扣码，否则才使用折扣码的减免优惠
            if (newPlan.getDurationUnit() == DurationUnit.YEAR) {
                return String.valueOf(AEPaymentConstants.DSC_ALONE_ANNUAL_PLAN_DEFAULT_REDUCE_DISCOUNT);
            }
            return null;
        }

        if (finalDiscountDTO.getDiscountInfoDTO() == null) {
            return null;
        }

        return String.valueOf(100 - finalDiscountDTO.getDiscountInfoDTO().getDiscount());
    }

    @Override
    protected String formatCycleDetailDate(LocalDate date) {
        return date.format(DateTimeFormatter.ofPattern("yyyy-M-d"));
    }
}
