package com.alibaba.copilot.enabler.service.user.service;

import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.enabler.client.user.constants.UserDTO;
import com.alibaba.copilot.enabler.client.user.dto.UserOverviewInfoDTO;
import com.alibaba.copilot.enabler.client.user.dto.UserSourceDTO;
import com.alibaba.copilot.enabler.client.user.service.UserQueryService;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import com.alibaba.copilot.enabler.domain.user.request.UserQuery;
import com.alibaba.copilot.enabler.infra.base.constants.ErrorCodes;
import com.alibaba.copilot.enabler.infra.base.utils.HttpUtils;
import com.alibaba.copilot.enabler.service.user.dto.UserShardInfoDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/4
 */
@Slf4j
@Service
public class UserQueryServiceImpl implements UserQueryService {

    @Resource
    private UserRepository userRepository;

    @Value("${commissionHttpServiceUrlPrefix}")
    private String commissionHttpServiceUrlPrefix;

    /**
     * 个人中心推广信息查询
     */
    private static final String USER_SHARE_INFO = "/commission/v2/share/info";

    @Override
    @Monitor(name = "个人信息查询", level = Monitor.Level.P0, layer = Monitor.Layer.SERVICE)
    public UserOverviewInfoDTO queryUserOverviewInfo(Long userId, String appCode) {
        log.info("UserQueryServiceImpl#queryUserOverviewInfo# request userId: {}, appCode: {}", userId, appCode);
        if (userId == null || StringUtils.isBlank(appCode)) {
            return null;
        }

        User user = userRepository.getUser(userId);
        if (user == null) {
            log.warn("UserQueryServiceImpl#queryUserOverviewInfo warn, getUser result is null, userId is: {}", userId);
            throw new RuntimeException(ErrorCodes.USER_NOT_EXIST.getMessage());
        }

        UserShardInfoDTO userShareInfo = getUserShareInfo(appCode, user.getEmail());
        UserOverviewInfoDTO.ShareInfo shareInfo = null;
        if (userShareInfo != null) {
            shareInfo = UserOverviewInfoDTO.ShareInfo.builder()
                    .url(userShareInfo.getUrl())
                    .registered(userShareInfo.getProgressInfo() == null ? null : userShareInfo.getProgressInfo().getRegistered())
                    .shareText(userShareInfo.getShareText())
                    .converted(userShareInfo.getProgressInfo() == null ? null : userShareInfo.getProgressInfo().getConverted())
                    .build();
        }
        return UserOverviewInfoDTO.builder()
                .userId(user.getUserId())
                .email(user.getEmail())
                .shareInfo(shareInfo)
                .spreadUser(shareInfo != null ? StringUtils.isNotBlank(shareInfo.getUrl()) : Boolean.FALSE)
                .build();
    }

    @Override
    public UserDTO queryUserDTO(Long userId, String appCode) {
        User user = userRepository.getUser(userId);
        if (user == null) {
            return null;
        }

        return new UserDTO()
                .setUserId(user.getUserId())
                .setEmail(user.getEmail())
                .setGmtCreate(user.getGmtCreate())
                .setAppCode(user.getAppCode())
                .setRegisterTime(user.getAttributes().getRegisterTime());
    }

    @Override
    public UserSourceDTO queryUserSource(Long userId, String email) {
        UserQuery query = new UserQuery();
        query.setUserId(userId);
        query.setEmail(email);
        User user = userRepository.queryUserByParams(query);
        if (user == null) {
            return null;
        }

        return new UserSourceDTO()
                .setFirstChannel(user.getAttributes().getFirstChannel())
                .setLastChannel(user.getAttributes().getLastChannel())
                .setEmail(user.getEmail())
                .setUserId(user.getUserId());
    }

    /**
     * 查询推广信息
     *
     * @param appCode
     * @param email
     * @return
     */
    private UserShardInfoDTO getUserShareInfo(String appCode, String email) {
        Map<String, String> params = new HashMap<>();
        params.put("app", appCode);
        params.put("email", email);
        UserShardInfoDTO userShareInfo = null;
        try {
            log.info("UserQueryServiceImpl#queryUserOverviewInfo#getUserShareInfo#params appCode {}, email: {}", appCode, email);
            userShareInfo = HttpUtils.get(commissionHttpServiceUrlPrefix + USER_SHARE_INFO, null, params, UserShardInfoDTO.class);
        } catch (Exception e) {
            log.error("UserQueryServiceImpl#queryUserOverviewInfo#getUserShareInfo http error, {}, param appCode: {}, email: {}", e.getMessage(), appCode, email);
            throw new RuntimeException(e);
        }
        log.info("UserQueryServiceImpl#queryUserOverviewInfo#getUserShareInfo#result is: {}", JSON.toJSONString(userShareInfo));
        return userShareInfo;
    }
}
