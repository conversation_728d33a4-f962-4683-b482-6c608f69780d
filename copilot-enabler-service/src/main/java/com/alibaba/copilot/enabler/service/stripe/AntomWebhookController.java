package com.alibaba.copilot.enabler.service.stripe;

import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.monitor.annotation.MonitorResult;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.service.antom.AntomService;
import com.alibaba.copilot.enabler.service.base.constant.Constants;
import com.alipay.global.api.tools.SignatureTool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

import static com.alibaba.copilot.enabler.service.antom.AntomConsts.ANTOM_CLIENT_ID_HEADER_KEY;
import static com.alibaba.copilot.enabler.service.antom.AntomConsts.ANTOM_REQUEST_TIME_HEADER_KEY;
import static com.alibaba.copilot.enabler.service.antom.AntomConsts.ANTOM_SIGNATURE_HEADER_KEY;

/**
 * Antom Webhook 控制器
 *
 * <AUTHOR>
 * @date 2025/05/01
 */
@Slf4j
@Api(tags = "Antom Callback")
@RestController
@RequestMapping(Constants.EXTERNAL_URI_PREFIX + "/antom/callback")
public class AntomWebhookController {

    @Resource
    private AntomService antomService;

    /**
     * Webhook 回调监听器
     *
     * @param entity HTTP 实体
     * @return 响应实体
     */
    @Monitor(name = "[Antom] Webhook", layer = Monitor.Layer.WEB, level = Monitor.Level.P1)
    @MonitorResult
    @ApiOperation("Webhook回调地址")
    @RequestMapping(value = {"/webhook"}, method = {RequestMethod.GET, RequestMethod.POST})
    public ResponseEntity<String> listener(HttpEntity<String> entity) {
        String webhookBody = entity.getBody();
        try {
            // 验证签名
            String signature = entity.getHeaders().getFirst(ANTOM_SIGNATURE_HEADER_KEY);
            String clientId = entity.getHeaders().getFirst(ANTOM_CLIENT_ID_HEADER_KEY);
            String requestTime = entity.getHeaders().getFirst(ANTOM_REQUEST_TIME_HEADER_KEY);

            log.info("AntomWebhookController listener, signature={}, clientId={}, requestTime={}, webhookBody={}",
                    signature, clientId, requestTime, webhookBody);

            // 验证签名
            if (StringUtils.isNotBlank(signature) && StringUtils.isNotBlank(clientId) && StringUtils.isNotBlank(requestTime)) {
                try {
                    boolean isValid = SignatureTool.verify(
                            "POST",
                            "/external/antom/callback/webhook",
                            clientId,
                            requestTime,
                            webhookBody,
                            signature,
                            SwitchConfig.antomPublicKey);
                    // todo huangmeng
                    if (true) {
                        log.warn("Invalid signature for Antom webhook: {}", signature);
                        return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
                    }
                } catch (Exception e) {
                    log.error("Failed to verify Antom webhook signature", e);
                }
            }

            // 发送事件到消息队列
            antomService.sendWebhookEvent(webhookBody);

            // 返回固定的成功响应，按照 Antom 文档要求
            return ResponseEntity.ok("{\"result\":{\"resultCode\":\"SUCCESS\",\"resultStatus\":\"S\",\"resultMessage\":\"success\"}}");
        } catch (Exception e) {
            log.error("Antom listener error, webhookBody={}", webhookBody, e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
    }

    /**
     * 预发 Webhook
     *
     * @param entity HTTP 实体
     * @return 响应实体
     */
    @ApiOperation("预发Webhook")
    @RequestMapping(value = {"/pre/webhook"}, method = {RequestMethod.GET, RequestMethod.POST})
    public ResponseEntity<String> preListener(HttpEntity<String> entity) {
        String webhookBody = entity.getBody();
        try {
            String signature = entity.getHeaders().getFirst(ANTOM_SIGNATURE_HEADER_KEY);
            String clientId = entity.getHeaders().getFirst(ANTOM_CLIENT_ID_HEADER_KEY);
            String requestTime = entity.getHeaders().getFirst(ANTOM_REQUEST_TIME_HEADER_KEY);

            log.info("AntomWebhookController pre listener, signature={}, clientId={}, requestTime={}, webhookBody={}",
                    signature, clientId, requestTime, webhookBody);

            if (StringUtils.isBlank(webhookBody)) {
                throw new RuntimeException("empty webhook body");
            }

            // 转发到预发环境
            String targetUrl = "https://pre-copilot-enabler.alibaba-inc.com/external/antom/callback/webhook";
            HttpURLConnection connection = (HttpURLConnection)(new URL(targetUrl)).openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty(ANTOM_SIGNATURE_HEADER_KEY, signature);
            connection.setRequestProperty(ANTOM_CLIENT_ID_HEADER_KEY, clientId);
            connection.setRequestProperty(ANTOM_REQUEST_TIME_HEADER_KEY, requestTime);
            connection.setDoOutput(true);
            connection.getOutputStream().write(webhookBody.getBytes());

            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            StringBuilder response = new StringBuilder();

            String line;
            while((line = reader.readLine()) != null) {
                response.append(line);
            }

            reader.close();
            log.info("Pre webhook response: {}", response.toString());

            // 返回固定的成功响应，按照 Antom 文档要求
            return ResponseEntity.ok("{\"result\":{\"resultCode\":\"SUCCESS\",\"resultStatus\":\"S\",\"resultMessage\":\"success\"}}");
        } catch (Exception e) {
            log.error("Antom pre listener error, webhookBody={}", webhookBody, e);
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
    }
}
