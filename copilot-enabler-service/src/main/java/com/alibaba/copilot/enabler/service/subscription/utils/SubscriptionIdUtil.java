package com.alibaba.copilot.enabler.service.subscription.utils;

import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.infra.subscription.vo.SubsciptionShareCodeVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

@Slf4j
public class SubscriptionIdUtil {

    public static BigDecimal getPrice(SubscriptionPlan plan,String shareCode) {

        BigDecimal originPlanPrice = null;

        if (StringUtils.equals(AppEnum.SEO_COPILOT_SITE.getCode(), plan.getAppCode())){
            //正常获取Attributes里的价格
            if (plan.getAttributes() != null && plan.getAttributes().containsKey("discountPrice")){
                originPlanPrice = BigDecimal.valueOf(plan.getAttributes().getAsDouble("discountPrice"));
            }
            if (StringUtils.isNotBlank(shareCode)){
                //有折扣码获取折扣码价格
                String durationUnit = plan.getDurationUnit() == DurationUnit.YEAR ? "YEAR" : plan.getDurationUnit() == DurationUnit.MONTH ? "MONTH" : "OTHER";
                String discountPriceKey = plan.getName() + "_" + durationUnit + "_" + shareCode;
                log.info("buildSelectedPlanInfo discountPriceKey={},shareCode={},planId={}", discountPriceKey, shareCode, plan.getId());
                if (SwitchConfig.shareCodeSubscriptionConfig.get(discountPriceKey) != null){
                    SubsciptionShareCodeVO subsciptionShareCodeVO = SwitchConfig.shareCodeSubscriptionConfig.get(discountPriceKey);
                    originPlanPrice = BigDecimal.valueOf(subsciptionShareCodeVO.getSharePrice());
                }
            }
        }
        return originPlanPrice;
    }
}
