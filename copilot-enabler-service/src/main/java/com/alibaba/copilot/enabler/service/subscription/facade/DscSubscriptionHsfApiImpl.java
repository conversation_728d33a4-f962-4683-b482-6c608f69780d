package com.alibaba.copilot.enabler.service.subscription.facade;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.dto.*;
import com.alibaba.copilot.enabler.client.subscription.enums.FeatureUseType;
import com.alibaba.copilot.enabler.client.subscription.facade.DscSubscriptionHsfApi;
import com.alibaba.copilot.enabler.client.subscription.service.DscSubscriptionService;
import com.alibaba.copilot.enabler.client.subscription.service.ShopifySubscriptionService;
import com.alibaba.copilot.enabler.domain.subscription.model.FeatureUsage;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.FeatureUsageDO;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionFeatureDO;
import com.alibaba.copilot.enabler.infra.subscription.factory.FeatureUsageConverter;
import com.alibaba.copilot.enabler.infra.subscription.mapper.FeatureUsageMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionFeatureMapper;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2024/1/30
 */
@Slf4j
@Service
@HSFProvider(serviceInterface = DscSubscriptionHsfApi.class)
public class DscSubscriptionHsfApiImpl implements DscSubscriptionHsfApi {

    @Resource
    private DscSubscriptionService dscSubscriptionService;

    @Resource
    private FeatureUsageMapper featureUsageMapper;

    @Resource
    private SubscriptionFeatureMapper subscriptionFeatureMapper;

    @Resource
    private ShopifySubscriptionService shopifySubscriptionService;

    @Override
    public SingleResult<DscSubscriptionUsageInfoDTO> currentSubscriptionInfo(Long userId) {
        DscSubscriptionUsageInfoDTO infoDTO = dscSubscriptionService.currentSubscriptionInfo(userId);
        return SingleResult.buildSuccess(infoDTO);
    }

    @Override
    public SingleResult<DscSubscriptionInfoDTO> useSubscriptionFeature(UseSubscriptionFeatureDTO featureLimitDTO) {
        if (featureLimitDTO == null || featureLimitDTO.getUserId() == null || featureLimitDTO.getUseType() == null) {
            return SingleResult.buildSuccess(null);
        }

        if (!FeatureUseType.USE.equals(featureLimitDTO.getUseType())) {
            return SingleResult.buildSuccess(null);
        }

        // 查询订阅计划 & 特性
        DscSubscriptionInfoDTO subscriptionInfoDTO = new DscSubscriptionInfoDTO();
        DscSubscriptionUsageInfoDTO usageInfoDTO = dscSubscriptionService.currentSubscriptionInfo(featureLimitDTO.getUserId());

        // 直接查询特性使用情况
        QueryWrapper<FeatureUsageDO> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", featureLimitDTO.getUserId());
        wrapper.eq("feature_type", featureLimitDTO.getFeatureType());
        wrapper.eq("app_code", featureLimitDTO.getAppCode());
        List<FeatureUsageDO> featureUsageDOS = featureUsageMapper.selectList(wrapper);

        subscriptionInfoDTO = updateUsageInfo(usageInfoDTO, featureUsageDOS, featureLimitDTO);

        return SingleResult.buildSuccess(subscriptionInfoDTO);
    }

    private DscSubscriptionInfoDTO updateUsageInfo(DscSubscriptionUsageInfoDTO usageInfoDTO, List<FeatureUsageDO> featureUsageDOS, UseSubscriptionFeatureDTO featureLimitDTO) {
        DscSubscriptionInfoDTO subscriptionInfoDTO = new DscSubscriptionInfoDTO();

        // 无订阅订单, 默认 basic plan & 写入使用量
        String planNameStr = null;
        if (usageInfoDTO == null) {
            planNameStr = "Basic";
        } else {
            planNameStr = usageInfoDTO.getSubscriptionPlanName();
        }

        // 存在特性使用记录
        if (!CollectionUtils.isEmpty(featureUsageDOS)) {
            ShopifySubscribedPlanQueryDTO shopifySubscribedPlanQueryDTO = new ShopifySubscribedPlanQueryDTO()
                    .setAppCode(featureLimitDTO.getAppCode())
                    .setUserId(featureLimitDTO.getUserId());
            ShopifySubscribedPlanResultDTO subscribedPlan = shopifySubscriptionService.getSubscribedPlan(
                    shopifySubscribedPlanQueryDTO);
            if (subscribedPlan == null) {
                return null;
            }

            // 判断当前计划中的用量是否超过限制
            FeatureUsageDO first = featureUsageDOS.stream().filter(usage -> {
                if (StringUtils.isNotBlank(usage.getAttributes()) && !"{}".equals(usage.getAttributes())) {
                    JSONObject jsonObject = JSONObject.parseObject(usage.getAttributes());
                    if (jsonObject != null) {
                        String featureType = String.valueOf(jsonObject.get("featureType"));
                        String planName = String.valueOf(jsonObject.get("planName"));
                        return StringUtils.isNotBlank(featureType) &&
                                subscribedPlan.getPlanName().equals(planName) &&
                                featureType.equals(featureLimitDTO.getFeatureType());
                    }
                }
                return false;
            }).findFirst().orElse(null);
            if (first == null) {
                // 写入使用量
                dscSubscriptionService.updateSubscriptionFeature(featureLimitDTO.getUserId(), featureLimitDTO.getFeatureType(), planNameStr, Boolean.TRUE);
                return null;
            } else {
                // 判断是否超限
                FeatureUsage featureUsage = FeatureUsageConverter.INSTANCE.convertA2B(first);
                // 超限
                if (first.getUsageCount() >= first.getQuota()) {
                    QueryWrapper<SubscriptionFeatureDO> queryWrapper = new QueryWrapper<SubscriptionFeatureDO>();
                    queryWrapper.eq("name", featureLimitDTO.getFeatureName() + planNameStr);
                    queryWrapper.eq("app_code", featureLimitDTO.getAppCode());

                    List<SubscriptionFeatureDO> subscriptionFeatureDOS = subscriptionFeatureMapper.selectList(queryWrapper);
                    if (!CollectionUtils.isEmpty(subscriptionFeatureDOS)) {
                        SubscriptionFeatureDO subscriptionFeatureDO = subscriptionFeatureDOS.get(0);
                        if (subscriptionFeatureDO != null) {
                            subscriptionInfoDTO.setFeatureId(subscriptionFeatureDO.getId());
                        }
                    }

                    subscriptionInfoDTO.setPlanName(subscribedPlan.getPlanName());
                    subscriptionInfoDTO.setFeatureName(featureLimitDTO.getFeatureName());
                    return subscriptionInfoDTO;
                } else {
                    // 未超限
                    dscSubscriptionService.updateSubscriptionFeature(first.getUserId(), featureLimitDTO.getFeatureType(), featureUsage.getAttributes().getPlanName(), Boolean.FALSE);
                    return null;
                }
            }
        } else {
            // 不存在特性使用记录
            // 写入使用量
            dscSubscriptionService.updateSubscriptionFeature(featureLimitDTO.getUserId(), featureLimitDTO.getFeatureType(), planNameStr, Boolean.TRUE);
            return null;
        }
    }

    @Override
    public SingleResult<Long> returnSubscriptionFeature(Long userId, Long featureId, String featureType) {
        if (featureId == null || userId == null) {
            SingleResult.buildSuccess(null);
        }

        DscSubscriptionUsageInfoDTO usageInfoDTO = dscSubscriptionService.currentSubscriptionInfo(userId);
        if (usageInfoDTO == null || CollectionUtils.isEmpty(usageInfoDTO.getFeatureInfoList())) {
            return SingleResult.buildSuccess(null);
        }

        DscSubscriptionUsageInfoDTO.SubscriptionFeatureUsageInfo info = usageInfoDTO.getFeatureInfoList().stream()
                .filter(feature -> feature.getFeatureId().equals(featureId))
                .findFirst()
                .orElse(null);
        if (info == null) {
            return SingleResult.buildSuccess(null);
        }

        dscSubscriptionService.rollbackFeatureUsage(userId, featureId, featureType);

        return SingleResult.buildSuccess(featureId);
    }

    @Override
    public SingleResult<List<Long>> querySubscribeToSeventhDayUsers() {
        List<Long> userList = dscSubscriptionService.querySubscribeToSeventhDayUsers();
        return SingleResult.buildSuccess(userList);
    }

    @Override
    public SingleResult<List<Long>> querySubscribeToFourteenDayUsers() {
        List<Long> userList = dscSubscriptionService.querySubscribeToFourteenDayUsers();
        return SingleResult.buildSuccess(userList);
    }

    @Override
    public SingleResult<List<Long>> queryRenewSubscribeFiveDayUsers() {
        List<Long> userList = dscSubscriptionService.queryRenewSubscribeFiveDayUsers();
        return SingleResult.buildSuccess(userList);
    }

    @Override
    public SingleResult<List<Long>> queryCancelSubscribeThreeDayUsers() {
        List<Long> userList = dscSubscriptionService.queryCancelSubscribeThreeDayUsers();
        return SingleResult.buildSuccess(userList);
    }

}
