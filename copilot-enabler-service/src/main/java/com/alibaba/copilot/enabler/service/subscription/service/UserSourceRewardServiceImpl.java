package com.alibaba.copilot.enabler.service.subscription.service;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.enums.UserInvitationSourceType;
import com.alibaba.copilot.enabler.client.subscription.service.UserSourceRewardService;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.UserInvitationDO;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.UserInvitationRewardDO;
import com.alibaba.copilot.enabler.infra.subscription.mapper.UserInvitationMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.UserInvitationRewardMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.JSONObject;

/**
 * UserSourceRewardService实现类
 * 注意：该实现使用已有的UserInvitationDO和UserInvitationRewardDO表来存储来源福利数据
 *
 * <AUTHOR>
 * @date 2025/3/19
 */
@Slf4j
@Service
public class UserSourceRewardServiceImpl implements UserSourceRewardService {

    @Autowired
    private UserInvitationMapper userInvitationMapper;

    @Autowired
    private UserInvitationRewardMapper userInvitationRewardMapper;

    @Override
    public SingleResult<Boolean> hasReceivedSourceReward(Long userId, UserInvitationSourceType sourceType, String rewardType) {
        try {
            if (userId == null || sourceType == null) {
                log.warn("Invalid parameters for hasReceivedSourceReward: userId={}, sourceType={}", userId, sourceType);
                return SingleResult.buildFailure("参数无效");
            }

            // 如果不是一次性福利，则直接返回false表示可以领取
            if (!sourceType.isOneTimeReward()) {
                log.info("Source type is not one-time reward, always allow: userId={}, sourceType={}", 
                         userId, sourceType);
                return SingleResult.buildSuccess(false);
            }

            // 使用sourceType.getUserId()作为邀请人ID来查询
            Long sourceUserId = sourceType.getUserId();
            
            // 首先检查是否存在关联关系
            UserInvitationDO existingInvitation = userInvitationMapper.selectByInviterAndInvitee(
                sourceUserId, userId);
            
            if (existingInvitation == null) {
                // 不存在关联关系，说明未领取过
                return SingleResult.buildSuccess(false);
            }
            
            // 如果指定了奖励类型，则进一步检查是否存在对应的奖励记录
            if (StringUtils.isNotBlank(rewardType)) {
                QueryWrapper<UserInvitationRewardDO> rewardWrapper = new QueryWrapper<>();
                rewardWrapper.eq("user_id", userId);
                rewardWrapper.eq("invite_id", existingInvitation.getId());
                rewardWrapper.eq("reward_type", rewardType);
                
                Long count = userInvitationRewardMapper.selectCount(rewardWrapper);
                boolean hasReceived = count != null && count > 0;
                
                log.info("Check if user has received specific source reward: userId={}, sourceType={}, rewardType={}, result={}", 
                         userId, sourceType, rewardType, hasReceived);
                
                return SingleResult.buildSuccess(hasReceived);
            }
            
            // 存在关联关系，说明已领取过
            log.info("User has received source reward: userId={}, sourceType={}", userId, sourceType);
            return SingleResult.buildSuccess(true);
        } catch (Exception e) {
            log.error("Error checking if user has received source reward: userId={}, sourceType={}, error={}", 
                     userId, sourceType, e.getMessage(), e);
            return SingleResult.buildFailure("查询失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SingleResult<Boolean> createSourceReward(Long userId, UserInvitationSourceType sourceType, 
                                                 String rewardType, String attributes) {
        try {
            if (userId == null || sourceType == null) {
                log.warn("Invalid parameters for createSourceReward: userId={}, sourceType={}", userId, sourceType);
                return SingleResult.buildFailure("参数无效");
            }

            // 使用sourceType.getUserId()作为邀请人ID
            Long sourceUserId = sourceType.getUserId();
            
            // 1. 创建或获取邀请关系
            UserInvitationDO invitationDO = userInvitationMapper.selectByInviterAndInvitee(sourceUserId, userId);
            Long inviteId;
            
            if (invitationDO == null) {
                // 如果不存在关系，则创建新关系
                invitationDO = new UserInvitationDO();
                invitationDO.setInviterId(sourceUserId);
                invitationDO.setInviteeId(userId);
                // 设置附加属性表示这是一个来源福利关系
                // 可以在这里添加额外逻辑来标记特殊来源，如注释或备注字段
                
                userInvitationMapper.insert(invitationDO);
                inviteId = invitationDO.getId();
                
                log.info("Created source reward relationship: sourceType={}, userId={}, inviteId={}", 
                         sourceType, userId, inviteId);
            } else {
                // 已存在关系，获取ID
                inviteId = invitationDO.getId();
                log.info("Found existing source reward relationship: sourceType={}, userId={}, inviteId={}", 
                         sourceType, userId, inviteId);
            }
            
            // 2. 解析attributes中的奖励类型和数量
            Map<String, Integer> rewardTypes = new HashMap<>();
            try {
                if (StringUtils.isNotBlank(attributes)) {
                    JSONObject jsonObject = JSON.parseObject(attributes);
                    if (jsonObject != null && jsonObject.containsKey("items")) {
                        // 尝试获取items字段中的奖励类型和数量
                        rewardTypes = JSON.parseObject(jsonObject.getString("items"), 
                            new TypeReference<Map<String, Integer>>() {});
                    }
                }
            } catch (Exception e) {
                log.warn("Failed to parse reward attributes: {}, will use default values", attributes, e);
            }
            
            // 如果解析失败或者奖励类型为空，则记录日志但继续处理
            if (rewardTypes.isEmpty()) {
                log.warn("No valid reward types found in attributes: {}", attributes);
            }
            
            // 3. 为每种奖励类型创建记录
            for (Map.Entry<String, Integer> entry : rewardTypes.entrySet()) {
                String featureType = entry.getKey();
                Integer amount = entry.getValue();
                
                if (amount <= 0) {
                    log.warn("Skipping invalid reward amount: feature={}, amount={}", featureType, amount);
                    continue;
                }
                
                // 创建永久有效（1000年）的奖励记录
                UserInvitationRewardDO rewardDO = new UserInvitationRewardDO();
                rewardDO.setUserId(userId);
                rewardDO.setInviteId(inviteId);
                rewardDO.setRewardType(featureType);  // 使用传入的特性类型
                rewardDO.setRewardAmount(amount);     // 使用传入的奖励数量
                
                // 设置有效期为1000年（永久）
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.YEAR, 1000);
                rewardDO.setExpiryTime(calendar.getTime());
                
                userInvitationRewardMapper.insert(rewardDO);
                log.info("Created user_invitation_reward record: sourceType={}, userId={}, featureType={}, amount={}, id={}", 
                         sourceType, userId, featureType, amount, rewardDO.getId());
            }
            
            return SingleResult.buildSuccess(true);
        } catch (Exception e) {
            log.error("Error creating source reward record: userId={}, sourceType={}, error={}", 
                     userId, sourceType, e.getMessage(), e);
            return SingleResult.buildFailure("创建记录失败: " + e.getMessage());
        }
    }
} 