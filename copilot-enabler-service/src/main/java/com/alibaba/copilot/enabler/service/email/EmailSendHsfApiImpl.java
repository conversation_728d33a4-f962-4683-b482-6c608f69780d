package com.alibaba.copilot.enabler.service.email;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.email.dto.EmailProductFocusDTO;
import com.alibaba.copilot.enabler.client.email.facade.EmailSendHsfApi;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.global.midplatform.constants.ChannelTopicEnum;
import com.alibaba.global.midplatform.domain.GlobalMessage;
import com.alibaba.global.midplatform.domain.GlobalSendResult;
import com.alibaba.global.midplatform.exception.NotifyPushException;
import com.alibaba.global.midplatform.starter.MessageCenterPushClient;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2024/1/24
 */
@Slf4j
@Service
@HSFProvider(serviceInterface = EmailSendHsfApi.class)
public class EmailSendHsfApiImpl implements EmailSendHsfApi {
    public static String TENANT_ID = "AI_GLOBAL";

    @Autowired
    private MessageCenterPushClient messageCenterPushClient;

    @Override
    public SingleResult<Boolean> sendEmail(String email, EmailProductFocusDTO emailTemplateDTO) {
        log.info("sendEmail, email={}, emailTemplateDTO={}", email, JSON.toJSONString(emailTemplateDTO));
        Assertor.assertNotBlank(email, "email is blank");
        Assertor.assertNonNull(emailTemplateDTO, "emailTemplateDTO is null");

        // 查询邮箱模板配置
//        Class<?> objType = emailInfoObj.getClass();
//        Assertor.asserts(objType.isAnnotationPresent(EmailTemplate.class), "No @EmailTemplate configured for " + objType);
//        EmailTemplate emailTemplate = objType.getAnnotation(EmailTemplate.class);
//        String templateId = emailTemplate.id();
//        String templateName = emailTemplate.name();
//        String templateInstanceId = Optional.of(emailTemplate.instanceId())
//                .filter(StringUtils::isNotEmpty)
//                .orElse(templateName);
        String templateId = emailTemplateDTO.getTemplateId();
        String templateName = emailTemplateDTO.getTemplateName();
        String templateInstanceId = Optional.of(emailTemplateDTO.getTemplateInstanceId())
                .filter(StringUtils::isNotEmpty)
                .orElse(templateName);

        if (StringUtils.isAnyBlank(templateId, templateName, templateInstanceId)) {
            return SingleResult.buildSuccess(Boolean.FALSE);
        }


        // 构建请求信息
        Type mapType = new TypeReference<Map<String, Object>>() {
        }.getType();
        Map<String, Object> extDataParam = JSON.parseObject(JSON.toJSONString(emailTemplateDTO), mapType);
        GlobalMessage globalMessage = new GlobalMessage();
        globalMessage.setRequirementId(templateId);

        globalMessage.setTemplateName(templateName);
        globalMessage.setTemplateInstanceId(templateInstanceId);
        globalMessage.setSendToUser(Lists.newArrayList(email));
        globalMessage.setExtDataParam(extDataParam);
        // 默认多租户环境和多租户应用，如果是非多租户应用使用下面第二个发送方法
        sendMessage(globalMessage);

        return SingleResult.buildSuccess(Boolean.TRUE);
    }

    /**
     * 指定目标租户ID，并将消息投递到该租户。适用于中心化、单元化、非多租户应用。
     * send message to target channel for app without landlord.
     *
     * @param message 标准消息模型 standard message domain model
     */
    private void sendMessage(GlobalMessage message) {
        try {
            log.info("sendMessage, message={}", JSON.toJSONString(message));
            GlobalSendResult<String> globalSendResult = messageCenterPushClient.sendMessageWithTenantId(
                    TENANT_ID, message, ChannelTopicEnum.EMAIL);
            if (globalSendResult.isSuccess()) {
                // 代表消息投递成功，必须打印或记录这个messageId，否则出了问题消息域无法排查
                // it means the message push successfully, This message ID must be printed or recorded, it help us to check problem.
                String messageId = globalSendResult.getResult();
                log.info("sendMessage, send success, messageId={}", messageId);
            } else {
                // 代表消息投递失败，重试或者其他异常处理
                // it means the message push failed, retry or other exception handling
                log.error("sendMessage, send error, result={}", JSON.toJSONString(globalSendResult));
            }
        } catch (NotifyPushException e) {
            // 处理异常，记录日志
            log.error("sendMessage, invoke error", e);
        }
    }
}
