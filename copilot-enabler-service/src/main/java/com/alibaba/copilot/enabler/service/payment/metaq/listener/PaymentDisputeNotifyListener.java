package com.alibaba.copilot.enabler.service.payment.metaq.listener;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.aepay.fund.business.api.payment.dto.notify.DisputeNotifyDTO;
import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.infra.base.utils.MetaqUtils;
import com.alibaba.copilot.enabler.service.payment.metaq.handler.PaymentDisputeNotifyHandler;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.taobao.eagleeye.EagleEye;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 拒付通知listener
 */
@Component(value = "paymentDisputeNotifyListener")
public class PaymentDisputeNotifyListener implements MessageListenerConcurrently {
    public static final Logger TASK_LOG = LoggerFactory.getLogger("taskLogger");

    @Autowired
    private PaymentDisputeNotifyHandler disputeNotifyHandler;

    @Override
    @Monitor(name = "拒付通知listener", level = Monitor.Level.P1, layer = Monitor.Layer.CONSUMER)
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        MessageExt messageExt = CollectionUtil.getFirst(msgs);
        if (messageExt == null) {
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        String msgId = messageExt.getMsgId();

        JSONObject notifyContentJsonObject = MetaqUtils.getJSONObjOfMessageExtBody(messageExt);
        String notifyContentStr = notifyContentJsonObject == null ? null : notifyContentJsonObject.toJSONString();
        // 交易流水id
        String tradeNo = null;

        try {
            EagleEye.startTrace(null, this.getClass().getName(), EagleEye.TYPE_CUSTOM_MESSAGE_SUB);
            TASK_LOG.info("paymentDisputeNotifyListener notifyContent:::{}", notifyContentStr);

            if (notifyContentJsonObject == null) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

            // 仅接收"发生争议"的消息，"争议已被判定"、"争议被用户取消"、"已提交争议答辩文件"等消息不处理
            DisputeNotifyDTO disputeNotifyDTO = notifyContentJsonObject.toJavaObject(DisputeNotifyDTO.class);
            if (!Objects.equals(disputeNotifyDTO.getDisputeNotificationType(), AEPaymentConstants.DISPUTE_PAYMENT_RESULT_NOTIFY_TYPE)) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

            tradeNo = disputeNotifyDTO.getPaymentRequestId();

            Assertor.asserts(StringUtils.isNotBlank(tradeNo), "paymentDisputeNotifyListener msg refundRequestId illegal");

            disputeNotifyHandler.handle(tradeNo, msgId, notifyContentStr, disputeNotifyDTO);

            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (BizException bizException) {
            TASK_LOG.error("paymentDisputeNotifyListener bizError,msg:{},notifyContent:{}",
                    bizException.getMessage(), notifyContentStr, bizException);
            disputeNotifyHandler.handleFail(tradeNo, msgId, notifyContentStr);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            TASK_LOG.error("paymentDisputeNotifyListener error,msg:{},notifyContent:{}",
                    e.getMessage(), notifyContentStr, e);

            // Metaq消费自动重试
            if (MetaqUtils.isResumeAutoRetry(messageExt, context, notifyContentStr)) {
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
            disputeNotifyHandler.handleFail(tradeNo, msgId, notifyContentStr);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } finally {
            EagleEye.endTrace(null);
        }
    }
}