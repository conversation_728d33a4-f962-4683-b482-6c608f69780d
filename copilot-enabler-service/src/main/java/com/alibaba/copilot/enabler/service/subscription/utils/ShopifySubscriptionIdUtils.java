package com.alibaba.copilot.enabler.service.subscription.utils;

import com.alibaba.copilot.enabler.service.subscription.constant.ShopifySubscriptionConstant;

/**
 * <AUTHOR>
 * @version 2023/10/31
 */
public class ShopifySubscriptionIdUtils {

    /**
     * 通过graphQLApiId获取shopifySubscriptionId
     */
    public static Long getShopifySubscriptionId(String graphQLApiId) {
        String idStr = graphQLApiId.replace(ShopifySubscriptionConstant.SHOPIFY_SUBSCRIPTION_ID_PREFIX, "");
        return Long.valueOf(idStr);
    }

    /**
     * 通过shopifySubscriptionId获取graphQLApiId
     */
    public static String getGraphQLApiId(Long shopifySubscriptionId) {
        return ShopifySubscriptionConstant.SHOPIFY_SUBSCRIPTION_ID_PREFIX + shopifySubscriptionId;
    }
}
