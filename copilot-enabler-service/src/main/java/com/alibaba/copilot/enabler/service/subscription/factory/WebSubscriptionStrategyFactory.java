package com.alibaba.copilot.enabler.service.subscription.factory;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 订阅策略工厂
 *
 * <AUTHOR>
 * @date 2024/9/27 上午10:54
 */
@Slf4j
public class WebSubscriptionStrategyFactory {

    private static final Map<SubscriptionSource, WebSubscriptionStrategy> MAP = new ConcurrentHashMap<>();


    public static void register(SubscriptionSource source, WebSubscriptionStrategy strategy) {
        if (Objects.nonNull(source) && Objects.nonNull(strategy)) {
            MAP.put(source, strategy);
        }
    }

    public static WebSubscriptionStrategy getStrategy(WebSubscriptionContext context) {
        SubscriptionSource source = SubscriptionSource.fromName(context.getSubscribePlanDTO().getSubscriptionSource());
        return getStrategy(source);
    }

    public static WebSubscriptionStrategy getStrategy(SubscriptionSource subscriptionSource) {
        return MapUtils.getObject(MAP, subscriptionSource);
    }
}