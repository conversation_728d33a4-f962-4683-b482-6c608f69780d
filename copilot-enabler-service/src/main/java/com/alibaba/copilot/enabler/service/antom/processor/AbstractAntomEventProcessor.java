package com.alibaba.copilot.enabler.service.antom.processor;

import com.alibaba.copilot.enabler.client.payment.constant.PaymentStatusEnum;
import com.alibaba.copilot.enabler.client.payment.dto.PaymentResultDTO;
import com.alibaba.copilot.enabler.domain.payment.service.PaymentDomainService;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * Antom 事件处理器抽象类
 *
 * <AUTHOR>
 * @date 2025/05/01
 */
@Slf4j
public abstract class AbstractAntomEventProcessor implements AntomEventProcessor {

    @Resource
    protected PaymentDomainService paymentDomainService;

    @Resource
    protected SubscriptionOrderRepository subscriptionOrderRepository;

    @PostConstruct
    public void init() {
        AntomEventProcessorFactory.register(this);
    }

    @Override
    public void process(AntomExecuteContext context) {
        try {
            log.info("{} start, context={}", this.getClass().getSimpleName(), JSON.toJSONString(context));
            doProcess(context);
        } catch (Exception e) {
            log.error("{} error, context={}", this.getClass().getSimpleName(), JSON.toJSONString(context), e);
        }
    }

    /**
     * 处理事件
     *
     * @param context 执行上下文
     * @throws Exception 处理异常
     */
    protected abstract void doProcess(AntomExecuteContext context) throws Exception;

    /**
     * 处理支付成功
     *
     * @param tradeNo 交易号
     * @param outTradeNo 外部交易号
     */
    protected void processSuccess(String tradeNo, String outTradeNo) {
        log.info("Process payment success: tradeNo={}, outTradeNo={}", tradeNo, outTradeNo);

        PaymentResultDTO paymentResultDTO = new PaymentResultDTO();
    }

    /**
     * 处理支付失败
     *
     * @param tradeNo 交易号
     * @param outTradeNo 外部交易号
     */
    protected void processFailed(String tradeNo, String outTradeNo) {
        log.info("Process payment failed: tradeNo={}, outTradeNo={}", tradeNo, outTradeNo);

    }
}
