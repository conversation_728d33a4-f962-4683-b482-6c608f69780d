package com.alibaba.copilot.enabler.service.antom.dto;

import com.alibaba.copilot.enabler.domain.payment.constant.StripeCheckoutSessionMode;
import lombok.Data;

/**
 * Antom 事件元数据
 *
 * <AUTHOR>
 * @date 2025/05/01
 */
@Data
public class AntomEventMetadata {
    
    /**
     * 会话模式
     */
    private String sessionMode;
    
    /**
     * 应用代码
     */
    private String appCode;
    
    /**
     * 交易号
     */
    private String tradeNo;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 订阅订单ID
     */
    private String subscriptionOrderId;
    
    /**
     * 支付请求ID
     */
    private String paymentRequestId;
    
    /**
     * 支付ID
     */
    private String paymentId;
    
    /**
     * 是否为支付模式
     *
     * @return 是否为支付模式
     */
    public boolean whetherPaymentMode() {
        return StripeCheckoutSessionMode.PAYMENT.name().equals(sessionMode);
    }
    
    /**
     * 是否为设置模式
     *
     * @return 是否为设置模式
     */
    public boolean whetherSetupMode() {
        return StripeCheckoutSessionMode.SETUP.name().equals(sessionMode);
    }
    
    /**
     * 是否为订阅模式
     *
     * @return 是否为订阅模式
     */
    public boolean whetherSubscriptionMode() {
        return StripeCheckoutSessionMode.SUBSCRIPTION.name().equals(sessionMode);
    }
}
