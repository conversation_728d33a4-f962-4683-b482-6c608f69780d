package com.alibaba.copilot.enabler.service.subscription.log.unifybizlog;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2023/11/24
 */
@Data
@Accessors(chain = true)
public class SubscriptionEventBizLog implements Serializable {

    /**
     * 之前套餐的名称
     */
    private String oldPlanName;

    /**
     * 之后套餐的名称
     */
    private String newPlanName;
    /**
     * 业务code
     * SEO_COPILOT
     */
    private String appCode;
    /**
     * YEAR/MONTH
     */
    private String durationUnit;
    /**
     * 新套餐付款金额
     */
    private BigDecimal newPlanPrice;

}
