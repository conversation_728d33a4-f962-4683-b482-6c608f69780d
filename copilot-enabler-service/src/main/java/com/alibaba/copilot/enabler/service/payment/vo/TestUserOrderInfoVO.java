package com.alibaba.copilot.enabler.service.payment.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户订单信息test
 */
@Data
@Accessors(chain = true)
public class TestUserOrderInfoVO {

    /**
     * 序号
     */
    private Integer id;

    private Long orderId;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 产品唯一code
     */
    private String appCode;

    /**
     * 计划ID
     */
    private Long subscriptionPlanId;

    /**
     * 计划名称
     */
    private String subscriptionPlanName;

    /**
     * 下次执行的计划ID
     */
    private Long nextPlanId;

    /**
     * 下次执行的计划名称
     */
    private String nextPlanName;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 订单状态描述
     */
    private String statusDesc;

    /**
     * 折扣tag
     */
    private String subscriptionDiscountTag;

    /**
     * 是否续定（否取消订阅）
     */
    private String autoRenew;

    /**
     * 下次自动续费的时间
     */
    private String nextRenewalTime;

    /**
     * 是否包含试用期
     */
    private String isIncludeTrial;

    /**
     * 计划价格（计划的原价）
     */
    private String planOriginPrice;

    /**
     * 履约开始时间
     */
    private String performStartTime;

    /**
     * 结束时间
     */
    private String performEndTime;

    /**
     * 支付流水列表
     */
    private List<TestTradeRecordInfoVO> tradeRecords = new ArrayList<>();

    @Data
    @Accessors(chain = true)
    public static class TestTradeRecordInfoVO {

        /**
         * 唯一流水号
         */
        private String tradeNo;

        /**
         * 外部流水号
         */
        private String outTradeNo;

        /**
         * 创建时间
         */
        private String gmtCreate;

        /**
         * 支付类型（用户主动付款、系统系统定期代扣）
         */
        private String paymentType;

        /**
         * 交易金额
         */
        private String tradeAmount;

        /**
         * 币种
         */
        private String tradeCurrency;

        /**
         * 交易方向
         */
        private String tradeDirection;

        /**
         * 交易时间
         */
        private String tradeTime;

        /**
         * 状态
         */
        private String status;

        /**
         * 是否已发起支付
         * （指是否已请求第三方支付平台）
         */
        private String hadInitiatePay;
    }
}
