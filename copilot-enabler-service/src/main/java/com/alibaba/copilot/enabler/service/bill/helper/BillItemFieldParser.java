package com.alibaba.copilot.enabler.service.bill.helper;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.copilot.enabler.client.bill.annotation.BillItemField;
import com.alibaba.copilot.enabler.client.bill.dto.AlphaRankBillItemDTO;
import com.alibaba.copilot.enabler.client.bill.dto.BillItemDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2024/3/26
 */
public class BillItemFieldParser {

    private static final List<FieldItem> FIELD_ITEMS = new ArrayList<>();
    private static final List<FieldItem> ALPHA_RANK_FIELD_ITEMS = new ArrayList<>();


    static {
        Arrays.stream(BillItemDTO.class.getDeclaredFields()).forEach(field -> {
            String fieldName = field.getName();
            final String filedAlias;
            if (field.isAnnotationPresent(BillItemField.class)) {
                filedAlias = field.getAnnotation(BillItemField.class).value();
            } else {
                filedAlias = fieldName;
            }
            FieldItem fieldItem = new FieldItem(fieldName, filedAlias);
            FIELD_ITEMS.add(fieldItem);
        });

        Arrays.stream(ReflectUtil.getFields(AlphaRankBillItemDTO.class)).forEach(field -> {
            String fieldName = field.getName();
            final String filedAlias;
            if (field.isAnnotationPresent(BillItemField.class)) {
                filedAlias = field.getAnnotation(BillItemField.class).value();
            } else {
                filedAlias = fieldName;
            }
            FieldItem fieldItem = new FieldItem(fieldName, filedAlias);
            ALPHA_RANK_FIELD_ITEMS.add(fieldItem);
        });
    }

    public static List<FieldItem> getFieldItems() {
        return Collections.unmodifiableList(FIELD_ITEMS);
    }

    public static List<FieldItem> getAlphaRankFieldItems() {
        return Collections.unmodifiableList(ALPHA_RANK_FIELD_ITEMS);
    }

    public static Object getValue(BillItemDTO dto, String filedName) {
        try {
            Field field = ReflectUtil.getField(dto.getClass(), filedName) ;
            field.setAccessible(true);
            return field.get(dto);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    @Getter
    @AllArgsConstructor
    public static class FieldItem implements Serializable {
        private String fieldName;
        private String fieldAlias;
    }
}
