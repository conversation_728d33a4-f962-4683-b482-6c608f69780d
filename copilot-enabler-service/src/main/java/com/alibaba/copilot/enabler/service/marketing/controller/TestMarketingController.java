package com.alibaba.copilot.enabler.service.marketing.controller;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.marketing.constant.*;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.marketing.model.DiscountRule;
import com.alibaba.copilot.enabler.domain.marketing.model.DiscountRuleAttributes;
import com.alibaba.copilot.enabler.domain.marketing.repository.DiscountRecordRepository;
import com.alibaba.copilot.enabler.domain.marketing.repository.DiscountRuleRepository;
import com.alibaba.copilot.enabler.infra.base.utils.EnvUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2024/2/28
 */
@Api(tags = "营销测试接口")
@Slf4j
@RestController
@RequestMapping("/api/test/marketing")
public class TestMarketingController {

    @Resource
    private DiscountRuleRepository discountRuleRepository;

    @Resource
    private DiscountRecordRepository discountRecordRepository;

    @ApiOperation("为Pic创建首月减免的折扣规则")
    @GetMapping("/createFirstMonthDiscountForPic")
    public SingleResult<DiscountRule> createFirstMonthDiscountForPic(
            @ApiParam(value = "套餐ID") @RequestParam Long planId,
            @ApiParam(value = "语言") @RequestParam Language language,
            @ApiParam(value = "折扣值") @RequestParam BigDecimal discountValue
    ) {
        EnvUtils.interceptForOnline();

        DiscountRuleAttributes attributes = new DiscountRuleAttributes()
                .setLanguage(language)
                .setDiscountWay(DiscountWay.FIXED)
                .setDiscountValue(discountValue);

        DiscountRule discountRule = new DiscountRule()
                .setAppCode(AppEnum.PIC_COPILOT)
                .setStatus(DiscountRuleStatus.ENABLED)
                .setDiscountType(DiscountType.FIRST_MONTH)
                .setEntityType(DiscountRuleEntityType.PLAN)
                .setEntityId(planId)
                .setAttributes(attributes);

        DiscountRule result = discountRuleRepository.create(discountRule);
        return SingleResult.buildSuccess(result);
    }

    @ApiOperation("更改折扣规则（简化2）")
    @GetMapping("/updateDiscountRuleSimple")
    public SingleResult<DiscountRule> updateDiscountRuleSimple(
            @ApiParam(value = "折扣规则ID") @RequestParam Long id,
            @ApiParam(value = "是否启用") @RequestParam(required = false) Boolean enable,
            @ApiParam(value = "折扣值") @RequestParam(required = false) BigDecimal discountValue
    ) {
        EnvUtils.interceptForOnline();

        DiscountRule discountRule = discountRuleRepository.queryById(id);
        Assertor.assertNonNull(discountRule, "该折扣规则不存在");

        if (enable != null) {
            discountRule.setStatus(enable ? DiscountRuleStatus.ENABLED : DiscountRuleStatus.DISABLED);
        }
        if (discountValue != null) {
            discountRule.getAttributes().setDiscountValue(discountValue);
        }
        DiscountRule result = discountRuleRepository.update(discountRule);
        return SingleResult.buildSuccess(result);
    }

    @ApiOperation("更改折扣规则（通用）")
    @GetMapping("/updateDiscountRule")
    public SingleResult<DiscountRule> updateDiscountRule(
            @ApiParam(value = "折扣规则ID") @RequestParam DiscountRule rule
    ) {
        EnvUtils.interceptForOnline();

        Long id = rule.getId();
        Assertor.assertNonNull(id, "折扣规则ID不能为空");

        DiscountRule discountRule = discountRuleRepository.queryById(id);
        Assertor.assertNonNull(discountRule, "该折扣规则不存在");

        DiscountRule result = discountRuleRepository.update(rule);
        return SingleResult.buildSuccess(result);
    }

    @ApiOperation("删除折扣记录")
    @GetMapping("/deleteDiscountRecord")
    public SingleResult<String> deleteDiscountRecord(
            @ApiParam(value = "折扣记录ID") @RequestParam Long id
    ) {
        EnvUtils.interceptForOnline();

        discountRecordRepository.deleteById(id);
        return SingleResult.buildSuccess("删除成功");
    }
}
