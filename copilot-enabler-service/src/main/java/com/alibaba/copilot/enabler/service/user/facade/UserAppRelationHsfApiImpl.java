package com.alibaba.copilot.enabler.service.user.facade;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.user.dto.UserAppRelationDTO;
import com.alibaba.copilot.enabler.client.user.facade.UserAppRelationHsfApi;
import com.alibaba.copilot.enabler.client.user.request.AppBindingRequest;
import com.alibaba.copilot.enabler.client.user.request.UserAppRelationQuery;
import com.alibaba.copilot.enabler.client.user.service.UserAppRelationService;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/11
 */
@Service
@Slf4j
@HSFProvider(serviceInterface = UserAppRelationHsfApi.class)
public class UserAppRelationHsfApiImpl implements UserAppRelationHsfApi {
    @Autowired
    private UserAppRelationService userAppRelationService;

    @Override
    public SingleResult<UserAppRelationDTO> queryUserAppRelation(UserAppRelationQuery userAppRelationQuery) {
        log.info("UserAppRelationHsfApiImpl.queryUserAppRelation, userAppRelationQuery={}", JSON.toJSONString(userAppRelationQuery));
        UserAppRelationDTO userAppRelationDTO = userAppRelationService.queryUserAppRelation(userAppRelationQuery);
        log.info("UserAppRelationHsfApiImpl.queryUserAppRelation, userAppRelationDTO={}", JSON.toJSONString(userAppRelationDTO));
        return SingleResult.buildSuccess(userAppRelationDTO);
    }

    @Override
    public SingleResult<List<UserAppRelationDTO>> queryUserAppRelationList(UserAppRelationQuery userAppRelationQuery) {
        List<UserAppRelationDTO> appRelationList = userAppRelationService.queryUserAppRelationList(userAppRelationQuery);
        return SingleResult.buildSuccess(appRelationList);
    }

    @Override
    public SingleResult<UserAppRelationDTO> createUserAppRelation(Long userId, AppBindingRequest appBindingRequest) {
        UserAppRelationDTO createUserAppRelationResult = null;
        try {
            createUserAppRelationResult = userAppRelationService.createUserAppRelation(userId, appBindingRequest);
        } catch (Exception e) {
            return SingleResult.buildFailure(e.getMessage());
        }
        return SingleResult.buildSuccess(createUserAppRelationResult);
    }

    /**
     * updateUserAppRelationById
     *
     * @param id
     * @param userAppRelationDTO
     * @return
     */
    @Override
    public SingleResult<Boolean> updateUserAppRelation(Long id, UserAppRelationDTO userAppRelationDTO) {
        return SingleResult.buildSuccess(userAppRelationService.updateUserAppRelation(id, userAppRelationDTO));
    }
}
