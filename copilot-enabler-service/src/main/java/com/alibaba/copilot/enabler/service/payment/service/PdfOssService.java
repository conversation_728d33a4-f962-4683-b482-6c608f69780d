package com.alibaba.copilot.enabler.service.payment.service;

import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.service.base.constant.FileExtEnum;
import com.alibaba.copilot.enabler.service.base.service.OssAbstractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.time.Duration;

@Slf4j
@Component("pdfOssService")
public class PdfOssService extends OssAbstractService {

    /**
     * PDF的默认保存时长
     */
    private final Duration DEFAULT_SAVED_DURATION_7_DAY = Duration.ofDays(7);

    /**
     * 用户id进行md5
     */
    private final String USERID_MD5_SALT_VALUE = "0326_d63e39ef97711e8b_AIB";

    @Monitor(name = "[saveFile] 将PDF文件上传到OSS上", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    public String saveFile(AppEnum appEnum, Long userId, String fileName, InputStream inputStream) {
        if (inputStream == null) {
            return null;
        }

        String fileUri = getFileUri(appEnum, userId);

        return super.putObject(fileUri, fileName, inputStream, FileExtEnum.PDF, DEFAULT_SAVED_DURATION_7_DAY);
    }

    @Monitor(name = "[getFileUrl] 获取pdf文件Url", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    public String getFileUrl(AppEnum appEnum, Long userId, String fileName) {
        String fileUri = getFileUri(appEnum, userId);

        boolean isExist = super.isFileExist(fileUri, fileName, FileExtEnum.PDF);
        if (!isExist) {
            return null;
        }

        return super.getFileUrl(fileUri, fileName, FileExtEnum.PDF);
    }

    private String getFileUri(AppEnum appEnum, Long userId) {
        Assertor.asserts(appEnum != null && userId != null, "appEnum or userId illegal");
        return appEnum.name() + "/" + DigestUtil.md5Hex(userId + USERID_MD5_SALT_VALUE) + "/";
    }
}