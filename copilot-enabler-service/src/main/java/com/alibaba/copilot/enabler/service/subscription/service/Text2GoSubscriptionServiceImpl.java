package com.alibaba.copilot.enabler.service.subscription.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.Calendar;
import java.util.Arrays;

import javax.annotation.Resource;

import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.dto.DscSubscriptionUsageInfoDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyFeatureAllDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyFeatureQuery;
import com.alibaba.copilot.enabler.client.subscription.dto.TrialDurationDTO;
import com.alibaba.copilot.enabler.client.subscription.service.ShopifySubscriptionService;
import com.alibaba.copilot.enabler.client.subscription.service.Text2GoSubscriptionService;
import com.alibaba.copilot.enabler.domain.base.utils.TimeUtils;
import com.alibaba.copilot.enabler.domain.subscription.model.FeatureUsage;
import com.alibaba.copilot.enabler.domain.subscription.model.FeatureUsageAttributes;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionFeature;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionOrderQuery;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.FeatureUsageDO;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionFeatureDO;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionPlanFeatureDO;
import com.alibaba.copilot.enabler.infra.subscription.factory.FeatureUsageConverter;
import com.alibaba.copilot.enabler.infra.subscription.mapper.FeatureUsageMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionFeatureMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionPlanFeatureMapper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;

/**
 * @ClassName Text2GoSubscriptionServiceImpl
 * <AUTHOR>
 * @Date 2025/2/21 14:25
 */
@Slf4j
@Service
public class Text2GoSubscriptionServiceImpl implements Text2GoSubscriptionService {

    private static final String TEXT2GO_APP_CODE = "TEXT2GO";
    @Resource
    private SubscriptionOrderRepository subscriptionOrderRepository;

    @Resource
    private SubscriptionPlanFeatureMapper subscriptionPlanFeatureMapper;

    @Resource
    private SubscriptionFeatureMapper subscriptionFeatureMapper;

    @Resource
    private FeatureUsageMapper featureUsageMapper;

    @Resource
    private ShopifySubscriptionService shopifySubscriptionService;

    @Resource
    private SubscriptionPlanRepository subscriptionPlanRepository;

    @Resource
    private SubscriptionOrderRepository orderRepository;

    @Override
    public DscSubscriptionUsageInfoDTO currentSubscriptionInfo(Long userId) {
        log.info("Text2Go Service currentSubscriptionInfo start, userId={}", userId);
        if (userId == null) {
            log.warn("Text2Go Service currentSubscriptionInfo invalid userId");
            return null;
        }
        try {
            // 查询当前生效的订单
            SubscriptionOrder effectOrder = getEffectOrder(TEXT2GO_APP_CODE, userId);
            log.info("Text2Go Service currentSubscriptionInfo getEffectOrder result={}", JSON.toJSONString(effectOrder));

            // 查询当前生效的套餐
            SubscriptionPlan effectPlan = Optional.ofNullable(effectOrder)
                .map(SubscriptionOrder::getSubscriptionPlanId)
                .map(planId -> subscriptionPlanRepository.queryByPlanId(planId, false))
                // 查不到当前生效的订单时, 默认取免费套餐使用
                .orElseGet(() -> subscriptionPlanRepository.queryFreePlan(TEXT2GO_APP_CODE, false));
            log.info("Text2Go Service currentSubscriptionInfo query plan result={}", JSON.toJSONString(effectPlan));

            QueryWrapper<SubscriptionPlanFeatureDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("subscription_plan_id", effectPlan.getId());
            List<SubscriptionPlanFeatureDO> subscriptionPlanFeatureDOS = subscriptionPlanFeatureMapper.selectList(queryWrapper);
            log.info("Text2Go Service currentSubscriptionInfo query plan features result={}", JSON.toJSONString(subscriptionPlanFeatureDOS));

            DscSubscriptionUsageInfoDTO infoDTO = new DscSubscriptionUsageInfoDTO();
            infoDTO.setUserId(userId);
            infoDTO.setAppCode(TEXT2GO_APP_CODE);
            infoDTO.setSubscriptionPlanId(effectPlan.getId());
            infoDTO.setSubscriptionPlanName(effectPlan.getName());
            if (CollectionUtils.isEmpty(subscriptionPlanFeatureDOS)) {
                return infoDTO;
            }

            // 查询特性
            List<Long> featureIds = subscriptionPlanFeatureDOS.stream().map(
                SubscriptionPlanFeatureDO::getSubscriptionFeatureId).collect(
                Collectors.toList());
            log.info("Text2Go Service featureIds, result={}", JSON.toJSONString(featureIds));

            QueryWrapper<SubscriptionFeatureDO> featureDOQueryWrapper = new QueryWrapper<>();
            featureDOQueryWrapper.in("id", featureIds);
            featureDOQueryWrapper.eq("deleted", Boolean.FALSE);
            List<SubscriptionFeatureDO> subscriptionFeatureDOS = subscriptionFeatureMapper.selectList(
                featureDOQueryWrapper);
            log.info("Text2Go Service subscriptionFeatureDOS, result={}", JSON.toJSONString(subscriptionFeatureDOS));

            QueryWrapper<FeatureUsageDO> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.eq("app_code", TEXT2GO_APP_CODE);
            queryWrapper1.eq("user_id", userId);
            queryWrapper1.in("feature_type",
                subscriptionFeatureDOS.stream().map(SubscriptionFeatureDO::getType).collect(Collectors.toList()));
            List<FeatureUsageDO> featureUsageDOS = featureUsageMapper.selectList(queryWrapper1);
            log.info("Text2Go Service featureUsageDOS, result={}", JSON.toJSONString(featureUsageDOS));

            List<DscSubscriptionUsageInfoDTO.SubscriptionFeatureUsageInfo> featureInfoList = new ArrayList<>();
            for (SubscriptionFeatureDO subscriptionFeatureDO : subscriptionFeatureDOS) {
                DscSubscriptionUsageInfoDTO.SubscriptionFeatureUsageInfo info
                    = new DscSubscriptionUsageInfoDTO.SubscriptionFeatureUsageInfo();
                info.setFeatureId(subscriptionFeatureDO.getId());
                info.setAppCode(TEXT2GO_APP_CODE);
                info.setName(subscriptionFeatureDO.getName());
                info.setDescription(subscriptionFeatureDO.getDescription());
                info.setType(subscriptionFeatureDO.getType());
                info.setIsDepletion(subscriptionFeatureDO.getIsDepletion());

                String benefit = subscriptionFeatureDO.getBenefit();
                if (StringUtils.isNotBlank(benefit) && !"null".equals(benefit)) {
                    JSONObject jsonObject = JSONObject.parseObject(benefit);
                    if (jsonObject != null) {
                        JSONObject attributes = jsonObject.getJSONObject("attributes");
                        if (attributes != null && attributes.containsKey("quota")) {
                            info.setQuota(attributes.getLongValue("quota"));
                        }
                    }
                }

                Optional<FeatureUsageDO> usageDO = featureUsageDOS.stream().filter(
                    item -> item.getFeatureType().equals(subscriptionFeatureDO.getType())).findFirst();
                usageDO.ifPresent(usage -> {
                    info.setUsageCount(usage.getUsageCount());
                });
                if (info.getUsageCount() == null) {
                    info.setUsageCount(0L);
                }
                featureInfoList.add(info);

                log.info("Text2Go Service SubscriptionFeatureUsageInfo, result={}", JSON.toJSONString(info));
            }

            infoDTO.setFeatureInfoList(featureInfoList);
            log.info("Text2Go Service currentSubscriptionInfo complete, result={}", JSON.toJSONString(infoDTO));
            return infoDTO;
        } catch (Exception e) {
            log.error("Text2Go Service currentSubscriptionInfo error, userId={}, error={}", userId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 查询当前生效的订单
     *
     * @param appCode
     * @param userId
     * @return
     */
    private SubscriptionOrder getEffectOrder(String appCode, Long userId) {
        SubscriptionOrderQuery orderQuery = SubscriptionOrderQuery.builder()
            .status(Lists.newArrayList(SubscriptionOrderStatus.IN_EFFECT))
            .appCode(appCode)
            .userId(userId)
            .build();
        List<SubscriptionOrder> orders = subscriptionOrderRepository.querySubscriptionOrders(orderQuery);
        if (CollectionUtil.size(orders) > 1) {
            log.warn("ShopifySubscriptionServiceImpl#getEffectOrder, more than one effect order found, orders={}",
                JSON.toJSONString(orders));
        }
        return CollectionUtil.getFirst(orders);
    }

    @Override
    public void updateSubscriptionFeature(Long userId, String featureType, String planName, Boolean isNew,
        Long usageCount) {
        updateSubscriptionFeature(userId, featureType, planName, isNew, usageCount, null);
    }

    /**
     * Update subscription feature with optional custom expiry time
     *
     * @param userId user ID
     * @param featureType feature type
     * @param planName plan name
     * @param isNew whether this is a new feature record
     * @param usageCount usage count
     * @param yearsValid number of years until expiration (optional)
     */
    @Override
    public void updateSubscriptionFeature(Long userId, String featureType, String planName, Boolean isNew, Long usageCount, Integer yearsValid) {
        log.info("Text2Go Service updateSubscriptionFeature start, userId={}, featureType={}, planName={}, isNew={}, usageCount={}, yearsValid={}", 
            userId, featureType, planName, isNew, usageCount, yearsValid);
            
        ShopifyFeatureQuery shopifyFeatureQuery = new ShopifyFeatureQuery();
        shopifyFeatureQuery.setFeatureType(featureType);
        shopifyFeatureQuery.setAppCode(TEXT2GO_APP_CODE);
        shopifyFeatureQuery.setId(userId);

        try {
            if (shopifyFeatureQuery == null || StringUtils.isBlank(featureType)
                || StringUtils.isBlank(shopifyFeatureQuery.getAppCode())
                || shopifyFeatureQuery.getId() == null) {
                log.warn("Text2Go Service updateSubscriptionFeature invalid params");
                return;
            }

            List<SubscriptionFeature> effectPlanFeatures = getEffectiveFeatures(shopifyFeatureQuery);
            log.info("Text2Go Service updateSubscriptionFeature get effective features result={}", 
                JSON.toJSONString(effectPlanFeatures));

            if (CollectionUtil.isEmpty(effectPlanFeatures)) {
                log.warn("Text2Go Service updateSubscriptionFeature no effective features found");
                return;
            }

            for (SubscriptionFeature feature : effectPlanFeatures) {
                if (org.apache.commons.lang.StringUtils.equals(feature.getType(), shopifyFeatureQuery.getFeatureType())) {
                    if (feature.getIsDepletion()) {
                        FeatureUsage featureUsage = null;
                        if (isNew) {
                            featureUsage = buildFeatureUsage(feature, shopifyFeatureQuery, planName, yearsValid);
                            featureUsage.setUsageCount(usageCount);
                            subscriptionPlanRepository.saveFeatureUsage(featureUsage);
                            return;
                        }

                        //消耗型选择性
                        List<FeatureUsage> list = queryAllFeatureUsage(shopifyFeatureQuery);
                        if (CollectionUtils.isEmpty(list)) {
                            featureUsage = buildFeatureUsage(feature, shopifyFeatureQuery, planName, yearsValid);
                            featureUsage.setUsageCount(usageCount);
                            subscriptionPlanRepository.saveFeatureUsage(featureUsage);
                            return;
                        } else {
                            FeatureUsage first = list.stream().filter(item -> {
                                    return item.getAttributes() != null
                                        && StringUtils.isNotBlank(item.getAttributes().getPlanName())
                                        && item.getAttributes().getPlanName().equals(planName);
                                }).findFirst()
                                .orElse(null);
                            if (first == null) {
                                // 在有效期内无用量记录
                                featureUsage = buildFeatureUsage(feature, shopifyFeatureQuery, planName, yearsValid);
                                featureUsage.setUsageCount(usageCount);
                                subscriptionPlanRepository.saveFeatureUsage(featureUsage);
                                return;
                            } else {
                                // 在有效期内有用量记录 + usageCount
                                first.setUsageCount(first.getUsageCount() + usageCount);
                                
                                // Update expiry time if yearsValid is provided
                                if (yearsValid != null) {
                                    Calendar calendar = Calendar.getInstance();
                                    calendar.add(Calendar.YEAR, yearsValid);
                                    first.setEndTime(calendar.getTime());
                                }
                                
                                subscriptionPlanRepository.saveFeatureUsage(first);
                                return;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Text2Go Service updateSubscriptionFeature error, error={}", e.getMessage(), e);
        }
    }

    /**
     * 创建带初始配额的订阅特性使用记录
     *
     * @param userId 用户ID
     * @param featureType 特性类型
     * @param planName 计划名称
     * @param isNew 是否为新记录
     * @param usageCount 使用量
     * @param initialQuota 初始配额
     */
    @Override
    public void createSubscriptionFeatureWithQuota(Long userId, String featureType, String planName, Boolean isNew, Long usageCount, Long initialQuota) {
        log.info("Text2Go Service createSubscriptionFeatureWithQuota start, userId={}, featureType={}, planName={}, isNew={}, usageCount={}, initialQuota={}", 
            userId, featureType, planName, isNew, usageCount, initialQuota);
            
        ShopifyFeatureQuery shopifyFeatureQuery = new ShopifyFeatureQuery();
        shopifyFeatureQuery.setFeatureType(featureType);
        shopifyFeatureQuery.setAppCode(TEXT2GO_APP_CODE);
        shopifyFeatureQuery.setId(userId);

        try {
            if (shopifyFeatureQuery == null || StringUtils.isBlank(featureType)
                || StringUtils.isBlank(shopifyFeatureQuery.getAppCode())
                || shopifyFeatureQuery.getId() == null) {
                log.warn("Text2Go Service createSubscriptionFeatureWithQuota invalid params");
                return;
            }

            List<SubscriptionFeature> effectPlanFeatures = getEffectiveFeatures(shopifyFeatureQuery);
            log.info("Text2Go Service createSubscriptionFeatureWithQuota get effective features result={}", 
                JSON.toJSONString(effectPlanFeatures));

            if (CollectionUtil.isEmpty(effectPlanFeatures)) {
                log.warn("Text2Go Service createSubscriptionFeatureWithQuota no effective features found");
                return;
            }

            for (SubscriptionFeature feature : effectPlanFeatures) {
                if (org.apache.commons.lang.StringUtils.equals(feature.getType(), shopifyFeatureQuery.getFeatureType())) {
                    if (feature.getIsDepletion()) {
                        FeatureUsage featureUsage = null;
                        
                        // 新建使用记录
                        featureUsage = buildFeatureUsage(feature, shopifyFeatureQuery, planName);
                        featureUsage.setUsageCount(usageCount);
                        
                        // 设置初始配额
                        if (initialQuota != null && initialQuota > 0) {
                            featureUsage.setQuota(initialQuota);
                            log.info("Text2Go Service createSubscriptionFeatureWithQuota setting initial quota: {}", initialQuota);
                        }
                        
                        // 保存记录
                        subscriptionPlanRepository.saveFeatureUsage(featureUsage);
                        log.info("Text2Go Service createSubscriptionFeatureWithQuota saved new usage record with quota: {}", initialQuota);
                        return;
                    } else {
                        // 对于非消耗型特性，不需要设置配额
                        log.info("Text2Go Service createSubscriptionFeatureWithQuota feature is not depletion type, no need to set quota");
                        updateSubscriptionFeature(userId, featureType, planName, isNew, usageCount);
                        return;
                    }
                }
            }
            
            log.warn("Text2Go Service createSubscriptionFeatureWithQuota no matching feature found for type: {}", featureType);
        } catch (Exception e) {
            log.error("Text2Go Service createSubscriptionFeatureWithQuota error, error={}", e.getMessage(), e);
        }
    }

    private List<FeatureUsage> queryAllFeatureUsage(ShopifyFeatureQuery shopifyFeatureQuery) {
        log.info("Text2Go Service queryAllFeatureUsage start, query={}", JSON.toJSONString(shopifyFeatureQuery));
        List<FeatureUsage> list = new ArrayList<>();
        try {
            Assertor.asserts(StringUtils.isNotBlank(shopifyFeatureQuery.getAppCode()),
                "queryFeatureUsage:appCode can not null!");
            Assertor.asserts(shopifyFeatureQuery.getId() != null, "queryFeatureUsage:id can not null!");
            Assertor.asserts(StringUtils.isNotBlank(shopifyFeatureQuery.getFeatureType()),
                "queryFeatureUsage:featureType can not null!");

            LambdaQueryWrapper<FeatureUsageDO> shopifyFeatureQueryWrapper = new LambdaQueryWrapper<>();
            shopifyFeatureQueryWrapper.eq(FeatureUsageDO::getAppCode, shopifyFeatureQuery.getAppCode());
            shopifyFeatureQueryWrapper.eq(FeatureUsageDO::getUserId, shopifyFeatureQuery.getId());
            shopifyFeatureQueryWrapper.eq(FeatureUsageDO::getFeatureType, shopifyFeatureQuery.getFeatureType());

            List<FeatureUsageDO> featureUsageDOS = featureUsageMapper.selectList(shopifyFeatureQueryWrapper);
            log.info("Text2Go Service queryAllFeatureUsage query result={}", JSON.toJSONString(featureUsageDOS));

            for (FeatureUsageDO featureUsageDO : featureUsageDOS) {
                if (isWithinTimeRange(featureUsageDO.getStartTime(), featureUsageDO.getEndTime())) {
                    FeatureUsage featureUsage = FeatureUsageConverter.INSTANCE.convertA2B(featureUsageDO);
                    list.add(featureUsage);
                }
            }

            log.info("Text2Go Service queryAllFeatureUsage complete, result size={}", list.size());
            return list;
        } catch (Exception e) {
            log.error("Text2Go Service queryAllFeatureUsage error, query={}, error={}", 
                JSON.toJSONString(shopifyFeatureQuery), e.getMessage(), e);
            return list;
        }
    }

    /**
     * 判断当前时间是否在startTime和endTime之间
     *
     * @param startTime
     * @param endTime
     * @return
     */
    private boolean isWithinTimeRange(Date startTime, Date endTime) {
        Date currentTime = new Date();
        return currentTime.after(startTime) && currentTime.before(endTime);
    }

    /**
     * 获取当前用户在appcode下的所有有效features
     *
     * @param shopifyFeatureQuery
     * @return
     */
    private List<SubscriptionFeature> getEffectiveFeatures(ShopifyFeatureQuery shopifyFeatureQuery) {
        SubscriptionPlan plan = getSubscriptionPlan(shopifyFeatureQuery);
        if (plan == null) {
            return null;
        }
        return plan.getFeatures();
    }

    private SubscriptionPlan getSubscriptionPlan(ShopifyFeatureQuery shopifyFeatureQuery) {
        if (shopifyFeatureQuery == null
            || org.apache.commons.lang.StringUtils.isBlank(shopifyFeatureQuery.getAppCode())
            || shopifyFeatureQuery.getId() == null) {
            return null;
        }
        try {
            // 查询当前生效的订单
            SubscriptionOrder effectOrder = orderRepository.queryEffectOrder(shopifyFeatureQuery.getAppCode(),
                shopifyFeatureQuery.getId());
            log.info("orderRepository.queryEffectOrder, getEffectOrder, result={}", JSON.toJSONString(effectOrder));
            // 查询当前生效的套餐
            SubscriptionPlan effectPlan = Optional.ofNullable(effectOrder)
                .map(SubscriptionOrder::getSubscriptionPlanId)
                .map(planId ->
                    subscriptionPlanRepository.queryByPlanId(planId, true))
                // 查不到当前生效的订单时, 默认取免费套餐使用
                .orElseGet(() ->
                    subscriptionPlanRepository.queryFreePlan(shopifyFeatureQuery.getAppCode(), true));
            log.info("ShopifySubscriptionServiceImpl#getFeature, query plan finished, result={}",
                JSON.toJSONString(effectPlan));

            if (effectPlan == null || org.apache.commons.collections.CollectionUtils.isEmpty(
                effectPlan.getFeatures())) {
                return null;
            }
            return effectPlan;
        } catch (Exception e) {
            log.error("getEffectiveFeatures exp,shopifyFeatureQuery={}", shopifyFeatureQuery, e);
        }
        return null;
    }

    /**
     * Build feature usage record with custom expiry time
     *
     * @param feature subscription feature
     * @param shopifyFeatureQuery query parameters
     * @param planName plan name
     * @param yearsValid number of years until expiration (optional)
     * @return feature usage entity
     */
    private FeatureUsage buildFeatureUsage(SubscriptionFeature feature, ShopifyFeatureQuery shopifyFeatureQuery,
        String planName, Integer yearsValid) {
        Date now = new Date();
        FeatureUsageAttributes featureUsageAttributes = new FeatureUsageAttributes(null);
        featureUsageAttributes.setFeatureType(feature.getType());
        featureUsageAttributes.setPlanName(planName);

        // Calculate end time based on yearsValid if provided, otherwise use feature benefit duration
        Date endTime;
        if (yearsValid != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.YEAR, yearsValid);
            endTime = calendar.getTime();
        } else {
            endTime = TimeUtils.getEndTime(now, Long.parseLong(String.valueOf(feature.getBenefit().getDuration())),
                feature.getBenefit().getDurationUnit());
        }

        FeatureUsage featureUsage = FeatureUsage.builder()
            .gmtCreate(now)
            .gmtModified(now)
            .appCode(feature.getAppCode())
            .userId(shopifyFeatureQuery.getId())
            .featureType(feature.getType())
            .quota(feature.getBenefit().getAsLong("quota"))
            .usageCount(0L)
            .startTime(TimeUtils.getStartOfDay())
            .endTime(endTime)
            .deleted(false)
            .attributes(featureUsageAttributes)
            .build();
        return featureUsage;
    }

    /**
     * Original buildFeatureUsage method for backward compatibility
     */
    private FeatureUsage buildFeatureUsage(SubscriptionFeature feature, ShopifyFeatureQuery shopifyFeatureQuery,
        String planName) {
        return buildFeatureUsage(feature, shopifyFeatureQuery, planName, null);
    }

    @Override
    public void rollbackFeatureUsage(Long userId, Long featureId, String featureType, Long usageCount) {
        log.info("Text2Go Service rollbackFeatureUsage start, userId={}, featureId={}, featureType={}, usageCount={}", 
            userId, featureId, featureType, usageCount);
            
        try {
            ShopifyFeatureQuery shopifyFeatureQuery = new ShopifyFeatureQuery();
            shopifyFeatureQuery.setFeatureType(featureType);
            shopifyFeatureQuery.setAppCode(TEXT2GO_APP_CODE);
            shopifyFeatureQuery.setId(userId);
            
            Boolean result = rollbackFeatureUsage(shopifyFeatureQuery, usageCount);
            log.info("Text2Go Service rollbackFeatureUsage complete, result={}", result);
        } catch (Exception e) {
            log.error("Text2Go Service rollbackFeatureUsage error, userId={}, featureId={}, featureType={}, usageCount={}, error={}", 
                userId, featureId, featureType, usageCount, e.getMessage(), e);
        }
    }

    public Boolean rollbackFeatureUsage(ShopifyFeatureQuery shopifyFeatureQuery, Long usageCount) {
        if (shopifyFeatureQuery == null || org.apache.commons.lang.StringUtils.isBlank(
            shopifyFeatureQuery.getFeatureType())
            || org.apache.commons.lang.StringUtils.isBlank(shopifyFeatureQuery.getAppCode())
            || shopifyFeatureQuery.getId() == null) {
            return null;
        }
        List<SubscriptionFeature> effectPlanFeatures = getEffectiveFeatures(shopifyFeatureQuery);
        if (CollectionUtil.isEmpty(effectPlanFeatures)) {
            return null;
        }
        for (SubscriptionFeature feature : effectPlanFeatures) {
            if (org.apache.commons.lang.StringUtils.equals(feature.getType(), shopifyFeatureQuery.getFeatureType())) {
                if (feature.getIsDepletion()) {
                    //消耗型选择性
                    FeatureUsage featureUsage = subscriptionPlanRepository.queryFeatureUsage(shopifyFeatureQuery);
                    if (featureUsage != null && featureUsage.getUsageCount() > 0) {
                        //在有效期内有用量记录 +1
                        featureUsage.setUsageCount(featureUsage.getUsageCount() - usageCount);
                    }
                    subscriptionPlanRepository.saveFeatureUsage(featureUsage);
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public ShopifyFeatureAllDTO getFeatureAll(ShopifyFeatureQuery shopifyFeatureQuery) {
        log.info("Text2Go Service getFeatureAll start, query={}", JSON.toJSONString(shopifyFeatureQuery));

        if (!isValidQuery(shopifyFeatureQuery)) {
            log.warn("Text2Go Service getFeatureAll invalid params");
            return null;
        }

        try {
            SubscriptionPlan plan = getSubscriptionPlan(shopifyFeatureQuery);
            log.info("Text2Go Service getFeatureAll get subscription plan result={}", JSON.toJSONString(plan));

            if (plan == null || CollectionUtils.isEmpty(plan.getFeatures())) {
                log.warn("Text2Go Service getFeatureAll no plan or features found");
                return null;
            }

            return buildFeatureAllDTO(shopifyFeatureQuery, plan);
        } catch (Exception e) {
            log.error("Text2Go Service getFeatureAll error, query={}, error={}",
                JSON.toJSONString(shopifyFeatureQuery), e.getMessage(), e);
            return null;
        }
    }

    private boolean isValidQuery(ShopifyFeatureQuery query) {
        return query != null
            && StringUtils.isNotBlank(query.getFeatureType())
            && StringUtils.isNotBlank(query.getAppCode())
            && query.getId() != null;
    }

    private ShopifyFeatureAllDTO buildFeatureAllDTO(ShopifyFeatureQuery query, SubscriptionPlan plan) {
        ShopifyFeatureAllDTO dto = new ShopifyFeatureAllDTO();
        dto.setHasAuthorize(false);
        dto.setDepletion(false);

        for (SubscriptionFeature feature : plan.getFeatures()) {
            if (StringUtils.equals(feature.getType(), query.getFeatureType())) {
                dto.setHasAuthorize(true);
                dto.setAllOverQuota(feature.getBenefit().getAsLong("quota"));

                if (feature.getIsDepletion()) {
                    handleDepletionFeature(dto, feature, query, plan);
                } else {
                    handleNonDepletionFeature(dto, feature);
                }
                break;
            }
        }

        log.info("Text2Go Service getFeatureAll complete, result={}", JSON.toJSONString(dto));
        return dto;
    }

    private void handleDepletionFeature(ShopifyFeatureAllDTO dto, SubscriptionFeature feature,
        ShopifyFeatureQuery query, SubscriptionPlan plan) {
        dto.setDepletion(true);
        List<FeatureUsage> featureUsages = queryFeatureUsage(query);
        if (!featureUsages.isEmpty()) {
            updateRemainQuota(dto, feature, featureUsages, plan);
        } else {
            subscriptionPlanRepository.saveFeatureUsage(buildFeatureUsage(feature, query, plan.getName()));
            dto.setRemainQuota(feature.getBenefit().getAsLong("quota"));
        }
    }

    private void updateRemainQuota(ShopifyFeatureAllDTO dto, SubscriptionFeature feature,
        List<FeatureUsage> featureUsages, SubscriptionPlan plan) {
        long totalUsageCount = featureUsages.stream()
            .mapToLong(FeatureUsage::getUsageCount)
            .sum();
        long remainQuota = feature.getBenefit().getAsLong("quota") - totalUsageCount;
        dto.setRemainQuota(Math.max(remainQuota, 0));
    }

    private void handleNonDepletionFeature(ShopifyFeatureAllDTO dto, SubscriptionFeature feature) {
        if (feature.getBenefit() != null && feature.getBenefit().getAsLong("quota") != null) {
            dto.setRemainQuota(feature.getBenefit().getAsLong("quota"));
        }
    }

    public List<FeatureUsage> queryFeatureUsage(ShopifyFeatureQuery query) {
        try {
            Assertor.asserts(StringUtils.isNotBlank(query.getAppCode()), "queryFeatureUsage:appCode can not null!");
            Assertor.asserts(query.getId() != null, "queryFeatureUsage:id can not null!");
            Assertor.asserts(StringUtils.isNotBlank(query.getFeatureType()), "queryFeatureUsage:featureType can not null!");

            LambdaQueryWrapper<FeatureUsageDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FeatureUsageDO::getAppCode, query.getAppCode())
                .eq(FeatureUsageDO::getUserId, query.getId())
                .eq(FeatureUsageDO::getFeatureType, query.getFeatureType())
                .eq(FeatureUsageDO::getDeleted, 0)
                .orderByDesc(FeatureUsageDO::getId);

            List<FeatureUsageDO> allUsages = featureUsageMapper.selectList(wrapper);

            return allUsages.stream()
                .filter(this::isWithinTimeRange)
                .map(FeatureUsageConverter.INSTANCE::convertA2B)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("SubscriptionPlanRepositoryImpl queryFeatureUsage exp,shopifyFeatureQuery={}",
                JSON.toJSONString(query), e);
            return Collections.emptyList();
        }
    }

    private boolean isWithinTimeRange(FeatureUsageDO usage) {
        Date currentTime = new Date();
        return currentTime.after(usage.getStartTime()) && currentTime.before(usage.getEndTime());
    }

    @Override
    public TrialDurationDTO subscriptionIsTrial(Long userId) {
        log.info("Text2Go Service subscriptionIsTrial start, userId={}", userId);
        if (userId == null) {
            log.warn("Failed to check trial eligibility, userId is null");
            return null;
        }

        try {
            // Get trial days configuration
            Long planTrialDays = SwitchConfig.planTrialDaysForText2GO;
            log.info("Trial period is configured for {} days", planTrialDays);

            // Check if user has history subscription records (status IN_EFFECT, UNSUBSCRIBE or COMPLETED)
            String appCode = TEXT2GO_APP_CODE;
            List<SubscriptionOrderStatus> targetStatusList = Arrays.asList(
                    SubscriptionOrderStatus.IN_EFFECT,
                    SubscriptionOrderStatus.UNSUBSCRIBE,
                    SubscriptionOrderStatus.COMPLETED
            );

            // Build query to find user's subscription records
            SubscriptionOrderQuery query = SubscriptionOrderQuery.builder()
                    .appCode(appCode)
                    .userId(userId)
                    .status(targetStatusList)
                    .build();
                    
            List<SubscriptionOrder> subscriptionOrders = subscriptionOrderRepository.querySubscriptionOrders(query);
            log.info("User's history order count: {}, userId={}", subscriptionOrders.size(), userId);
            
            // If there are matching orders, user is not eligible for trial
            if (!CollectionUtils.isEmpty(subscriptionOrders)) {
                log.info("User has subscription history, not eligible for trial, userId={}, order count={}", userId, subscriptionOrders.size());
                return new TrialDurationDTO()
                        .setIsTrial(false)
                        .setRemainTrialDay(0L);
            }

            // No subscription records, eligible for full trial period
            log.info("User has no subscription history, eligible for full trial period, userId={}, trial days={}", userId, planTrialDays);
            return new TrialDurationDTO()
                    .setIsTrial(true)
                    .setRemainTrialDay(planTrialDays);
        } catch (Exception e) {
            log.error("Error checking trial eligibility, userId={}, error={}", userId, e.getMessage(), e);
            return null;
        }
    }

}
