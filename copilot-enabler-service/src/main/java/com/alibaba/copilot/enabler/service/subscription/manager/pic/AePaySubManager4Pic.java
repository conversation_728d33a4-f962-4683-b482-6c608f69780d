package com.alibaba.copilot.enabler.service.subscription.manager.pic;

import com.alibaba.copilot.boot.basic.enums.IEnum;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.monitor.annotation.MonitorResult;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageStatusEnum;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageTypeEnum;
import com.alibaba.copilot.enabler.client.payment.dto.CreatePaymentSessionDTO;
import com.alibaba.copilot.enabler.client.payment.dto.PaymentSessionDTO;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribePlanResultDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.factory.IPaymentChannelStrategy;
import com.alibaba.copilot.enabler.domain.payment.factory.PaymentChannelFactory;
import com.alibaba.copilot.enabler.domain.payment.model.MessageInfo;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.service.OrderDomainService;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategy;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategyFactory;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.CreateOrderAndTradeResultDTO;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.SubscribeContext;
import com.alibaba.copilot.enabler.service.payment.metaq.trade.TradeStartProducer;
import com.alibaba.copilot.enabler.service.subscription.factory.CreatePaymentSessionDTOBuilder;
import com.alibaba.copilot.enabler.service.subscription.factory.SubscribeContextBuilder;
import com.alibaba.copilot.enabler.service.subscription.factory.SubscribePlanResultDTOResultBuilder;
import com.alibaba.copilot.enabler.service.subscription.manager.AbstractSubscriptionManager;
import com.alibaba.copilot.enabler.service.subscription.manager.BizSubscriptionContext;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/27
 */
@Component
@Slf4j
public class AePaySubManager4Pic extends AbstractSubscriptionManager {

    @Resource
    private SubscribeContextBuilder subscribeContextBuilder;
    @Resource
    private CreatePaymentSessionDTOBuilder createPaymentSessionDTOBuilder;
    @Resource
    private OrderDomainService orderDomainService;
    @Resource
    private TradeStartProducer tradeStartProducer;

    @Override
    public SubscriptionPayType getSubscriptionPayType() {
        return SubscriptionPayType.AEPay;
    }

    @Override
    public String getAppCode() {
        return AppEnum.PIC_COPILOT.getCode();
    }

    @Monitor(name = "[Pic] 订阅-AePay", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    @MonitorResult
    @Override
    protected SingleResult<SubscribePlanResultDTO> doSubscribe(BizSubscriptionContext context) {
        SubscribePlanDTO subscribePlanDTO = context.getSubscribePlanDTO();
        // 构建订阅请求的上下文信息
        SubscribeContext subscribeContext = subscribeContextBuilder.build(subscribePlanDTO);

        // 创建订单和流水
        CreateOrderAndTradeResultDTO handleResult = createOrderAndTrade(subscribePlanDTO.getAppCode(), subscribeContext);

        TradeRecord payRecord = handleResult.getPayRecord();
        if (payRecord == null) {
            // 存在不需要支付的场景 (如PicCopilot中的高套餐切换低套餐)
            return SingleResult.buildSuccess(new SubscribePlanResultDTO().setNeedPay(false));
        }

        // 组装CreateSession请求
        CreatePaymentSessionDTO createSessionDTO = createPaymentSessionDTOBuilder.build(subscribePlanDTO, handleResult, subscribeContext);

        // 执行CreateSession
        SingleResult<PaymentSessionDTO> paymentSession = createPaymentSession(subscribePlanDTO, createSessionDTO);

        // 保存消息记录
        saveMessageRecord(subscribePlanDTO, createSessionDTO, paymentSession);

        // 投递MQ消息, 开始主动查询交易结果
        postPayStartMessage(subscribePlanDTO, payRecord, paymentSession);

        return new SubscribePlanResultDTOResultBuilder(payRecord, createSessionDTO, paymentSession).build();
    }

    private static CreateOrderAndTradeResultDTO createOrderAndTrade(String appCode, SubscribeContext subscribeContext) {
        SubscriptionStrategy subscriptionStrategy = SubscriptionStrategyFactory.getStrategy(appCode, subscribeContext.getSubscriptionPayType());
        CreateOrderAndTradeResultDTO handleResult = subscriptionStrategy.createOrderAndTrade(subscribeContext);
        log.info("subscribePlan handleResult:{}", JSON.toJSONString(handleResult));
        return handleResult;
    }

    private static SingleResult<PaymentSessionDTO> createPaymentSession(SubscribePlanDTO subscribePlanDTO, CreatePaymentSessionDTO createSessionDTO) {
        IPaymentChannelStrategy paymentChannelStrategy = PaymentChannelFactory.getPaymentByApp(subscribePlanDTO.getAppCode());
        createSessionDTO.setAppEnum(IEnum.of(AppEnum.class, subscribePlanDTO.getAppCode()));
        return paymentChannelStrategy.createPaymentSession(createSessionDTO);
    }

    private void saveMessageRecord(SubscribePlanDTO subscribePlanDTO, CreatePaymentSessionDTO createSessionDTO, SingleResult<PaymentSessionDTO> paymentSession) {
        MessageInfo messageInfo = MessageInfo.builder()
                .notifyContentOrRequestParam(JSON.toJSONString(createSessionDTO))
                .response(JSON.toJSONString(paymentSession))
                .typeEnum(MessageTypeEnum.PAYMENT_AUTH_NOTIFY)
                .statusEnum(MessageStatusEnum.SUCCESS)
                .entityId(createSessionDTO.getOrder().getReferenceOrderId())
                .build();
        orderDomainService.saveMessageRecord(messageInfo, subscribePlanDTO.getAppCode(), subscribePlanDTO.getUserId());
    }

    private void postPayStartMessage(SubscribePlanDTO subscribePlanDTO, TradeRecord payRecord, SingleResult<PaymentSessionDTO> paymentSession) {
        String payTradeNo = payRecord.getTradeNo();
        if (paymentSession.isSuccess() && StringUtils.isNotEmpty(payTradeNo)) {
            tradeStartProducer.postPayStartMessage(payTradeNo, subscribePlanDTO.getAppCode());
        }
    }
}