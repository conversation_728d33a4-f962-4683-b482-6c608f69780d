package com.alibaba.copilot.enabler.service.subscription.strategy.pic;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.dto.NeedPayToSubscribePlanDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.TrialDurationDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.constant.StripeCheckoutSessionMode;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.StripeGateway;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeCreateRefundRequest;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.StripeEventMetadata;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.response.EmailResponse;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategyConfig;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.*;
import com.alibaba.copilot.enabler.service.subscription.factory.RefundRecordBuilder;
import com.alibaba.copilot.enabler.service.subscription.strategy.BaseSubscriptionStrategy;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/05
 */
@Slf4j
@Component
@SubscriptionStrategyConfig(value = AppEnum.PIC_COPILOT, payType = SubscriptionPayType.STRIPE)
public class PicStripeSubStrategy extends BaseSubscriptionStrategy {

    @Resource
    private PicSubscriptionStrategy picSubscriptionStrategy;
    @Resource
    private StripeGateway stripeGateway;

    @Override
    public TrialDurationDTO computeTrialDuration(ComputeTrialContext context) {
        return picSubscriptionStrategy.computeTrialDuration(context);
    }

    @Override
    public CreateOrderAndTradeResultDTO createOrderAndTrade(SubscribeContext context) {
        return picSubscriptionStrategy.createOrderAndTrade(context);
    }

    @Override
    public void handleRefundLogicWhenOldOrderCompleted(@NotNull SubscriptionOrder oldEffectOrder) {
        picSubscriptionStrategy.handleRefundLogicWhenOldOrderCompleted(oldEffectOrder);
    }

    @Override
    public ComputeNewPlanPriceResultDTO computeNewPlanPrice(ComputeNewPlanPriceContext context) {
        return picSubscriptionStrategy.computeNewPlanPrice(context);
    }

    @Override
    public ComputeCycleFeeResultDTO computeCycleFee(ComputeNewPlanPriceContext context) {
        return picSubscriptionStrategy.computeCycleFee(context);
    }

    @Override
    protected Date computeOrderEndTimeForTrial(Date now, long planDays, long remainTrialDays) {
        return picSubscriptionStrategy.computeOrderEndTimeForTrial(now, planDays, remainTrialDays);
    }

    @Override
    protected boolean needPayToSubscribePlanWhenPlanNeedCost(NeedPayToSubscribePlanDTO dto, SubscriptionPlan targetPlan) {
        return picSubscriptionStrategy.needPayToSubscribePlanWhenPlanNeedCost(dto, targetPlan);
    }

    @Override
    public EmailResponse sendEmail(Long userId, Object emailInfoObj) {
        return picSubscriptionStrategy.sendEmail(userId, emailInfoObj);
    }

    @Override
    protected String formatCycleDetailDate(LocalDate date) {
        return picSubscriptionStrategy.formatCycleDetailDate(date);
    }

    @Override
    public void refundForTrialOrder(SubscriptionOrder order) {
        log.info("refundForTrialOrder, order={}", JSON.toJSONString(order));

        // 1. 查询支付流水
        TradeRecord payRecord = tradeRecordRepository.queryPaySuccessRecordByOrderId(order.getId());
        if (payRecord == null) {
            return;
        }

        // 2. 创建退款流水
        TradeRecord refundRecord = new RefundRecordBuilder(payRecord, getAppEnum()).build();
        tradeRecordRepository.createOrUpdateTradeRecord(refundRecord);

        // 3. 执行退款操作
        StripeCreateRefundRequest refundRequest = new StripeCreateRefundRequest();
        // metadata
        StripeEventMetadata metadata = StripeEventMetadata.of(
                StripeCheckoutSessionMode.SETUP,
                order.getAppCode(),
                payRecord.getTradeNo());
        metadata.setUserId(order.getUserId().toString());
        metadata.setSubscriptionOrderId(payRecord.getSubscriptionOrderId().toString());
        metadata.setRefundTradeId(refundRecord.getId().toString());


        refundRequest.setPaymentIntentId(payRecord.getOutTradeNo());
        refundRequest.setMetadata(metadata);
        stripeGateway.createRefund(refundRequest);

        // 4. 发送试用期退订事件
        subscriptionPlanChangedEventProducer.sendCancelWhenTrialEvent(order);
    }
}