package com.alibaba.copilot.enabler.service.subscription.strategy.basic.seo;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.dto.TrialDurationDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategyConfig;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.*;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.copilot.enabler.service.subscription.strategy.basic.BasicSubscriptionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * SeoCopilot应用的订阅处理策略
 *
 * <AUTHOR>
 * @version 2023/11/2
 */
@Slf4j
@Component
@SubscriptionStrategyConfig(value = AppEnum.SEO_COPILOT, payType = SubscriptionPayType.Shopify)
public class SeoSubscriptionStrategy extends BasicSubscriptionStrategy {

    @Override
    public TrialDurationDTO computeTrialDuration(ComputeTrialContext context) {
        SubscriptionPlan targetPlan = context.getTargetPlan();
        List<SubscriptionOrder> historyOrders = context.getHistoryOrders();
        Assertor.assertNonNull(targetPlan, "targetPlan is null");

        if (targetPlan.isFree()) {
            // 免费套餐, 不计算试用期信息
            log.info("computeTrialDuration, no trial plan");
            return new TrialDurationDTO().setIsTrial(false);
        }

        if (CollectionUtil.size(historyOrders) >= 1) {
            // 历史订单不止一笔时, 就没有试用期了
            log.info("computeTrialDuration, more than one order found");
            return new TrialDurationDTO().setIsTrial(false);
        }

        return new TrialDurationDTO()
                .setIsTrial(true)
                .setRemainTrialDay(Long.valueOf(SwitchConfig.seoSubscriptionTrialDays));
    }

    /**
     * 获取实际的试用期开始时间
     */
    @Override
    public CreateOrderAndTradeResultDTO createOrderAndTrade(SubscribeContext context) {
        // Shopify订阅不支持该操作
        throw getNotSupportedException();
    }

    @Override
    public void handleRefundLogicWhenOldOrderCompleted(@NotNull SubscriptionOrder oldEffectOrder) {
        // Shopify订阅不支持该操作
        throw getNotSupportedException();
    }

    @Override
    public ComputeCycleFeeResultDTO computeCycleFee(ComputeNewPlanPriceContext context) {
        // Shopify订阅不支持该操作
        throw getNotSupportedException();
    }
}
