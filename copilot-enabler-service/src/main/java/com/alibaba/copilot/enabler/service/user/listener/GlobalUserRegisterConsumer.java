package com.alibaba.copilot.enabler.service.user.listener;

import com.alibaba.copilot.boot.event.eventbus.base.EventBus;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.monitor.annotation.MonitorResult;
import com.alibaba.copilot.enabler.client.user.event.UserFlowedBackEvent;
import com.alibaba.copilot.enabler.client.user.request.UserRegisterRequest;
import com.alibaba.copilot.enabler.client.user.service.UserRegisterService;
import com.alibaba.copilot.enabler.infra.base.metaq.MetaqMessageProducer;
import com.alibaba.copilot.enabler.service.user.dto.UicUserRegisterDTO;
import com.alibaba.copilot.enabler.service.user.factory.UicUserRegisterBuilder;
import com.alibaba.copilot.enabler.service.user.factory.UserRegisterConverter;
import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/29
 */
@Slf4j
@Service(value = "globalUserRegisterConsumer")
public class GlobalUserRegisterConsumer implements MessageListenerConcurrently {
    private static final String GL_EK_STR = "edgeshop";
    private static final String GL_EK_KEY = "_GL_EK";

    @Resource
    private UserRegisterService userRegisterService;

    @Override
    @Monitor(name = "消费 UIC 用户注册消息", level = Monitor.Level.P0, layer = Monitor.Layer.CONSUMER)
    @MonitorResult(errCode = "$[name != 'CONSUME_SUCCESS'].name")
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> messages, ConsumeConcurrentlyContext context) {
        for (MessageExt message : messages) {
            String msgId = message.getMsgId();
            String GL_EK = message.getProperties().get(GL_EK_KEY);
            if (!GL_EK_STR.equals(GL_EK)) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

            String msgContent = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("GlobalUserRegisterConsumer msgId: {}, msgBody: {}", msgId, msgContent);
            try {
                UicUserRegisterDTO userRegisterDTO = buildRegisterDTO(msgContent);
                log.info("GlobalUserRegisterConsumer#buildRegisterDTO#userRegisterDTO: {}", JSON.toJSONString(userRegisterDTO));
                if (userRegisterDTO != null) {
                    // 执行用户注册相关逻辑
                    registerActive(userRegisterDTO);

                }
            } catch (Exception e) {
                log.error("GlobalUserRegisterConsumer consume failed: {}", e.getMessage());
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 解析 UIC 注册消息体
     *
     * @param msgContent
     * @return
     */
    private UicUserRegisterDTO buildRegisterDTO(String msgContent) {
        if (StringUtils.isBlank(msgContent)) {
            return null;
        }
        UicUserRegisterBuilder builder = new UicUserRegisterBuilder();
        builder.setRegisterMsgContent(msgContent);
        return builder.build();
    }

    /**
     * 执行注册动作
     *
     * @param userRegisterDTO
     */
    private void registerActive(UicUserRegisterDTO userRegisterDTO) {
        UserRegisterRequest registerRequest = UserRegisterConverter.INSTANCE.convertA2B(userRegisterDTO);
        userRegisterService.register(registerRequest);
    }
}
