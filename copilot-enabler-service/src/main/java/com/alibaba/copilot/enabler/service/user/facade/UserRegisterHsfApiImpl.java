package com.alibaba.copilot.enabler.service.user.facade;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.user.facade.UserRegisterHsfApi;
import com.alibaba.copilot.enabler.client.user.request.SeoRegisterRequest;
import com.alibaba.copilot.enabler.client.user.request.UserAndAppRelationRegisterRequest;
import com.alibaba.copilot.enabler.client.user.request.UserRegisterRequest;
import com.alibaba.copilot.enabler.client.user.service.UserRegisterService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/11
 */
@Service
@Slf4j
@HSFProvider(serviceInterface = UserRegisterHsfApi.class)
public class UserRegisterHsfApiImpl implements UserRegisterHsfApi {

    @Autowired
    private UserRegisterService userRegisterService;

    @Override
    public SingleResult<Boolean> checkEmailExist(String email) {
        Boolean checked = userRegisterService.checkEmailExist(email);
        return SingleResult.buildSuccess(checked);
    }

    @Override
    public void register(UserRegisterRequest registerRequest) {
        userRegisterService.register(registerRequest);
    }

    /**
     * updateRegisterData
     *
     * @param seoRegisterRequest
     */
    @Override
    public void updateRegisterData(SeoRegisterRequest seoRegisterRequest) {
        userRegisterService.updateRegisterData(seoRegisterRequest);
    }

    @Override
    public SingleResult<Boolean> registerUserAndAppRelation(UserAndAppRelationRegisterRequest registerRequest) {
        try {
            return SingleResult.buildSuccess(userRegisterService.registerUserAndAppRelation(registerRequest));
        } catch (Exception e) {
            log.error("UserRegisterHsfApiImpl.registerUserAndAppRelation, error, registerRequest={}", JSON.toJSONString(registerRequest), e);
            return SingleResult.buildFailure(e.getMessage());
        }
    }
}
