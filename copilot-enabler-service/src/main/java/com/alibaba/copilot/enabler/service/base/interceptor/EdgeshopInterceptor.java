package com.alibaba.copilot.enabler.service.base.interceptor;

import com.alibaba.copilot.enabler.infra.base.utils.EnvUtils;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static com.alibaba.copilot.enabler.service.base.constant.Constants.EXTERNAL_URI_PREFIX;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/05
 */
@Slf4j
@Component
public class EdgeshopInterceptor implements HandlerInterceptor {

    private static final String EXTERNAL_DOMAIN = "enabler.edgeshop.ai";
    @Override
    public boolean preHandle(@NotNull HttpServletRequest request,
                             @NotNull HttpServletResponse response,
                             @NotNull Object handler) throws Exception {

        String serverName = request.getServerName();
        String uri = request.getRequestURI();

        // 线上环境，外网域名，非 /external/** 路径禁止访问
        if (EnvUtils.isOnline() && serverName.contains(EXTERNAL_DOMAIN) && !uri.startsWith(EXTERNAL_URI_PREFIX)) {
            log.warn("EdgeshopInterceptor not pass, serverName={}, uri={}", serverName, uri);
            return false;
        }
        return true;
    }

    @Override
    public void afterCompletion(@NotNull HttpServletRequest request,
                                @NotNull HttpServletResponse response,
                                @NotNull Object handler, Exception ex) throws Exception {
        if (ex != null) {
            log.error("interceptor invoke error", ex);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
}