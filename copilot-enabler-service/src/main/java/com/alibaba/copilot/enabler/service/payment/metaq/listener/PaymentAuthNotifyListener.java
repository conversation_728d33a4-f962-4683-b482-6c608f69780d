package com.alibaba.copilot.enabler.service.payment.metaq.listener;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.aepay.fund.business.api.payment.dto.notify.PaymentNotifyDTO;
import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.infra.base.utils.MetaqUtils;
import com.alibaba.copilot.enabler.service.payment.metaq.handler.PaymentAuthNotifyHandler;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.taobao.eagleeye.EagleEye;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 支付授权结果通知listener
 */
@Component(value = "paymentAuthNotifyListener")
public class PaymentAuthNotifyListener implements MessageListenerConcurrently {
    public static final Logger TASK_LOG = LoggerFactory.getLogger("taskLogger");

    @Autowired
    private PaymentAuthNotifyHandler paymentAuthNotifyHandler;

    @Override
    @Monitor(name = "支付授权结果通知listener", level = Monitor.Level.P1, layer = Monitor.Layer.CONSUMER)
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        MessageExt messageExt = CollectionUtil.getFirst(msgs);
        if (messageExt == null) {
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        JSONObject notifyContentJsonObject = MetaqUtils.getJSONObjOfMessageExtBody(messageExt);
        String notifyContentStr = notifyContentJsonObject == null ? null : notifyContentJsonObject.toJSONString();
        // 交易流水id
        String tradeNo = null;

        PaymentNotifyDTO paymentNotifyDTO = null;
        try {
            EagleEye.startTrace(null, this.getClass().getName(), EagleEye.TYPE_CUSTOM_MESSAGE_SUB);

            TASK_LOG.info("paymentAuthNotifyListener notifyContent:::{}", notifyContentStr);
            if (notifyContentJsonObject == null) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

            paymentNotifyDTO = notifyContentJsonObject.toJavaObject(PaymentNotifyDTO.class);
            if (!Objects.equals(paymentNotifyDTO.getNotifyType(), AEPaymentConstants.AUTH_PAYMENT_RESULT_NOTIFY_TYPE)) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

            // 交易流水id
            tradeNo = paymentNotifyDTO.getPaymentRequestId();

            Assertor.asserts(StringUtils.isNotBlank(tradeNo), "paymentAuthNotifyListener msg paymentRequestId illegal");
            Assertor.asserts(paymentNotifyDTO.getResult() != null
                            && Objects.equals(paymentNotifyDTO.getResult().getResultCode(), AEPaymentConstants.PAYMENT_RESULT_NOTIFY_SUCCESS),
                    "paymentAuthNotifyListener msg not success");

            // 支付授权处理
            paymentAuthNotifyHandler.handle(tradeNo, messageExt.getMsgId(), notifyContentStr, paymentNotifyDTO);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (BizException bizException) {
            TASK_LOG.error("paymentAuthNotifyListener bizError,msg:{},notifyContent:{}",
                    bizException.getMessage(), notifyContentStr, bizException);
            // 支付授权失败处理
            paymentAuthNotifyHandler.handleFail(tradeNo, messageExt.getMsgId(), notifyContentStr, paymentNotifyDTO);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            TASK_LOG.error("paymentAuthNotifyListener error,msg:{},notifyContent:{}",
                    e.getMessage(), notifyContentStr, e);

            // Metaq消费自动重试
            if (MetaqUtils.isResumeAutoRetry(messageExt, context, notifyContentStr)) {
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }

            // 支付授权失败处理
            paymentAuthNotifyHandler.handleFail(tradeNo, messageExt.getMsgId(), notifyContentStr, paymentNotifyDTO);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } finally {
            EagleEye.endTrace(null);
        }
    }
}

