package com.alibaba.copilot.enabler.service.subscription.factory;

import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribedAppDTO;
import com.alibaba.copilot.enabler.client.user.dto.UserAppRelationDTO;


/**
 * @ClassName SubscribedAppUserConverter
 * <AUTHOR>
 * @Date 2023/9/4 16:00
 */
public class SubscribedUserRelationConverter implements Converter<SubscribedAppDTO, UserAppRelationDTO> {

    public static final Converter<SubscribedAppDTO, UserAppRelationDTO> INSTANCE = new SubscribedUserRelationConverter();

    @Override
    public UserAppRelationDTO convertA2B(SubscribedAppDTO subscribedAppDto) {
        UserAppRelationDTO userAppRelationDto = new UserAppRelationDTO();
        if (subscribedAppDto == null) {
            return userAppRelationDto;
        }
        userAppRelationDto.setAppCode(userAppRelationDto.getAppCode());
        userAppRelationDto.setUserId(userAppRelationDto.getUserId());
        userAppRelationDto.setAppName(userAppRelationDto.getAppName());
        return userAppRelationDto;
    }

    @Override
    public SubscribedAppDTO convertB2A(UserAppRelationDTO userAppRelationDto) {
        SubscribedAppDTO subscribedAppDto = new SubscribedAppDTO();
        if (userAppRelationDto == null) {
            return subscribedAppDto;
        }
        subscribedAppDto.setAppCode(userAppRelationDto.getAppCode());
        subscribedAppDto.setUserId(userAppRelationDto.getUserId());
        subscribedAppDto.setAppName(userAppRelationDto.getAppName());
        subscribedAppDto.setOrdeId(userAppRelationDto.getId());
        return subscribedAppDto;
    }
}
