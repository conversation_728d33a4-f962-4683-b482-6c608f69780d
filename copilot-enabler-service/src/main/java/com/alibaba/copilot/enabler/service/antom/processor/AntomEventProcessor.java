package com.alibaba.copilot.enabler.service.antom.processor;

/**
 * Antom 事件处理器接口
 *
 * <AUTHOR>
 * @date 2025/05/01
 */
public interface AntomEventProcessor {

    /**
     * 获取通知类型
     *
     * @return 通知类型
     */
    String getNotifyType();

    /**
     * 获取应用代码
     *
     * @return 应用代码
     */
    String getAppCode();

    /**
     * 处理事件
     *
     * @param context 执行上下文
     */
    void process(AntomExecuteContext context);
}
