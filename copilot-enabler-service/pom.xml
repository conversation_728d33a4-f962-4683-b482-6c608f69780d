<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.alibaba.copilot</groupId>
        <artifactId>copilot-enabler</artifactId>
        <version>1.0.39-RELEASE</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>copilot-enabler-service</artifactId>
    <packaging>jar</packaging>
    <name>copilot-enabler-service</name>

    <dependencies>
        <!-- 依赖子模块 -->
        <dependency>
            <groupId>com.alibaba.copilot</groupId>
            <artifactId>copilot-enabler-infrastructure</artifactId>
        </dependency>
        <!-- 中间件 -->

        <!-- 二方包 -->
        <dependency>
            <groupId>com.alibaba.copilot</groupId>
            <artifactId>copilot-boot-llm-openai</artifactId>
            <version>${copilot.boot.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.copilot</groupId>
            <artifactId>copilot-boot-event-starter</artifactId>
            <version>${copilot.boot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.copilot</groupId>
            <artifactId>copilot-boot-web</artifactId>
            <version>${copilot.boot.version}</version>
        </dependency>
        <!-- 三方包 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <!-- swagger -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>swagger-bootstrap-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.taobao.tbsession</groupId>
            <artifactId>tbsession</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.tbsession</groupId>
            <artifactId>tbsession-springboot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ofpay</groupId>
            <artifactId>logback-mdc-ttl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-sentinel-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-hsf-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-metaq-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.aepay</groupId>
            <artifactId>fund-business-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.middleware</groupId>
            <artifactId>hsf-sdk</artifactId>
        </dependency>

        <!-- Excel表格制作 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>

        <!-- 绘制PDF -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext7-core</artifactId>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alipay.global.sdk</groupId>
            <artifactId>global-open-sdk-java</artifactId>
            <version>2.0.58</version>
        </dependency>
    </dependencies>

</project>
