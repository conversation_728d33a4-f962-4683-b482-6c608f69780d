# 1. 更多关于Release文件的规范和约定，请点击: https://yuque.antfin-inc.com/aone/geh0qs/252891532

# 构建打包使用jdk版本
baseline.jdk=ajdk11_11.0.17.16

# 构建打包所用的maven版本，pandora boot应用需要使用maven 3
# amaven相比于之前的maven能提升构建速度，详见：https://ata.alibaba-inc.com/articles/243464
# 使用maven构建：build.tools.maven=maven3.5.0
build.tools.maven=amaven3.5.0

# 构建打包使用的maven settings文件
build.tools.maven.settings=tao

# 构建源码语言类型
code.language=java

# 打包为tgz文件的目录，pom.xml里指定ant插件解压fat jar到target/${appName}目录
build.output=copilot-enabler-starter/target/copilot-enabler


# 设置Dockerfile里的APP_NAME变量，必须要配置
build.tools.docker.args=--build-arg APP_NAME=${APP_NAME}

