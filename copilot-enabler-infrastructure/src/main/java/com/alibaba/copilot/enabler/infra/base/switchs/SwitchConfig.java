package com.alibaba.copilot.enabler.infra.base.switchs;

import cn.hutool.core.date.DateUtil;
import com.alibaba.copilot.boot.shopify.web.data.graphql.subscription.AppSubscriptionReplacementBehavior;
import com.alibaba.copilot.enabler.client.payment.dto.PayBillConfigDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.infra.base.utils.EnvUtils;
import com.alibaba.copilot.enabler.infra.subscription.vo.SubsciptionShareCodeVO;
import com.google.common.collect.Lists;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Switch开关配置
 */
public class SwitchConfig {

    @AppSwitch(des = "环境：daily，pre，online", level = Switch.Level.p1)
    public static String env = EnvUtils.DAILY;

    @AppSwitch(des = "超期未支付订单自动取消的时长(单位:秒)", level = Switch.Level.p1)
    public static Long unpaidOrderOverdueSeconds = 60 * 60L;

    @AppSwitch(des = "系统定期代扣提前进行扣款的时长(单位:秒)", level = Switch.Level.p1)
    public static Long scheduledDeductionAdvanceSeconds = 30 * 60L;

    @AppSwitch(des = "系统定期代扣提前进行邮件通知的时长(单位:天)", level = Switch.Level.p1)
    public static Integer scheduledEmailNotifyForDeductionAdvanceDays = 3;

    @AppSwitch(des = "PicCopilot应用的套餐试用天数", level = Switch.Level.p1)
    public static Long planTrialDaysForPicCopilot = 7L;

    @AppSwitch(des = "PicCopilot折扣时的周期费用描述", level = Switch.Level.p1)
    public static String deductDescriptionForPicCopilot = "Upgrade by just paying the difference";

    @AppSwitch(des = "Shopify订阅中SEO应用的月度折扣的结束时间", level = Switch.Level.p1)
    public static Date shopifySubscriptionMonthDiscountEndTimeForSeo = DateUtil.parse("2023-12-12");

    @AppSwitch(des = "Shopify订阅中SEO应用的月度折扣率（8折对应0.8）", level = Switch.Level.p1)
    public static Double shopifySubscriptionMonthDiscountRateForSeo = 0.1;

    @AppSwitch(des = "Shopify订阅中SEO应用的年度折扣率（8折对应0.8）", level = Switch.Level.p1)
    public static Double shopifySubscriptionYearDiscountRateForSeo = 0.75;

    @AppSwitch(des = "折扣码和对应折扣信息", level = Switch.Level.p1)
    public static Map<String, SubsciptionShareCodeVO> shareCodeSubscriptionConfig = new HashMap<String, SubsciptionShareCodeVO>() {
        {
            put("Advanced_MONTH_ZiXuan100", buildSubsciptionShareCodeVO());
        }
    };
    @AppSwitch(des = "付款方式", level = Switch.Level.p1)
    public static String stripePaymentMethodConfig = "pmc_1QtR5BBUuFYymNc8AdtWqf2N";

    /**
     * 构建 SubsciptionShareCode 折扣信息
     */
    private static SubsciptionShareCodeVO buildSubsciptionShareCodeVO() {
        SubsciptionShareCodeVO subsciptionShareCodeVO = new SubsciptionShareCodeVO();
        subsciptionShareCodeVO.setSharePrice(9.9);
        subsciptionShareCodeVO.setCanShareCount(5);
        subsciptionShareCodeVO.setOverdueTime("2024-05-29 00:00:00");
        return subsciptionShareCodeVO;
    }

    @AppSwitch(des = "Shopify订阅的套餐变更策略", level = Switch.Level.p1)
    public static String shopifySubscriptionBehavior = AppSubscriptionReplacementBehavior.APPLY_IMMEDIATELY.name();

    @AppSwitch(des = "Shopify订阅是否使用测试环境（!!!线上环境慎用!!!）", level = Switch.Level.p1)
    public static Boolean shopifySubscriptionUseTestEnv = false;

    @AppSwitch(des = "Shopify订阅的测试金额（线上环境不生效）", level = Switch.Level.p1)
    public static Double shopifySubscriptionTestPayPrice = 0.0;

    @AppSwitch(des = "SEO应用的订阅试用期天数", level = Switch.Level.p1)
    public static Integer seoSubscriptionTrialDays = 1;

    @AppSwitch(des = "DSC应用的订阅试用期天数", level = Switch.Level.p1)
    public static Integer dscSubscriptionTrialDays = 3;

    @AppSwitch(des = "线上环境是否允许触发交易流水的主动查询逻辑", level = Switch.Level.p1)
    public static Boolean enableTriggerTradeQueryForOnline = false;

    @AppSwitch(des = "是否允许强制刷新查询订单状态", level = Switch.Level.p1)
    public static Boolean allowForceRefreshOrderStatus = false;

    @AppSwitch(des = "允许退款的用户白名单", level = Switch.Level.p1)
    public static List<Long> userWhitelistForAllowRefund = Lists.newArrayList();

    @AppSwitch(des = "支付对应的用户token有效时长(单位:秒)", level = Switch.Level.p1)
    public static Long payUserTokenEffectiveSeconds = 2 * 60 * 60L;

    @AppSwitch(des = "测试用户接口调用开关", level = Switch.Level.p1)
    public static Boolean testUserControllerSwitch = false;

    @AppSwitch(des = "stripeApikey", level = Switch.Level.p1)
    public static String stripeApiKey = "sk_test_51PEB93BUuFYymNc8YYpUAZoTzCdhjqh3iOUYiRuG7t1sjVjoWNloajSiLVddNLqBfg8hlis2eenhkDDTmZzPRSmj00N4YsiQtQ";

    @AppSwitch(des = "t2gSecondMonthFreeCoupon", level = Switch.Level.p1)
    public static String t2gSecondMonthFreeCoupon = "rmEo88cC";

    @AppSwitch(des = "Text2GO应用的套餐试用天数", level = Switch.Level.p1)
    public static Long planTrialDaysForText2GO = 7L;

    @AppSwitch(des = "stripeBpcId", level = Switch.Level.p1)
    public static String stripeBpcId = "bpc_1QvxP5BUuFYymNc82CIjsfgM";

    @AppSwitch(des = "stripeBpcId", level = Switch.Level.p1)
    public static String stripeBpcCancelNowId = "bpc_1RCXiJBUuFYymNc8bsBmkN2i";

    @AppSwitch(des = "Antom客户端ID", level = Switch.Level.p1)
    public static String antomClientId = "your_client_id";

    @AppSwitch(des = "Antom公钥（用于验证签名）", level = Switch.Level.p1)
    public static String antomPublicKey = "antom_public_key";

    @AppSwitch(des = "商户私钥（用于签名，请确保安全存储以防泄露）", level = Switch.Level.p1)
    public static String merchantPrivateKey = "your_private_key";

    /**
     * 需要新配置token时, 让使用方访问 <a href="https://pre-copilot-enabler.alibaba-inc.com/api/bill/new/generateToken">该链接</a>
     * 然后将结果作为此处的key, value建议为花名
     */
    @AppSwitch(des = "账单令牌配置", level = Switch.Level.p1)
    public static Map<String, String> billTokenConfig = new HashMap<>();

    @AppSwitch(des = "对接支付的业务方信息配置", level = Switch.Level.p2)
    public static Map<String, PayBillConfigDTO> appPayInfoConfigs = new HashMap<String, PayBillConfigDTO>() {
        {
            put(AppEnum.DS_COPILOT.getCode(), buildDSPayInfoConfig());
            put(AppEnum.SEO_COPILOT_SITE.getCode(), buildAlphaRankPayInfoConfig());
        }
    };

    @AppSwitch(des = "planId", level = Switch.Level.p2)
    public static Map<String, String> t2gPlanId2LookupKey = new HashMap<String, String>() {
        {
            put("69", "T2G#20250220#0");
            put("70", "T2G#20250220#1199");
            put("71", "T2G#20250225#9588");
            put("72", "T2G#20250220#1499");
            put("73", "T2G#20250225#11988");
            put("74", "T2G#20250220#4999");
            put("75", "T2G#20250220#23988");

        }
    };

    @AppSwitch(des = "planId", level = Switch.Level.p2)
    public static Map<String, List<String>> t2gPlanName2IdMap = new HashMap<String, List<String>>() {
        {
            put("Basic", Lists.newArrayList("T2G#20250220#1199",  "T2G#20250225#9588"));
            put("Pro", Lists.newArrayList("T2G#20250220#1499", "T2G#20250225#11988"));
            put("Ultimate", Lists.newArrayList("T2G#20250220#4999",  "T2G#20250220#23988"));
        }
    };

    /**
     * 构建DSCopilot支付信息
     */
    private static PayBillConfigDTO buildDSPayInfoConfig() {
        PayBillConfigDTO payBillConfigDTO = new PayBillConfigDTO();
        payBillConfigDTO.setProductName("DSCopilot");
        payBillConfigDTO.setProductLogo("https://img.alicdn.com/imgextra/i3/O1CN01BAXGIB1H9W7lAqc4f_!!6000000000715-2-tps-3405-721.png");
        payBillConfigDTO.setCompanyName("NEURALNETICS PTE. LTD.");
        payBillConfigDTO.setCompanyAddress("51 Bras Basah Road, #01-21\nLazada One, Singapore 189554\n\n");
        payBillConfigDTO.setCompanyEmail("<EMAIL>");
        return payBillConfigDTO;
    }

    /**
     * 构建AlphaRank支付信息
     */
    private static PayBillConfigDTO buildAlphaRankPayInfoConfig() {
        PayBillConfigDTO payBillConfigDTO = new PayBillConfigDTO();
        payBillConfigDTO.setProductName("AlphaRank");
        payBillConfigDTO.setProductLogo("https://img.alicdn.com/imgextra/i3/O1CN01BAXGIB1H9W7lAqc4f_!!6000000000715-2-tps-3405-721.png");
        payBillConfigDTO.setCompanyName("NEURALNETICS PTE. LTD.");
        payBillConfigDTO.setCompanyAddress("51 Bras Basah Road, #01-21\nLazada One, Singapore 189554\n\n");
        payBillConfigDTO.setCompanyEmail("<EMAIL>");
        return payBillConfigDTO;
    }

    @AppSwitch(des = "planId2ShoplazzaPlanKey", level = Switch.Level.p1)
    public static Map<String, String> planId2ShoplazzaPlanKey = new HashMap<String, String>() {
        {
            put("SEO_COPILOT_SITE#57", "ALPHARANK#0");
            put("SEO_COPILOT_SITE#58", "ALPHARANK#3199");
            put("SEO_COPILOT_SITE#59", "ALPHARANK#35988");
            put("SEO_COPILOT_SITE#60", "ALPHARANK#5599");
            put("SEO_COPILOT_SITE#61", "ALPHARANK#62988");
        }
    };

    @AppSwitch(des = "pricingRedirectUrl", level = Switch.Level.p1)
    public static String pricingRedirectUrl = "https://blog.alpha-rank.com/pricing";
}
