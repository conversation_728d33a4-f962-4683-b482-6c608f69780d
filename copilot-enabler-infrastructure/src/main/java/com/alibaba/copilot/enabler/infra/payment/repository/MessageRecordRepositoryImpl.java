package com.alibaba.copilot.enabler.infra.payment.repository;

import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.domain.payment.model.MessageRecord;
import com.alibaba.copilot.enabler.domain.payment.repository.MessageRecordRepository;
import com.alibaba.copilot.enabler.infra.payment.dataobject.MessageRecordDO;
import com.alibaba.copilot.enabler.infra.payment.factory.MessageRecordConverter;
import com.alibaba.copilot.enabler.infra.payment.mapper.MessageRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 消息记录 repository
 */
@Repository
public class MessageRecordRepositoryImpl implements MessageRecordRepository {

    @Autowired
    private MessageRecordMapper messageRecordMapper;

    @Monitor(name = "创建or更新消息记录", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public Long createOrUpdateMessageRecord(MessageRecord messageRecord) {
        Assertor.asserts(messageRecord != null, "messageRecord can not null");

        MessageRecordDO messageRecordDO = MessageRecordConverter.INSTANCE.convertB2A(messageRecord);
        if (messageRecordDO.getId() != null) {
            messageRecordMapper.updateById(messageRecordDO);
            return messageRecordDO.getId();
        }

        messageRecordMapper.insert(messageRecordDO);
        messageRecord.setId(messageRecordDO.getId());
        return messageRecordDO.getId();
    }
}
