package com.alibaba.copilot.enabler.infra.subscription.dataobject;

import com.alibaba.copilot.enabler.infra.base.bean.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @desc table feature_usage
 * <AUTHOR>
 */
@Setter
@Getter
@TableName(value = "feature_usage")
public class FeatureUsageDO extends BaseDO {

    /**
     * 产品唯一code
     */
    private String appCode;
    /**
     * 用户ID 或 shopifyShopId
     */
    private Long userId;

    /**
     * 特性类型
     */
    private String featureType;

    /**
     * 原始额度，不变
     */
    private Long quota;
    /**
     * 当前周期内已经使用的次数，周期内使用额度，递增
     */
    private Long usageCount;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 属性（JSON格式）
     */
    private String attributes;


}
