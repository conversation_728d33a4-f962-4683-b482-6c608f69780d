package com.alibaba.copilot.enabler.infra.base.metaq;

import com.alibaba.copilot.boot.tools.compress.CompressType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Metaq配置信息
 **/
@Setter
@Getter
@ConfigurationProperties(prefix = "copilot.metaq.message")
public class MetaqMessageProperties {

    /**
     * 事件主题
     */
    private String topic;
    /**
     * 生产者组，同时设置为eventBusId
     */
    private String producerGroup;
    /**
     * 事件生产单元
     */
    private String producerUnitName;
    /**
     * 事件生产实例
     */
    private String producerInstanceName;
    /**
     * 核心线程数
     */
    private int coreThreadSize = 5;
    /**
     * 最大线程数
     */
    private int maxThreadSize = 10;
    /**
     * 消息压缩类型
     */
    private String compressType = CompressType.NONE.getType();
    /**
     * 消息序列化类型
     */
    private String serializeType = FastjsonSerializer.INSTANCE.type();
}
