package com.alibaba.copilot.enabler.infra.payment.factory;

import com.alibaba.aepay.fund.business.api.payment.dto.Result;
import com.alibaba.aepay.fund.business.api.payment.response.PaymentResponse;
import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.domain.payment.dto.PaymentAsyncResultDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2024/1/15
 */
@Slf4j
@AllArgsConstructor
public class PaymentAsyncResultDTOResultBuilder implements Builder<SingleResult<PaymentAsyncResultDTO>> {

    private final PaymentResponse response;

    @Override
    public SingleResult<PaymentAsyncResultDTO> build() {
        Assertor.asserts(response != null
                        && response.getResult() != null
                        && (Objects.equals(response.getResult().getResultCode(), "SUCCESS") ||
                        (Objects.equals(response.getResult().getResultCode(), "PAYMENT_IN_PROCESS"))),
                "call initiatePay error");

        PaymentAsyncResultDTO paymentResultDTO = new PaymentAsyncResultDTO();

        Result result = response.getResult();
        paymentResultDTO.setResultCode(result.getResultCode());
        paymentResultDTO.setResultStatus(result.getResultStatus());
        paymentResultDTO.setResultMessage(result.getResultMessage());

        paymentResultDTO.setPaymentRequestId(response.getPaymentRequestId());
        paymentResultDTO.setPaymentId(response.getPaymentId());
        paymentResultDTO.setPaymentAmount(response.getPaymentAmount());
        paymentResultDTO.setPaymentCreateTime(response.getPaymentCreateTime());
        paymentResultDTO.setRedirectActionForm(response.getRedirectActionForm());

        paymentResultDTO.setNormalUrl(response.getNormalUrl());

        return SingleResult.buildSuccess(paymentResultDTO);
    }
}
