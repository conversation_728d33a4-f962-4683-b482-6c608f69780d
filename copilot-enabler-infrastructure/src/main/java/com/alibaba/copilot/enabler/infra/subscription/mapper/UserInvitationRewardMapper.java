package com.alibaba.copilot.enabler.infra.subscription.mapper;

import com.alibaba.copilot.enabler.infra.subscription.dataobject.UserInvitationRewardDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * User Invitation Reward Mapper Interface
 *
 * <AUTHOR>
 * @date 2025/2/21
 */
@Mapper
public interface UserInvitationRewardMapper extends BaseMapper<UserInvitationRewardDO> {

    /**
     * Query all reward records by user ID
     *
     * @param userId user ID
     * @return list of reward records
     */
    @Select("SELECT * FROM user_invitation_reward WHERE user_id = #{userId}")
    List<UserInvitationRewardDO> selectByUserId(Long userId);

    /**
     * Query all valid (not expired) reward records by user ID
     *
     * @param userId      user ID
     * @param currentTime current time
     * @return list of valid reward records
     */
    @Select("SELECT * FROM user_invitation_reward WHERE user_id = #{userId} AND expiry_time > #{currentTime}")
    List<UserInvitationRewardDO> selectValidRewardsByUserId(
            @Param("userId") Long userId,
            @Param("currentTime") Date currentTime);

    /**
     * Query valid reward records by user ID and reward type
     *
     * @param userId     user ID
     * @param rewardType reward type
     * @param currentTime current time
     * @return list of valid reward records, ordered by expiry time ascending
     */
    @Select("SELECT * FROM user_invitation_reward WHERE user_id = #{userId} AND reward_type = #{rewardType} AND expiry_time > #{currentTime} ORDER BY expiry_time ASC")
    List<UserInvitationRewardDO> selectValidRewardsByUserIdAndType(
            @Param("userId") Long userId,
            @Param("rewardType") String rewardType,
            @Param("currentTime") Date currentTime);

    /**
     * Calculate the sum of valid reward amounts by user ID and reward type
     *
     * @param userId     user ID
     * @param rewardType reward type
     * @param currentTime current time
     * @return total reward amount
     */
    @Select("SELECT IFNULL(SUM(reward_amount), 0) FROM user_invitation_reward WHERE user_id = #{userId} AND reward_type = #{rewardType} AND expiry_time > #{currentTime}")
    Integer sumValidRewardAmount(
            @Param("userId") Long userId,
            @Param("rewardType") String rewardType,
            @Param("currentTime") Date currentTime);

    /**
     * Query reward records by invitation ID
     *
     * @param inviteId invitation ID
     * @return list of reward records
     */
    @Select("SELECT * FROM user_invitation_reward WHERE invite_id = #{inviteId}")
    List<UserInvitationRewardDO> selectByInviteId(Long inviteId);

    /**
     * Calculate the sum of valid reward usage by user ID and reward type
     *
     * @param userId     user ID
     * @param rewardType reward type
     * @param currentTime current time
     * @return total reward usage
     */
    @Select("SELECT IFNULL(SUM(reward_usage), 0) FROM user_invitation_reward WHERE user_id = #{userId} AND reward_type = #{rewardType} AND expiry_time > #{currentTime}")
    Integer sumValidRewardUsage(
            @Param("userId") Long userId,
            @Param("rewardType") String rewardType,
            @Param("currentTime") Date currentTime);
} 