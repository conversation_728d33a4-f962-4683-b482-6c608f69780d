package com.alibaba.copilot.enabler.infra.payment.factory;

import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentToken;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentTokenAttributes;
import com.alibaba.copilot.enabler.infra.payment.dataobject.PaymentTokenDO;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @version 2024/1/11
 */
public class PaymentTokenConverter implements Converter<PaymentTokenDO, PaymentToken> {

    public static final Converter<PaymentTokenDO, PaymentToken> INSTANCE = new PaymentTokenConverter();

    private static final String[] ignoreProperties = new String[]{"appCode", "paymentMethod", "attributes"};

    @Override
    public PaymentToken convertA2B(PaymentTokenDO paymentTokenDO) {
        if (paymentTokenDO == null) {
            return null;
        }

        // 基础字段
        PaymentToken paymentToken = new PaymentToken();
        BeanUtils.copyProperties(paymentTokenDO, paymentToken, ignoreProperties);

        // 需单独处理的字段
        paymentToken.setAppCode(AppEnum.getAppByCode(paymentTokenDO.getAppCode()));
        paymentToken.setPaymentMethod(PaymentMethodEnum.ofName(paymentTokenDO.getPaymentMethod()));
        paymentToken.setAttributes(new PaymentTokenAttributes(paymentTokenDO.getAttributes()));

        return paymentToken;
    }

    @Override
    public PaymentTokenDO convertB2A(PaymentToken paymentToken) {
        if (paymentToken == null) {
            return null;
        }

        // 基础字段
        PaymentTokenDO paymentTokenDO = new PaymentTokenDO();
        BeanUtils.copyProperties(paymentToken, paymentTokenDO, ignoreProperties);

        // 需单独处理的字段
        paymentTokenDO.setAppCode(paymentToken.getAppCode().name());
        paymentTokenDO.setPaymentMethod(paymentToken.getPaymentMethod().name());
        paymentTokenDO.setAttributes(paymentToken.getAttributes().toString());

        return paymentTokenDO;
    }
}
