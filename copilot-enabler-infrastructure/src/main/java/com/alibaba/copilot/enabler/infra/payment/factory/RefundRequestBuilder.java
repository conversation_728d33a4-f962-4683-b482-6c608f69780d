package com.alibaba.copilot.enabler.infra.payment.factory;

import com.alibaba.aepay.fund.business.api.payment.request.RefundRequest;
import com.alibaba.copilot.boot.basic.enums.IEnum;
import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.enabler.client.payment.dto.RefundDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.global.money.Money;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @version 2024/1/15
 */
@AllArgsConstructor
public class RefundRequestBuilder implements Builder<RefundRequest> {

    private final RefundDTO refundDTO;

    @Override
    public RefundRequest build() {
        RefundRequest refundRequest = new RefundRequest();
        refundRequest.setIdentityCode(IEnum.of(AppEnum.class, refundDTO.getAppCode()).getAePayIdentityCode().name());
        refundRequest.setPaymentId(refundDTO.getPaymentId());
        refundRequest.setRefundRequestId(refundDTO.getRefundRequestId());
        Money money = Money.of(refundDTO.getRefundAmount(), AEPaymentConstants.CURRENCY_CODE);
        refundRequest.setRefundAmount(money);
        return refundRequest;
    }
}
