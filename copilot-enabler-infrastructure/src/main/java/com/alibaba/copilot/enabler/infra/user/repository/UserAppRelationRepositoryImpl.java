package com.alibaba.copilot.enabler.infra.user.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.user.request.UserAppRelationQuery;
import com.alibaba.copilot.enabler.domain.user.model.UserAppRelation;
import com.alibaba.copilot.enabler.domain.user.repository.UserAppRelationRepository;
import com.alibaba.copilot.enabler.infra.base.constants.ErrorCodes;
import com.alibaba.copilot.enabler.infra.user.dataobject.UserAppRelationDO;
import com.alibaba.copilot.enabler.infra.user.factory.UserAppRelationConverter;
import com.alibaba.copilot.enabler.infra.user.mapper.UserAppRelationMapper;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/4
 */
@Slf4j
@Repository
public class UserAppRelationRepositoryImpl implements UserAppRelationRepository {

    @Resource
    private UserAppRelationMapper userAppRelationMapper;

    @Override
    public UserAppRelation getUserAppRelationById(Long id) {
        Assertor.assertNonNull(id, ErrorCodes.INVALID_PARAM);

        QueryWrapper<UserAppRelationDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", Boolean.FALSE);
        queryWrapper.eq("id", id);

        UserAppRelationDO userAppRelationDO = userAppRelationMapper.selectOne(queryWrapper);

        return UserAppRelationConverter.INSTANCE.convertA2B(userAppRelationDO);
    }

    @Override
    public UserAppRelation getUserAppRelation(Long userId, String appCode, String email) {
        Assertor.assertNotBlank(appCode, ErrorCodes.NULL_APP_CODE);

        QueryWrapper<UserAppRelationDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", Boolean.FALSE);
        queryWrapper.eq("app_code", appCode);
        if (userId != null) {
            queryWrapper.eq("user_id", userId);
        }
        if (StringUtils.isNotBlank(email)) {
            queryWrapper.eq("email", email);
        }

//        UserAppRelationDO userAppRelationDO = userAppRelationMapper.selectOne(queryWrapper);
        List<UserAppRelationDO> userAppRelationDOS = userAppRelationMapper.selectList(queryWrapper);
        UserAppRelationDO userAppRelationDO = CollectionUtil.getFirst(userAppRelationDOS);

        return UserAppRelationConverter.INSTANCE.convertA2B(userAppRelationDO);
    }

    @Override
    public List<UserAppRelation> queryUserAppRelationList(UserAppRelationQuery userAppRelationQuery) {
        Assertor.asserts(userAppRelationQuery != null && userAppRelationQuery.getUserId() != null, ErrorCodes.NULL_USER_ID);

        QueryWrapper<UserAppRelationDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", Boolean.FALSE);
        queryWrapper.in("user_id", userAppRelationQuery.getUserId());
        queryWrapper.in("app_code", userAppRelationQuery.getAppCode());
        List<UserAppRelationDO> userAppRelationList = userAppRelationMapper.selectList(queryWrapper);

        return UserAppRelationConverter.INSTANCE.convertA2B(userAppRelationList);
    }

    @Override
    public UserAppRelation saveUserAppRelation(UserAppRelation relationDomain) {
        Assertor.assertNonNull(relationDomain, ErrorCodes.NULL_USER_APP_RELATION);

        UserAppRelationDO userAppRelationDO = UserAppRelationConverter.INSTANCE.convertB2A(relationDomain);
        userAppRelationDO.setGmtCreate(new Date());
        userAppRelationDO.setGmtModified(new Date());
        userAppRelationDO.setDeleted(Boolean.FALSE);
        userAppRelationMapper.insert(userAppRelationDO);

        return UserAppRelationConverter.INSTANCE.convertA2B(userAppRelationDO);
    }

    @Override
    public Boolean updateUserAppRelation(UserAppRelation userAppRelation) {
        log.info("UserAppRelationRepositoryImpl.updateUserAppRelation, userAppRelation={}", JSON.toJSONString(userAppRelation));
        UserAppRelationDO userAppRelationDO = UserAppRelationConverter.INSTANCE.convertB2A(userAppRelation);
        int result = userAppRelationMapper.updateById(userAppRelationDO);
        log.info("UserAppRelationRepositoryImpl.updateUserAppRelation, userAppRelation={}, result={}", JSON.toJSONString(userAppRelation), result);
        return result > 0;
    }
}
