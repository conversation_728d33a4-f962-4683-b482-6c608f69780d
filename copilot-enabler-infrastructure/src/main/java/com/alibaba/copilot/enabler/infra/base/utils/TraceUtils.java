package com.alibaba.copilot.enabler.infra.base.utils;

import com.taobao.eagleeye.EagleEye;
import com.taobao.eagleeye.EagleEyeSlf4jMdcUpdater;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 全链路日志增加traceId
 */
@Component
public class TraceUtils {
    private static final ThreadLocal<String> trace = new ThreadLocal<>();

    @PostConstruct
    public void init() {
        //增加鹰眼日志MDC
        EagleEye.addRpcContextListener(EagleEyeSlf4jMdcUpdater.getInstance());
    }

    public static final String TRACE_ID = "EAGLEEYE_TRACE_ID";

    public static void setTraceId(String traceId) {
        trace.set(traceId);
    }

    public static String getTraceId() {
        return trace.get();
    }

    public static void initTraceInfo(String traceId) {
        trace.set(traceId);
        MDC.put(TRACE_ID, traceId);
    }

    public static void clearTraceInfo() {
        trace.set(null);
        MDC.remove(TRACE_ID);
    }
}