package com.alibaba.copilot.enabler.infra.payment.factory;

import cn.hutool.core.util.StrUtil;
import com.alibaba.aepay.fund.business.api.payment.dto.*;
import com.alibaba.aepay.fund.business.api.payment.request.CreatePaymentSessionRequest;
import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.dto.BuyerDTO;
import com.alibaba.copilot.enabler.client.payment.dto.CreatePaymentSessionDTO;
import com.alibaba.copilot.enabler.client.payment.dto.GoodsDTO;
import com.alibaba.copilot.enabler.client.payment.dto.OrderDTO;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.global.money.Money;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2024/1/15
 */
@Slf4j
@AllArgsConstructor
public class CreatePaymentSessionRequestBuilder implements Builder<CreatePaymentSessionRequest> {

    private final CreatePaymentSessionDTO sessionDTO;

    @Override
    public CreatePaymentSessionRequest build() {
        CreatePaymentSessionRequest request = new CreatePaymentSessionRequest();

        // 基础信息
        request.setIdentityCode(sessionDTO.getAppEnum().getAePayIdentityCode().name());
        request.setPaymentRequestId(sessionDTO.getPaymentRequestId());
        request.setPaymentAmount(Money.of(sessionDTO.getActualPayAmount(), AEPaymentConstants.CURRENCY_CODE));

        // 重定向链接
        request.setPaymentRedirectUrl(sessionDTO.getRedirectUrl());

        // 支付策略
        request.setSettlementStrategy(buildSettlementStrategy());

        // 根据不同的支付方式设置对应的参数信息
        // 参: https://aliyuque.antfin.com/global.finnet/ofst5q/sf7gcmyek9anqgwl#IrVEw
        PaymentMethodEnum paymentMethod = sessionDTO.getPaymentMethod();
        if (PaymentMethodEnum.isAlipayPaymentMethod(paymentMethod)) {
            fillAlipayRequest(request, sessionDTO);
        } else if (paymentMethod == PaymentMethodEnum.CREDIT_CARD) {
            fillCardRequest(request, sessionDTO);
        } else {
            log.error("paymentMethod not support: {}", paymentMethod);
            throw new IllegalArgumentException("paymentMethod not support: " + paymentMethod);
        }

        return request;
    }

    @NotNull
    private static SettlementStrategy buildSettlementStrategy() {
        SettlementStrategy settlementStrategy = new SettlementStrategy();
        settlementStrategy.setSettlementCurrency(AEPaymentConstants.CURRENCY_CODE);
        return settlementStrategy;
    }

    private void fillCardRequest(CreatePaymentSessionRequest request, CreatePaymentSessionDTO sessionDTO) {
        // 产品码
        request.setProductCode(AEPaymentConstants.PRODUCT_CODE_FOR_CASHIER);

        // 支付方式
        request.setPaymentMethod(buildPaymentMethodForCard(sessionDTO));

        // 设置env
        request.setEnv(buildEnvInfo(sessionDTO, true));

        // 订单信息
        request.setOrder(buildOrderInfo(sessionDTO, true));

        // 支付因子
        request.setPaymentFactor(buildPaymentFactor());

        // 设置订单超时时间
        long sessionExpiryTime = System.currentTimeMillis() + SwitchConfig.unpaidOrderOverdueSeconds * 1000;
        request.setPaymentSessionExpiryTime(sessionExpiryTime);
    }

    @NotNull
    private static PaymentFactor buildPaymentFactor() {
        PaymentFactor paymentFactor = new PaymentFactor();
        paymentFactor.setIsAuthorization(true);
        return paymentFactor;
    }

    private void fillAlipayRequest(CreatePaymentSessionRequest request, CreatePaymentSessionDTO sessionDTO) {
        // 产品码
        if (PaymentTypeEnum.ONE_TIME_PAYMENT.equals(sessionDTO.getPaymentType())) {
            request.setProductCode(AEPaymentConstants.PRODUCT_CODE_FOR_CASHIER);
        } else {
            request.setProductCode(AEPaymentConstants.PRODUCT_CODE_FOR_AGREEMENT);
        }

        request.setProductScene(AEPaymentConstants.PRODUCT_SCENE_FOR_ALIPAY);

        // 支付方式
        request.setPaymentMethod(buildPaymentMethodForAlipay(sessionDTO));

        // env
        request.setEnv(buildEnvInfo(sessionDTO, false));

        // 订单信息
        request.setOrder(buildOrderInfo(sessionDTO, false));

        // 授权信息
        request.setAgreementInfo(buildAgreementInfo(sessionDTO));
    }

    @NotNull
    private static AgreementInfo buildAgreementInfo(CreatePaymentSessionDTO sessionDTO) {
        AgreementInfo agreementInfo = new AgreementInfo();
        agreementInfo.setAuthState(sessionDTO.getAuthState());
        return agreementInfo;
    }

    private Order buildOrderInfo(CreatePaymentSessionDTO sessionDTO, boolean setExtendInfo) {
        OrderDTO orderDTO = sessionDTO.getOrder();
        if (orderDTO == null) {
            return null;
        }

        Order order = new Order();
        order.setOrderAmount(Money.of(orderDTO.getOrderAmount(), AEPaymentConstants.CURRENCY_CODE));
        order.setReferenceOrderId(orderDTO.getReferenceOrderId());
        // 取决于支付时的显示内容：if goodsName 参考淘宝：用户备注（如有） else if goodsName
        order.setOrderDescription(orderDTO.getOrderDescription());

        // 添加额外的业务扩展，从整个支付链路回传过来, 考虑（appCode & userId）用于后期分库分表
        if (setExtendInfo) {
            order.setExtendInfo(orderDTO.getExtendInfo());
        }

        // 商品信息
        order.setGoods(buildGoodsList(orderDTO.getGoodsList()));

        // 买家信息
        order.setBuyer(buildBuyer(orderDTO.getBuyer()));

        return order;
    }

    @NotNull
    private static Env buildEnvInfo(CreatePaymentSessionDTO sessionDTO, boolean setTerminalType) {
        Env env = new Env();
        if (setTerminalType) {
            env.setTerminalType(AEPaymentConstants.DEFAULT_TERMINAL_TYPE);
        }
        env.setClientIp(sessionDTO.getClientIp());
        return env;
    }

    private List<Goods> buildGoodsList(List<GoodsDTO> goodsList) {
        if (CollectionUtils.isEmpty(goodsList)) {
            return null;
        }

        return goodsList.stream()
                .map(goodsDTO -> {
                    Goods goods = new Goods();
                    // planId
                    goods.setReferenceGoodsId(goodsDTO.getGoodsId());
                    // planName
                    goods.setGoodsName(goodsDTO.getGoodsName());
                    // PHYSICAL: 实物; DIGITAL: 数字;
                    goods.setDeliveryMethodType(AEPaymentConstants.GOODS_DELIVERY_METHOD_TYPE);
                    return goods;
                })
                .collect(Collectors.toList());
    }

    private Buyer buildBuyer(BuyerDTO buyerDTO) {
        if (buyerDTO == null) {
            return null;
        }

        Buyer buyer = new Buyer();
        buyer.setReferenceBuyerId(buyerDTO.getBuyerId());
        buyer.setBuyerRegistrationTime(buyerDTO.getBuyerRegistrationTime());
        return buyer;
    }

    @NotNull
    private PaymentMethod buildPaymentMethodForCard(CreatePaymentSessionDTO sessionDTO) {
        String cardToken = sessionDTO.getCardToken();
        String networkTransactionId = sessionDTO.getNetworkTransactionId();

        PaymentMethod paymentMethod = new PaymentMethod();
        paymentMethod.setPaymentMethodType(PaymentMethodEnum.CREDIT_CARD.getValue());
        paymentMethod.setPaymentMethodId(cardToken);
        PaymentMethodMetaData paymentMethodMetaData = new PaymentMethodMetaData();
        paymentMethodMetaData.setIs3DSAuthentication(true);
        paymentMethodMetaData.setIsCardOnFile(StrUtil.isNotEmpty(cardToken));
        paymentMethodMetaData.setNetworkTransactionId(networkTransactionId);
        paymentMethod.setPaymentMethodMetaData(paymentMethodMetaData);
        return paymentMethod;
    }

    @NotNull
    private PaymentMethod buildPaymentMethodForAlipay(CreatePaymentSessionDTO sessionDTO) {
        PaymentMethod paymentMethod = new PaymentMethod();
        paymentMethod.setPaymentMethodType(sessionDTO.getPaymentMethod().getValue());
        paymentMethod.setPaymentMethodId(sessionDTO.getAlipayToken());
        return paymentMethod;
    }
}
