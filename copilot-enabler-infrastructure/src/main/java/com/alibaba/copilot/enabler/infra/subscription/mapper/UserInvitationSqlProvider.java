package com.alibaba.copilot.enabler.infra.subscription.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * SQL Provider for UserInvitationMapper
 * 
 * <AUTHOR>
 * @date 2025/3/21
 */
public class UserInvitationSqlProvider {
    
    /**
     * Generate dynamic SQL for counting non-system invitations
     *
     * @param params parameters including inviterId and excludeUserIds
     * @return SQL string
     */
    public String countByInviterAndNotFromSystem(Map<String, Object> params) {
        Long inviterId = (Long) params.get("inviterId");
        List<Long> excludeUserIds = (List<Long>) params.get("excludeUserIds");
        
        return new SQL() {{
            SELECT("COUNT(*)");
            FROM("user_invitation");
            WHERE("inviter_id = #{inviterId}");
            if (excludeUserIds != null && !excludeUserIds.isEmpty()) {
                StringBuilder inClause = new StringBuilder();
                inClause.append("invitee_id NOT IN (");
                for (int i = 0; i < excludeUserIds.size(); i++) {
                    if (i > 0) {
                        inClause.append(", ");
                    }
                    inClause.append(excludeUserIds.get(i));
                }
                inClause.append(")");
                WHERE(inClause.toString());
            }
        }}.toString();
    }
    
    /**
     * Generate dynamic SQL for counting non-system invitations within time range
     *
     * @param params parameters including inviterId, startTime, endTime, and excludeUserIds
     * @return SQL string
     */
    public String countByInviterAndTimeRangeExcludeSystem(Map<String, Object> params) {
        Long inviterId = (Long) params.get("inviterId");
        Date startTime = (Date) params.get("startTime");
        Date endTime = (Date) params.get("endTime");
        List<Long> excludeUserIds = (List<Long>) params.get("excludeUserIds");
        
        return new SQL() {{
            SELECT("COUNT(*)");
            FROM("user_invitation");
            WHERE("inviter_id = #{inviterId}");
            WHERE("gmt_create >= #{startTime}");
            WHERE("gmt_create < #{endTime}");
            if (excludeUserIds != null && !excludeUserIds.isEmpty()) {
                StringBuilder inClause = new StringBuilder();
                inClause.append("invitee_id NOT IN (");
                for (int i = 0; i < excludeUserIds.size(); i++) {
                    if (i > 0) {
                        inClause.append(", ");
                    }
                    inClause.append(excludeUserIds.get(i));
                }
                inClause.append(")");
                WHERE(inClause.toString());
            }
        }}.toString();
    }
} 