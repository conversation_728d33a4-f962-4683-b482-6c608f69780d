package com.alibaba.copilot.enabler.infra.user.dataobject;

import com.alibaba.copilot.enabler.infra.base.bean.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/30
 */
@Setter
@Getter
@TableName(value = "user_app_relation")
public class UserAppRelationDO extends BaseDO {
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 产品名称
     */
    private String appName;

    /**
     * 产品编码
     */
    private String appCode;

    /**
     * 产品类型
     */
    private String appType;

    /**
     * 产品绑定状态
     */
    private String bindStatus;

    /**
     * 绑定来源
     */
    private String bindSource;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 拓展字段（JSON格式）
     * * json字符串转map 或 map转json字符串，建议使用 {@link com.alibaba.copilot.boot.tools.utils.AttributesUtils} 工具类
     */
    private String attributes;
}
