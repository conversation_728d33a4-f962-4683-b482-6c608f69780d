package com.alibaba.copilot.enabler.infra.subscription.gateway;

import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.monitor.annotation.MonitorResult;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.domain.subscription.dto.DiscountInfoDTO;
import com.alibaba.copilot.enabler.domain.subscription.gateway.SpreadGateway;
import com.alibaba.copilot.enabler.domain.subscription.request.DiscountInfoRequest;
import com.alibaba.copilot.enabler.infra.base.utils.HttpUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.alibaba.copilot.enabler.infra.base.constants.ErrorCodes.DISCOUNT_INFO_ERROR;

/**
 * 推广域服务
 */
@Slf4j
@Service
public class SpreadGatewayImpl implements SpreadGateway {

    /**
     * 推广域折扣信息查询接口
     */
    public static final String DISCOUNT_INFO_URL = "/commission/v2/rights/query";

    @Value("${commissionHttpServiceUrlPrefix}")
    private String spreadBaseUrl;

    @Monitor(name = "[Spread Gateway] 推广域-获取折扣信息", layer = Monitor.Layer.GATEWAY, level = Monitor.Level.P1)
    @MonitorResult
    @Override
    public DiscountInfoDTO getDiscountInfo(DiscountInfoRequest request) {
        Map<String, String> params = new HashMap<>();
        params.put("app", convertAppCode(request.getAppCode()));
        params.put("tag", request.getDiscountTag());
        JSONObject spreadResult = null;
        try {
            log.info("调用推广入参：{}", JSON.toJSONString(params));
            spreadResult = HttpUtils.get(spreadBaseUrl.concat(DISCOUNT_INFO_URL), null, params, JSONObject.class);
            log.info("调用推广返回：{}", JSON.toJSONString(spreadResult));
        } catch (Exception e) {
            log.error("调用推广异常，异常信息：{}", e.getMessage(), e);
        }
        Assertor.assertNonNull(spreadResult, DISCOUNT_INFO_ERROR);
        Integer retCode = spreadResult.getInteger("retCode");
        Assertor.asserts(retCode == 0, DISCOUNT_INFO_ERROR, spreadResult.getString("msg"));

        return DiscountInfoDTO.builder()
                .DiscountTag(request.getDiscountTag())
                .discount(100 - spreadResult.getJSONObject("content").getInteger("reduction_rate"))
                .discountDuration(spreadResult.getJSONObject("content").getLong("reduction_cycle"))
                .discountDeadline(spreadResult.getJSONObject("content").getDate("expire"))
                .build();
    }

    /**
     * 营销侧和我们这边配置的appCode不一定一样，所以要有一个转换逻辑
     */
    @Nullable
    private static String convertAppCode(String appCode) {
        if (Objects.equals(appCode, "DS_COPILOT")) {
            return "dscopilot";
        }

        return appCode;
    }
}
