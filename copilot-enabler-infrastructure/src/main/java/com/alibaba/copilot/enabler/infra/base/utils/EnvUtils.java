package com.alibaba.copilot.enabler.infra.base.utils;

import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;

/**
 * 环境配置
 */
public class EnvUtils {

    public static final String DAILY = "daily";
    public static final String PRE = "pre";
    public static final String ONLINE = "online";

    public static boolean isDaily() {
        return DAILY.equals(SwitchConfig.env);
    }

    public static boolean isPre() {
        return PRE.equals(SwitchConfig.env);
    }

    public static boolean isOnline() {
        return ONLINE.equals(SwitchConfig.env);
    }

    public static String getEnv() {
        return SwitchConfig.env;
    }

    /**
     * 线上环境禁止调用
     */
    public static void interceptForOnline() {
        Assertor.asserts(!isOnline(), "disallowed for online env");
    }
}
