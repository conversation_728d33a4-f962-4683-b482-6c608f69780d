package com.alibaba.copilot.enabler.infra.payment.factory;

import com.alibaba.aepay.fund.business.api.payment.request.InquiryRefundRequest;
import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.enabler.client.payment.dto.QueryRefundDTO;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @version 2024/1/15
 */
@AllArgsConstructor
public class InquiryRefundRequestBuilder implements Builder<InquiryRefundRequest> {

    private final QueryRefundDTO queryRefundDTO;

    @Override
    public InquiryRefundRequest build() {
        InquiryRefundRequest request = new InquiryRefundRequest();
        request.setIdentityCode(queryRefundDTO.getAppEnum().getAePayIdentityCode().name());
        request.setRefundId(queryRefundDTO.getRefundId());
        request.setRefundRequestId(queryRefundDTO.getRefundRequestId());
        return request;
    }
}
