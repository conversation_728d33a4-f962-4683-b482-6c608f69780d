package com.alibaba.copilot.enabler.infra.subscription.factory;

import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.copilot.enabler.domain.subscription.model.FeatureUsage;
import com.alibaba.copilot.enabler.domain.subscription.model.FeatureUsageAttributes;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.FeatureUsageDO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * @dscr FeatureUsageConverter
 * <AUTHOR>
 */
@Component
public class FeatureUsageConverter implements Converter<FeatureUsageDO, FeatureUsage> {

    public static final Converter<FeatureUsageDO, FeatureUsage> INSTANCE = new FeatureUsageConverter();

    private static final String[] ignoreProperties = new String[]{"attributes"};

    @Override
    public FeatureUsage convertA2B(FeatureUsageDO featureUsageDO) {
        if (featureUsageDO == null) {
            return null;
        }
        FeatureUsage featureUsage = new FeatureUsage();
        BeanUtils.copyProperties(featureUsageDO, featureUsage);
        featureUsage.setAttributes(new FeatureUsageAttributes(featureUsageDO.getAttributes()));
        return featureUsage;
    }

    @Override
    public FeatureUsageDO convertB2A(FeatureUsage featureUsage) {
        if (featureUsage == null) {
            return null;
        }
        FeatureUsageDO featureUsageDO = new FeatureUsageDO();
        BeanUtils.copyProperties(featureUsage, featureUsageDO);
        featureUsageDO.setAttributes(featureUsage.getAttributes().toString());
        return featureUsageDO;
    }
}
