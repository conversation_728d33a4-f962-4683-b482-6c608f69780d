package com.alibaba.copilot.enabler.infra.base.utils;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.common.message.MessageExt;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;

/**
 * Metaq相关工具
 */
@Slf4j
public class MetaqUtils {

    /**
     * 是否继续自动重试
     * （借助Metaq自动重试机制，延迟级别从level1~level18）
     *
     * @return 是否继续重试（true：继续）
     */
    public static boolean isResumeAutoRetry(MessageExt msg, ConsumeConcurrentlyContext context, String jsonMsg) {
        // 已重试的次数
        int retryTime = msg.getReconsumeTimes();
        // 延迟级别
        context.setDelayLevelWhenNextConsume(retryTime + 1);
        if (retryTime >= 18) {
            log.error("metaq auto retry reached max limit,msgContent:{}", jsonMsg);
            return false;
        }

        return true;
    }

    /**
     * 解析MessageExt中的body字段转换为JSONObject
     *
     * @return {@link JSONObject}
     */
    public static JSONObject getJSONObjOfMessageExtBody(MessageExt messageExt) {
        String json = new String(messageExt.getBody(), StandardCharsets.UTF_8);
        if (StringUtils.isBlank(json)) {
            return null;
        }

        return JSONObject.parseObject(json);
    }
}
