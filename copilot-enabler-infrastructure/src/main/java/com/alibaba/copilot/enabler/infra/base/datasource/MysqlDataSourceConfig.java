package com.alibaba.copilot.enabler.infra.base.datasource;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Description mysql基础配置
 * <AUTHOR>
 * @Date 2022/1/19 上午11:57
 **/
@Configuration
public class MysqlDataSourceConfig {
    @Bean("mysqlGlobalConfig")
    public GlobalConfig globalConfig() {
        GlobalConfig globalConfig = new GlobalConfig();
        // 是否控制台 print mybatis-plus 的 LOGO
        globalConfig.setBanner(true);

        //注入自定义的自动填充处理逻辑
        globalConfig.setMetaObjectHandler(new TimeMetaObjectHandler());
        GlobalConfig.DbConfig dbConfig = new GlobalConfig.DbConfig();

        // 表名下划线命名默认true
        dbConfig.setTableUnderline(true);

        // id类型,默认为数据库自增，若为分表，则id可通过@TableId在DO中设置
        dbConfig.setIdType(IdType.AUTO);
        globalConfig.setDbConfig(dbConfig);
        return globalConfig;
    }

    /**
     * 分页插件
     */
    @Bean(name = "mysqlMybatisPlusInterceptor")
    public MybatisPlusInterceptor mysqlMybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }
}
