package com.alibaba.copilot.enabler.infra.marketing.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.marketing.constant.DiscountRuleEntityType;
import com.alibaba.copilot.enabler.client.marketing.constant.DiscountRuleStatus;
import com.alibaba.copilot.enabler.client.marketing.constant.DiscountType;
import com.alibaba.copilot.enabler.domain.marketing.model.DiscountRule;
import com.alibaba.copilot.enabler.domain.marketing.model.DiscountRuleAttributes;
import com.alibaba.copilot.enabler.domain.marketing.repository.DiscountRuleRepository;
import com.alibaba.copilot.enabler.infra.marketing.dataobject.DiscountRuleDO;
import com.alibaba.copilot.enabler.infra.marketing.factory.DiscountRuleConverter;
import com.alibaba.copilot.enabler.infra.marketing.mapper.DiscountRuleMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2024/2/26
 */
@Repository
public class DiscountRuleRepositoryImpl implements DiscountRuleRepository {

    @Resource
    private DiscountRuleMapper discountRuleMapper;

    @Override
    public DiscountRule create(DiscountRule discountRule) {
        Assertor.assertNull(discountRule.getId(), "id is not null");
        Assertor.assertNonNull(discountRule.getAppCode(), "appCode is null");
        Assertor.assertNonNull(discountRule.getStatus(), "status is null");
        Assertor.assertNonNull(discountRule.getEntityType(), "entityType is null");
        Assertor.assertNonNull(discountRule.getEntityId(), "entityId is null");

        DiscountRuleAttributes attributes = discountRule.getAttributes();
        Assertor.assertNonNull(attributes, "attributes is null");
        Assertor.assertNonNull(attributes.getDiscountWay(), "attributes#discountWay is null");
        Assertor.assertNonNull(attributes.getDiscountValue(), "attributes#discountValue is null");

        DiscountRuleDO discountRuleDO = DiscountRuleConverter.INSTANCE.convertB2A(discountRule);
        discountRuleMapper.insert(discountRuleDO);

        return DiscountRuleConverter.INSTANCE.convertA2B(discountRuleDO);
    }

    @Override
    public DiscountRule queryById(Long id) {
        return Optional.ofNullable(discountRuleMapper.selectById(id))
                .filter(rule -> !rule.getDeleted())
                .map(DiscountRuleConverter.INSTANCE::convertA2B)
                .orElse(null);
    }

    @Override
    public DiscountRule update(DiscountRule discountRule) {
        Assertor.assertNonNull(discountRule, "discountRule is null");

        Long id = discountRule.getId();
        Assertor.assertNonNull(id, "id is null");

        DiscountRuleDO discountRuleDO = DiscountRuleConverter.INSTANCE.convertB2A(discountRule);
        int updateResult = discountRuleMapper.updateById(discountRuleDO);
        Assertor.asserts(updateResult > 0, "update discountRule failed");

        DiscountRuleDO newDiscountRuleDO = discountRuleMapper.selectById(id);
        return DiscountRuleConverter.INSTANCE.convertA2B(newDiscountRuleDO);
    }

    @Override
    public void updateStatus(Long id, DiscountRuleStatus status) {
        Assertor.assertNonNull(id, "id is null");
        Assertor.assertNonNull(status, "status is null");

        DiscountRuleDO discountRuleDO = discountRuleMapper.selectById(id);
        discountRuleDO.setStatus(status.name());
        discountRuleMapper.updateById(discountRuleDO);
    }

    @Override
    public List<DiscountRule> queryFirstMonthRulesByAppCode(String appCode) {
        Assertor.assertNotBlank(appCode, "appCode is blank");

        List<DiscountRuleDO> discountRuleDOS = queryValidList(wrapper -> wrapper
                .eq(DiscountRuleDO::getDiscountType, DiscountType.FIRST_MONTH)
                .eq(DiscountRuleDO::getAppCode, appCode)
                .eq(DiscountRuleDO::getStatus, DiscountRuleStatus.ENABLED.name())
        );

        return DiscountRuleConverter.INSTANCE.convertA2B(discountRuleDOS);
    }

    @Override
    public Map<Long, DiscountRule> queryFirstMonthRules(List<Long> planIds) {
        Assertor.assertNotEmpty(planIds, "planIds is empty");

        List<DiscountRuleDO> discountRuleDOS = queryValidList(wrapper -> wrapper
                .eq(DiscountRuleDO::getDiscountType, DiscountType.FIRST_MONTH)
                .eq(DiscountRuleDO::getEntityType, DiscountRuleEntityType.PLAN)
                .in(DiscountRuleDO::getEntityId, planIds)
                .eq(DiscountRuleDO::getStatus, DiscountRuleStatus.ENABLED.name())
        );

        return discountRuleDOS.stream().collect(Collectors.toMap(
                DiscountRuleDO::getEntityId,
                DiscountRuleConverter.INSTANCE::convertA2B,
                (v1, v2) -> v1
        ));
    }

    @Override
    public Map<Long, List<DiscountRule>> queryByPlanIds(List<Long> planIds, @Nullable DiscountRuleStatus status) {
        Assertor.assertNotEmpty(planIds, "planIds is empty");

        List<DiscountRuleDO> discountRuleDOS = queryValidList(wrapper -> wrapper
                .eq(DiscountRuleDO::getEntityType, DiscountRuleEntityType.PLAN)
                .in(DiscountRuleDO::getEntityId, planIds)
                .eq(status != null, DiscountRuleDO::getStatus, status)
        );

        List<DiscountRule> discountRules = DiscountRuleConverter.INSTANCE.convertA2B(discountRuleDOS);

        return discountRules.stream()
                .collect(Collectors.groupingBy(DiscountRule::getEntityId, Collectors.toList()));
    }

    @Override
    public Map<Long, DiscountRule> queryEnabledByPlanIds(List<Long> planIds) {
        Map<Long, List<DiscountRule>> planId2DiscountRules = queryByPlanIds(planIds, DiscountRuleStatus.ENABLED);

        Map<Long, DiscountRule> result = new HashMap<>();
        planId2DiscountRules.forEach((planId, discountRules) -> {
            if (CollectionUtil.isNotEmpty(discountRules)) {
                result.put(planId, discountRules.get(0));
            }
        });

        return result;
    }

    /**
     * 封装通用的查询逻辑, 减少重复设置, 防止遗漏公共过滤参数
     */
    private List<DiscountRuleDO> queryValidList(Consumer<LambdaQueryWrapper<DiscountRuleDO>> consumer) {
        LambdaQueryWrapper<DiscountRuleDO> queryWrapper = new LambdaQueryWrapper<DiscountRuleDO>()
                .eq(DiscountRuleDO::getDeleted, Boolean.FALSE);
        if (consumer != null) {
            consumer.accept(queryWrapper);
        }
        return discountRuleMapper.selectList(queryWrapper);
    }
}
