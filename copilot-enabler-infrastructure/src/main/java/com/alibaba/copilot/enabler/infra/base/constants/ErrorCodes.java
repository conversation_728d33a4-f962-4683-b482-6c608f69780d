package com.alibaba.copilot.enabler.infra.base.constants;

import com.alibaba.copilot.boot.basic.exception.ErrorCode;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/4
 */
public class ErrorCodes extends ErrorCode {
    /**
     * 账号模块
     */
    public static final ErrorCode NULL_USER_ID = new ErrorCode("USER0001", "user id is null!");
    public static final ErrorCode NULL_EMAIL = new ErrorCode("USER0002", "email is null!");
    public static final ErrorCode NULL_APP_CODE = new ErrorCode("USER0003", "app code is null!");
    public static final ErrorCode NULL_USER_APP_RELATION = new ErrorCode("USER0004", "save user app relation is null!");
    public static final ErrorCode NULL_APP_BIND_SOURCE = new ErrorCode("USER0005", "save user app relation bind source is null!");
    public static final ErrorCode USER_NOT_EXIST = new ErrorCode("USER0006", "the user is not exist!");

    /**
     * 订阅模块
     */
    public static final ErrorCode USER_NOT_SUBSCRIBE_APP = new ErrorCode("SUBS0001", "You have not activated this app yet!");

    public static final ErrorCode PLAN_NOT_EXIST = new ErrorCode("SUBS0002", "Failed to find the plan according to the plan number!");
    public static final ErrorCode NON_CAN_CANCEL_ORDER = new ErrorCode("SUBS0003", "There are no orders available for cancellation!");
    public static final ErrorCode DISCOUNT_INFO_ERROR = new ErrorCode("SUBS0004", "Failed to obtain discount information!");
    public static final ErrorCode ORDER_NO_TRADE_RECORD = new ErrorCode("SUBS0005", "No trade record for current order!");
    public static final ErrorCode ORDER_CALL_SHOPIFY_ERROR = new ErrorCode("SUBS0006", "Call shopify api error!");
    public static final ErrorCode SWITCH_WITH_SAME_PLAN = new ErrorCode("SUBS0007", "Can not switch with same plan!");

    /**
     * 支付
     */
    public static final ErrorCode PAYMENT_RECORD_ERROR = new ErrorCode("PAYM0001", "Failed to query order transaction history!");
    public static final ErrorCode PAYMENT_ORDERID_ERROR = new ErrorCode("PAYM0002", "order id can not null!");
    public static final ErrorCode PAYMENT_AE_ERROR = new ErrorCode("PAYM0003", "System error");
}
