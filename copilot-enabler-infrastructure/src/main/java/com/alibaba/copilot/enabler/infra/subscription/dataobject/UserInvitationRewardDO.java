package com.alibaba.copilot.enabler.infra.subscription.dataobject;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@TableName(value = "user_invitation_reward")
/**
 * 用户邀请奖励数据对象
 *
 * <AUTHOR>
 * @date 2025/2/21
 */
public class UserInvitationRewardDO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 获得奖励的用户ID
     */
    private Long userId;

    /**
     * 关联的邀请记录ID
     */
    private Long inviteId;

    /**
     * 奖励特性类型
     */
    private String rewardType;

    /**
     * 奖励数量
     */
    private Integer rewardAmount;

    /**
     * 消耗数量
     */
    private Integer rewardUsage;

    /**
     * 过期时间
     */
    private Date expiryTime;

    /**
     * 扩展属性
     */
    private String attributes;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getInviteId() {
        return inviteId;
    }

    public void setInviteId(Long inviteId) {
        this.inviteId = inviteId;
    }

    public String getRewardType() {
        return rewardType;
    }

    public void setRewardType(String rewardType) {
        this.rewardType = rewardType;
    }

    public Integer getRewardAmount() {
        return rewardAmount;
    }

    public void setRewardAmount(Integer rewardAmount) {
        this.rewardAmount = rewardAmount;
    }

    public Date getExpiryTime() {
        return expiryTime;
    }

    public void setExpiryTime(Date expiryTime) {
        this.expiryTime = expiryTime;
    }

    public String getAttributes() {
        return attributes;
    }

    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }

    public Integer getRewardUsage() {
        return rewardUsage;
    }

    public void setRewardUsage(Integer rewardUsage) {
        this.rewardUsage = rewardUsage;
    }
}