package com.alibaba.copilot.enabler.infra.payment.gateway;

import com.alibaba.copilot.boot.basic.exception.GatewayException;
import com.alibaba.copilot.enabler.domain.payment.dto.ShoplazzaSubscriptionResponse;
import com.alibaba.copilot.enabler.domain.payment.gateway.ShoplazzaGateway;
import com.alibaba.copilot.enabler.domain.payment.request.ShoplazzaSubscriptionRequest;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 店匠gateway
 *
 * <AUTHOR>
 * @date 2024/9/24 下午3:01
 */
@Service
@Slf4j
public class ShoplazzaGatewayImpl implements ShoplazzaGateway {

    private static final String ERROR_MSG = "status code not 200, code= %s, response= %s";
    private static final String RECURRING_APPLICATION_CHARGES_URL = "https://%s/openapi/2022-01/recurring_application_charges";
    private static final String CANCEL_RECURRING_APPLICATION_URL = "https://%s/openapi/2022-01/recurring_application_charges/%s";

    /**
     * 订阅
     *
     * @param shoplazzaDomain
     * @param accessToken
     * @param request
     * @return
     */
    @Override
    public ShoplazzaSubscriptionResponse subscribePlan(String shoplazzaDomain, String accessToken, ShoplazzaSubscriptionRequest request) {
        try {
            log.info("ShoplazzaGatewayImpl.subscribePlan, shoplazzaDomain={}, accessToken={}, request={}", shoplazzaDomain, accessToken, JSON.toJSONString(request));
            String recurringApplicationChargesUrl = String.format(RECURRING_APPLICATION_CHARGES_URL, shoplazzaDomain);
            Map<String, ShoplazzaSubscriptionRequest> paramMap = new HashMap<>();
            paramMap.put("recurring_application_charge", request);
            return doPost(recurringApplicationChargesUrl, accessToken, paramMap, ShoplazzaSubscriptionResponse.class);
        } catch (IOException e) {
            log.error("ShoplazzaGatewayImpl.subscribePlan exception, shoplazzaDomain={}, accessToken={}, request={}", shoplazzaDomain, accessToken, JSON.toJSONString(request), e);
            throw new GatewayException(e.getMessage());
        }
    }

    /**
     * 取消订阅
     *
     * @param shoplazzaDomain
     * @param accessToken
     * @param chargeId
     * @return
     */
    @Override
    public void cancelSubscribedPlan(String shoplazzaDomain, String accessToken, String chargeId) {
        try {
            log.info("ShoplazzaGatewayImpl.cancelSubscribedPlan, shoplazzaDomain={}, accessToken={}, chargeId={}", shoplazzaDomain, accessToken, chargeId);
            String cancelRecurringApplicationUrl = String.format(CANCEL_RECURRING_APPLICATION_URL, shoplazzaDomain, chargeId);
            doDelete(cancelRecurringApplicationUrl, accessToken, null);
        } catch (IOException e) {
            log.error("ShoplazzaGatewayImpl.cancelSubscribedPlan exception, shoplazzaDomain={}, accessToken={}, chargeId={}", shoplazzaDomain, accessToken, chargeId, e);
            throw new GatewayException(e.getMessage());
        }
    }

    /**
     * get 请求
     *
     * @param url
     * @param accessToken
     * @param clazz
     * @param <T>
     * @return
     * @throws IOException
     */
    private <T> T doGet(String url, String accessToken, Class<T> clazz) throws IOException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);
            httpGet.setHeader("accept", "application/json");
            httpGet.setHeader("content-type", "application/json");
            httpGet.setHeader("access-token", accessToken);

            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {

                int statusCode = response.getStatusLine().getStatusCode();
                HttpEntity entity = response.getEntity();
                String responseString = EntityUtils.toString(entity);
                if (statusCode == 200) {
                    return JSON.parseObject(responseString, clazz);
                } else {
                    log.error("ShoplazzaGatewayImpl.doGet error, url={}, accessToken={}, stateCode={}, response={}", url, accessToken, statusCode, responseString);
                    throw new GatewayException(String.format(ERROR_MSG, statusCode, responseString));
                }
            }
        }
    }

    /**
     * post请求
     *
     * @param url
     * @param accessToken
     * @param clazz
     * @param <T>
     * @return
     * @throws IOException
     */
    private <T> T doPost(String url, String accessToken, Object requestBody, Class<T> clazz) throws IOException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {

            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("accept", "application/json");
            httpPost.setHeader("content-type", "application/json");
            httpPost.setHeader("access-token", accessToken);

            httpPost.setEntity(new StringEntity(JSON.toJSONString(requestBody), "UTF-8"));

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {

                int statusCode = response.getStatusLine().getStatusCode();
                HttpEntity entity = response.getEntity();
                String responseString = EntityUtils.toString(entity);
                if (statusCode == 200) {
                    return JSON.parseObject(responseString, clazz);
                } else {
                    log.error("ShoplazzaGatewayImpl.doGet error, url={}, accessToken={}, stateCode={}, response={}", url, accessToken, statusCode, responseString);
                    throw new GatewayException(String.format(ERROR_MSG, statusCode, responseString));
                }
            }
        }
    }

    /**
     * delete请求
     *
     * @param url
     * @param accessToken
     * @param clazz
     * @param <T>
     * @return
     * @throws IOException
     */
    private <T> T doDelete(String url, String accessToken, Class<T> clazz) throws IOException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {

            HttpDelete httpDelete = new HttpDelete(url);
            httpDelete.setHeader("accept", "application/json");
            httpDelete.setHeader("content-type", "application/json");
            httpDelete.setHeader("access-token", accessToken);

            try (CloseableHttpResponse response = httpClient.execute(httpDelete)) {

                int statusCode = response.getStatusLine().getStatusCode();
                HttpEntity entity = response.getEntity();
                String responseString = EntityUtils.toString(entity);
                if (statusCode == 200) {
                    return JSON.parseObject(responseString, clazz);
                } else {
                    log.error("ShoplazzaGatewayImpl.doGet error, url={}, accessToken={}, stateCode={}, response={}", url, accessToken, statusCode, responseString);
                    throw new GatewayException(String.format(ERROR_MSG, statusCode, responseString));
                }
            }
        }
    }
}
