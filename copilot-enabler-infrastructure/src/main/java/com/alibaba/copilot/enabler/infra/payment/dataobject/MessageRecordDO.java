package com.alibaba.copilot.enabler.infra.payment.dataobject;

import com.alibaba.copilot.enabler.infra.base.bean.BaseDO;
import com.alibaba.copilot.enabler.infra.base.utils.EnvUtils;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;


/**
 * 本地消息表
 */
@Setter
@Getter
@TableName(value = "message_record")
public class MessageRecordDO extends BaseDO {

    /**
     * 事件的唯一标识（对应eventId，一般使用UUID）
     */
    private String messageId;

    /**
     * 消息方向（值为in或者out，即发送的消息or接收的消息）
     */
    private String direction;

    /**
     * App code 业务身份
     */
    private String appCode;

    /**
     * 实体id（表明本事件是由那个具体的实体发出的）
     */
    private String entityId;

    /**
     * 用户ID（冗余字段，方便快速筛选）
     */
    private String userId;

    /**
     * 事件类型（描述了业务的具体行为，如发起支付）
     */
    private String messageType;

    /**
     * 事件数据（消息内容的具体数据或rpc/http调用的param）
     */
    private String messageData;

    /**
     * 事件状态（待发送、发送/接收成功、发送/接收失败）
     */
    private String status;

    /**
     * 重试次数，记录该事件已经尝试发送的次数。
     */
    private Integer retryCount;

    /**
     * rpc/http调用信息（url、httpMethod或bean、method）
     */
    private String callInfo;

    /**
     * rpc/http响应内容
     */
    private String callResponse;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 环境
     */
    private String env = EnvUtils.getEnv();

    /**
     * 属性（JSON格式）
     */
    private String attributes;
}
