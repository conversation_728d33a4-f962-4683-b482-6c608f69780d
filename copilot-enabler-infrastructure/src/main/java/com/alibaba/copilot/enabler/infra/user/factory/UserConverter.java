package com.alibaba.copilot.enabler.infra.user.factory;

import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.model.UserAttributes;
import com.alibaba.copilot.enabler.infra.user.dataobject.UserDO;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/4
 */
public class UserConverter implements Converter<UserDO, User> {
    public static final Converter<UserDO, User> INSTANCE = new UserConverter();

    @Override
    public User convertA2B(UserDO userDO) {
        if (userDO == null) {
            return null;
        }

        return User.builder()
                .id(userDO.getId())
                .gmtCreate(userDO.getGmtCreate())
                .userId(userDO.getUserId())
                .email(userDO.getEmail())
                .appCode(userDO.getAppCode())
                .signUpChannel(userDO.getSignUpChannel())
                .attributes(new UserAttributes(userDO.getAttributes()))
                .build();
    }

    @Override
    public UserDO convertB2A(User user) {
        if (user == null) {
            return null;
        }

        UserDO userDO = new UserDO();
        userDO.setId(user.getId());
        userDO.setGmtCreate(user.getGmtCreate());
        userDO.setUserId(user.getUserId());
        userDO.setEmail(user.getEmail());
        userDO.setAppCode(user.getAppCode());
        userDO.setSignUpChannel(user.getSignUpChannel());
        userDO.setGmtCreate(user.getUserRegistrationTime());
        userDO.setAttributes(user.getAttributes().toString());

        return userDO;
    }
}
