package com.alibaba.copilot.enabler.infra.subscription.dataobject;

import com.alibaba.copilot.enabler.infra.base.bean.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;


/**
 * 订阅计划-计划特性关联表
 */
@Setter
@Getter
@TableName(value = "subscription_plan_feature")
public class SubscriptionPlanFeatureDO extends BaseDO {
    /**
     * 计划ID
     */
    private Long subscriptionPlanId;

    /**
     * 特性ID
     */
    private Long subscriptionFeatureId;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 属性（JSON格式）
     */
    private String attributes;
}
