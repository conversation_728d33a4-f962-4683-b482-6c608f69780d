package com.alibaba.copilot.enabler.infra.user.dataobject;

import com.alibaba.copilot.enabler.infra.base.bean.BaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/30
 */
@Setter
@Getter
@TableName(value = "user_account")
public class UserDO extends BaseDO {
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 注册来源
     */
    @TableField(value = "sign_up_source")
    private String appCode;

    /**
     * 注册渠道
     */
    private String signUpChannel;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 拓展字段（JSON格式）
     */
    private String attributes;
}
