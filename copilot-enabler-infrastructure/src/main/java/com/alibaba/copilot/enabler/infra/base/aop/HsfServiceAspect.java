package com.alibaba.copilot.enabler.infra.base.aop;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.client.user.constants.UserAppBindStatusEnum;
import com.alibaba.copilot.enabler.client.user.request.UserRegisterRequest;
import com.alibaba.copilot.enabler.client.user.service.UserRegisterService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/10/8
 */
@Slf4j
@Aspect
@Component
public class HsfServiceAspect {

    @Resource
    private UserRegisterService userRegisterService;

    @Before("execution(* com.alibaba.copilot.enabler.client..*.facade.*.*(..)) && " +
            "!execution(* com.alibaba.copilot.enabler.client.user.facade.UserRegisterHsfApi.*(..))" +
            "!execution(* com.alibaba.copilot.enabler.client.email.facade.EmailSendHsfApi.*(..))")
    public void preRegister(JoinPoint joinPoint) {
        try {
            doPreRegister(joinPoint);
        } catch (NoSuchFieldException ignored) {
        } catch (Exception e) {
            log.warn("preRegister, invoke error", e);
        }
    }

    private void doPreRegister(JoinPoint joinPoint) throws NoSuchFieldException {
        Object[] args = joinPoint.getArgs();
        log.info("doPreRegister, args={}", JSON.toJSONString(args));
        for (Object arg : args) {
            if (arg == null) {
                continue;
            }

            AppEnum app = getAppCode(arg);
            Long userId = getUserId(arg);
            if (app == null || userId == null) {
                // 取不到appCode时, 不做额外处理
                continue;
            }

            if (AppEnum.SEO_COPILOT == app || AppEnum.SEO_COPILOT_SITE == app) {
                continue;
            }

            UserRegisterRequest registerRequest = new UserRegisterRequest();

            registerRequest.setUserId(userId);
            registerRequest.setAppCode(app.getCode());
            registerRequest.setAppName(app.getName());
            registerRequest.setAppType(app.getType());
            String userEmail = getUserEmail(arg);
            registerRequest.setEmail(userEmail);

            registerRequest.setBindSource(app.getCode());
            registerRequest.setSignUpSource(app.getCode());
            registerRequest.setSignUpChannel(app.getCode());
            registerRequest.setBindStatus(UserAppBindStatusEnum.BINDING.value());

            Long userRegisterTime = getUserRegisterTime(arg);
            if (userRegisterTime != null && userRegisterTime > 0) {
                registerRequest.setRegisterTime(new Date(userRegisterTime));
            }

            userRegisterService.registerForSubscription(registerRequest);
        }
    }

    private Long getUserId(Object arg) {
        Long userId = null;
        try {
            userId = (Long) ReflectUtil.getFieldValue(arg, "userId");
            Assertor.assertNonNull(userId, "userId is null!");
        } catch (Exception e) {
            log.info("Can't find `userId` field");
        }
        return userId;
    }

    private AppEnum getAppCode(Object arg) {
        try {
            String appCode = (String) ReflectUtil.getFieldValue(arg, "appCode");
            if (StringUtils.isNotEmpty(appCode)) {
                return AppEnum.getAppByCode(appCode);
            }
        } catch (Exception e) {
            log.info("Can't find `appCode` field");
        }
        return null;
    }

    private Long getUserRegisterTime(Object arg) {
        try {
            return (Long) ReflectUtil.getFieldValue(arg, "userRegistrationTime");
        } catch (Exception e) {
            log.info("Can't find `userRegistrationTime` field");
        }
        return null;
    }

    private String getUserEmail(Object arg) {
        try {
            return (String) ReflectUtil.getFieldValue(arg, "email");
        } catch (Exception e) {
            log.info("Can't find `email` field");
        }
        return null;
    }
}
