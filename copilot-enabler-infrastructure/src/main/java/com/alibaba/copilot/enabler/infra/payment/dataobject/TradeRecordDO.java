package com.alibaba.copilot.enabler.infra.payment.dataobject;

import com.alibaba.copilot.enabler.infra.base.bean.BaseDO;
import com.alibaba.copilot.enabler.infra.base.utils.EnvUtils;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 付费记录表
 */
@Setter
@Getter
@TableName(value = "trade_record")
public class TradeRecordDO extends BaseDO {

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 订单ID
     */
    private Long subscriptionOrderId;

    /**
     * App code 业务身份
     */
    private String appCode;

    /**
     * 支付类型（用户主动支付、系统定期代扣）
     */
    private String paymentType;

    /**
     * 本流水中交易最终金额
     */
    private BigDecimal tradeAmount;

    /**
     * 币种
     */
    private String tradeCurrency;

    /**
     * 交易方向
     */
    private String tradeDirection;

    /**
     * 支付方式（Credit Card、Google Pay、Paypal、Alipay）
     */
    private String paymentMethod;

    /**
     * 交易时间
     */
    private Date tradeTime;

    /**
     * 状态
     */
    private String status;

    /**
     * 税费
     */
    private BigDecimal taxAmount;

    /**
     * 税费币种
     */
    private String taxCurrency;

    /**
     * 手续费
     */
    private BigDecimal transactionAmount;

    /**
     * 手续费币种
     */
    private String transactionCurrency;

    /**
     * 唯一流水id
     */
    @TableField(value = "trade_id")
    private String tradeNo;

    /**
     * 外部流水id
     */
    @TableField(value = "out_trade_id")
    private String outTradeNo;

    /**
     * 是否已发起支付
     */
    private Boolean hadInitiatePay;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 环境
     */
    private String env = EnvUtils.getEnv();

    /**
     * 属性（JSON格式）
     */
    private String attributes;
}
