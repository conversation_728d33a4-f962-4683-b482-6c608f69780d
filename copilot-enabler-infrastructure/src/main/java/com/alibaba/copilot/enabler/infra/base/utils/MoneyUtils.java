package com.alibaba.copilot.enabler.infra.base.utils;

import cn.hutool.core.math.Money;

import java.math.BigDecimal;

/**
 * money相关util
 */
public class MoneyUtils {

    /**
     * BigDecimal转换成分
     */
    public static long convertBigDecimalToCents(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            throw new IllegalArgumentException("BigDecimal cannot be null.");
        }
        Money money = new Money(bigDecimal);
        return money.getCent();
    }

    /**
     * 判断两个BigDecimal是否相等
     */
    public static boolean isEqualBigDecimal(BigDecimal bigDecimalA, BigDecimal bigDecimalB) {
        if (bigDecimalA == null || bigDecimalB == null) {
            return false;
        }
        return convertBigDecimalToCents(bigDecimalA) == convertBigDecimalToCents(bigDecimalB);
    }
}
