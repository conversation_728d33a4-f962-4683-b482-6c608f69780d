package com.alibaba.copilot.enabler.infra.email.service;

import com.alibaba.copilot.edm.client.seocopilot.EdmDynamicClient;
import com.alibaba.copilot.edm.client.seocopilot.request.EdmActivityTriggerRequest;
import com.alibaba.copilot.enabler.domain.base.email.EdmService;
import org.springframework.stereotype.Service;

@Service
public class EdmServiceImpl implements EdmService {
    public void triggerEmail(Long id, String email, Object params ) {
        EdmActivityTriggerRequest request = new EdmActivityTriggerRequest()
                .setToEmail(email)
                .setId(id)
                .setParams(params);
        EdmDynamicClient edmDynamicClient = new EdmDynamicClient();
        edmDynamicClient.sendEmail(request);
    }

    public void triggerEmail(EdmActivityTriggerRequest request) {
        EdmDynamicClient edmDynamicClient = new EdmDynamicClient();
        edmDynamicClient.sendEmail(request);
    }
}
