package com.alibaba.copilot.enabler.infra.payment.factory;

import com.alibaba.aepay.fund.business.api.payment.dto.*;
import com.alibaba.aepay.fund.business.api.payment.request.PaymentRequest;
import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.domain.subscription.request.InitiatePaymentRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 * @version 2024/1/15
 */
@Slf4j
@AllArgsConstructor
public class CashierPaymentRequestBuilder implements Builder<PaymentRequest> {

    private final InitiatePaymentRequest request;

    @Override
    public PaymentRequest build() {
        PaymentRequest paymentRequest = new PaymentRequest();

        paymentRequest.setEnv(buildEnv());

        // 基础信息
        paymentRequest.setIdentityCode(request.getAppEnum().getAePayIdentityCode().name());
        paymentRequest.setPaymentRequestId(request.getPaymentRequestId());
        paymentRequest.setPaymentAmount(request.getPaymentAmount());
        paymentRequest.setPaymentRedirectUrl(request.getPaymentRedirectUrl());

        // 订单信息
        paymentRequest.setOrder(request.getOrder());

        // 支付策略
        paymentRequest.setSettlementStrategy(buildSettlementStrategy());

        // 根据不同的支付方式设置对应的参数信息
        // 参: https://aliyuque.antfin.com/global.finnet/ofst5q/sf7gcmyek9anqgwl#CDnhN
        PaymentMethodEnum paymentMethod = request.getPaymentMethod();
        paymentRequest.setProductCode(AEPaymentConstants.PRODUCT_CODE_FOR_CASHIER);
        if (PaymentMethodEnum.isAlipayPaymentMethod(paymentMethod)) {
            fillAlipayRequest(paymentRequest);
        } else if (paymentMethod == PaymentMethodEnum.CREDIT_CARD) {
            fillCardRequest(paymentRequest);
        } else {
            log.error("paymentMethod not support: {}", paymentMethod);
            throw new IllegalArgumentException("paymentMethod not support: " + paymentMethod);
        }

        return paymentRequest;
    }


    private void fillAlipayRequest(PaymentRequest paymentRequest) {
        paymentRequest.setPaymentMethod(buildPaymentMethodForAlipay());
    }

    private void fillCardRequest(PaymentRequest paymentRequest) {
        paymentRequest.setPaymentMethod(buildPaymentMethodForCard());
        PaymentFactor paymentFactor = buildPaymentFactor();
        paymentRequest.setPaymentFactor(paymentFactor);
    }

    @NotNull
    private static Env buildEnv() {
        Env env = new Env();
        env.setTerminalType(AEPaymentConstants.DEFAULT_TERMINAL_TYPE);
        return env;
    }

    @NotNull
    private static PaymentFactor buildPaymentFactor() {
        PaymentFactor paymentFactor = new PaymentFactor();
        paymentFactor.setIsAuthorization(true);
        return paymentFactor;
    }

    @NotNull
    private static SettlementStrategy buildSettlementStrategy() {
        SettlementStrategy settlementStrategy = new SettlementStrategy();
        settlementStrategy.setSettlementCurrency(AEPaymentConstants.CURRENCY_CODE);
        return settlementStrategy;
    }

    @NotNull
    private PaymentMethod buildPaymentMethodForCard() {
        PaymentMethod paymentMethod = new PaymentMethod();
        paymentMethod.setPaymentMethodType(PaymentMethodEnum.CREDIT_CARD.getValue());
        paymentMethod.setPaymentMethodId(request.getCardToken());

        PaymentMethodMetaData paymentMethodMetaData = new PaymentMethodMetaData();
        paymentMethodMetaData.setIs3DSAuthentication(false);
        paymentMethodMetaData.setIsCardOnFile(true);
        paymentMethodMetaData.setRecurringType(AEPaymentConstants.RECURRING_TYPE);
        paymentMethodMetaData.setNetworkTransactionId(request.getNetworkTransactionId());
        paymentMethodMetaData.setEnableAuthenticationUpgrade(false);
        paymentMethod.setPaymentMethodMetaData(paymentMethodMetaData);
        return paymentMethod;
    }

    @NotNull
    private PaymentMethod buildPaymentMethodForAlipay() {
        PaymentMethod paymentMethod = new PaymentMethod();
        paymentMethod.setPaymentMethodType(request.getPaymentMethod().name());
        return paymentMethod;
    }
}
