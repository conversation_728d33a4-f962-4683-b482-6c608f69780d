package com.alibaba.copilot.enabler.infra.payment.factory;

import com.alibaba.aepay.fund.business.api.payment.dto.Result;
import com.alibaba.aepay.fund.business.api.payment.response.RefundResponse;
import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.payment.dto.RefundResultDTO;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.global.money.Money;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 2024/1/15
 */
@AllArgsConstructor
public class RefundResultDTOResultBuilder implements Builder<SingleResult<RefundResultDTO>> {

    private final RefundResponse refundResponse;

    @Override
    public SingleResult<RefundResultDTO> build() {
        BigDecimal refundAmount = Optional.ofNullable(refundResponse.getRefundAmount())
                .map(Money::getAmount)
                .orElse(null);
        RefundResultDTO refundResultDTO = new RefundResultDTO()
                .setPaymentId(refundResponse.getPaymentId())
                .setRefundAmount(refundAmount)
                .setRefundId(refundResponse.getPaymentId())
                .setRefundRequestId(refundResponse.getRefundRequestId())
                .setRefundTime(refundResponse.getRefundTime());

        // 此处特殊处理, 刚调用完退款时, 不会立刻成功
        Result result = refundResponse.getResult();
        if (isCallSuccess(result)) {
            return SingleResult.buildSuccess(refundResultDTO);
        }

        //noinspection unchecked
        SingleResult<RefundResultDTO> failureResult = SingleResult.buildFailure(
                result.getResultCode(), result.getResultMessage());
        // 失败时, 也把data返回出去 (如有)
        failureResult.setData(refundResultDTO);
        return failureResult;
    }

    /**
     * 判断调用是否成功
     */
    private static boolean isCallSuccess(Result result) {
        String resultStatus = result.getResultStatus();
        boolean isSuccessStatus = AEPaymentConstants.RESULT_STATUS_SUCCESS.equals(resultStatus);
        boolean isPendingStatus = AEPaymentConstants.RESULT_STATUS_UNKNOWN.equals(resultStatus)
                && AEPaymentConstants.REFUND_RESULT_CODE_IN_PROCESS.equals(result.getResultCode());
        return isSuccessStatus || isPendingStatus;
    }
}
