package com.alibaba.copilot.enabler.infra.subscription.mapper;

import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionOrderDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface SubscriptionOrderMapper extends BaseMapper<SubscriptionOrderDO> {

    /**
     * 删除下一笔套餐的信息 (删除nextPlanId和nextPlanName)
     *
     * @param orderId 订单ID
     * @return 操作影响的数据行
     */
    @Update("update `subscription_order` set `next_plan_id` = null, `next_plan_name` = null where `id` = #{orderId}")
    int clearNextPlan(@Param("orderId") Long orderId);
}
