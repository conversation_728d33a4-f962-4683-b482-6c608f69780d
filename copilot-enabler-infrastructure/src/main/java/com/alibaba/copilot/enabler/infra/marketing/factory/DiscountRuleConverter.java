package com.alibaba.copilot.enabler.infra.marketing.factory;

import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.copilot.enabler.client.marketing.constant.DiscountRuleEntityType;
import com.alibaba.copilot.enabler.client.marketing.constant.DiscountRuleStatus;
import com.alibaba.copilot.enabler.client.marketing.constant.DiscountType;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.marketing.model.DiscountRule;
import com.alibaba.copilot.enabler.domain.marketing.model.DiscountRuleAttributes;
import com.alibaba.copilot.enabler.infra.marketing.dataobject.DiscountRuleDO;
import org.springframework.beans.BeanUtils;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @version 2024/2/26
 */
public class DiscountRuleConverter implements Converter<DiscountRuleDO, DiscountRule> {

    public static final DiscountRuleConverter INSTANCE = new DiscountRuleConverter();

    private static final String[] ignoreProperties = new String[]{
            "appCode",
            "status",
            "discountType",
            "entityType",
            "attributes",
    };

    @Override
    public DiscountRule convertA2B(DiscountRuleDO discountRuleDO) {
        if (discountRuleDO == null) {
            return null;
        }

        DiscountRule result = new DiscountRule();
        BeanUtils.copyProperties(discountRuleDO, result, ignoreProperties);

        ifExists(discountRuleDO.getAppCode(), appCode -> result.setAppCode(AppEnum.getAppByCode(appCode)));
        ifExists(discountRuleDO.getStatus(), status -> result.setStatus(DiscountRuleStatus.of(status)));
        ifExists(discountRuleDO.getDiscountType(), discountType -> result.setDiscountType(DiscountType.of(discountType)));
        ifExists(discountRuleDO.getEntityType(), entityType -> result.setEntityType(DiscountRuleEntityType.of(entityType)));
        ifExists(discountRuleDO.getAttributes(), attributes -> result.setAttributes(new DiscountRuleAttributes(attributes)));

        return result;
    }

    @Override
    public DiscountRuleDO convertB2A(DiscountRule discountRule) {
        if (discountRule == null) {
            return null;
        }

        DiscountRuleDO result = new DiscountRuleDO();
        result.setDeleted(false);
        BeanUtils.copyProperties(discountRule, result, ignoreProperties);

        ifExists(discountRule.getAppCode(), appCode -> result.setAppCode(appCode.getCode()));
        ifExists(discountRule.getStatus(), status -> result.setStatus(status.name()));
        ifExists(discountRule.getDiscountType(), discountType -> result.setDiscountType(discountType.name()));
        ifExists(discountRule.getEntityType(), entityType -> result.setEntityType(entityType.name()));
        ifExists(discountRule.getAttributes(), attributes -> result.setAttributes(attributes.toString()));

        return result;
    }

    private static <T> void ifExists(T subject, Consumer<T> consumer) {
        Optional.ofNullable(subject).ifPresent(consumer);
    }
}
