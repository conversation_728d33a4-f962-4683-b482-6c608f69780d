package com.alibaba.copilot.enabler.infra.base.metaq;

import com.alibaba.copilot.boot.event.eventbus.annotation.Event;
import com.alibaba.copilot.boot.event.eventbus.model.EventContext;
import com.alibaba.copilot.boot.event.eventbus.model.EventException;
import com.alibaba.copilot.boot.event.mqueue.MessageInfo;
import com.alibaba.copilot.boot.event.mqueue.MessageSendResult;
import com.alibaba.copilot.boot.event.mqueue.metaq.MessageConverter;
import com.alibaba.copilot.enabler.client.base.event.DomainEventJsonProducer;
import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.common.message.Message;
import com.taobao.metaq.client.MetaProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Objects;
import java.util.UUID;

@Component
@Slf4j
public class MetaqMessageProducer implements DomainEventJsonProducer {
    private final MetaProducer metaProducer;

    private final String topic;

    @Autowired
    public MetaqMessageProducer(MetaqMessageProperties messageProperties) throws MQClientException {
        this.metaProducer = new MetaProducer(messageProperties.getTopic());
        this.topic = messageProperties.getTopic();

        if (StringUtils.isNotBlank(messageProperties.getProducerUnitName())) {
            this.metaProducer.setUnitName(messageProperties.getProducerUnitName());
        }

        if (StringUtils.isNotBlank(messageProperties.getProducerInstanceName())) {
            this.metaProducer.setInstanceName(messageProperties.getProducerInstanceName());
        }

        this.metaProducer.start();
    }

    @Override
    public boolean publish(Object event) {
        Assert.notNull(event, "event can not be null");
        EventContext eventContext = new EventContext();
        eventContext.setTopic(topic);
        eventContext.setEventObject(event);
        Event eventConfig = event.getClass().getAnnotation(Event.class);
        if (Objects.nonNull(eventConfig)) {
            eventContext.setDelayLevel(eventConfig.delayLevel().getLevel());
            eventContext.setEventId(eventConfig.id());
        }

        MessageInfo messageInfo = this.toFastjsonMessage(eventContext);
        Message message = MessageConverter.toMessage(messageInfo);

        try {
            SendResult result = this.metaProducer.send(message);
            log.info("MetaqMessageProducer send message: topic={}, msgKey={}, delayLevel={}, sendResult={}",
                    message.getTopic(), messageInfo.getKey(), message.getDelayTimeLevel(), result);
            MessageSendResult sendResult = MessageSendResult.of(result.getMsgId(), true);
            return sendResult.isSuccess();
        } catch (Throwable var5) {
            String msg = String.format("MetaqProducer send message fail, msgKey=%s", messageInfo.getKey());
            log.error(msg, var5);
            throw new EventException(msg, var5);
        }
    }

    private MessageInfo toFastjsonMessage(EventContext eventContext) {
        Assert.isTrue(StringUtils.isNotBlank(eventContext.getTopic()), "topic is blank");
        byte[] body = FastjsonSerializer.INSTANCE.serialize(eventContext);
        MessageInfo message = new MessageInfo(
                UUID.randomUUID().toString(),
                eventContext.getTopic(),
                eventContext.getEventId(),
                body
        );
        message.setMessageId(eventContext.getAttribute(EventContext.ATTR_MESSAGE_ID));
        message.setDelayTimeLevel(eventContext.getDelayLevel());
        message.getProperties().putAll(eventContext.getAttributes());
        return message;
    }
}
