package com.alibaba.copilot.enabler.infra.subscription.dataobject;

import com.alibaba.copilot.enabler.infra.base.bean.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;


/**
 * 订阅计划-特性表
 */
@Setter
@Getter
@TableName(value = "subscription_feature")
public class SubscriptionFeatureDO extends BaseDO {
    /**
     * 产品唯一code
     */
    private String appCode;

    /**
     * 特性名称
     */
    private String name;

    /**
     * 特性描述
     */
    private String description;

    /**
     * 特性类型
     */
    private String type;

    /**
     * 是否消耗型特性
     */
    private Boolean isDepletion;

    /**
     * 权益信息（json）
     */
    private String benefit;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 属性（JSON格式）
     */
    private String attributes;
}
