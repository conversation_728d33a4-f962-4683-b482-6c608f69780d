package com.alibaba.copilot.enabler.infra.base.oss;

import com.alibaba.normandy.credential.Credential;
import com.alibaba.normandy.credential.CredentialProvider;
import com.alibaba.normandy.credential.ResourceNames;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2023/6/25
 */
@Configuration
public class OssConfiguration {

    @Value("${algorithm_image_oss_service_endpoint}")
    private String endpoint = "oss-ap-southeast-1.aliyuncs.com";

    @Value("${algorithm_image_oss_service_bucket_name}")
    private String bucketName = "t-selection-algorithms-image";

    @Resource
    private CredentialProvider credentialProvider;

    @Bean
    public OSS oss() {
        String resourceName = ResourceNames.ofAliyunOssBucketName(bucketName);
        Credential credential = credentialProvider.getCredential(resourceName);

        return new OSSClientBuilder().build(
                endpoint, credential.getAccessKeyId(), credential.getAccessKeySecret()
        );
    }
}
