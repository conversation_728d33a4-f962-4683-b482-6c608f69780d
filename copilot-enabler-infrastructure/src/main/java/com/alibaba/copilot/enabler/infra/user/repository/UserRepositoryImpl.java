package com.alibaba.copilot.enabler.infra.user.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.domain.base.utils.RedisUtils;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import com.alibaba.copilot.enabler.domain.user.request.UserQuery;
import com.alibaba.copilot.enabler.infra.base.constants.ErrorCodes;
import com.alibaba.copilot.enabler.infra.user.dataobject.UserDO;
import com.alibaba.copilot.enabler.infra.user.factory.UserConverter;
import com.alibaba.copilot.enabler.infra.user.mapper.UserMapper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/4
 */
@Repository
public class UserRepositoryImpl implements UserRepository {

    @Resource
    private UserMapper userMapper;

    private static final String GET_USER_CACHE_PREFIX = "get:user:cache";
    private static final Long USER_CACHE_EXPIRE = 60 * 60 * 24L;

    @Override
    public User getUser(Long userId) {
        Assertor.assertNonNull(userId, ErrorCodes.NULL_USER_ID);
        User user = null;

        String userInfo = RedisUtils.get(GET_USER_CACHE_PREFIX + userId);
        if (StringUtils.isNotBlank(userInfo)) {
            user = JSONObject.parseObject(userInfo, User.class);
        }

        if (user == null) {
            LambdaQueryWrapper<UserDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserDO::getUserId,  userId);
            queryWrapper.eq(UserDO::getDeleted, Boolean.FALSE);
            List<UserDO> users = userMapper.selectList(queryWrapper);
            UserDO firstUser = CollectionUtil.getFirst(users);
            user = UserConverter.INSTANCE.convertA2B(firstUser);
        }

        if (user != null) {
            RedisUtils.setex(GET_USER_CACHE_PREFIX + userId, USER_CACHE_EXPIRE, JSON.toJSONString(user));
        }

        return user;
    }

    @Override
    public User getUser(String appCode, Long userId) {
        Assertor.assertNonNull(userId, ErrorCodes.NULL_USER_ID);

        LambdaQueryWrapper<UserDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserDO::getUserId,  userId);
        queryWrapper.eq(UserDO::getDeleted, Boolean.FALSE);
        queryWrapper.eq(UserDO::getAppCode, appCode);
        List<UserDO> users = userMapper.selectList(queryWrapper);
        UserDO firstUser = CollectionUtil.getFirst(users);
        User user = UserConverter.INSTANCE.convertA2B(firstUser);

        if (user != null) {
            RedisUtils.setex(GET_USER_CACHE_PREFIX + userId, USER_CACHE_EXPIRE, JSON.toJSONString(user));
        }

        return user;
    }

    @Override
    public User createUser(User user) {
        Assertor.assertNonNull(user, "save user entity is null!");

        UserDO userDO = UserConverter.INSTANCE.convertB2A(user);
        userDO.setGmtModified(new Date());
        userDO.setDeleted(Boolean.FALSE);
        userMapper.insert(userDO);

        return UserConverter.INSTANCE.convertA2B(userDO);
    }

    @Override
    public User updateUser(User user) {
        Assertor.assertNonNull(user, "user cannot be null.");
        Assertor.assertNonNull(user.getUserId(), "userId cannot be null.");

        UserDO userDO = UserConverter.INSTANCE.convertB2A(user);
        userDO.setGmtModified(new Date());
        userDO.setDeleted(Boolean.FALSE);
        userMapper.updateById(userDO);

        if (RedisUtils.exist(GET_USER_CACHE_PREFIX + user.getUserId())) {
            RedisUtils.delete(GET_USER_CACHE_PREFIX + user.getUserId());
        }
        return UserConverter.INSTANCE.convertA2B(userDO);
    }

    @Override
    public User queryUser(UserQuery query) {
        Assertor.assertNonNull(query, "query user request is null!");
        Assertor.assertNotBlank(query.getEmail(), ErrorCodes.NULL_EMAIL);

        QueryWrapper<UserDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("email", query.getEmail());
        UserDO userDO = userMapper.selectOne(queryWrapper);

        return UserConverter.INSTANCE.convertA2B(userDO);
    }

    @Override
    public User queryUserByParams(UserQuery query) {
        Assertor.assertNonNull(query, "query user request is null!");

        if (query.getUserId() == null && StringUtils.isBlank(query.getEmail())) {
            return null;
        }

        QueryWrapper<UserDO> queryWrapper = new QueryWrapper<>();
        if (query.getUserId() != null) {
            queryWrapper.eq("user_id", query.getUserId());
        }
        if (StringUtils.isNotBlank(query.getEmail())) {
            queryWrapper.eq("email", query.getEmail());
        }
        UserDO userDO = userMapper.selectOne(queryWrapper);

        return UserConverter.INSTANCE.convertA2B(userDO);
    }

    @Override
    public List<User> allUsers() {
        List<User> users = new ArrayList<>();
        List<UserDO> userDOS = userMapper.selectList(null);
        for (UserDO userDO : userDOS) {
            User user = UserConverter.INSTANCE.convertA2B(userDO);
            users.add(user);
        }
        return users;
    }
}
