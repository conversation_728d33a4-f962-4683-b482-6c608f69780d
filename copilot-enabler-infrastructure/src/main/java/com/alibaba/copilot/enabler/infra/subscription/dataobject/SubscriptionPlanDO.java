package com.alibaba.copilot.enabler.infra.subscription.dataobject;

import com.alibaba.copilot.enabler.infra.base.bean.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;


/**
 * 订阅计划-计划表
 */
@Setter
@Getter
@TableName(value = "subscription_plan")
public class SubscriptionPlanDO extends BaseDO {
    /**
     * 产品唯一code
     */
    private String appCode;

    /**
     * 计划名称
     */
    private String name;

    /**
     * 计划描述
     */
    private String description;

    /**
     * 计划价格（原价）
     */
    private BigDecimal price;

    /**
     * 计划周期
     */
    private Long duration;

    /**
     * 周期单位
     */
    private String durationUnit;

    /**
     * 是否可以试用
     */
    private Boolean isHasTrial;

    /**
     * 试用周期
     */
    private Long trialDuration;

    /**
     * 试用周期单位
     */
    private String trialDurationUnit;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 属性（JSON格式）
     */
    private String attributes;
}
