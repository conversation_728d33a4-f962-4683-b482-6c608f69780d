package com.alibaba.copilot.enabler.infra.base.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.FastTimeZone;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/4/28
 */
@Slf4j
public class DateUtils {
    public final static String ZERO_TIMEZONE = "GMT+00:00";

    public final static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public final static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    public final static String UTC_DATE = "yyyy-MM-dd'T'HH:mm:ss'Z'";
    public final static String MM_DD_YYYY = "MM-dd-yyyy";
    public final static String MM_DD = "MM/dd";
    public final static String UTC_ID = "UTC";

    /**
     * 时间戳转 LocalDateTime
     *
     * @param timestamp 时间戳
     * @return 时间
     */
    public static LocalDateTime ofTimestamp(long timestamp) {
        return ofEpochSecond(timestamp / 1000);
    }

    public static long convert2Timestamp(LocalDateTime dateTime) {
        return dateTime.toEpochSecond(ZoneOffset.ofHours(8)) * 1000;
    }

    public static Date convert2Date(long timestamp) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
        dateFormat.setTimeZone(TimeZone.getTimeZone(ZERO_TIMEZONE));
        try {
            return dateFormat.parse(dateFormat.format(new Date(timestamp * 1000L)));
        } catch (ParseException e) {
            log.error("convert2Date error: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 秒转 LocalDateTime
     *
     * @param epochSecond the number of seconds from the epoch of 1970-01-01T00:00:00Z
     * @return 时间
     */
    public static LocalDateTime ofEpochSecond(long epochSecond) {
        return LocalDateTime.ofEpochSecond(epochSecond, 0, ZoneOffset.ofHours(8));
    }

    public static TimeZone getZeroTimeZone() {
        return FastTimeZone.getGmtTimeZone(ZERO_TIMEZONE);
    }

    public static ZoneId getZeroZoneId() {
        return FastTimeZone.getGmtTimeZone(ZERO_TIMEZONE).toZoneId();
    }

    /**
     * 获取两个时间之间的所有日期
     *
     * @param start 开始时间
     * @param end   结束时间
     * @return 中间的所有日期（包括开始和结束）
     */
    public static List<LocalDateTime> getBetweenDate(LocalDateTime start, LocalDateTime end) {
        // 用起始时间作为流的源头，按照每次加一天的方式创建一个无限流
        return Stream.iterate(start, localDate -> localDate.plusDays(1))
                // 截断无限流，长度为起始时间和结束时间的差+1个
                .limit(ChronoUnit.DAYS.between(start, end) + 1)
                .collect(Collectors.toList());
    }

    public static String dateToUtcString(Date date) {
        return DateFormatUtils.format(date, UTC_DATE, TimeZone.getTimeZone(UTC_ID));
    }

    public static Date utcStringToDate(String utcDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(UTC_DATE);
            ZonedDateTime utcDateTime = ZonedDateTime.parse(utcDate, formatter.withZone(ZoneId.of(UTC_ID)));
            return Date.from(utcDateTime.withZoneSameInstant(ZoneId.systemDefault()).toInstant());
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 格式化
     *
     * @param date
     * @param format
     * @return
     */
    public static String format(Date date, String format) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        return simpleDateFormat.format(date);
    }

    /**
     * 计算间隔年数
     * @param dateString yyyy-MM-dd
     * @return
     */
    public static int calculateYears(String dateString) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 将字符串解析为LocalDate对象
        LocalDate startDate = LocalDate.parse(dateString, formatter);
        // 获取当前日期
        LocalDate endDate = LocalDate.now();
        // 计算时间间隔
        Period period = Period.between(startDate, endDate);
        // 获取年份间隔
        return Math.max(period.getYears(), 1);
    }

    public static void main(String[] args) {
//        String format = format(new Date("2023-03-01 01:47:29"), "yyyy-MM-dd");
        System.out.println("2023-03-01 01:47:29".substring(5, 7) + "-" + "2023-03-01 01:47:29".substring(8, 10) + "-" + "2023-03-01 01:47:29".substring(0, 4));
        System.out.println("20230301".substring(4, 6) + "/" + "20230301".substring(6, 8));
        System.out.println("2023-03-01 01:47:29".substring(8, 10));
    }
}
