package com.alibaba.copilot.enabler.infra.email.service;

import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.user.constants.UserDTO;
import com.alibaba.copilot.enabler.client.user.service.UserQueryService;
import com.alibaba.copilot.enabler.client.email.annotation.EmailTemplate;
import com.alibaba.copilot.enabler.domain.subscription.response.EmailResponse;
import com.alibaba.copilot.enabler.infra.email.constant.EmailConstant;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.global.midplatform.constants.ChannelTopicEnum;
import com.alibaba.global.midplatform.domain.GlobalMessage;
import com.alibaba.global.midplatform.domain.GlobalSendResult;
import com.alibaba.global.midplatform.exception.NotifyPushException;
import com.alibaba.global.midplatform.starter.MessageCenterPushClient;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 2023/10/16
 */
@Slf4j
@Component
public class EmailService {

    @Autowired
    private MessageCenterPushClient messageCenterPushClient;

    @Autowired
    private UserQueryService userQueryService;

    /**
     * 发送邮件
     *
     * @param appCode      应用标识
     * @param userId       用户ID
     * @param emailInfoObj 邮件对象 (需要添加{@link EmailTemplate}注解)
     */
    public EmailResponse sendEmail(String appCode, Long userId, Object emailInfoObj) {
        try {
            internalSendEmail(appCode, userId, emailInfoObj);
            return EmailResponse.buildSuccessRes();
        } catch (Exception e) {
            return EmailResponse.buildErrorRes(e.getMessage());
        }

    }
    private void internalSendEmail(String appCode, Long userId, Object emailInfoObj) {
        log.info("sendEmail, appCode={}, userId={}, emailInfoObj={}", appCode, userId, JSON.toJSONString(emailInfoObj));
        Assertor.assertNonNull(appCode, "appCode is null");
        Assertor.assertNonNull(userId, "userIds is null");
        Assertor.assertNonNull(emailInfoObj, "emailInfoObj is null");

        // 查询邮箱模板配置
        Class<?> objType = emailInfoObj.getClass();
        Assertor.asserts(objType.isAnnotationPresent(EmailTemplate.class), "No @EmailTemplate configured for " + objType);
        EmailTemplate emailTemplate = objType.getAnnotation(EmailTemplate.class);
        String templateId = emailTemplate.id();
        String templateName = emailTemplate.name();
        String templateInstanceId = Optional.of(emailTemplate.instanceId())
                .filter(StringUtils::isNotEmpty)
                .orElse(templateName);

        // 查询用户邮箱
        UserDTO userDTO = userQueryService.queryUserDTO(userId, appCode);
        if (userDTO == null) {
            log.warn("No user found, appCode={}, userId={}", appCode, userId);
            throw new RuntimeException("No user found");
        }
        String email = userDTO.getEmail();
        if (StringUtils.isEmpty(email)) {
            log.warn("Email is empty, user={}", JSON.toJSONString(userDTO));
            throw new RuntimeException("User email is empty");
        }

        // 构建请求信息
        Type mapType = new TypeReference<Map<String, Object>>() {
        }.getType();
        Map<String, Object> extDataParam = JSON.parseObject(JSON.toJSONString(emailInfoObj), mapType);
        GlobalMessage globalMessage = new GlobalMessage();
        globalMessage.setRequirementId(templateId);
        globalMessage.setTemplateName(templateName);
        globalMessage.setTemplateInstanceId(templateInstanceId);
        globalMessage.setSendToUser(Lists.newArrayList(email));
        globalMessage.setExtDataParam(extDataParam);
        // 默认多租户环境和多租户应用，如果是非多租户应用使用下面第二个发送方法
        sendMessage(globalMessage);
    }

    /**
     * 指定目标租户ID，并将消息投递到该租户。适用于中心化、单元化、非多租户应用。
     * send message to target channel for app without landlord.
     *
     * @param message 标准消息模型 standard message domain model
     */
    private void sendMessage(GlobalMessage message) {
        try {
            log.info("sendMessage, message={}", JSON.toJSONString(message));
            GlobalSendResult<String> globalSendResult = messageCenterPushClient.sendMessageWithTenantId(
                    EmailConstant.TENANT_ID, message, ChannelTopicEnum.EMAIL);
            if (globalSendResult.isSuccess()) {
                // 代表消息投递成功，必须打印或记录这个messageId，否则出了问题消息域无法排查
                // it means the message push successfully, This message ID must be printed or recorded, it help us to check problem.
                String messageId = globalSendResult.getResult();
                log.info("sendMessage, send success, messageId={}", messageId);
            } else {
                // 代表消息投递失败，重试或者其他异常处理
                // it means the message push failed, retry or other exception handling
                log.error("sendMessage, send error, result={}", JSON.toJSONString(globalSendResult));
                throw new RuntimeException("sendMessage error, " + globalSendResult.getErrorMsg());
            }
        } catch (NotifyPushException e) {
            // 处理异常，记录日志
            log.error("sendMessage, invoke error", e);
            throw new RuntimeException("sendMessage error, " + e.getMessage());
        }
    }
}
