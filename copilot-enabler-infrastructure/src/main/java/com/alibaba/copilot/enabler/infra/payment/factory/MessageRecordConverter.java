package com.alibaba.copilot.enabler.infra.payment.factory;

import com.alibaba.copilot.boot.basic.enums.IEnum;
import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageDirectionEnum;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageStatusEnum;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageTypeEnum;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.model.MessageRecord;
import com.alibaba.copilot.enabler.domain.payment.model.MessageRecordAttributes;
import com.alibaba.copilot.enabler.infra.payment.dataobject.MessageRecordDO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * MessageRecordConverter
 */
@Component
public class MessageRecordConverter implements Converter<MessageRecordDO, MessageRecord> {
    public static final Converter<MessageRecordDO, MessageRecord> INSTANCE = new MessageRecordConverter();

    private static final String[] ignoreProperties = new String[]{"attributes", "direction", "status"};

    @Override
    public MessageRecord convertA2B(MessageRecordDO messageRecordDO) {
        if (messageRecordDO == null) {
            return null;
        }

        MessageRecord messageRecord = new MessageRecord();
        BeanUtils.copyProperties(messageRecordDO, messageRecord, ignoreProperties);
        messageRecord.setAppCode(IEnum.of(AppEnum.class, messageRecordDO.getAppCode()));
        messageRecord.setDirection(IEnum.of(MessageDirectionEnum.class, messageRecordDO.getDirection()));
        messageRecord.setMessageType(IEnum.of(MessageTypeEnum.class, messageRecordDO.getMessageType()));
        messageRecord.setStatus(IEnum.of(MessageStatusEnum.class, messageRecordDO.getStatus()));
        messageRecord.setAttributes(new MessageRecordAttributes(messageRecordDO.getAttributes()));
        return messageRecord;
    }

    @Override
    public MessageRecordDO convertB2A(MessageRecord messageRecord) {
        if (messageRecord == null) {
            return null;
        }
        MessageRecordDO messageRecordDO = new MessageRecordDO();
        BeanUtils.copyProperties(messageRecord, messageRecordDO, ignoreProperties);
        messageRecordDO.setAppCode(messageRecord.getAppCode().name());
        messageRecordDO.setDirection(messageRecord.getDirection().name());
        messageRecordDO.setMessageType(messageRecord.getMessageType().name());
        messageRecordDO.setStatus(messageRecord.getStatus().name());
        messageRecordDO.setGmtModified(new Date());
        messageRecordDO.setAttributes(messageRecord.getAttributes().toString());

        MessageRecordAttributes attributes = messageRecord.getAttributes();
        if (attributes != null) {
            messageRecordDO.setAttributes(attributes.toString());
        }
        return messageRecordDO;
    }
}
