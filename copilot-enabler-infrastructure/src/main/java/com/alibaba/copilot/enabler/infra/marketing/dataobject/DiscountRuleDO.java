package com.alibaba.copilot.enabler.infra.marketing.dataobject;

import com.alibaba.copilot.enabler.infra.base.bean.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 折扣规则表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "discount_rule")
public class DiscountRuleDO extends BaseDO {

    /**
     * 应用标识
     */
    private String appCode;

    /**
     * 状态
     */
    private String status;

    /**
     * 折扣类型
     */
    private String discountType;

    /**
     * 实体类型
     */
    private String entityType;

    /**
     * 实体ID
     */
    private Long entityId;

    /**
     * 扩展属性（JSON格式）
     */
    private String attributes;

    /**
     * 逻辑删除
     */
    private Boolean deleted;
}
