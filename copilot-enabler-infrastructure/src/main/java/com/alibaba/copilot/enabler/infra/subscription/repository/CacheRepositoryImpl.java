package com.alibaba.copilot.enabler.infra.subscription.repository;

import com.alibaba.copilot.boot.tools.lock.Lock;
import com.alibaba.copilot.boot.tools.lock.RedisLock;
import com.alibaba.copilot.enabler.domain.subscription.repository.CacheRepository;
import com.alibaba.normandy.credential.Credential;
import com.alibaba.normandy.credential.CredentialProvider;
import com.alibaba.normandy.credential.ResourceNames;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import javax.annotation.PostConstruct;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * CacheRepositoryImpl
 *
 * <AUTHOR>
 * @date 2024/9/24 下午4:54
 */
@Service
public class CacheRepositoryImpl implements CacheRepository {

    @Value("${redis.instance-id}")
    private String REDIS_INSTANCE_ID;

    @Value("${redis.endpoint}")
    private String REDIS_HOST;

    private final CredentialProvider credentialProvider;
    private JedisPool jedisPool = null;

    @Autowired
    public CacheRepositoryImpl(CredentialProvider credentialProvider) {
        this.credentialProvider = credentialProvider;
    }

    @PostConstruct
    void init() {
        String resourceName = ResourceNames.ofAliyunKvStoreInstanceId(REDIS_INSTANCE_ID);
        Credential credential = credentialProvider.getCredential(resourceName);
        String password = credential.getUsername() + ":" + credential.getPassword();
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(32);
        config.setMaxIdle(4);
        config.setMinIdle(2);
        config.setBlockWhenExhausted(true);
        jedisPool = new JedisPool(config, REDIS_HOST, 6379, 3000, password);
    }

    public JedisPool getJedisPool() {
        return jedisPool;
    }


    @Override
    public boolean put(String key, Serializable value) {
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.append(key, String.valueOf(value));
            return true;
        }
    }

    @Override
    public boolean put(String key, Serializable value, Long timeout) {
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.setex(key, timeout, String.valueOf(value));
            return true;
        }
    }

    @Override
    public long increment(String key) {
        try (Jedis jedis = jedisPool.getResource()) {
            // 自增操作并返回新值
            return jedis.incr(key);
        }
    }

    @Override
    public boolean set(String key, Serializable value) {
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.set(key, String.valueOf(value));
            return true;
        }
    }

    @Override
    public boolean set(String key, Serializable value, Long timeout) {
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.setex(key, timeout, String.valueOf(value));
            return true;
        }
    }

    @Override
    public Object get(String key) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.get(key);
        }
    }

    @Override
    public boolean delete(String key) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.del(key) > 0L;
        }
    }

    @Override
    public boolean delete(List<String> keys) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.del(keys.toArray(new String[0])) > 0L;
        }
    }

    @Override
    public Lock getLock(String lockSpace, String lockId) {
        return RedisLock.of(jedisPool, lockSpace, lockId);
    }

    @Override
    public Long pushCacheQueue(String queueKey, String... values) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.lpush(queueKey, values);
        }
    }

    @Override
    public List<String> popCacheQueue(String queueKey, int count) {
        List<String> values = new ArrayList<>();
        try (Jedis jedis = jedisPool.getResource()) {
            for (int i = 0; i < count; i++) {
                String value = jedis.rpop(queueKey);
                if (value != null) {
                    values.add(value);
                } else {
                    break;
                }
            }
        }
        return values;
    }

    @Override
    public Long cacheQueueSize(String queueKey) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.llen(queueKey);
        }
    }

    @Override
    public Long addCacheSet(String setKey, String value, long expireSecond) {
        try (Jedis jedis = jedisPool.getResource()) {
            long sadd = jedis.sadd(setKey, value);
            jedis.expire(setKey, expireSecond);
            return sadd;
        }
    }

    @Override
    public List<String> randomCacheSet(String setKey, int count) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.srandmember(setKey, count);
        }
    }

    @Override
    public long removeCacheSet(String setKey, List<String> value) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.srem(setKey, value.toArray(new String[]{}));
        }
    }
}
