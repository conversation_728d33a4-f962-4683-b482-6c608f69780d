package com.alibaba.copilot.enabler.infra.payment.utils;

import com.alibaba.aepay.fund.business.api.payment.dto.Result;
import com.alibaba.aepay.fund.business.api.payment.response.BaseApiResponse;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 2023/10/8
 */
@Slf4j
public class AEResultConvertUtils {

    public static <Response extends BaseApiResponse, Model> SingleResult<Model> convertResult(
            Response aeResponse, Function<Response, Model> modelConverter) {
        Result result = aeResponse.getResult();
        String status = result.getResultStatus();
        if (!AEPaymentConstants.RESULT_STATUS_SUCCESS.equals(status)) {
            String code = result.getResultCode();
            String message = result.getResultMessage();
            log.warn("convertResult invoke failed, code={}, message={}", code, message);
            //noinspection unchecked
            return SingleResult.buildFailure(code, message);
        }

        Model model = modelConverter.apply(aeResponse);
        return SingleResult.buildSuccess(model);
    }
}
