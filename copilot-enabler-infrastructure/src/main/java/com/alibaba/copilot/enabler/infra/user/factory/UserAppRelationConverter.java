package com.alibaba.copilot.enabler.infra.user.factory;

import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.copilot.enabler.domain.user.model.UserAppRelation;
import com.alibaba.copilot.enabler.infra.user.dataobject.UserAppRelationDO;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/4
 */
public class UserAppRelationConverter implements Converter<UserAppRelationDO, UserAppRelation> {
    public static final Converter<UserAppRelationDO, UserAppRelation> INSTANCE = new UserAppRelationConverter();

    @Override
    public UserAppRelation convertA2B(UserAppRelationDO userAppRelationDO) {
        if (userAppRelationDO == null) {
            return null;
        }

        return UserAppRelation.builder()
                .id(userAppRelationDO.getId())
                .userId(userAppRelationDO.getUserId())
                .email(userAppRelationDO.getEmail())
                .appCode(userAppRelationDO.getAppCode())
                .appType(userAppRelationDO.getAppType())
                .appName(userAppRelationDO.getAppName())
                .bindStatus(userAppRelationDO.getBindStatus())
                .bindSource(userAppRelationDO.getBindSource())
                .build();
    }

    @Override
    public UserAppRelationDO convertB2A(UserAppRelation userAppRelation) {
        if (userAppRelation == null) {
            return null;
        }

        UserAppRelationDO userAppRelationDO = new UserAppRelationDO();
        userAppRelationDO.setUserId(userAppRelation.getUserId());
        userAppRelationDO.setEmail(userAppRelation.getEmail());
        userAppRelationDO.setAppCode(userAppRelation.getAppCode());
        userAppRelationDO.setAppName(userAppRelation.getAppName());
        userAppRelationDO.setAppType(userAppRelation.getAppType());
        userAppRelationDO.setBindStatus(userAppRelation.getBindStatus());
        userAppRelationDO.setBindSource(userAppRelation.getBindSource());

        return userAppRelationDO;
    }
}
