package com.alibaba.copilot.enabler.infra.subscription.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.Date;
import java.util.List;

import com.alibaba.copilot.enabler.infra.subscription.dataobject.UserInvitationDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * User Invitation Mapper Interface
 *
 * <AUTHOR>
 * @date 2025/2/21
 */
@Mapper
public interface UserInvitationMapper extends BaseMapper<UserInvitationDO> {

    /**
     * Query by inviter ID and invitee ID
     *
     * @param inviterId inviter user ID
     * @param inviteeId invitee user ID
     * @return invitation record
     */
    @Select("SELECT * FROM user_invitation WHERE inviter_id = #{inviterId} AND invitee_id = #{inviteeId} LIMIT 1")
    UserInvitationDO selectByInviterAndInvitee(@Param("inviterId") Long inviterId, @Param("inviteeId") Long inviteeId);

    /**
     * Query all invitation records by inviter ID
     *
     * @param inviterId inviter user ID
     * @return list of invitation records
     */
    @Select("SELECT * FROM user_invitation WHERE inviter_id = #{inviterId}")
    List<UserInvitationDO> selectByInviter(Long inviterId);

    /**
     * Query all invitation records by invitee ID
     *
     * @param inviteeId invitee user ID
     * @return list of invitation records
     */
    @Select("SELECT * FROM user_invitation WHERE invitee_id = #{inviteeId}")
    List<UserInvitationDO> selectByInvitee(Long inviteeId);

    /**
     * Count total invitations by inviter ID
     *
     * @param inviterId inviter user ID
     * @return count of invitations
     */
    @Select("SELECT COUNT(*) FROM user_invitation WHERE inviter_id = #{inviterId}")
    int countByInviter(Long inviterId);

    /**
     * Count invitations from normal users only (excluding system user IDs)
     * This method uses SqlProvider for dynamic SQL generation
     *
     * @param inviterId inviter user ID
     * @param excludeUserIds 需要排除的系统用户ID列表
     * @return count of normal user invitations
     */
    @SelectProvider(type = UserInvitationSqlProvider.class, method = "countByInviterAndNotFromSystem")
    int countByInviterAndNotFromSystem(@Param("inviterId") Long inviterId, @Param("excludeUserIds") List<Long> excludeUserIds);

    /**
     * Count invitations within a specific time range
     *
     * @param inviterId  inviter user ID
     * @param startTime  start time
     * @param endTime    end time
     * @return count of invitations
     */
    @Select("SELECT COUNT(*) FROM user_invitation WHERE inviter_id = #{inviterId} AND gmt_create >= #{startTime} AND gmt_create < #{endTime}")
    int countByInviterAndTimeRange(
            @Param("inviterId") Long inviterId,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime);

    /**
     * Count invitations within a specific time range and exclude system users
     * This method uses SqlProvider for dynamic SQL generation
     *
     * @param inviterId     inviter user ID
     * @param startTime     start time
     * @param endTime       end time
     * @param excludeUserIds 需要排除的系统用户ID列表
     * @return count of invitations
     */
    @SelectProvider(type = UserInvitationSqlProvider.class, method = "countByInviterAndTimeRangeExcludeSystem")
    int countByInviterAndTimeRangeExcludeSystem(
            @Param("inviterId") Long inviterId,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("excludeUserIds") List<Long> excludeUserIds);
} 