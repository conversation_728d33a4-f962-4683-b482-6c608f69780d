package com.alibaba.copilot.enabler.infra.subscription.factory;


import com.alibaba.copilot.boot.basic.enums.IEnum;
import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrderAttributes;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionOrderDO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Optional;

/**
 * SubscriptionOrderConverter
 */
@Component
public class SubscriptionOrderConverter implements Converter<SubscriptionOrderDO, SubscriptionOrder> {
    public static final Converter<SubscriptionOrderDO, SubscriptionOrder> INSTANCE = new SubscriptionOrderConverter();

    private static final String[] ignoreProperties = new String[]{"attributes"};

    @Override
    public SubscriptionOrder convertA2B(SubscriptionOrderDO subscriptionOrderDO) {
        if (subscriptionOrderDO == null) {
            return null;
        }
        SubscriptionOrder subscriptionOrder = new SubscriptionOrder();
        BeanUtils.copyProperties(subscriptionOrderDO, subscriptionOrder, ignoreProperties);
        subscriptionOrder.setStatus(SubscriptionOrderStatus.of(subscriptionOrderDO.getStatus()));

        SubscriptionPayType subscriptionPayType = Optional.ofNullable(subscriptionOrderDO.getSubscriptionPayType())
                .map(payType -> IEnum.of(SubscriptionPayType.class, payType))
                .orElseGet(() -> AppEnum.getAppByCode(subscriptionOrderDO.getAppCode()).getDefaultSubscriptionPayType());
        subscriptionOrder.setSubscriptionPayType(subscriptionPayType);

        subscriptionOrder.setAttributes(new SubscriptionOrderAttributes(subscriptionOrderDO.getAttributes()));
        return subscriptionOrder;
    }

    @Override
    public SubscriptionOrderDO convertB2A(SubscriptionOrder subscriptionOrder) {
        if (subscriptionOrder == null) {
            return null;
        }
        SubscriptionOrderDO subscriptionOrderDO = new SubscriptionOrderDO();
        BeanUtils.copyProperties(subscriptionOrder, subscriptionOrderDO, ignoreProperties);
        subscriptionOrderDO.setStatus(subscriptionOrder.getStatus().name());
        SubscriptionOrderAttributes attributes = subscriptionOrder.getAttributes();
        subscriptionOrderDO.setGmtModified(new Date());

        SubscriptionPayType subscriptionPayType = Optional.ofNullable(subscriptionOrder.getSubscriptionPayType())
                .orElseGet(() -> AppEnum.getAppByCode(subscriptionOrder.getAppCode()).getDefaultSubscriptionPayType());
        subscriptionOrderDO.setSubscriptionPayType(subscriptionPayType.name());

        subscriptionOrderDO.setAttributes(attributes.toString());
        return subscriptionOrderDO;
    }
}
