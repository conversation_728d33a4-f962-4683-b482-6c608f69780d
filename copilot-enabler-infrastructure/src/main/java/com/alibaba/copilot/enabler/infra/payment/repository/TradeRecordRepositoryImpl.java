package com.alibaba.copilot.enabler.infra.payment.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.base.dto.PageDTO;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.payment.request.TradeRecordPageQuery;
import com.alibaba.copilot.enabler.client.payment.request.TradeRecordsQuery;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.domain.base.exception.BizException;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.infra.base.utils.EnvUtils;
import com.alibaba.copilot.enabler.infra.payment.dataobject.TradeRecordDO;
import com.alibaba.copilot.enabler.infra.payment.factory.TradeRecordConverter;
import com.alibaba.copilot.enabler.infra.payment.mapper.TradePaymentRecordMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

@Repository
@Slf4j
public class TradeRecordRepositoryImpl implements TradeRecordRepository {

    @Autowired
    private TradePaymentRecordMapper tradePaymentRecordMapper;

    @Monitor(name = "创建或更新交易流水", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public String createOrUpdateTradeRecord(TradeRecord tradeRecord) {
        Assertor.asserts(tradeRecord != null, "tradeRecord can not null");

        TradeRecordDO tradeRecordDO = TradeRecordConverter.INSTANCE.convertB2A(tradeRecord);
        if (tradeRecordDO.getId() != null) {
            int count = tradePaymentRecordMapper.updateById(tradeRecordDO);
            log.warn("update tradeRecord count={}, id={}, userId={}, tradeNo={}, status={}",
                    count, tradeRecordDO.getId(), tradeRecordDO.getUserId(), tradeRecordDO.getTradeNo(), tradeRecordDO.getStatus());
            return tradeRecordDO.getTradeNo();
        }

        int count = tradePaymentRecordMapper.insert(tradeRecordDO);
        //noinspection ConstantConditions
        TradeRecordDO savedDO = tradePaymentRecordMapper.selectById(tradeRecordDO.getId());
        tradeRecord.setId(tradeRecordDO.getId());
        tradeRecord.setGmtCreate(savedDO.getGmtCreate());
        tradeRecord.setGmtModified(savedDO.getGmtModified());
        log.warn("insert tradeRecord count={}, id={}, userId={}, tradeNo={}",
                count, tradeRecordDO.getId(), tradeRecordDO.getUserId(), tradeRecordDO.getTradeNo());
        return tradeRecordDO.getTradeNo();
    }


    private int update2FailStatus(TradeRecordDO tradeRecordDO) {
        UpdateWrapper<TradeRecordDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", tradeRecordDO.getId());
        String targetStatus = tradeRecordDO.getStatus();
        if (Objects.nonNull(targetStatus)) {
            updateWrapper.set("status", targetStatus);
            // 已经是成功状态的，不能改为失败/取消
            if (isFailedStatus(tradeRecordDO)) {
                updateWrapper.ne("status", TradeRecordStatus.SUCC.name());
            }
        }

        if (Objects.nonNull(tradeRecordDO.getAttributes())) {
            updateWrapper.set("attributes", tradeRecordDO.getAttributes());
        }
        if (Objects.nonNull(tradeRecordDO.getOutTradeNo())) {
            updateWrapper.set("out_trade_id", tradeRecordDO.getOutTradeNo());
        }
        if (Objects.nonNull(tradeRecordDO.getTradeTime())) {
            updateWrapper.set("trade_time", tradeRecordDO.getTradeTime());
        }
        int count = tradePaymentRecordMapper.update(tradeRecordDO, updateWrapper);
        if (count < 1) {
            throw new BizException("update tradeRecord to failed status, update count=" + count);
        }
        return count;
    }

    private boolean isFailedStatus(TradeRecordDO tradeRecordDO) {
        String targetStatus = tradeRecordDO.getStatus();
        return TradeRecordStatus.FAIL.name().equals(targetStatus)
                || TradeRecordStatus.CANCELLED.name().equals(targetStatus);
    }


    @Monitor(name = "查询交易流水", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public List<TradeRecord> queryTradeRecords(TradeRecordsQuery query) {
        LambdaQueryWrapper<TradeRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(query.getUserId() != null, TradeRecordDO::getUserId, query.getUserId());
        queryWrapper.eq(query.getOrderId() != null, TradeRecordDO::getSubscriptionOrderId, query.getOrderId());
        queryWrapper.eq(query.getTradeNo() != null, TradeRecordDO::getTradeNo, query.getTradeNo());
        queryWrapper.eq(query.getAppCode() != null, TradeRecordDO::getAppCode, query.getAppCode());
        queryWrapper.eq(query.getOutTradeNo() != null, TradeRecordDO::getOutTradeNo, query.getOutTradeNo());
        queryWrapper.eq(TradeRecordDO::getEnv, EnvUtils.getEnv());

        TradeRecordStatus recordStatus = query.getStatus();
        if (recordStatus != null) {
            queryWrapper.eq(TradeRecordDO::getStatus, recordStatus.name());
        }

        TradeDirection tradeDirection = query.getTradeDirection();
        if (tradeDirection != null) {
            queryWrapper.eq(TradeRecordDO::getTradeDirection, tradeDirection.name());
        }
        List<TradeRecordDO> tradePaymentRecordDOS = tradePaymentRecordMapper.selectList(queryWrapper);
        return TradeRecordConverter.INSTANCE.convertA2B(tradePaymentRecordDOS);
    }

    @Override
    public boolean hasRefundRecordWithTodoStatus(String appCode, Long userId) {
        TradeRecordsQuery recordsQuery = TradeRecordsQuery.builder()
                .appCode(appCode)
                .userId(userId)
                .tradeDirection(TradeDirection.REFUND)
                .status(TradeRecordStatus.TODO)
                .build();
        List<TradeRecord> tradeRecords = queryTradeRecords(recordsQuery);

        return CollectionUtil.size(tradeRecords) > 0;
    }

    @Monitor(name = "查询交易流水分页信息", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public PageDTO<TradeRecord> queryTradeRecordsPage(TradeRecordPageQuery query) {
        Integer pageNum = query.getPageNum();
        Integer pageSize = query.getPageSize();
        Assertor.assertNonNull(pageNum, "pageNum is null");
        Assertor.assertNonNull(pageSize, "pageSize is null");

        Page<TradeRecordDO> page = Page.of(pageNum, pageSize);
        Long userId = query.getUserId();
        List<TradeRecordStatus> statusList = query.getStatusList();
        LambdaQueryWrapper<TradeRecordDO> queryWrapper = new LambdaQueryWrapper<TradeRecordDO>()
                .eq(userId != null, TradeRecordDO::getUserId, userId)
                .eq(TradeRecordDO::getEnv, EnvUtils.getEnv())
                .in(statusList != null, TradeRecordDO::getStatus, query.getStatusList())
                .orderByDesc(TradeRecordDO::getGmtCreate);
        if (query.getAppEnum() != null) {
            queryWrapper.eq(TradeRecordDO::getAppCode, query.getAppEnum().getCode());
        }

        Page<TradeRecordDO> selectPage = tradePaymentRecordMapper.selectPage(page, queryWrapper);

        List<TradeRecordDO> recordDOS = selectPage.getRecords();
        List<TradeRecord> tradeRecords = TradeRecordConverter.INSTANCE.convertA2B(recordDOS);

        return new PageDTO<TradeRecord>()
                .setPageNum(selectPage.getCurrent())
                .setPageSize(selectPage.getSize())
                .setTotal(selectPage.getTotal())
                .setTotalPage(selectPage.getPages())
                .setList(tradeRecords);
    }

    /**
     * 查询交易流水分页信息
     *
     * @param query
     */
    @Override
    public PageDTO<TradeRecord> queryTradeRecordsPageWithStripe(TradeRecordPageQuery query) {
        Integer pageNum = query.getPageNum();
        Integer pageSize = query.getPageSize();
        Assertor.assertNonNull(pageNum, "pageNum is null");
        Assertor.assertNonNull(pageSize, "pageSize is null");

        Page<TradeRecordDO> page = Page.of(pageNum, pageSize);
        Long userId = query.getUserId();
        List<TradeRecordStatus> statusList = query.getStatusList();
        LambdaQueryWrapper<TradeRecordDO> queryWrapper = new LambdaQueryWrapper<TradeRecordDO>()
                .eq(userId != null, TradeRecordDO::getUserId, userId)
                .eq(TradeRecordDO::getEnv, EnvUtils.getEnv())
                .in(statusList != null, TradeRecordDO::getStatus, query.getStatusList())
                .gt(TradeRecordDO::getTradeAmount, 0)
                .orderByDesc(TradeRecordDO::getGmtCreate);
        if (query.getAppEnum() != null) {
            queryWrapper.eq(TradeRecordDO::getAppCode, query.getAppEnum().getCode());
        }

        Page<TradeRecordDO> selectPage = tradePaymentRecordMapper.selectPage(page, queryWrapper);

        List<TradeRecordDO> recordDOS = selectPage.getRecords();
        List<TradeRecord> tradeRecords = TradeRecordConverter.INSTANCE.convertA2B(recordDOS);

        return new PageDTO<TradeRecord>()
                .setPageNum(selectPage.getCurrent())
                .setPageSize(selectPage.getSize())
                .setTotal(selectPage.getTotal())
                .setTotalPage(selectPage.getPages())
                .setList(tradeRecords);
    }

    @Monitor(name = "扫描交易流水", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public List<TradeRecord> scanTradeRecords(TradeRecordsQuery query) {
        LambdaQueryWrapper<TradeRecordDO> queryWrapper = new LambdaQueryWrapper<>();

        Long userId = query.getUserId();
        queryWrapper.eq(userId != null, TradeRecordDO::getUserId, userId);

        if (query.getAppCode() != null) {
            queryWrapper.eq(TradeRecordDO::getAppCode, query.getAppCode());
        }

        if (query.getTradeDirection() != null) {
            queryWrapper.eq(TradeRecordDO::getTradeDirection, query.getTradeDirection().name());
        }
        queryWrapper.ge(query.getStartCreateTime() != null, TradeRecordDO::getGmtCreate,
                query.getStartCreateTime());
        queryWrapper.le(query.getEndCreateTime() != null, TradeRecordDO::getGmtCreate,
                query.getEndCreateTime());

        if (query.getStatus() != null) {
            queryWrapper.eq(TradeRecordDO::getStatus, query.getStatus().name());
        }
        queryWrapper.eq(TradeRecordDO::getHadInitiatePay, query.getHadInitiatePay());
        if (query.getPaymentType() != null) {
            queryWrapper.eq(TradeRecordDO::getPaymentType, query.getPaymentType().name());
        }

        if (query.getEnv() != null) {
            queryWrapper.eq(TradeRecordDO::getEnv, query.getEnv());
        } else {
            queryWrapper.eq(TradeRecordDO::getEnv, EnvUtils.getEnv());
        }
        queryWrapper.eq(TradeRecordDO::getDeleted, 0);
        log.info("query: {} scanTradeRecords queryWrapper:{}", query, queryWrapper.getTargetSql());
        List<TradeRecordDO> tradePaymentRecordDOS = tradePaymentRecordMapper.selectList(queryWrapper);
        return TradeRecordConverter.INSTANCE.convertA2B(tradePaymentRecordDOS);
    }

    @Monitor(name = "查询交易流水", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public TradeRecord queryByTradeNo(String tradeNo) {
        TradeRecordsQuery query = TradeRecordsQuery.builder()
                .tradeNo(tradeNo)
                .build();
        List<TradeRecord> tradeRecords = queryTradeRecords(query);
        return CollectionUtil.getFirst(tradeRecords);
    }

    @Override
    public TradeRecord queryByOutTradeNoAndTradeDirection(String outTradeNo,TradeDirection tradeDirection) {
        TradeRecordsQuery query = TradeRecordsQuery.builder()
                .outTradeNo(outTradeNo)
                .tradeDirection(tradeDirection)
                .build();
        List<TradeRecord> tradeRecords = queryTradeRecords(query);
        return CollectionUtil.getFirst(tradeRecords);
    }

    @Monitor(name = "查询交易流水", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public TradeRecord queryById(Long id) {
        TradeRecordsQuery query = TradeRecordsQuery.builder()
                .id(id)
                .build();
        List<TradeRecord> tradeRecords = queryTradeRecords(query);
        return CollectionUtil.getFirst(tradeRecords);
    }

    @Monitor(name = "根据指定订单号查询流水数据", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public TradeRecord queryByOrderId(Long orderId, TradeDirection tradeDirection) {
        Assertor.assertNonNull(orderId, "orderId is null");
        Assertor.assertNonNull(tradeDirection, "tradeDirection is null");

        LambdaQueryWrapper<TradeRecordDO> queryWrapper = new LambdaQueryWrapper<TradeRecordDO>()
                .eq(TradeRecordDO::getSubscriptionOrderId, orderId)
                .eq(TradeRecordDO::getTradeDirection, tradeDirection.name())
                .eq(TradeRecordDO::getEnv, EnvUtils.getEnv());
        List<TradeRecordDO> tradeRecordDOS = tradePaymentRecordMapper.selectList(queryWrapper);
        TradeRecordDO recordDO = CollectionUtil.getFirst(tradeRecordDOS);
        return TradeRecordConverter.INSTANCE.convertA2B(recordDO);
    }

    @Override
    public Boolean isPaid(String appCode, Long userId) {
        Assertor.assertNotBlank(appCode, "appCode is empty");
        Assertor.assertNonNull(userId, "userId is null");

        LambdaQueryWrapper<TradeRecordDO> queryWrapper = new LambdaQueryWrapper<TradeRecordDO>()
                .eq(TradeRecordDO::getAppCode, appCode)
                .eq(TradeRecordDO::getUserId, userId)
                .eq(TradeRecordDO::getTradeDirection, TradeDirection.FORWARD.name())
                .eq(TradeRecordDO::getStatus, TradeRecordStatus.SUCC.name())
                .eq(TradeRecordDO::getDeleted, false)
                .eq(TradeRecordDO::getEnv, EnvUtils.getEnv());
        return tradePaymentRecordMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public TradeRecord queryPaySuccessRecordByOrderId(Long orderId) {
        Assertor.assertNonNull(orderId, "orderId is null");

        TradeRecordsQuery query = TradeRecordsQuery.builder()
                .orderId(orderId)
                .status(TradeRecordStatus.SUCC)
                .tradeDirection(TradeDirection.FORWARD)
                .build();
        List<TradeRecord> tradeRecords = queryTradeRecords(query);
        return CollectionUtil.getFirst(tradeRecords);
    }

    @Override
    public List<TradeRecord> queryListForBill(String appCode, Date startTime, Date endTime) {
        Assertor.assertNotBlank(appCode, "appCode is blank");

        List<TradeRecordDO> tradeRecordDOS = queryValidList(wrapper -> wrapper
                .eq(TradeRecordDO::getStatus, TradeRecordStatus.SUCC.name())
                .eq(TradeRecordDO::getTradeDirection, TradeDirection.FORWARD.name())
                .eq(TradeRecordDO::getAppCode, appCode)
                .ge(startTime != null, TradeRecordDO::getTradeTime, startTime)
                .le(endTime != null, TradeRecordDO::getTradeTime, endTime)
        );

        return TradeRecordConverter.INSTANCE.convertA2B(tradeRecordDOS);
    }

    @Override
    public List<TradeRecord> queryRefundRecordsForBill(@NotNull Collection<Long> orderIds) {
        Assertor.assertNonNull(orderIds, "orderIds is null");

        if (CollectionUtil.isEmpty(orderIds)) {
            return Lists.newArrayList();
        }

        // 查询账单接口, 仅查询线上环境数据
        String env = EnvUtils.ONLINE;
        List<TradeRecordDO> tradeRecordDOS = queryValidList(env, wrapper -> wrapper
                .eq(TradeRecordDO::getTradeDirection, TradeDirection.REFUND)
                .in(TradeRecordDO::getSubscriptionOrderId, orderIds)
        );
        return TradeRecordConverter.INSTANCE.convertA2B(tradeRecordDOS);
    }

    @Override
    public List<TradeRecord> queryPaySuccessRecordsForBill(Collection<Long> orderIds) {
        Assertor.assertNonNull(orderIds, "orderIds is null");

        if (CollectionUtil.isEmpty(orderIds)) {
            return Lists.newArrayList();
        }

        // 查询账单接口, 仅查询线上环境数据
        String env = EnvUtils.ONLINE;
        List<TradeRecordDO> tradeRecordDOS = queryValidList(env, wrapper -> wrapper
                .eq(TradeRecordDO::getTradeDirection, TradeDirection.FORWARD)
                .eq(TradeRecordDO::getStatus, TradeRecordStatus.SUCC.name())
                .in(TradeRecordDO::getSubscriptionOrderId, orderIds)
        );
        return TradeRecordConverter.INSTANCE.convertA2B(tradeRecordDOS);
    }

    /**
     * 查询有效的数据列表 (封装公共的信息过滤条件)
     */
    private List<TradeRecordDO> queryValidList(Consumer<LambdaQueryWrapper<TradeRecordDO>> consumer) {
        return queryValidList(EnvUtils.getEnv(), consumer);
    }

    /**
     * 查询有效的数据列表 (封装公共的信息过滤条件)
     */
    private List<TradeRecordDO> queryValidList(String env, Consumer<LambdaQueryWrapper<TradeRecordDO>> consumer) {
        LambdaQueryWrapper<TradeRecordDO> queryWrapper = new LambdaQueryWrapper<TradeRecordDO>()
                .eq(TradeRecordDO::getEnv, env)
                .eq(TradeRecordDO::getDeleted, Boolean.FALSE);
        if (consumer != null) {
            consumer.accept(queryWrapper);
        }
        return tradePaymentRecordMapper.selectList(queryWrapper);
    }
}
