package com.alibaba.copilot.enabler.infra.email.config;

import com.taobao.hsf.app.api.util.HSFApiConsumerBean;
import com.taobao.wireless.im.facade.service.ImMessageService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 2023/10/20
 */
@Configuration
public class EmailConfiguration {

    @Bean
    public ImMessageService getImMessageService() throws Exception {
        // ---------------------- 装配 -----------------------//
        // [设置] HSF服务订阅逻辑
        HSFApiConsumerBean hsfApiConsumerBean = new HSFApiConsumerBean();
        // [设置] 订阅服务的接口
        hsfApiConsumerBean.setInterfaceName("com.taobao.wireless.im.facade.service.ImMessageService");
        // [设置] 服务的版本
        hsfApiConsumerBean.setVersion("1.0.0");
        hsfApiConsumerBean.setClientTimeout(3000);
        // [设置] 服务的归组
        hsfApiConsumerBean.setGroup("HSF");
        // hsfApiConsumerBean.setConfigserverCenter(properties.getUnits());
        // ---------------------- 订阅 -----------------------//
        // [订阅] HSF服务，同步等待地址推送，默认false(异步)，同步默认超时时间3000毫秒
        hsfApiConsumerBean.init(true);
        return (ImMessageService) hsfApiConsumerBean.getObject();
    }
}
