package com.alibaba.copilot.enabler.infra.base.metaq;

import com.alibaba.copilot.boot.tools.serialize.Serializer;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.ParserConfig;
import com.alibaba.fastjson.serializer.SerializerFeature;

/**
 * fastjson序列化
 **/
public class FastjsonSerializer implements Serializer {
    public static final Serializer INSTANCE = new FastjsonSerializer();

    public FastjsonSerializer() {

    }

    @Override
    public String type() {
        return "fastjson";
    }

    private static final ParserConfig parserConfig = new ParserConfig();
    private static final SerializerFeature[] features = new SerializerFeature[]{
            SerializerFeature.SkipTransientField,
            SerializerFeature.IgnoreNonFieldGetter
    };

    @Override
    public byte[] serialize(Object object) {
        String text = JSON.toJSONString(object, features);
        return text.getBytes();
    }


    /**
     * 请谨慎使用JSON不带类型的发序列化，因为打开了AutoType，使用不当会有安全隐患：
     * 安全公告：https://github.com/alibaba/fastjson/wiki/security_update_20170315
     * AutoType漏洞说明： https://blog.csdn.net/wuShiJingZuo/article/details/107526944
     *
     * @param bytes array of bytes
     * @param <T>
     * @return
     */
    @Override
    public <T> T deserialize(byte[] bytes) {
        return (T) JSON.parse(new String(bytes), parserConfig);
    }
}
