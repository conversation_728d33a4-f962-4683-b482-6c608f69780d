package com.alibaba.copilot.enabler.infra.subscription.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPlanName;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyFeatureQuery;
import com.alibaba.copilot.enabler.domain.subscription.model.FeatureUsage;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionFeature;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionPlanQuery;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionPlanQueryInfo;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.*;
import com.alibaba.copilot.enabler.infra.subscription.factory.FeatureUsageConverter;
import com.alibaba.copilot.enabler.infra.subscription.factory.SubscriptionFeatureConverter;
import com.alibaba.copilot.enabler.infra.subscription.factory.SubscriptionPlanConverter;
import com.alibaba.copilot.enabler.infra.subscription.mapper.FeatureUsageMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionFeatureMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionPlanFeatureMapper;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionPlanMapper;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class SubscriptionPlanRepositoryImpl implements SubscriptionPlanRepository {

    @Autowired
    private SubscriptionPlanMapper subscriptionPlanMapper;
    @Autowired
    private SubscriptionPlanFeatureMapper subscriptionPlanFeatureMapper;
    @Autowired
    private SubscriptionFeatureMapper subscriptionFeatureMapper;
    @Autowired
    private FeatureUsageMapper featureUsageMapper;

    @Monitor(name = "查询订阅计划", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public List<SubscriptionPlan> querySubscriptionPlans(SubscriptionPlanQuery query) {
        Assertor.asserts(StringUtils.isNotBlank(query.getAppCode()), "querySubscriptionPlans:appCode can not null!");
        LambdaQueryWrapper<SubscriptionPlanDO> planDOQueryWrapper = new LambdaQueryWrapper<>();
        planDOQueryWrapper.eq(SubscriptionPlanDO::getAppCode, query.getAppCode());
        planDOQueryWrapper.eq(SubscriptionPlanDO::getDeleted, 0);
        planDOQueryWrapper.eq(query.getPlanId() != null, SubscriptionPlanDO::getId, query.getPlanId());
        planDOQueryWrapper.eq(query.getPlanName() != null, SubscriptionPlanDO::getName, query.getPlanName());
        planDOQueryWrapper.orderBy(true, true, SubscriptionPlanDO::getPrice);
        List<SubscriptionPlanDO> subscriptionPlanDOS = subscriptionPlanMapper.selectList(planDOQueryWrapper);
        if (CollectionUtils.isEmpty(subscriptionPlanDOS)) {
            return new ArrayList<>();
        }

        List<SubscriptionPlan> subscriptionPlans = SubscriptionPlanConverter.INSTANCE.convertA2B(subscriptionPlanDOS);
        if (CollectionUtils.isNotEmpty(query.getQueryInfos()) && query.getQueryInfos().contains(SubscriptionPlanQueryInfo.FEATURE)) {
            enrichFeature(subscriptionPlans);
        }
        return subscriptionPlans;
    }

    @Monitor(name = "根据套餐ID查询套餐信息", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public SubscriptionPlan queryByPlanId(Long planId, boolean fillFeatureInfo) {
        SubscriptionPlanDO planDO = subscriptionPlanMapper.selectById(planId);
        SubscriptionPlan subscriptionPlan = SubscriptionPlanConverter.INSTANCE.convertA2B(planDO);
        if (subscriptionPlan != null && fillFeatureInfo) {
            enrichFeature(Lists.newArrayList(subscriptionPlan));
        }
        return subscriptionPlan;
    }

    @Monitor(name = "查询免费套餐", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public SubscriptionPlan queryFreePlan(String appCode, boolean fillFeatureInfo) {
        // AppCode相等 && (planName == FREE || planPrice > 0)
        LambdaQueryWrapper<SubscriptionPlanDO> queryWrapper = new LambdaQueryWrapper<SubscriptionPlanDO>()
                .eq(SubscriptionPlanDO::getAppCode, appCode)
                .and(wrapper -> wrapper
                        .eq(SubscriptionPlanDO::getName, SubscriptionPlanName.FREE.name())
                        .or()
                        .le(SubscriptionPlanDO::getPrice, 0)
                );
        List<SubscriptionPlanDO> subscriptionPlanDOS = subscriptionPlanMapper.selectList(queryWrapper);
        SubscriptionPlanDO subscriptionPlanDO = CollectionUtil.getFirst(subscriptionPlanDOS);

        SubscriptionPlan subscriptionPlan = SubscriptionPlanConverter.INSTANCE.convertA2B(subscriptionPlanDO);
        if (fillFeatureInfo && subscriptionPlan != null) {
            enrichFeature(Lists.newArrayList(subscriptionPlan));
        }

        return subscriptionPlan;
    }

    @Monitor(name = "查询下一个同周期推荐套餐", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public SubscriptionPlan queryNextPlan(String appCode, boolean fillFeatureInfo, String planDurationUnit, BigDecimal price) {
        // AppCode相等 && (planName == FREE || planPrice > 0)
        LambdaQueryWrapper<SubscriptionPlanDO> queryWrapper = new LambdaQueryWrapper<SubscriptionPlanDO>()
                .eq(SubscriptionPlanDO::getAppCode, appCode)
                .and(wrapper -> wrapper
                        .eq(SubscriptionPlanDO::getDurationUnit, planDurationUnit)
                        .gt(SubscriptionPlanDO::getPrice, price)
                );
        List<SubscriptionPlanDO> subscriptionPlanDOS = subscriptionPlanMapper.selectList(queryWrapper);
        if (subscriptionPlanDOS == null || subscriptionPlanDOS.isEmpty()) {
            return null;
        }

        SubscriptionPlanDO subscriptionPlanDO = CollectionUtil.getFirst(subscriptionPlanDOS);

        SubscriptionPlan subscriptionPlan = SubscriptionPlanConverter.INSTANCE.convertA2B(subscriptionPlanDO);
        if (fillFeatureInfo && subscriptionPlan != null) {
            enrichFeature(Lists.newArrayList(subscriptionPlan));
        }

        return subscriptionPlan;
    }

    @Override
    public int saveFeatureUsage(FeatureUsage featureUsage) {

        try {
            FeatureUsageDO featureUsageDO = FeatureUsageConverter.INSTANCE.convertB2A(featureUsage);
            if (featureUsageDO.getId() != null) {
                return featureUsageMapper.updateById(featureUsageDO);
            } else {
                return featureUsageMapper.insert(featureUsageDO);
            }
        } catch (Exception e) {
            log.error("SubscriptionPlanRepositoryImpl saveFeatureUsage exp,featureUsage={}", JSON.toJSONString(featureUsage), e);
        }
        return 0;
    }

    @Override
    public FeatureUsage queryFeatureUsage(ShopifyFeatureQuery shopifyFeatureQuery) {

        try {
            Assertor.asserts(StringUtils.isNotBlank(shopifyFeatureQuery.getAppCode()), "queryFeatureUsage:appCode can not null!");
            Assertor.asserts(shopifyFeatureQuery.getId() != null, "queryFeatureUsage:id can not null!");
            Assertor.asserts(StringUtils.isNotBlank(shopifyFeatureQuery.getFeatureType()), "queryFeatureUsage:featureType can not null!");

            LambdaQueryWrapper<FeatureUsageDO> shopifyFeatureQueryWrapper = new LambdaQueryWrapper<>();
            shopifyFeatureQueryWrapper.eq(FeatureUsageDO::getAppCode, shopifyFeatureQuery.getAppCode());
            shopifyFeatureQueryWrapper.eq(FeatureUsageDO::getUserId, shopifyFeatureQuery.getId());
            shopifyFeatureQueryWrapper.eq(FeatureUsageDO::getFeatureType, shopifyFeatureQuery.getFeatureType());
            shopifyFeatureQueryWrapper.eq(FeatureUsageDO::getDeleted, 0);

            List<FeatureUsageDO> featureUsageDOS = featureUsageMapper.selectList(shopifyFeatureQueryWrapper);
            for (FeatureUsageDO featureUsageDO : featureUsageDOS) {
                if (isWithinTimeRange(featureUsageDO.getStartTime(), featureUsageDO.getEndTime())) {
                    return FeatureUsageConverter.INSTANCE.convertA2B(featureUsageDO);
                }
            }
        } catch (Exception e) {
            log.error("SubscriptionPlanRepositoryImpl queryFeatureUsage exp,shopifyFeatureQuery={}", JSON.toJSONString(shopifyFeatureQuery), e);
        }
        return null;
    }

    @Override
    public List<FeatureUsage> queryFeatureUsages(ShopifyFeatureQuery shopifyFeatureQuery) {

        try {
            Assertor.asserts(StringUtils.isNotBlank(shopifyFeatureQuery.getAppCode()), "queryFeatureUsage:appCode can not null!");
            Assertor.asserts(shopifyFeatureQuery.getId() != null, "queryFeatureUsage:id can not null!");
//            Assertor.asserts(StringUtils.isNotBlank(shopifyFeatureQuery.getFeatureType()), "queryFeatureUsage:featureType can not null!");

            LambdaQueryWrapper<FeatureUsageDO> shopifyFeatureQueryWrapper = new LambdaQueryWrapper<>();
            shopifyFeatureQueryWrapper.eq(FeatureUsageDO::getAppCode, shopifyFeatureQuery.getAppCode());
            shopifyFeatureQueryWrapper.eq(FeatureUsageDO::getUserId, shopifyFeatureQuery.getId());
//            shopifyFeatureQueryWrapper.eq(FeatureUsageDO::getFeatureType, shopifyFeatureQuery.getFeatureType());
            List<FeatureUsage> featureUsages = new ArrayList<>();
            List<FeatureUsageDO> featureUsageDOS = featureUsageMapper.selectList(shopifyFeatureQueryWrapper);
            for (FeatureUsageDO featureUsageDO : featureUsageDOS) {
                if (isWithinTimeRange(featureUsageDO.getStartTime(), featureUsageDO.getEndTime())) {
                    featureUsages.add(FeatureUsageConverter.INSTANCE.convertA2B(featureUsageDO));
                }
            }
            return featureUsages;
        } catch (Exception e) {
            log.error("SubscriptionPlanRepositoryImpl queryFeatureUsage exp,shopifyFeatureQuery={}", JSON.toJSONString(shopifyFeatureQuery), e);
        }
        return null;
    }

    @Override
    public List<SubscriptionPlan> queryByIds(List<Long> planIds) {
        Assertor.assertNonNull(planIds, "planIds is null");

        LambdaQueryWrapper<SubscriptionPlanDO> queryWrapper = new LambdaQueryWrapper<SubscriptionPlanDO>()
                .eq(SubscriptionPlanDO::getDeleted, Boolean.FALSE)
                .in(SubscriptionPlanDO::getId, planIds);

        List<SubscriptionPlanDO> subscriptionPlanDOS = subscriptionPlanMapper.selectList(queryWrapper);
        return SubscriptionPlanConverter.INSTANCE.convertA2B(subscriptionPlanDOS);
    }

    /**
     * 判断当前时间是否在startTime和endTime之间
     *
     * @param startTime
     * @param endTime
     * @return
     */
    private boolean isWithinTimeRange(Date startTime, Date endTime) {
        Date currentTime = new Date();
        return currentTime.after(startTime) && currentTime.before(endTime);
    }

    /**
     * 计划关联的特性
     *
     * @param subscriptionPlans
     */
    private void enrichFeature(List<SubscriptionPlan> subscriptionPlans) {
        LambdaQueryWrapper<SubscriptionPlanFeatureDO> planFeatureDOQueryWrapper = new LambdaQueryWrapper<>();
        List<Long> planId = subscriptionPlans.stream().map(SubscriptionPlan::getId).distinct().collect(Collectors.toList());
        planFeatureDOQueryWrapper.in(SubscriptionPlanFeatureDO::getSubscriptionPlanId, planId);
        planFeatureDOQueryWrapper.eq(SubscriptionPlanFeatureDO::getDeleted, 0);
        List<SubscriptionPlanFeatureDO> subscriptionPlanFeatureDOS = subscriptionPlanFeatureMapper.selectList(planFeatureDOQueryWrapper);

        if (CollectionUtils.isEmpty(subscriptionPlanFeatureDOS)) {
            return;
        }

        LambdaQueryWrapper<SubscriptionFeatureDO> featureDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<Long> featureId = subscriptionPlanFeatureDOS.stream().map(SubscriptionPlanFeatureDO::getSubscriptionFeatureId).distinct().collect(Collectors.toList());
        featureDOLambdaQueryWrapper.in(SubscriptionFeatureDO::getId, featureId);
        featureDOLambdaQueryWrapper.eq(SubscriptionFeatureDO::getDeleted, 0);
        featureDOLambdaQueryWrapper.orderBy(true, true, SubscriptionFeatureDO::getId);
        List<SubscriptionFeatureDO> subscriptionFeatureDOS = subscriptionFeatureMapper.selectList(featureDOLambdaQueryWrapper);

        if (CollectionUtils.isEmpty(subscriptionFeatureDOS)) {
            return;
        }

        List<SubscriptionFeature> subscriptionFeatures = SubscriptionFeatureConverter.INSTANCE.convertA2B(subscriptionFeatureDOS);

        subscriptionPlans.forEach(plan -> doEnrich(plan, subscriptionPlanFeatureDOS, subscriptionFeatures));
    }

    /**
     * enrich
     *
     * @param plan
     * @param subscriptionPlanFeatureDOS
     * @param subscriptionFeatures
     */
    private void doEnrich(SubscriptionPlan plan, List<SubscriptionPlanFeatureDO> subscriptionPlanFeatureDOS, List<SubscriptionFeature> subscriptionFeatures) {
        {
            // 当前plan的关联的feature
            List<SubscriptionPlanFeatureDO> planFeatures = subscriptionPlanFeatureDOS.stream()
                    .filter(planFeature -> planFeature.getSubscriptionPlanId().equals(plan.getId()))
                    .collect(Collectors.toList());
            // feature具体内容
            List<SubscriptionFeature> features = new ArrayList<>();
            planFeatures.forEach(planFeature -> {
                Optional<SubscriptionFeature> opSubscriptionFeature = subscriptionFeatures.stream()
                        .filter(feature -> feature.getId().equals(planFeature.getSubscriptionFeatureId()))
                        .findFirst();
                if (opSubscriptionFeature.isPresent()) {
                    SubscriptionFeature feature = new SubscriptionFeature();
                    SubscriptionFeature subscriptionFeature = opSubscriptionFeature.get();
                    BeanUtils.copyProperties(subscriptionFeature, feature);
                    feature.setPlanFeatureId(planFeature.getId());
                    features.add(feature);
                }
            });
            // enrich
            plan.setFeatures(features);
        }
    }
}
