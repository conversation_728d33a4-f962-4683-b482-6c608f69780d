package com.alibaba.copilot.enabler.infra.payment.factory;

import com.alibaba.aepay.fund.business.api.payment.response.InquiryRefundResponse;
import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.payment.constant.RefundStatusEnum;
import com.alibaba.copilot.enabler.client.payment.dto.QueryRefundResultDTO;
import com.alibaba.copilot.enabler.infra.payment.utils.AEResultConvertUtils;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @version 2024/1/15
 */
@AllArgsConstructor
public class QueryRefundResultDTOResultBuilder implements Builder<SingleResult<QueryRefundResultDTO>> {

    private final InquiryRefundResponse response;

    @Override
    public SingleResult<QueryRefundResultDTO> build() {
        return AEResultConvertUtils.convertResult(response, this::convertDTO);
    }

    private QueryRefundResultDTO convertDTO(InquiryRefundResponse rsp) {
        return new QueryRefundResultDTO()
                .setRefundAmount(rsp.getRefundAmount().getAmount())
                .setRefundId(rsp.getRefundId())
                .setRefundRequestId(rsp.getRefundRequestId())
                .setRefundStatus(RefundStatusEnum.of(rsp.getRefundStatus()))
                .setRefundTime(rsp.getRefundTime());
    }
}
