package com.alibaba.copilot.enabler.infra.subscription.dataobject;

import com.alibaba.copilot.enabler.infra.base.bean.BaseDO;
import com.alibaba.copilot.enabler.infra.base.utils.EnvUtils;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 订阅计划-用户订阅关系表（订单表）
 */
@Setter
@Getter
@TableName(value = "subscription_order")
public class SubscriptionOrderDO extends BaseDO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 产品唯一code
     */
    private String appCode;

    /**
     * 用户产品关系ID
     */
    private Long userAppRelationId;

    /**
     * 计划ID
     */
    private Long subscriptionPlanId;

    /**
     * 计划名称
     */
    private String subscriptionPlanName;

    /**
     * 下次执行的计划ID
     */
    private Long nextPlanId;

    /**
     * 下次执行的计划名称
     */
    private String nextPlanName;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 折扣tag
     */
    private String subscriptionDiscountTag;

    /**
     * 是否续定（否取消订阅）
     */
    private Boolean autoRenew;

    /**
     * 下次自动续费的时间
     */
    private Date nextRenewalTime;

    /**
     * 是否包含试用期
     */
    private Boolean isIncludeTrial;

    /**
     * 计划价格
     */
    private BigDecimal planPrice;

    /**
     * 真实付款费用
     */
    private BigDecimal actualFee;

    /**
     * 订单履约开始时间
     */
    @TableField(value = "start_time")
    private Date performStartTime;

    /**
     * 订单履约结束时间
     */
    @TableField(value = "end_time")
    private Date performEndTime;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 已发起下一次续订
     */
    @TableField(value = "initiate_payed")
    private Boolean hadNextRenew;

    /**
     * 环境
     */
    private String env = EnvUtils.getEnv();

    /**
     * Shopify订阅ID
     */
    private Long shopifySubscriptionId;

    /**
     * 订阅支付类型
     */
    private String subscriptionPayType;

    /**
     * 外部订阅ID
     */
    private String outerSubscriptionId;

    /**
     * 属性（JSON格式）
     */
    private String attributes;
}
