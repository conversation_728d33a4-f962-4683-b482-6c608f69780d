package com.alibaba.copilot.enabler.infra.subscription.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionOrderQuery;
import com.alibaba.copilot.enabler.infra.base.utils.EnvUtils;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionOrderDO;
import com.alibaba.copilot.enabler.infra.subscription.factory.SubscriptionOrderConverter;
import com.alibaba.copilot.enabler.infra.subscription.mapper.SubscriptionOrderMapper;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Repository
public class SubscriptionOrderRepositoryImpl implements SubscriptionOrderRepository {

    @Autowired
    private SubscriptionOrderMapper subscriptionOrderMapper;

    @Monitor(name = "查询订单", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public List<SubscriptionOrder> querySubscriptionOrders(SubscriptionOrderQuery query) {
        Assertor.asserts(StringUtils.isNotBlank(query.getAppCode()), "querySubscriptionPlans:appCode can not null!");

        List<SubscriptionOrderDO> subscriptionOrderDOS = queryValidList(queryWrapper -> queryWrapper
                .eq(query.getUserId() != null, SubscriptionOrderDO::getUserId, query.getUserId())
                .eq(SubscriptionOrderDO::getAppCode, query.getAppCode())
                .in(CollectionUtils.isNotEmpty(query.getOrderIds()), SubscriptionOrderDO::getId, query.getOrderIds())
                .in(query.getStatus() != null && !query.getStatus().isEmpty(), SubscriptionOrderDO::getStatus, query.getStatus())
                .orderByDesc(SubscriptionOrderDO::getId)
                .last(query.getLimit() != null, "limit" + StringUtils.SPACE + query.getLimit())
        );

        return SubscriptionOrderConverter.INSTANCE.convertA2B(subscriptionOrderDOS);
    }

    @Monitor(name = "查询已订阅开始时间周期的订单", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public List<SubscriptionOrder> queryStartSubscriptionOrders(SubscriptionOrderQuery query) {
        Assertor.asserts(StringUtils.isNotBlank(query.getAppCode()), "queryStartSubscriptionOrders:appCode can not null!");

        List<SubscriptionOrderDO> subscriptionOrderDOS = queryValidList(queryWrapper -> queryWrapper
                .eq(SubscriptionOrderDO::getAppCode, query.getAppCode())
                .in(CollectionUtils.isNotEmpty(query.getOrderIds()), SubscriptionOrderDO::getId, query.getOrderIds())
                .in(query.getStatus() != null, SubscriptionOrderDO::getStatus, query.getStatus())
                .ge(query.getStartTime() != null, SubscriptionOrderDO::getPerformStartTime, query.getStartTime())
                .le(query.getEndTime() != null, SubscriptionOrderDO::getPerformStartTime, query.getEndTime())
                .orderByDesc(SubscriptionOrderDO::getId)
                .last(query.getLimit() != null, "limit" + StringUtils.SPACE + query.getLimit())
        );

        return SubscriptionOrderConverter.INSTANCE.convertA2B(subscriptionOrderDOS);
    }


    @Monitor(name = "查询结束订阅时间周期的订单", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public List<SubscriptionOrder> queryCancelSubscriptionOrders(SubscriptionOrderQuery query) {
        Assertor.asserts(StringUtils.isNotBlank(query.getAppCode()), "queryCancelSubscriptionOrders:appCode can not null!");

        List<SubscriptionOrderDO> subscriptionOrderDOS = queryValidList(queryWrapper -> queryWrapper
                .eq(SubscriptionOrderDO::getAppCode, query.getAppCode())
                .eq(query.getAutoRenew() != null, SubscriptionOrderDO::getAutoRenew, query.getAutoRenew())
                .in(CollectionUtils.isNotEmpty(query.getOrderIds()), SubscriptionOrderDO::getId, query.getOrderIds())
                .in(query.getStatus() != null, SubscriptionOrderDO::getStatus, query.getStatus())
                .ge(query.getNextRenewalStartTime() != null, SubscriptionOrderDO::getPerformEndTime, query.getNextRenewalStartTime())
                .le(query.getNextRenewalEndTime() != null, SubscriptionOrderDO::getPerformEndTime, query.getNextRenewalEndTime())
                .orderByDesc(SubscriptionOrderDO::getId)
                .last(query.getLimit() != null, "limit" + StringUtils.SPACE + query.getLimit())
        );

        return SubscriptionOrderConverter.INSTANCE.convertA2B(subscriptionOrderDOS);
    }

    @Monitor(name = "扫描订单", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public List<SubscriptionOrder> scanSubscriptionOrders(SubscriptionOrderQuery query) {
        List<SubscriptionOrderDO> subscriptionOrderDOS = queryValidList(queryWrapper -> {
            Optional.ofNullable(query.getStatus())
                    .ifPresent(status -> queryWrapper.in(SubscriptionOrderDO::getStatus, status));

            Optional.ofNullable(query.getHadNextRenew())
                    .ifPresent(hadNextRenew -> queryWrapper.eq(SubscriptionOrderDO::getHadNextRenew, hadNextRenew));

            Optional.ofNullable(query.getAutoRenew())
                    .ifPresent(autoRenew -> queryWrapper.eq(SubscriptionOrderDO::getAutoRenew, autoRenew));

            Optional.ofNullable(query.getNextRenewalStartTime())
                    .ifPresent(startTime -> queryWrapper.ge(SubscriptionOrderDO::getNextRenewalTime, startTime));

            Optional.ofNullable(query.getNextRenewalEndTime())
                    .ifPresent(endTime -> queryWrapper.le(SubscriptionOrderDO::getNextRenewalTime, endTime));
        });

        return SubscriptionOrderConverter.INSTANCE.convertA2B(subscriptionOrderDOS);
    }

    @Monitor(name = "保存订单", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public int saveSubscriptionOrder(SubscriptionOrder order) {
        log.info("SubscriptionOrderRepositoryImpl.saveSubscriptionOrder, order={}", JSON.toJSONString(order));

        SubscriptionOrderDO subscriptionOrderDO = SubscriptionOrderConverter.INSTANCE.convertB2A(order);
        if (subscriptionOrderDO.getId() == null) {
            subscriptionOrderMapper.insert(subscriptionOrderDO);
            order.setId(subscriptionOrderDO.getId());
        } else {
            subscriptionOrderMapper.updateById(subscriptionOrderDO);
        }
        return 1;
    }

    @Override
    public int clearNextPlan(Long orderId) {
        Assertor.assertNonNull(orderId, "orderId is null");
        return subscriptionOrderMapper.clearNextPlan(orderId);
    }

    @Monitor(name = "查询用户对应App的历史订单", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public List<SubscriptionOrder> getHistoryOrders(Long userId, String appCode) {
        Assertor.assertNonNull(userId, "userId can not null!");
        Assertor.assertNotBlank(appCode, "appCode can not null!");

        SubscriptionOrderQuery subscriptionOrderQuery = SubscriptionOrderQuery.builder()
                .userId(userId)
                .appCode(appCode)
                .build();
        return querySubscriptionOrders(subscriptionOrderQuery);
    }

    @Monitor(name = "根据订单ID查询订单信息", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public SubscriptionOrder getByOrderId(Long orderId) {
        SubscriptionOrderDO orderDO = queryValidFirst(queryWrapper -> queryWrapper
                .eq(SubscriptionOrderDO::getId, orderId)
        );
        return SubscriptionOrderConverter.INSTANCE.convertA2B(orderDO);
    }

    /**
     * 根据订单ID查询订单信息
     *
     * @param userId
     * @param orderId 订单ID
     * @return 订单信息
     */
    @Override
    public SubscriptionOrder getByOrderId(Long userId, Long orderId) {
        SubscriptionOrderDO orderDO = queryValidFirst(queryWrapper -> queryWrapper
                .eq(userId != null, SubscriptionOrderDO::getUserId, userId)
                .eq(SubscriptionOrderDO::getId, orderId)
        );
        return SubscriptionOrderConverter.INSTANCE.convertA2B(orderDO);
    }

    /**
     * 根据外部订阅ID查询订单信息
     *
     * @param userId
     * @param outerSubscriptionId
     * @return
     */
    @Override
    public SubscriptionOrder getByOuterSubscriptionId(Long userId, String outerSubscriptionId) {
        SubscriptionOrderDO orderDO = queryValidFirst(queryWrapper -> queryWrapper
                .eq(userId != null, SubscriptionOrderDO::getUserId, userId)
                .eq(SubscriptionOrderDO::getOuterSubscriptionId, outerSubscriptionId)
        );
        return SubscriptionOrderConverter.INSTANCE.convertA2B(orderDO);
    }

    @Monitor(name = "查询当前生效的订单", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public SubscriptionOrder queryEffectOrder(String appCode, Long userId) {
        Assertor.assertNonNull(appCode, "appCode is null");
        Assertor.assertNonNull(userId, "userId is null");

        List<SubscriptionOrderDO> subscriptionOrderDOS = queryValidList(queryWrapper -> queryWrapper
                .eq(SubscriptionOrderDO::getAppCode, appCode)
                .eq(SubscriptionOrderDO::getUserId, userId)
                .eq(SubscriptionOrderDO::getStatus, SubscriptionOrderStatus.IN_EFFECT.name())
        );


        SubscriptionOrderDO orderDO = CollectionUtil.getFirst(subscriptionOrderDOS);

        return SubscriptionOrderConverter.INSTANCE.convertA2B(orderDO);
    }

    @Monitor(name = "查询指定订单ID列表对应的订单信息", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    @Override
    public List<SubscriptionOrder> queryByOrderIds(List<Long> orderIds) {
        Assertor.assertNotEmpty(orderIds, "orderIds is empty");

        List<Long> distinctOrderIds = orderIds.stream()
                .distinct()
                .collect(Collectors.toList());
        List<SubscriptionOrderDO> subscriptionOrderDOS = subscriptionOrderMapper.selectBatchIds(distinctOrderIds);
        return SubscriptionOrderConverter.INSTANCE.convertA2B(subscriptionOrderDOS);
    }

    @Override
    @Monitor(name = "查询指定 Shopify 订阅 ID 对应的订单信息", layer = Monitor.Layer.REPOSITORY, level = Monitor.Level.P1)
    public SubscriptionOrder queryByShopifySubscriptionId(Long shopifySubscriptionId) {
        Assertor.assertNonNull(shopifySubscriptionId, "shopify subscription id is null");

        SubscriptionOrderDO orderDO = queryValidFirst(queryWrapper -> queryWrapper
                .eq(SubscriptionOrderDO::getShopifySubscriptionId, shopifySubscriptionId)
        );

        return SubscriptionOrderConverter.INSTANCE.convertA2B(orderDO);
    }

    @Override
    public Map<Long, SubscriptionOrder> queryOrderByUserIds(List<Long> userIds, List<SubscriptionOrderStatus> statusList) {
        Assertor.assertNotEmpty(userIds, "userIds is empty");
        Assertor.assertNotEmpty(statusList, "statusList is empty");

        List<String> statuses = statusList.stream()
                .distinct()
                .map(SubscriptionOrderStatus::name)
                .collect(Collectors.toList());

        List<SubscriptionOrderDO> subscriptionOrderDOS = queryValidList(queryWrapper -> queryWrapper
                .in(SubscriptionOrderDO::getUserId, userIds)
                .in(SubscriptionOrderDO::getStatus, statuses)
        );

        return subscriptionOrderDOS.stream()
                .map(SubscriptionOrderConverter.INSTANCE::convertA2B)
                .collect(Collectors.toMap(SubscriptionOrder::getId, o -> o, (v1, v2) -> v1));
    }

    @Override
    public Map<Long, List<SubscriptionOrder>> queryUserId2OrderByUserIds(String appCode, List<Long> userIds, List<SubscriptionOrderStatus> statusList) {
        Assertor.assertNotEmpty(userIds, "userIds is empty");
        Assertor.assertNotEmpty(statusList, "statusList is empty");

        List<String> statuses = statusList.stream()
                .distinct()
                .map(SubscriptionOrderStatus::name)
                .collect(Collectors.toList());

        List<SubscriptionOrderDO> subscriptionOrderDOS = queryValidList(queryWrapper -> queryWrapper
                .eq(SubscriptionOrderDO::getAppCode, appCode)
                .in(SubscriptionOrderDO::getUserId, userIds)
                .in(SubscriptionOrderDO::getStatus, statuses)
        );

        Map<Long, List<SubscriptionOrder>> result = new HashMap<>();
        for (SubscriptionOrderDO subscriptionOrderDO : subscriptionOrderDOS) {
            if (subscriptionOrderDO.getUserId() == null) {
                continue;
            }
            if (result.containsKey(subscriptionOrderDO.getUserId())) {
                result.get(subscriptionOrderDO.getUserId()).add(SubscriptionOrderConverter.INSTANCE.convertA2B(subscriptionOrderDO));
            } else {
                result.put(subscriptionOrderDO.getUserId(), new ArrayList<>());
                result.get(subscriptionOrderDO.getUserId()).add(SubscriptionOrderConverter.INSTANCE.convertA2B(subscriptionOrderDO));
            }
        }

        return result;
    }

    @Override
    public List<SubscriptionOrder> queryListForBill(List<String> appCodeList, Date startTime, Date endTime) {
        Assertor.assertNotEmpty(appCodeList, "appCode is blank");
        Assertor.assertNonNull(startTime, "startTime is null");
        Assertor.assertNonNull(endTime, "endTime is null");

        List<String> targetStatusList = Stream.of(
                SubscriptionOrderStatus.IN_EFFECT,
                SubscriptionOrderStatus.UNSUBSCRIBE,
                SubscriptionOrderStatus.COMPLETED
        )
                .map(SubscriptionOrderStatus::name)
                .collect(Collectors.toList());

        // 查询账单接口, 仅查询线上环境数据
        String env = EnvUtils.ONLINE;
        List<SubscriptionOrderDO> subscriptionOrderDOS = queryValidList(env, queryWrapper -> queryWrapper
                .in(SubscriptionOrderDO::getAppCode, appCodeList)
                .in(SubscriptionOrderDO::getStatus, targetStatusList)
                .le(SubscriptionOrderDO::getPerformStartTime, endTime)
                .ge(SubscriptionOrderDO::getPerformEndTime, startTime)
        );
        return SubscriptionOrderConverter.INSTANCE.convertA2B(subscriptionOrderDOS);
    }

    /**
     * 封装通用的查询逻辑, 减少重复设置, 防止遗漏公共过滤参数
     */
    private SubscriptionOrderDO queryValidFirst(Consumer<LambdaQueryWrapper<SubscriptionOrderDO>> consumer) {
        return CollectionUtil.getFirst(queryValidList(consumer));
    }

    /**
     * 封装通用的查询逻辑, 减少重复设置, 防止遗漏公共过滤参数
     */
    private List<SubscriptionOrderDO> queryValidList(Consumer<LambdaQueryWrapper<SubscriptionOrderDO>> consumer) {
        return queryValidList(EnvUtils.getEnv(), consumer);
    }

    /**
     * 封装通用的查询逻辑, 减少重复设置, 防止遗漏公共过滤参数
     */
    private List<SubscriptionOrderDO> queryValidList(String env, Consumer<LambdaQueryWrapper<SubscriptionOrderDO>> consumer) {
        LambdaQueryWrapper<SubscriptionOrderDO> queryWrapper = new LambdaQueryWrapper<SubscriptionOrderDO>()
                .eq(SubscriptionOrderDO::getEnv, env)
                .eq(SubscriptionOrderDO::getDeleted, Boolean.FALSE);
        if (consumer != null) {
            consumer.accept(queryWrapper);
        }
        return subscriptionOrderMapper.selectList(queryWrapper);
    }
}
