package com.alibaba.copilot.enabler.infra.payment.gateway;

import com.alibaba.copilot.enabler.domain.payment.gateway.geoip.GeoIpGateway;
import com.alibaba.copilot.enabler.domain.payment.gateway.geoip.dto.GetAddressInfoByIpResponse;
import com.aliexpress.geoip.GeoipLocationV2;
import com.aliexpress.geoip.GeoipService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/06
 */
@Slf4j
@Service
public class GeoIpGatewayImpl implements GeoIpGateway {

    @Resource
    private GeoipService geoipService;

    @Override
    public GetAddressInfoByIpResponse getAddressInfoByIp(String ip) {
        GetAddressInfoByIpResponse response = new GetAddressInfoByIpResponse();
        response.setIp(ip);
        try {
            GeoipLocationV2 geoipLocationV2 = geoipService.queryV2(ip, "en");
            response.setCountryCode(geoipLocationV2.getCountryCode());
            response.setCountryName(geoipLocationV2.getCountryName());
            response.setRegionCode(geoipLocationV2.getRegionCode());
            response.setRegionName(geoipLocationV2.getRegionName());
            response.setCityCode(geoipLocationV2.getCityCode());
            response.setCityName(geoipLocationV2.getCityName());
            response.setCountyCode(geoipLocationV2.getCountyCode());
            response.setCountyName(geoipLocationV2.getCountyName());
        } catch (Exception e) {
            log.error("GeoIpGatewayImpl.getAddressInfoByIp error, ip:{}", ip, e);
        }
        return response;
    }
}