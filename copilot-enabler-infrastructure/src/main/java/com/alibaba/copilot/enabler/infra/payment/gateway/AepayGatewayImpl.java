package com.alibaba.copilot.enabler.infra.payment.gateway;

import com.alibaba.aepay.fund.business.api.payment.PaymentFacade;
import com.alibaba.aepay.fund.business.api.payment.PaymentInquiryFacade;
import com.alibaba.aepay.fund.business.api.payment.request.*;
import com.alibaba.aepay.fund.business.api.payment.response.*;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.enabler.client.payment.dto.*;
import com.alibaba.copilot.enabler.client.payment.request.CashierPayRequest;
import com.alibaba.copilot.enabler.domain.payment.dto.PaymentAsyncResultDTO;
import com.alibaba.copilot.enabler.domain.payment.dto.PaymentSyncResultDTO;
import com.alibaba.copilot.enabler.domain.payment.gateway.AepayGateway;
import com.alibaba.copilot.enabler.domain.subscription.request.InitiatePaymentRequest;
import com.alibaba.copilot.enabler.domain.subscription.request.InquiryPaymentRequest;
import com.alibaba.copilot.enabler.infra.payment.factory.*;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2023/9/27
 */
@Slf4j
@Service
public class AepayGatewayImpl implements AepayGateway {

    @Resource
    private PaymentFacade paymentFacade;

    @Resource
    private PaymentInquiryFacade paymentInquiryFacade;

    @Monitor(name = "创建支付会话", layer = Monitor.Layer.GATEWAY, level = Monitor.Level.P1)
    @Override
    public SingleResult<PaymentSessionDTO> createPaymentSession(CreatePaymentSessionDTO sessionDTO) {
        log.info("createPaymentSession sessionDTO={}", JSON.toJSONString(sessionDTO));

        CreatePaymentSessionRequest request = new CreatePaymentSessionRequestBuilder(sessionDTO).build();
        log.info("createPaymentSession request={}", JSON.toJSONString(request));

        PaymentSessionResponse response = paymentFacade.createPaymentSession(request);
        log.info("createPaymentSession response={}", JSON.toJSONString(response));

        return new PaymentSessionDTOResultBuilder(response).build();
    }

    @Monitor(name = "发起支付", layer = Monitor.Layer.GATEWAY, level = Monitor.Level.P1)
    @Override
    public SingleResult<PaymentAsyncResultDTO> initiatePay(InitiatePaymentRequest request) {
        PaymentRequest paymentRequest = new PaymentRequestBuilder(request).build();
        log.info("initiatePay paymentRequest={}", JSON.toJSONString(paymentRequest));

        PaymentResponse paymentResponse = paymentFacade.pay(paymentRequest);
        log.info("initiatePay response={}", JSON.toJSONString(paymentResponse));

        return new PaymentAsyncResultDTOResultBuilder(paymentResponse).build();
    }

    @Override
    @Monitor(name = "收银台支付", layer = Monitor.Layer.GATEWAY, level = Monitor.Level.P1)
    public SingleResult<PaymentAsyncResultDTO> cashierPay(InitiatePaymentRequest request) {
        PaymentRequest paymentRequest = new CashierPaymentRequestBuilder(request).build();
        log.info("initiatePay paymentRequest={}", JSON.toJSONString(paymentRequest));

        PaymentResponse paymentResponse = paymentFacade.pay(paymentRequest);
        log.info("initiatePay response={}", JSON.toJSONString(paymentResponse));

        return new PaymentAsyncResultDTOResultBuilder(paymentResponse).build();
    }

    @Monitor(name = "支付结果主动查询", layer = Monitor.Layer.GATEWAY, level = Monitor.Level.P1)
    @Override
    public SingleResult<PaymentSyncResultDTO> inquiryPayment(InquiryPaymentRequest request) {
        log.info("inquiryPayment request={}", JSON.toJSONString(request));

        PaymentInquiryRequest paymentInquiryRequest = new PaymentInquiryRequestBuilder(request).build();
        log.info("inquiryPayment paymentInquiryRequest={}", JSON.toJSONString(paymentInquiryRequest));

        PaymentInquiryResponse paymentInquiryResponse = paymentInquiryFacade.inquiryPayment(paymentInquiryRequest);
        log.info("inquiryPayment paymentInquiryResponse={}", JSON.toJSONString(paymentInquiryResponse));

        return new PaymentSyncResultDTOResultBuilder(paymentInquiryResponse).build();
    }

    @Monitor(name = "发起退款", layer = Monitor.Layer.GATEWAY, level = Monitor.Level.P1)
    @Override
    public SingleResult<RefundResultDTO> refund(RefundDTO refundDTO) {
        log.info("refund refundDTO={}", JSON.toJSONString(refundDTO));

        RefundRequest refundRequest = new RefundRequestBuilder(refundDTO).build();
        log.info("refund refundRequest={}", JSON.toJSONString(refundRequest));

        RefundResponse refundResponse = paymentFacade.refund(refundRequest);
        log.info("refund invoke completed, refundResponse={}", JSON.toJSONString(refundResponse));

        return new RefundResultDTOResultBuilder(refundResponse).build();
    }

    @Monitor(name = "退款结果查询", layer = Monitor.Layer.GATEWAY, level = Monitor.Level.P1)
    @Override
    public SingleResult<QueryRefundResultDTO> queryRefundResult(QueryRefundDTO queryRefundDTO) {
        log.info("queryRefundResult, queryRefundDTO={}", JSON.toJSONString(queryRefundDTO));

        InquiryRefundRequest request = new InquiryRefundRequestBuilder(queryRefundDTO).build();
        log.info("queryRefundResult request={}", JSON.toJSONString(request));

        InquiryRefundResponse response = paymentInquiryFacade.inquiryRefund(request);
        log.info("queryRefundResult response={}", JSON.toJSONString(response));

        return new QueryRefundResultDTOResultBuilder(response).build();
    }
}
