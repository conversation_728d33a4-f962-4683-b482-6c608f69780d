package com.alibaba.copilot.enabler.infra.subscription.factory;


import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.copilot.enabler.domain.subscription.model.Benefit;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionFeature;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionFeatureAttributes;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionFeatureDO;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * SubscriptionFeatureConverter
 */
@Component
public class SubscriptionFeatureConverter implements Converter<SubscriptionFeatureDO, SubscriptionFeature> {
    public static final Converter<SubscriptionFeatureDO, SubscriptionFeature> INSTANCE = new SubscriptionFeatureConverter();

    private static final String[] ignoreProperties = new String[]{"attributes"};

    @Override
    public SubscriptionFeature convertA2B(SubscriptionFeatureDO subscriptionFeatureDO) {
        if (subscriptionFeatureDO == null) {
            return null;
        }
        SubscriptionFeature subscriptionFeature = new SubscriptionFeature();
        BeanUtils.copyProperties(subscriptionFeatureDO, subscriptionFeature, ignoreProperties);
        subscriptionFeature.setBenefit(JSON.parseObject(subscriptionFeatureDO.getBenefit(), Benefit.class));
        subscriptionFeature.setAttributes(new SubscriptionFeatureAttributes(subscriptionFeatureDO.getAttributes()));
        return subscriptionFeature;
    }

    @Override
    public SubscriptionFeatureDO convertB2A(SubscriptionFeature subscriptionFeature) {
        if (subscriptionFeature == null) {
            return null;
        }
        SubscriptionFeatureDO subscriptionFeatureDO = new SubscriptionFeatureDO();
        BeanUtils.copyProperties(subscriptionFeature, subscriptionFeatureDO, ignoreProperties);
        subscriptionFeatureDO.setBenefit(JSON.toJSONString(subscriptionFeature.getBenefit()));
        subscriptionFeatureDO.setAttributes(subscriptionFeatureDO.getAttributes());
        return subscriptionFeatureDO;
    }
}
