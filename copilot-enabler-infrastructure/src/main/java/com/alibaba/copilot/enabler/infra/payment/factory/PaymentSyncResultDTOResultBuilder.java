package com.alibaba.copilot.enabler.infra.payment.factory;

import com.alibaba.aepay.fund.business.api.payment.dto.PaymentResultInfo;
import com.alibaba.aepay.fund.business.api.payment.dto.ThreeDSResult;
import com.alibaba.aepay.fund.business.api.payment.response.PaymentInquiryResponse;
import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentStatusEnum;
import com.alibaba.copilot.enabler.client.payment.dto.PaymentResultDTO;
import com.alibaba.copilot.enabler.client.payment.dto.ThreeDSResultDTO;
import com.alibaba.copilot.enabler.domain.payment.dto.PaymentSyncResultDTO;
import com.alibaba.copilot.enabler.infra.base.utils.ModelConvertUtils;
import com.alibaba.copilot.enabler.infra.payment.utils.AEResultConvertUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;

/**
 * <AUTHOR>
 * @version 2024/1/15
 */
@Slf4j
@AllArgsConstructor
public class PaymentSyncResultDTOResultBuilder implements Builder<SingleResult<PaymentSyncResultDTO>> {

    private final PaymentInquiryResponse response;

    @Override
    public SingleResult<PaymentSyncResultDTO> build() {
        return AEResultConvertUtils.convertResult(response, this::convertDTO);
    }

    private PaymentSyncResultDTO convertDTO(PaymentInquiryResponse response) {
        PaymentSyncResultDTO paymentSyncResultDTO = new PaymentSyncResultDTO();
        paymentSyncResultDTO.setPaymentResultCode(response.getPaymentResultCode());
        paymentSyncResultDTO.setPaymentResultMessage(response.getPaymentResultMessage());
        paymentSyncResultDTO.setPaymentRequestId(response.getPaymentRequestId());
        paymentSyncResultDTO.setPaymentId(response.getPaymentId());
        paymentSyncResultDTO.setPaymentAmount(response.getPaymentAmount());
        paymentSyncResultDTO.setPaymentCreateTime(response.getPaymentCreateTime());
        paymentSyncResultDTO.setPaymentTime(response.getPaymentTime());
        PaymentStatusEnum paymentStatusEnum = PaymentStatusEnum.of(response.getPaymentStatus());
        paymentSyncResultDTO.setPaymentStatus(paymentStatusEnum);

        paymentSyncResultDTO.setPaymentResultDTO(convertPaymentResultDTO(response));

        return paymentSyncResultDTO;
    }

    @Nullable
    private static PaymentResultDTO convertPaymentResultDTO(PaymentInquiryResponse response) {
        PaymentResultInfo paymentResultInfo = response.getPaymentResultInfo();
        if (paymentResultInfo == null) {
            return null;
        }

        PaymentResultDTO paymentResultDTO = ModelConvertUtils.copyByReflect(paymentResultInfo, PaymentResultDTO::new);

        ThreeDSResult threeDSResult = paymentResultInfo.getThreeDSResult();
        if (threeDSResult != null) {
            ThreeDSResultDTO threeDSResultDTO = ModelConvertUtils.copyByReflect(threeDSResult, ThreeDSResultDTO::new);
            paymentResultDTO.setThreeDSResultDTO(threeDSResultDTO);
        }

        return paymentResultDTO;
    }
}
