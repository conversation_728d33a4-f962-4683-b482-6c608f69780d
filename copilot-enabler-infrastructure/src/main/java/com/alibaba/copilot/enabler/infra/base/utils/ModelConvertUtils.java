package com.alibaba.copilot.enabler.infra.base.utils;

import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2023/10/7
 */
@Slf4j
public class ModelConvertUtils {

    /**
     * 通用模型转化
     *
     * @param a        原始模型
     * @param supplier 转化模型 的构造器
     * @param <A>      原始模型类型
     * @param <B>      转化模型类型
     * @return 转化模型
     */
    public static <A, B> B copyByReflect(@Nullable A a, @Nonnull Supplier<B> supplier) {
        return copyByReflect(a, ignored -> supplier.get());
    }

    /**
     * 通用模型转化
     *
     * @param a         原始模型
     * @param converter 模型转化器
     * @param <A>       原始模型类型
     * @param <B>       转化模型类型
     * @return 转化模型
     */
    public static <A, B> B copyByReflect(@Nullable A a, @Nonnull Function<A, B> converter) {
        return Optional.ofNullable(a)
                .map(obj -> {
                    B ret = converter.apply(obj);
                    BeanUtils.copyProperties(obj, ret);
                    return ret;
                })
                .orElse(null);
    }

    /**
     * 通用模型转化
     *
     * @param aList    原始模型列表
     * @param supplier 转化模型 的构造器
     * @param <A>      原始模型类型
     * @param <B>      转化模型类型
     * @return 转化模型
     */
    public static <A, B> List<B> copyListByReflect(@Nullable List<A> aList, @Nonnull Supplier<B> supplier) {
        return copyListByReflect(aList, ignored -> supplier.get());
    }

    /**
     * 通用模型转化
     *
     * @param aList     原始模型列表
     * @param converter 模型转化器
     * @param <A>       原始模型类型
     * @param <B>       转化模型类型
     * @return 转化模型
     */
    public static <A, B> List<B> copyListByReflect(@Nullable List<A> aList, @Nonnull Function<A, B> converter) {
        return Optional.ofNullable(aList)
                .map(as -> as.stream()
                        .map(a -> copyByReflect(a, converter))
                        .collect(Collectors.toList())
                )
                .orElse(null);
    }

    /**
     * 转化SingleResult
     *
     * @param result    原始Result
     * @param converter Model转换器
     * @param <A>       原始Result的data类型
     * @param <B>       转化后的data类型
     * @return 转化后的Result
     */
    public static <A, B> SingleResult<B> convertSingResult(SingleResult<A> result, Function<A, B> converter) {
        return convertSingResult(result, null, converter);
    }

    /**
     * 转化SingleResult
     *
     * @param result    原始Result
     * @param errorCode 指定的错误码 (不传时, 默认取result中的)
     * @param converter Model转换器
     * @param <A>       原始Result的data类型
     * @param <B>       转化后的data类型
     * @return 转化后的Result
     */
    public static <A, B> SingleResult<B> convertSingResult(SingleResult<A> result, ErrorCode errorCode, Function<A, B> converter) {
        SingleResult<B> finalResult = new SingleResult<>();
        boolean success = result.isSuccess();
        finalResult.setSuccess(success);
        if (errorCode == null) {
            finalResult.setCode(result.getCode());
            finalResult.setMessage(result.getMessage());
        } else {
            finalResult.setCode(errorCode.getCode());
            finalResult.setMessage(errorCode.getMessage());
        }
        finalResult.setAttributes(result.getAttributes());

        if (!success) {
            return finalResult;
        } else {
            Optional.ofNullable(result.getData())
                    .map(converter)
                    .ifPresent(finalResult::setData);
            return finalResult;
        }
    }

    /**
     * 包装 {@link SingleResult}结果, 出错时, 返回失败结果
     *
     * @param dataSupplier 数据获取函数
     * @param <T>          数据类型
     * @return 包装后的结果
     */
    public static <T> SingleResult<T> wrapResult(Supplier<T> dataSupplier) {
        try {
            T data = dataSupplier.get();
            return SingleResult.buildSuccess(data);
        } catch (Exception e) {
            log.error("wrapResult, invoke error", e);
            return wrapExceptionResult(e);
        }
    }

    @SuppressWarnings("unchecked")
    public static <T> SingleResult<T> wrapExceptionResult(Exception e) {
        if (e instanceof BizException) {
            ErrorCode errorCode = ((BizException) e).getErrorCode();
            if (errorCode != null) {
                return SingleResult.buildFailure(errorCode.getCode(), errorCode.getMessage());
            }
        }
        return SingleResult.buildFailure(e.getMessage());
    }
}
