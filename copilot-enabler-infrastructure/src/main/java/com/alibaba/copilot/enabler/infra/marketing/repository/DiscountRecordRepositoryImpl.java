package com.alibaba.copilot.enabler.infra.marketing.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.domain.marketing.model.DiscountRecord;
import com.alibaba.copilot.enabler.domain.marketing.repository.DiscountRecordRepository;
import com.alibaba.copilot.enabler.infra.marketing.dataobject.DiscountRecordDO;
import com.alibaba.copilot.enabler.infra.marketing.factory.DiscountRecordConverter;
import com.alibaba.copilot.enabler.infra.marketing.mapper.DiscountRecordMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @version 2024/2/26
 */
@Repository
public class DiscountRecordRepositoryImpl implements DiscountRecordRepository {

    @Resource
    private DiscountRecordMapper discountRecordMapper;

    @Override
    public DiscountRecord create(DiscountRecord discountRecord) {
        Assertor.assertNull(discountRecord.getId(), "id is not null");
        Assertor.assertNonNull(discountRecord.getAppCode(), "appCode is null");
        Assertor.assertNonNull(discountRecord.getUserId(), "userId is null");
        Assertor.assertNonNull(discountRecord.getDiscountRuleId(), "discountRuleId is null");
        Assertor.assertNonNull(discountRecord.getPlanOriginPrice(), "planOriginPrice is null");
        Assertor.assertNonNull(discountRecord.getDiscountPrice(), "discountPrice is null");

        DiscountRecordDO discountRecordDO = DiscountRecordConverter.INSTANCE.convertB2A(discountRecord);
        discountRecordMapper.insert(discountRecordDO);

        return DiscountRecordConverter.INSTANCE.convertA2B(discountRecordDO);
    }

    @Override
    public void deleteById(Long id) {
        Assertor.assertNonNull(id, "id is null");

        DiscountRecordDO discountRecordDO = discountRecordMapper.selectById(id);
        Assertor.assertNonNull(discountRecordDO, "data is not exist");

        discountRecordDO.setDeleted(Boolean.TRUE);
        discountRecordMapper.updateById(discountRecordDO);
    }

    @Override
    public boolean exists(String appCode, Long userId, List<Long> discountRuleIds) {
        Assertor.assertNotBlank(appCode, "appCode is blank");
        Assertor.assertNonNull(userId, "userId is null");
        Assertor.assertNotEmpty(discountRuleIds, "discountRuleIds is empty");

        List<DiscountRecordDO> discountRecordDOS = queryValidList(wrapper -> wrapper
                .eq(DiscountRecordDO::getAppCode, appCode)
                .eq(DiscountRecordDO::getUserId, userId)
                .in(DiscountRecordDO::getDiscountRuleId, discountRuleIds)
                .last("limit 1")
        );
        return CollectionUtil.isNotEmpty(discountRecordDOS);
    }

    /**
     * 封装通用的查询逻辑, 减少重复设置, 防止遗漏公共过滤参数
     */
    private List<DiscountRecordDO> queryValidList(Consumer<LambdaQueryWrapper<DiscountRecordDO>> consumer) {
        LambdaQueryWrapper<DiscountRecordDO> queryWrapper = new LambdaQueryWrapper<DiscountRecordDO>()
                .eq(DiscountRecordDO::getDeleted, Boolean.FALSE);
        if (consumer != null) {
            consumer.accept(queryWrapper);
        }
        return discountRecordMapper.selectList(queryWrapper);
    }
}
