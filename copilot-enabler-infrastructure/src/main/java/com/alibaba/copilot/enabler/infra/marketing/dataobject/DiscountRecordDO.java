package com.alibaba.copilot.enabler.infra.marketing.dataobject;

import com.alibaba.copilot.enabler.infra.base.bean.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 折扣记录表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "discount_record")
public class DiscountRecordDO extends BaseDO {

    /**
     * 应用标识
     */
    private String appCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 折扣规则ID
     */
    private Long discountRuleId;

    /**
     * 套餐原价
     */
    private BigDecimal planOriginPrice;

    /**
     * 折扣价
     */
    private BigDecimal discountPrice;

    /**
     * 扩展属性（JSON格式）
     */
    private String attributes;

    /**
     * 逻辑删除
     */
    private Boolean deleted;
}
