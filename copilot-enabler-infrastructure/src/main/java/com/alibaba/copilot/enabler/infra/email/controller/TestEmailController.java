package com.alibaba.copilot.enabler.infra.email.controller;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.email.dto.NotifyBeforeDeductEmailDTO;
import com.alibaba.copilot.enabler.client.email.dto.TestEmailDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategyFactory;
import com.alibaba.copilot.enabler.infra.base.utils.EnvUtils;
import com.alibaba.copilot.enabler.infra.email.service.EmailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2023/10/18
 */
@Api(tags = "发送邮箱测试接口")
@Slf4j
@RestController
@RequestMapping("api/test/email")
public class TestEmailController {

    @ApiOperation("发送测试邮件")
    @GetMapping("testSendEmail")
    public SingleResult<Void> testSendEmail() {
        EnvUtils.interceptForOnline();
        try {
            String appCode = AppEnum.PIC_COPILOT.getCode();
            TestEmailDTO emailDTO = new TestEmailDTO()
                    .setCode("5678");
            SubscriptionStrategyFactory.getStrategy(appCode, AppEnum.PIC_COPILOT.getDefaultSubscriptionPayType()).sendEmail(100L, emailDTO);
        } catch (Exception e) {
            log.error("testSendEmail, invoke error", e);
            //noinspection unchecked
            return SingleResult.buildFailure(e.getMessage());
        }
        return SingleResult.buildSuccess(null);
    }

    @ApiOperation("发送测试邮件:NotifyBeforeDeduct")
    @GetMapping("testSendEmailForNotifyBeforeDeduct")
    public SingleResult<Void> testSendEmailForNotifyBeforeDeduct() {
        EnvUtils.interceptForOnline();
        try {
            AppEnum appEnum = AppEnum.PIC_COPILOT;
            NotifyBeforeDeductEmailDTO emailDTO = new NotifyBeforeDeductEmailDTO()
                    .setAppName(appEnum.getName())
                    .setPlanName("标准套餐");
            SubscriptionStrategyFactory.getStrategy(appEnum, AppEnum.PIC_COPILOT.getDefaultSubscriptionPayType()).sendEmail(100L, emailDTO);
        } catch (Exception e) {
            log.error("testSendEmailForNotifyBeforeDeduct, invoke error", e);
            //noinspection unchecked
            return SingleResult.buildFailure(e.getMessage());
        }
        return SingleResult.buildSuccess(null);
    }
}
