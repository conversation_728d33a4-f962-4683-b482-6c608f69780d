package com.alibaba.copilot.enabler.infra.payment.factory;

import com.alibaba.aepay.fund.business.api.payment.response.PaymentSessionResponse;
import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.payment.dto.PaymentSessionDTO;
import com.alibaba.copilot.enabler.infra.payment.utils.AEResultConvertUtils;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @version 2024/1/15
 */
@AllArgsConstructor
public class PaymentSessionDTOResultBuilder implements Builder<SingleResult<PaymentSessionDTO>> {

    private final PaymentSessionResponse response;

    @Override
    public SingleResult<PaymentSessionDTO> build() {
        return AEResultConvertUtils.convertResult(response, this::convertDTO);
    }

    private PaymentSessionDTO convertDTO(PaymentSessionResponse rsp) {
        return new PaymentSessionDTO()
                .setPaymentSessionId(rsp.getPaymentSessionId())
                .setPaymentSessionData(rsp.getPaymentSessionData())
                .setPaymentSessionExpiryTime(rsp.getPaymentSessionExpiryTime())
                .setEnvironment(rsp.getEnvironment());
    }
}
