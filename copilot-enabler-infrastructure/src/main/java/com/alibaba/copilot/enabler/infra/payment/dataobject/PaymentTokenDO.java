package com.alibaba.copilot.enabler.infra.payment.dataobject;

import com.alibaba.copilot.enabler.infra.base.bean.BaseDO;
import com.alibaba.copilot.enabler.infra.base.utils.EnvUtils;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 支付令牌表
 *
 * <AUTHOR>
 * @version 2024/1/11
 */
@Data
@Accessors(chain = true)
@TableName(value = "payment_token")
@EqualsAndHashCode(callSuper = true)
public class PaymentTokenDO extends BaseDO {

    /**
     * 业务身份
     */
    private String appCode;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 支付方式 (如: ALIPAY_CN、ALIPAY_HK)
     */
    private String paymentMethod;

    /**
     * 授权标识 (关联外部支付钱包的授权上下文标识)
     */
    private String authState;

    /**
     * 获取授权时间
     */
    private Date authStartTime;

    /**
     * 结束授权时间
     */
    private Date authEndTime;

    /**
     * 支付令牌
     */
    private String token;

    /**
     * 支付令牌标识
     */
    private String tokenId;

    /**
     * 属性（JSON格式）
     */
    private String attributes;

    /**
     * 环境
     */
    private String env = EnvUtils.getEnv();

    /**
     * 删除标记
     */
    private Boolean deleted;
}
