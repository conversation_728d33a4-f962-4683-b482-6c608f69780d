package com.alibaba.copilot.enabler.infra.payment.factory;

import com.alibaba.copilot.boot.basic.enums.IEnum;
import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecordAttributes;
import com.alibaba.copilot.enabler.infra.payment.dataobject.TradeRecordDO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Optional;

/**
 * TradeRecordConverter
 */
@Component
public class TradeRecordConverter implements Converter<TradeRecordDO, TradeRecord> {
    public static final Converter<TradeRecordDO, TradeRecord> INSTANCE = new TradeRecordConverter();

    private static final String[] ignoreProperties = new String[]{"attributes"};

    @Override
    public TradeRecord convertA2B(TradeRecordDO tradePaymentRecordDO) {
        if (tradePaymentRecordDO == null) {
            return null;
        }
        TradeRecord tradeRecord = new TradeRecord();
        BeanUtils.copyProperties(tradePaymentRecordDO, tradeRecord, ignoreProperties);
        Optional.ofNullable(tradePaymentRecordDO.getTradeDirection())
                .map(direction -> IEnum.of(TradeDirection.class, direction))
                .ifPresent(tradeRecord::setTradeDirection);
        Optional.ofNullable(tradePaymentRecordDO.getPaymentType())
                .map(paymentType -> IEnum.of(PaymentTypeEnum.class, paymentType))
                .ifPresent(tradeRecord::setPaymentType);
        Optional.ofNullable(tradePaymentRecordDO.getStatus())
                .map(status -> IEnum.of(TradeRecordStatus.class, status))
                .ifPresent(tradeRecord::setStatus);
        tradeRecord.setAttributes(new TradeRecordAttributes(tradePaymentRecordDO.getAttributes()));
        return tradeRecord;
    }

    @Override
    public TradeRecordDO convertB2A(TradeRecord tradeRecord) {
        if (tradeRecord == null) {
            return null;
        }

        TradeRecordDO tradeRecordDO = new TradeRecordDO();
        BeanUtils.copyProperties(tradeRecord, tradeRecordDO, ignoreProperties);
        if (tradeRecord.getTradeDirection() != null) {
            tradeRecordDO.setTradeDirection(tradeRecord.getTradeDirection().name());
        }
        if (tradeRecord.getPaymentType() != null) {
            tradeRecordDO.setPaymentType(tradeRecord.getPaymentType().name());
        }
        if (tradeRecord.getStatus() != null) {
            tradeRecordDO.setStatus(tradeRecord.getStatus().name());
        }
        tradeRecordDO.setGmtModified(new Date());
        TradeRecordAttributes attributes = tradeRecord.getAttributes();
        tradeRecordDO.setAttributes(attributes.toString());
        return tradeRecordDO;
    }
}
