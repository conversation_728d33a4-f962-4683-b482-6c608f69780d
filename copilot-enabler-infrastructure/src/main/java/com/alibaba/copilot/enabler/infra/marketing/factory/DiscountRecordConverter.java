package com.alibaba.copilot.enabler.infra.marketing.factory;

import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.marketing.model.DiscountRecord;
import com.alibaba.copilot.enabler.domain.marketing.model.DiscountRecordAttributes;
import com.alibaba.copilot.enabler.infra.marketing.dataobject.DiscountRecordDO;
import org.springframework.beans.BeanUtils;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @version 2024/2/26
 */
public class DiscountRecordConverter implements Converter<DiscountRecordDO, DiscountRecord> {

    public static final DiscountRecordConverter INSTANCE = new DiscountRecordConverter();

    private static final String[] ignoreProperties = new String[]{
            "appCode",
            "attributes",
    };

    @Override
    public DiscountRecord convertA2B(DiscountRecordDO discountRecordDO) {
        if (discountRecordDO == null) {
            return null;
        }

        DiscountRecord result = new DiscountRecord();
        BeanUtils.copyProperties(discountRecordDO, result, ignoreProperties);

        ifExists(discountRecordDO.getAppCode(), appCode -> result.setAppCode(AppEnum.getAppByCode(appCode)));
        ifExists(discountRecordDO.getAttributes(), attributes -> result.setAttributes(new DiscountRecordAttributes(attributes)));

        return result;
    }

    @Override
    public DiscountRecordDO convertB2A(DiscountRecord discountRecord) {
        if (discountRecord == null) {
            return null;
        }

        DiscountRecordDO result = new DiscountRecordDO();
        result.setDeleted(false);
        BeanUtils.copyProperties(discountRecord, result, ignoreProperties);

        ifExists(discountRecord.getAppCode(), appCode -> result.setAppCode(appCode.getCode()));
        ifExists(discountRecord.getAttributes(), attributes -> result.setAttributes(attributes.toString()));

        return result;
    }

    private static <T> void ifExists(T subject, Consumer<T> consumer) {
        Optional.ofNullable(subject).ifPresent(consumer);
    }
}
