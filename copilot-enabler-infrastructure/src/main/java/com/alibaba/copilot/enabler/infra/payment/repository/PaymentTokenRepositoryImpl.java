package com.alibaba.copilot.enabler.infra.payment.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.domain.base.utils.Md5Utils;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentToken;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentTokenAttributes;
import com.alibaba.copilot.enabler.domain.payment.repository.PaymentTokenRepository;
import com.alibaba.copilot.enabler.infra.base.utils.EnvUtils;
import com.alibaba.copilot.enabler.infra.payment.dataobject.PaymentTokenDO;
import com.alibaba.copilot.enabler.infra.payment.factory.PaymentTokenConverter;
import com.alibaba.copilot.enabler.infra.payment.mapper.PaymentTokenMapper;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2024/1/11
 */
@Repository
public class PaymentTokenRepositoryImpl implements PaymentTokenRepository {

    @Resource
    private PaymentTokenMapper paymentTokenMapper;

    @Override
    public PaymentToken create(String appCode, Long userId, PaymentMethodEnum paymentMethod, String authState) {
        Assertor.assertNotBlank(appCode, "appCode is blank");
        Assertor.assertNonNull(userId, "userId is null");
        Assertor.assertNonNull(paymentMethod, "paymentMethod is null");
        Assertor.assertNotBlank(authState, "authState is blank");

        PaymentTokenDO paymentTokenDO = new PaymentTokenDO()
                .setAppCode(appCode)
                .setUserId(userId)
                .setPaymentMethod(paymentMethod.getValue())
                .setAuthState(authState)
                .setEnv(EnvUtils.getEnv())
                .setDeleted(false);
        paymentTokenMapper.insert(paymentTokenDO);

        return PaymentTokenConverter.INSTANCE.convertA2B(paymentTokenDO);
    }

    @Override
    public PaymentToken createByToken(String appCode, Long userId, PaymentMethodEnum paymentMethod, String token, PaymentTokenAttributes attributes) {
        Assertor.assertNotBlank(appCode, "appCode is blank");
        Assertor.assertNonNull(userId, "userId is null");
        Assertor.assertNonNull(paymentMethod, "paymentMethod is null");
        Assertor.assertNotBlank(token, "token is blank");

        String tokenId = getTokenIdByToken(token);
        PaymentTokenDO paymentTokenDO = new PaymentTokenDO()
                .setAppCode(appCode)
                .setUserId(userId)
                .setPaymentMethod(paymentMethod.getValue())
                .setTokenId(tokenId)
                .setToken(token)
                .setAuthStartTime(new Date())
                .setEnv(EnvUtils.getEnv())
                .setAttributes(attributes == null ? null : attributes.toString())
                .setDeleted(false);
        paymentTokenMapper.insert(paymentTokenDO);
        return PaymentTokenConverter.INSTANCE.convertA2B(paymentTokenDO);
    }

    @Override
    public PaymentToken createByToken(String appCode, Long userId, PaymentMethodEnum paymentMethod, String token) {
        Assertor.assertNotBlank(appCode, "appCode is blank");
        Assertor.assertNonNull(userId, "userId is null");
        Assertor.assertNonNull(paymentMethod, "paymentMethod is null");
        Assertor.assertNotBlank(token, "token is blank");

        String tokenId = getTokenIdByToken(token);
        PaymentTokenDO paymentTokenDO = new PaymentTokenDO()
                .setAppCode(appCode)
                .setUserId(userId)
                .setPaymentMethod(paymentMethod.getValue())
                .setTokenId(tokenId)
                .setToken(token)
                .setAuthStartTime(new Date())
                .setEnv(EnvUtils.getEnv())
                .setDeleted(false);
        paymentTokenMapper.insert(paymentTokenDO);
        return PaymentTokenConverter.INSTANCE.convertA2B(paymentTokenDO);
    }

    @Override
    public boolean updateToken(Long id, String token) {
        Assertor.assertNonNull(id, "id is null");
        Assertor.assertNotBlank(token, "token is blank");

        // 将token的md5值作为tokenId
        String tokenId = getTokenIdByToken(token);

        PaymentTokenDO paymentTokenDO = new PaymentTokenDO();
        paymentTokenDO.setId(id);
        paymentTokenDO.setTokenId(tokenId);
        paymentTokenDO.setToken(token);
        paymentTokenDO.setAuthStartTime(new Date());

        return paymentTokenMapper.updateById(paymentTokenDO) > 0;
    }

    /**
     * 根据token获取tokenId
     */
    private static String getTokenIdByToken(String token) {
        return Md5Utils.getPaymentTokenMd5Value(token);
    }

    @Override
    public PaymentToken queryByTokenId(String tokenId) {
        Assertor.assertNotBlank(tokenId, "tokenId is blank");

        PaymentTokenDO paymentTokenDO = queryValidFirst(queryWrapper -> queryWrapper
                .eq(PaymentTokenDO::getTokenId, tokenId)
        );
        return PaymentTokenConverter.INSTANCE.convertA2B(paymentTokenDO);
    }

    @Override
    public PaymentToken queryByAuthState(String authState) {
        Assertor.assertNotBlank(authState, "authState is blank");

        PaymentTokenDO paymentTokenDO = queryValidFirst(queryWrapper -> queryWrapper
                .eq(PaymentTokenDO::getAuthState, authState)
        );
        return PaymentTokenConverter.INSTANCE.convertA2B(paymentTokenDO);
    }

    @Override
    public PaymentToken queryByToken(String token) {
        Assertor.assertNotBlank(token, "token is blank");

        PaymentTokenDO paymentTokenDO = queryValidFirst(queryWrapper -> queryWrapper
                .eq(PaymentTokenDO::getToken, token)
        );
        return PaymentTokenConverter.INSTANCE.convertA2B(paymentTokenDO);
    }

    @Override
    public boolean deleteById(Long id) {
        Assertor.assertNonNull(id, "id is null");

        PaymentTokenDO paymentTokenDO = new PaymentTokenDO();
        paymentTokenDO.setId(id);
        paymentTokenDO.setDeleted(true);
        paymentTokenDO.setAuthEndTime(new Date());

        return paymentTokenMapper.updateById(paymentTokenDO) > 0;
    }

    @Override
    public Map<PaymentMethodEnum, PaymentToken> queryAuthedTokensByUser(String appCode, Long userId) {
        Assertor.assertNotBlank(appCode, "appCode is blank");
        Assertor.assertNonNull(userId, "userId is null");

        // 查询用户的有效token列表
        List<PaymentTokenDO> paymentTokenDOS = queryValidList(queryWrapper -> queryWrapper
                .eq(PaymentTokenDO::getAppCode, appCode)
                .eq(PaymentTokenDO::getUserId, userId)
                .isNotNull(PaymentTokenDO::getToken)
                .orderByDesc(PaymentTokenDO::getAuthStartTime)
        );

        // 组装结果
        return paymentTokenDOS.stream().collect(Collectors.toMap(
                paymentTokenDO -> PaymentMethodEnum.ofName(paymentTokenDO.getPaymentMethod()),
                PaymentTokenConverter.INSTANCE::convertA2B,
                (v1, v2) -> v1
        ));
    }

    @Override
    public List<PaymentToken> queryTokenByPaymentMethod(String appCode, Long userId, PaymentMethodEnum paymentMethod) {
        Assertor.assertNotBlank(appCode, "appCode is blank");
        Assertor.assertNonNull(userId, "userId is null");

        // 查询用户的有效token列表
        List<PaymentTokenDO> paymentTokenDOS = queryValidList(queryWrapper -> queryWrapper
                .eq(PaymentTokenDO::getAppCode, appCode)
                .eq(PaymentTokenDO::getUserId, userId)
                .eq(PaymentTokenDO::getPaymentMethod, paymentMethod.name())
                .isNotNull(PaymentTokenDO::getToken)
                .orderByDesc(PaymentTokenDO::getAuthStartTime)
        );

        return PaymentTokenConverter.INSTANCE.convertA2B(paymentTokenDOS);
    }

    /**
     * 查询首个有效的数据, 并进行数据转型 (封装公共的信息过滤条件)
     */
    private PaymentTokenDO queryValidFirst(Consumer<LambdaQueryWrapper<PaymentTokenDO>> consumer) {
        return CollectionUtil.getFirst(queryValidList(consumer));
    }

    /**
     * 查询有效的数据列表 (封装公共的信息过滤条件)
     */
    private List<PaymentTokenDO> queryValidList(Consumer<LambdaQueryWrapper<PaymentTokenDO>> consumer) {
        LambdaQueryWrapper<PaymentTokenDO> queryWrapper = new LambdaQueryWrapper<PaymentTokenDO>()
                .eq(PaymentTokenDO::getEnv, EnvUtils.getEnv())
                .eq(PaymentTokenDO::getDeleted, false);
        if (consumer != null) {
            consumer.accept(queryWrapper);
        }
        return paymentTokenMapper.selectList(queryWrapper);
    }
}
