package com.alibaba.copilot.enabler.infra.subscription.factory;


import com.alibaba.copilot.boot.basic.enums.IEnum;
import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlanAttributes;
import com.alibaba.copilot.enabler.infra.subscription.dataobject.SubscriptionPlanDO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * SubscriptionPlanConverter
 */
@Component
public class SubscriptionPlanConverter implements Converter<SubscriptionPlanDO, SubscriptionPlan> {
    public static final Converter<SubscriptionPlanDO, SubscriptionPlan> INSTANCE = new SubscriptionPlanConverter();

    private static final String[] ignoreProperties = new String[]{"attributes"};

    @Override
    public SubscriptionPlan convertA2B(SubscriptionPlanDO subscriptionPlanDO) {
        if (subscriptionPlanDO == null) {
            return null;
        }
        SubscriptionPlan subscriptionPlan = new SubscriptionPlan();
        BeanUtils.copyProperties(subscriptionPlanDO, subscriptionPlan, ignoreProperties);
        Optional.ofNullable(subscriptionPlanDO.getDurationUnit())
                .map(unit -> IEnum.of(DurationUnit.class, unit))
                .ifPresent(subscriptionPlan::setDurationUnit);
        subscriptionPlan.setAttributes(new SubscriptionPlanAttributes(subscriptionPlanDO.getAttributes()));
        return subscriptionPlan;
    }

    @Override
    public SubscriptionPlanDO convertB2A(SubscriptionPlan subscriptionPlan) {
        if (subscriptionPlan == null) {
            return null;
        }
        SubscriptionPlanDO subscriptionPlanDO = new SubscriptionPlanDO();
        BeanUtils.copyProperties(subscriptionPlan, subscriptionPlanDO, ignoreProperties);
        Optional.ofNullable(subscriptionPlan.getDurationUnit())
                .ifPresent(durationUnit -> subscriptionPlanDO.setDurationUnit(durationUnit.name()));
        Optional.ofNullable(subscriptionPlan.getAttributes())
                .ifPresent(attributes -> subscriptionPlanDO.setAttributes(attributes.toString()));
        return subscriptionPlanDO;
    }
}
