package com.alibaba.copilot.enabler.infra.payment.factory;

import com.alibaba.aepay.fund.business.api.payment.request.PaymentInquiryRequest;
import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.enabler.domain.subscription.request.InquiryPaymentRequest;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @version 2024/1/15
 */
@AllArgsConstructor
public class PaymentInquiryRequestBuilder implements Builder<PaymentInquiryRequest> {

    private final InquiryPaymentRequest request;

    @Override
    public PaymentInquiryRequest build() {
        PaymentInquiryRequest paymentInquiryRequest = new PaymentInquiryRequest();
        paymentInquiryRequest.setIdentityCode(request.getAppEnum().getAePayIdentityCode().name());
        paymentInquiryRequest.setPaymentId(request.getPaymentId());
        paymentInquiryRequest.setPaymentRequestId(request.getPaymentRequestId());
        return paymentInquiryRequest;
    }
}
