package com.alibaba.copilot.enabler.infra.payment.gateway;

import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.domain.base.exception.BizException;
import com.alibaba.copilot.enabler.domain.base.exception.BizGatewayException;
import com.alibaba.copilot.enabler.domain.payment.constant.StripeCheckoutSessionMode;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.StripeGateway;
import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.*;
import com.alibaba.fastjson.JSON;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSyntaxException;
import com.stripe.Stripe;
import com.stripe.exception.SignatureVerificationException;
import com.stripe.exception.StripeException;
import com.stripe.model.Customer;
import com.stripe.model.PaymentIntent;
import com.stripe.model.PaymentMethod;
import com.stripe.model.Refund;
import com.stripe.model.checkout.Session;
import com.stripe.net.Webhook;
import com.stripe.param.CustomerCreateParams;
import com.stripe.param.PaymentIntentCreateParams;
import com.stripe.param.RefundCreateParams;
import com.stripe.param.checkout.SessionCreateParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/06
 */
@Slf4j
@Service
public class StripeGatewayImpl implements StripeGateway {

    @Value("${stripe.webhook.secret}")
    private String webhookSecret;
    @Value("${stripe.api.key}")
    private String apiKey;


    @PostConstruct
    public void init() {
        Stripe.apiKey = apiKey;
    }


    @Override
    public StripeCreateSessionResponse createCheckoutSession(StripeCreateSessionRequest request) {
        Assertor.assertNonNull(request, "request is null");
        SessionCreateParams params = buildSessionCreateParams(request);
        try {
            Session session = Session.create(params);
            Assertor.assertNonNull(session, "stripe session is null");

            JsonObject rawJsonObject = session.getRawJsonObject();
            Assertor.assertNonNull(rawJsonObject, "stripe session#rawJsonObject is null");

            JsonPrimitive clientSecret = rawJsonObject.getAsJsonPrimitive("client_secret");
            Assertor.assertNonNull(rawJsonObject, "stripe clientSecret obj is null");

            String str = clientSecret.getAsString();
            Assertor.assertNotBlank(str, "stripe clientSecret is blank");

            StripeCreateSessionResponse response = new StripeCreateSessionResponse();
            response.setClientSecret(str);
            response.setExpiresAt(session.getExpiresAt());
            return response;
        } catch (StripeException e) {
            log.error("createCheckoutSession StripeException request={}", JSON.toJSONString(request), e);
            throw new BizGatewayException("stripe_ex:" + e.getMessage(), e);
        } catch (Exception e) {
            log.error("createCheckoutSession Exception request={}", JSON.toJSONString(request), e);
            throw new BizGatewayException("stripe_unknown_ex:" + e.getMessage(), e);
        }
    }

    @Override
    public StripeCreateCustomerResponse createCustomer(StripeCreateCustomerRequest request) {
        CustomerCreateParams customerCreateParams = buildCustomerCreateParams(request);
        try {
            Customer customer = Customer.create(customerCreateParams);
            StripeCreateCustomerResponse response = new StripeCreateCustomerResponse();
            response.setId(customer.getId());
            return response;
        } catch (StripeException e) {
            log.error("createCustomer StripeException request={}", JSON.toJSONString(request), e);
            throw new BizGatewayException("stripe_ex:" + e.getMessage(), e);
        } catch (Exception e) {
            log.error("createCustomer Exception request={}", JSON.toJSONString(request), e);
            throw new BizGatewayException("stripe_unknown_ex:" + e.getMessage(), e);
        }
    }

    @Override
    public StripeRetrieveCustomerResponse retrieveCustomer(String customerId) {
        try {
            Customer customer = Customer.retrieve(customerId);
            StripeRetrieveCustomerResponse response = new StripeRetrieveCustomerResponse();
            response.setId(customer.getId());
            response.setName(customer.getName());
            response.setEmail(customer.getEmail());
            response.setDescription(customer.getDescription());
            response.setDeleted(customer.getDeleted());
            return response;
        } catch (StripeException e) {
            log.error("retrieveCustomer StripeException customerId={}", customerId, e);
            throw new BizGatewayException("stripe_ex:" + e.getMessage(), e);
        } catch (Exception e) {
            log.error("retrieveCustomer Exception customerId={}", customerId, e);
            throw new BizGatewayException("stripe_unknown_ex:" + e.getMessage(), e);
        }
    }

    @Override
    public void createPaymentIntent(StripeCreatePaymentIntentRequest request) {
        try {
            log.warn("createPaymentIntent for setup model, start, request={}", JSON.toJSONString(request));
            PaymentIntentCreateParams params =
                    PaymentIntentCreateParams.builder()
                            .setAmount(request.getAmount())
                            .setCurrency(request.getCurrency())
                            .setCustomer(request.getCustomerId())
                            .setPaymentMethod(request.getPaymentMethodId())
                            
                            .setOffSession(true)
                            .setConfirm(true)
                            .putAllMetadata(request.getMetadata().toMap())
                            .build();

            PaymentIntent paymentIntent = PaymentIntent.create(params);
            log.warn("createPaymentIntent for setup model, finish, request={}, paymentIntent={}",
                    JSON.toJSONString(request), paymentIntent.getId());
        } catch (StripeException e) {
            throw new BizGatewayException("stripe_ex:" + e.getMessage(), e);
        } catch (Exception e) {
            throw new BizGatewayException("stripe_unknown_ex:" + e.getMessage(), e);
        }
    }

    @Override
    public void createRefund(StripeCreateRefundRequest request) {
        try {
            RefundCreateParams params = RefundCreateParams.builder()
                    .setPaymentIntent(request.getPaymentIntentId())
                    .putAllMetadata(request.getMetadata().toMap())
                    .build();

            Refund refund = Refund.create(params);
        } catch (StripeException e) {
            throw new BizGatewayException("stripe_ex:" + e.getMessage(), e);
        } catch (Exception e) {
            throw new BizGatewayException("stripe_unknown_ex:" + e.getMessage(), e);
        }
    }

    @Override
    public void checkWebhookEvent(String payload, String sigHeader) {
        try {
            Webhook.constructEvent(payload, sigHeader, webhookSecret);
        } catch (JsonSyntaxException e) {
            // Invalid payload
            throw new BizException("Invalid payload", e);
        } catch (SignatureVerificationException e) {
            // Invalid signature
            throw new BizException("Invalid signature", e);
        }
    }

    @Override
    public StripePaymentMethodResponse retrievePaymentMethod(String paymentMethodId) {
        try {
            PaymentMethod retrieve = PaymentMethod.retrieve(paymentMethodId);
            StripePaymentMethodResponse response = new StripePaymentMethodResponse();
            response.setAllowRedisplay(retrieve.getAllowRedisplay());
            response.setCustomerId(retrieve.getCustomer());
            response.setId(retrieve.getId());
            response.setType(retrieve.getType());
            if (StringUtils.equals(retrieve.getType(), "card")) {
                response.setBrand(retrieve.getCard().getBrand());
                response.setLast4(retrieve.getCard().getLast4());
                response.setFunding(retrieve.getCard().getFunding());
            }
            return response;
        } catch (StripeException e) {
            throw new BizGatewayException("stripe_ex:" + e.getMessage(), e);
        } catch (Exception e) {
            throw new BizGatewayException("stripe_unknown_ex:" + e.getMessage(), e);
        }
    }

    private CustomerCreateParams buildCustomerCreateParams(StripeCreateCustomerRequest request) {

        return CustomerCreateParams.builder()
                .setName(request.getName())
                .setEmail(request.getEmail())
                .setDescription(request.getDescription())
                .setMetadata(request.getMetadata())
                .build();
    }

    private SessionCreateParams buildSessionCreateParams(StripeCreateSessionRequest request) {
        StripeCheckoutSessionMode mode = request.getMode();
        Assertor.assertNonNull(mode, "request#mode is null");

        String finalReturnUrl = appendSessionIdTemplate(request.getReturnUrl());

        long expiresAt = System.currentTimeMillis() / 1000 + request.getTimeout();

        if (mode == StripeCheckoutSessionMode.SETUP) {
            return SessionCreateParams.builder()
                    .setCurrency(request.getCurrency())
                    .setMode(SessionCreateParams.Mode.SETUP)
                    .setUiMode(SessionCreateParams.UiMode.EMBEDDED)
                    .setReturnUrl(finalReturnUrl)
                    .putAllMetadata(exportMetadataMap(request.getMetadata()))
                    .setCustomer(request.getStripeCustomerId())
                    .setExpiresAt(expiresAt)
                    .setClientReferenceId(request.getClientReferenceId())
                    .build();
        } else if (mode == StripeCheckoutSessionMode.PAYMENT) {
            // 根据goods构建lineItems
            List<SessionCreateParams.LineItem> lineItems = buildLineItems(request);

            return SessionCreateParams.builder()
                    .setCurrency(request.getCurrency())
                    .setMode(SessionCreateParams.Mode.PAYMENT)
                    .setUiMode(SessionCreateParams.UiMode.EMBEDDED)
                    .setReturnUrl(finalReturnUrl)
                    .putAllMetadata(exportMetadataMap(request.getMetadata()))
                    .setPaymentIntentData(SessionCreateParams.PaymentIntentData.builder()
                            .putAllMetadata(exportMetadataMap(request.getMetadata()))
                            .build())
                    .setInvoiceCreation(SessionCreateParams.InvoiceCreation.builder()
                            .setEnabled(true)
                            .setInvoiceData(SessionCreateParams.InvoiceCreation.InvoiceData.builder()
                                    .putAllMetadata(exportMetadataMap(request.getMetadata()))
                                    .build())
                            .build())
                    .addAllLineItem(lineItems)
                    .setCustomer(request.getStripeCustomerId())
                    .setExpiresAt(expiresAt)
                    .setClientReferenceId(request.getClientReferenceId())
                    .setSavedPaymentMethodOptions(
                            SessionCreateParams.SavedPaymentMethodOptions.builder()
                                    .setPaymentMethodSave(
                                            SessionCreateParams.SavedPaymentMethodOptions.PaymentMethodSave.ENABLED
                                    )
                                    .build())
                    .build();
        } else {
            throw new UnsupportedOperationException("not support mode:" + mode.name());
        }
    }

    private List<SessionCreateParams.LineItem> buildLineItems(StripeCreateSessionRequest request) {
        return request.getGoodsList().stream().map(goods ->
                SessionCreateParams.LineItem.builder()
                        .setQuantity(goods.getQuantity())
                        .setPriceData(SessionCreateParams.LineItem.PriceData.builder()
                                // 币种
                                .setCurrency(request.getCurrency())
                                // 单价
                                .setUnitAmount(goods.getUnitAmount())
                                // 商品信息
                                .setProductData(SessionCreateParams.LineItem.PriceData.ProductData.builder()
                                        .setName(goods.getGoodsName())
                                        .setDescription(goods.getDescription())
                                        .build())
                                .build())
                        .build()).collect(Collectors.toList());
    }

    private static String appendSessionIdTemplate(String url) {
        if (StringUtils.isBlank(url)) {
            return url;
        }
        if (url.contains("?")) {
            url += "&";
        } else {
            url += "?";
        }
        url += "session_id={CHECKOUT_SESSION_ID}";
        return url;
    }

    private Map<String, String> exportMetadataMap(StripeEventMetadata metadata) {
        if (Objects.nonNull(metadata)) {
            return metadata.toMap();
        }
        return new HashMap<>();
    }

}