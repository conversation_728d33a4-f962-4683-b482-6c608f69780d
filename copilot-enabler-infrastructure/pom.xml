<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.alibaba.copilot</groupId>
		<artifactId>copilot-enabler</artifactId>
		<version>1.0.39-RELEASE</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <modelVersion>4.0.0</modelVersion>
	<artifactId>copilot-enabler-infrastructure</artifactId>
	<packaging>jar</packaging>
	<name>copilot-enabler-infrastructure</name>

	<dependencies>
		<dependency>
			<groupId>com.alibaba.copilot</groupId>
			<artifactId>copilot-boot-monitor-starter</artifactId>
			<version>${copilot.boot.version}</version>
		</dependency>
		<!-- 依赖子模块 -->
		<dependency>
			<groupId>com.alibaba.copilot</groupId>
			<artifactId>copilot-enabler-domain</artifactId>
		</dependency>
		<!-- 中间件 -->
		<dependency>
			<groupId>com.taobao.csp</groupId>
			<artifactId>switchcenter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>pandora-tddl-spring-boot-starter</artifactId>
		</dependency>
		<!-- 二方包 -->
		<dependency>
			<groupId>com.alibaba.copilot</groupId>
			<artifactId>copilot-boot-basic</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.copilot</groupId>
			<artifactId>copilot-boot-tools</artifactId>
		</dependency>
		<!-- 三方包 -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jcl-over-slf4j</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
        <dependency>
            <groupId>com.ofpay</groupId>
            <artifactId>logback-mdc-ttl</artifactId>
        </dependency>

		<dependency>
			<groupId>com.stripe</groupId>
			<artifactId>stripe-java</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>

		<!-- 发送邮件 -->
		<dependency>
			<groupId>com.alibaba.global.midplatform</groupId>
			<artifactId>global-message-client-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.edm</groupId>
			<artifactId>copilot-edm-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
		</dependency>

		<dependency>
			<groupId>com.aliexpress.middleware</groupId>
			<artifactId>ae-geoip-open</artifactId>
			<version>1.3.2</version>
		</dependency>

    </dependencies>

</project>
