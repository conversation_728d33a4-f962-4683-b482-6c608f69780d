<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.alibaba.copilot</groupId>
		<artifactId>copilot-enabler</artifactId>
		<version>1.0.39-RELEASE</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <modelVersion>4.0.0</modelVersion>
	<artifactId>copilot-enabler-domain</artifactId>
	<packaging>jar</packaging>
	<name>copilot-enabler-domain</name>

	<dependencies>
		<!-- 依赖子模块 -->
		<dependency>
			<groupId>com.alibaba.copilot</groupId>
			<artifactId>copilot-enabler-client</artifactId>
		</dependency>
		<!-- 中间件 -->

		<!-- 二方包 -->
		<dependency>
			<groupId>com.alibaba.copilot</groupId>
			<artifactId>copilot-boot-shopify</artifactId>
		</dependency>

		<!-- 三方包 -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
		</dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
			<scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
			<scope>provided</scope>
        </dependency>
		<dependency>
			<groupId>com.alibaba.copilot</groupId>
			<artifactId>copilot-boot-config</artifactId>
		</dependency>
		<dependency>
			<groupId>com.aliyun.schedulerx</groupId>
			<artifactId>schedulerx2-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.copilot</groupId>
			<artifactId>copilot-boot-monitor-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.aepay</groupId>
			<artifactId>fund-business-api</artifactId>
		</dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
		<!-- redis -->
		<dependency>
			<groupId>com.alibaba.normandy.credential</groupId>
			<artifactId>normandy-credential-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.edm</groupId>
			<artifactId>copilot-edm-client</artifactId>
		</dependency>

	</dependencies>

</project>
