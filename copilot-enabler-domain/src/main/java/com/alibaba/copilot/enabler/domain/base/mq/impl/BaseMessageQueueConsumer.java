package com.alibaba.copilot.enabler.domain.base.mq.impl;

import com.alibaba.copilot.enabler.domain.base.mq.MessageQueueConsumer;
import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.taobao.metaq.client.MetaPushConsumer;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;

/**
 * 消息队列消费者基类
 *
 * <AUTHOR>
 * @date 2024/11/12
 */
@Slf4j
public abstract class BaseMessageQueueConsumer<T> implements MessageQueueConsumer<T> {


    private MetaPushConsumer pushConsumer;


    @PostConstruct
    public void init() {
        try {
            pushConsumer = new MetaPushConsumer(getConsumerId());
            pushConsumer.subscribe(getTopic(), getTags());
            pushConsumer.setUnitName(getUnitName());
            pushConsumer.setInstanceName(getUnitName());
            pushConsumer.registerMessageListener((MessageListenerConcurrently)(list, consumeConcurrentlyContext) -> {
                boolean result = true;
                for (MessageExt msg : list) {
                    String bodyStr = new String(msg.getBody(), StandardCharsets.UTF_8);
                    try {
                        T event = JSON.parseObject(bodyStr, getEventClass());
                        getMessageHandler().handle(event);
                        log.info("consume metaq: topic={}, tags={}, msgId={}, msgBody={}, isSuccess={}",
                                msg.getTopic(), msg.getTags(), msg.getMsgId(), JSON.toJSONString(event), result);
                    } catch (Exception e) {
                        log.info("consume metaq failed: topic={}, tags={}, msgId={}, msgBodyStr={}",
                                msg.getTopic(), msg.getTags(), msg.getMsgId(), bodyStr, e);
                        result = false;
                    }
                }
                return result ? ConsumeConcurrentlyStatus.CONSUME_SUCCESS : ConsumeConcurrentlyStatus.RECONSUME_LATER;
            });

            pushConsumer.start();

        } catch (Exception e) {
            log.error("init metaq consumer failed, consumerId={}, topic={}, tags={}",
                    getConsumerId(), getTopic(), getTags(), e);
        }

    }

    /**
     * 消息事件类的class
     *
     * @return
     */
    protected abstract Class<T> getEventClass();


    /**
     * 消费者Id
     *
     * @return
     */
    protected abstract String getConsumerId();

    /**
     * 订阅的消息 topic
     *
     * @return
     */
    protected abstract String getTopic();

    /**
     * 订阅的消息 tags
     *
     * @return
     */
    protected abstract String getTags();

    /**
     * 所在单元标
     *
     * @return
     */
    protected abstract String getUnitName();

}
