package com.alibaba.copilot.enabler.domain.subscription.model;

import java.util.Date;

import lombok.*;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName FeatureUsage
 * <AUTHOR>
 * @Date 2023/9/5 16:53
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@Slf4j
public class FeatureUsage {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 产品唯一code
     */
    private String appCode;
    /**
     * 用户ID 或 shopifyShopId
     */
    private Long userId;

    /**
     * 特性类型
     */
    private String featureType;

    /**
     * 原始额度，不变
     */
    private Long quota;
    /**
     * 当前周期内已经使用的次数，周期内使用额度，递增
     */
    private Long usageCount;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 属性
     */
    private FeatureUsageAttributes attributes = new FeatureUsageAttributes(null);

    /**
     * 设置Attribute
     *
     * @param featureUsageAttributes
     */
    public void setAttributes(FeatureUsageAttributes featureUsageAttributes) {
        this.attributes = featureUsageAttributes;
    }

}
