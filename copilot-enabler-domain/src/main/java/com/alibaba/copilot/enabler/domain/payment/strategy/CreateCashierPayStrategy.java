package com.alibaba.copilot.enabler.domain.payment.strategy;


import com.alibaba.copilot.enabler.client.payment.request.CashierPayRequest;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;

/**
 * CreateCashierPay Strategy接口
 */
public interface CreateCashierPayStrategy {

    /**
     * 业务身份
     *
     * @return
     */
    AppEnum app();

    /**
     * 创建支付逻辑
     *
     * @param cashierPayRequest
     * @return
     */
    TradeRecord create(CashierPayRequest cashierPayRequest);

}
