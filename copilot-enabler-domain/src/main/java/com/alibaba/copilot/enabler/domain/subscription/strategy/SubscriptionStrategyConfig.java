package com.alibaba.copilot.enabler.domain.subscription.strategy;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 2023/11/2
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface SubscriptionStrategyConfig {

    /**
     * @return 关联的应用标识
     */
    AppEnum[] value();

    /**
     * @return 订阅支付类型
     */
    SubscriptionPayType payType();
}
