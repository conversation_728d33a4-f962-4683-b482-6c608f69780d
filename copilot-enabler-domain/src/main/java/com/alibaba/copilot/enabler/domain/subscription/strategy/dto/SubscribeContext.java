package com.alibaba.copilot.enabler.domain.subscription.strategy.dto;

import com.alibaba.copilot.enabler.client.marketing.dto.FirstMonthDiscountDTO;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.subscription.dto.TrialDurationDTO;
import com.alibaba.copilot.enabler.client.user.dto.UserAppRelationDTO;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.subscription.dto.FinalDiscountDTO;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.annotation.Nullable;
import java.util.Date;
import java.util.List;

/**
 * 订阅包含了巨量的参数逻辑, 为便于函数调用, 单独抽取了订阅逻辑的上下文类
 *
 * <AUTHOR>
 * @version 2023/10/12
 */
@Data
@AllArgsConstructor
public class SubscribeContext {

    /**
     * 支付方式
     */
    private PaymentMethodEnum paymentMethod;

    /**
     * 用户应用关系
     */
    private UserAppRelationDTO userAppRelationDTO;

    /**
     * 待订阅的套餐
     */
    private SubscriptionPlan newPlan;

    /**
     * 试用期信息
     */
    private TrialDurationDTO trialDurationDTO;

    /**
     * 折扣信息
     */
    private FinalDiscountDTO finalDiscountDTO;

    /**
     * 已生效订单
     */
    @Nullable
    private SubscriptionOrder oldOrder;

    /**
     * 已生效套餐
     */
    @Nullable
    private SubscriptionPlan oldPlan;

    /**
     * 已生效订单的支付流水
     */
    @Nullable
    private TradeRecord oldPayRecord;

    /**
     * 当前时间
     */
    private Date now;

    /**
     * 折扣码
     */
    private String shareCode;

    /**
     * 用户的历史订单
     */
    private List<SubscriptionOrder> historyOrders;

    /**
     * 首月减免折扣信息
     */
    private FirstMonthDiscountDTO firstMonthDiscountDTO;

    /**
     * 订阅支付类型
     * （建议传该字段。若没有传这个字段，使用{@link com.alibaba.copilot.enabler.client.user.constants.AppEnum}的defaultSubscriptionPayType字段）
     */
    private SubscriptionPayType subscriptionPayType;

    /**
     * 用户客户端ip
     */
    private String userClientIp;
}
