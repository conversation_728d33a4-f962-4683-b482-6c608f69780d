package com.alibaba.copilot.enabler.domain.base.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;

/**
 * MD5相关util
 */
public class Md5Utils {


    /**
     * 生成支付使用的用户令牌对应的盐值
     */
    private static final String PAYMENT_USER_TOKEN_MD5_SALT_VALUE = "enabler.678f8a478098218d#AI^0304";

    /**
     * 计算支付token的md5的值
     *
     * @param paymentToken 支付token
     * @return md5加密后的值
     */
    public static String getPaymentTokenMd5Value(String paymentToken) {
        return DigestUtil.md5Hex(paymentToken + AEPaymentConstants.PAYMENT_TOKEN_MD5_SALT_VALUE);
    }

    /**
     * 支付用的用户token
     *
     * @param userId  userId
     * @param appEnum 业务身份
     * @return 用户token
     */
    public static String getPayUserToken(Long userId, AppEnum appEnum) {
        // 月日小时对应的时间
        String date = DateUtil.format(DateUtil.date(), "MMddHH");
        return DigestUtil.md5Hex(userId + PAYMENT_USER_TOKEN_MD5_SALT_VALUE + appEnum.getCode() + "-" + date);
    }
}
