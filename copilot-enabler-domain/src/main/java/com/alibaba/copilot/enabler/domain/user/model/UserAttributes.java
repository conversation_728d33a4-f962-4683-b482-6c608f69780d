package com.alibaba.copilot.enabler.domain.user.model;

import com.alibaba.copilot.boot.basic.data.Attributes;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

/**
 * UserAttributes
 */
public class UserAttributes extends Attributes {

    /**
     * AE金融支付信息
     */
    private static final String ATTR_AE_PAY_INFO = "AEPayInfo";

    /**
     * 用户注册时间
     */
    private static final String ATTR_REGISTER_TIME = "RegisterTime";

    /**
     * 注册来源
     */
    private static final String FIRST_CHANNEL = "firstChannel";
    private static final String LAST_CHANNEL = "lastChannel";

    /**
     * ONESHOP_USERID
     */
    private static final String ONESHOP_USER_ID = "oneShopUserId";
    private static final String ONESHOP_USER_TOKEN = "oneShopUserToken";

    /**
     * Stripe Customer ID
     */
    private static final String STRIPE_INFO = "stripeInfo";

    public UserAttributes(String json) {
        super(json);
    }

    public void removeAEPayInfo() {
        remove(ATTR_AE_PAY_INFO);
    }

    public void setAEPayInfo(Map<String, AEPayInfo> aePayInfo) {
        put(ATTR_AE_PAY_INFO, aePayInfo);
    }

    @SuppressWarnings("unchecked")
    public Map<String, AEPayInfo> getAEPayInfo() {
        return Optional.ofNullable(getAttributes())
                .map(map -> map.get(ATTR_AE_PAY_INFO))
                .map(JSONObject::toJSONString)
                .map(json -> {
                    Type mapActualType = new TypeToken<Map<String, AEPayInfo>>() {
                    }.getType();
                    return (Map<String, AEPayInfo>) JSON.parseObject(json, mapActualType);
                })
                .orElseGet(Maps::newHashMap);
    }

    public Date getRegisterTime() {
        Long time = getAsLong(ATTR_REGISTER_TIME);
        if (time == null) {
            return new Date();
        }
        return new Date(time);
    }

    public void setRegisterTime(Date registerTime) {
        if (registerTime != null && registerTime.getTime() > 0) {
            getAttributes().put(ATTR_REGISTER_TIME, registerTime.getTime());
        }
    }

    public void setFirstChannel(String firstChannel) {
        getAttributes().put(FIRST_CHANNEL, firstChannel);
    }

    public void setLastChannel(String lastChannel) {
        getAttributes().put(LAST_CHANNEL, lastChannel);
    }

    public String getFirstChannel() {
        return getAsString(FIRST_CHANNEL);
    }

    public String getLastChannel() {
        return getAsString(LAST_CHANNEL);
    }

    public Long getOneShopUserId() {
        return getAsLong(ONESHOP_USER_ID);
    }

    public void setOneShopUserId(Long oneShopUserId) {
        getAttributes().put(ONESHOP_USER_ID, oneShopUserId);
    }

    public String getOneShopUserToken() {
        return getAsString(ONESHOP_USER_TOKEN);
    }

    public void setOneShopUserToken(String oneShopUserToken) {
        getAttributes().put(ONESHOP_USER_TOKEN, oneShopUserToken);
    }

    public StripeInfo getStripeInfoNotNull() {
        return Optional.ofNullable(getAttributes())
                .map(map -> map.get(STRIPE_INFO))
                .map(JSONObject::toJSONString)
                .map(json -> {
                    Type mapActualType = new TypeToken<StripeInfo>() {
                    }.getType();
                    return (StripeInfo) JSON.parseObject(json, mapActualType);
                })
                .orElseGet(StripeInfo::new);
    }

    public void setStripeInfo(StripeInfo stripeInfo) {
        getAttributes().put(STRIPE_INFO, stripeInfo);
    }


}
