package com.alibaba.copilot.enabler.domain.user.model;

import cn.hutool.core.util.StrUtil;
import com.alibaba.copilot.enabler.domain.base.utils.Md5Utils;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;

import java.util.*;
import java.util.stream.Collectors;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/30
 */
@Slf4j
@Builder
@Setter
@Getter
public class User {
    /**
     * 主键
     */
    @Setter
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 账号 id
     */
    private Long userId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 注册来源
     */
    private String appCode;

    /**
     * 注册渠道
     */
    private String signUpChannel;

    /**
     * 用户注册时间
     */
    private Date userRegistrationTime;

    /**
     * 属性（JSON格式）
     */
    @Builder.Default
    private UserAttributes attributes = new UserAttributes(null);

    /**
     * 保存支付卡信息
     */
    public void savePaymentCardInfo(UserRepository userRepository, AEPayInfo aePayInfo) {
        Map<String, AEPayInfo> aePayInfoMap = attributes.getAEPayInfo();
        aePayInfoMap.put(aePayInfo.getCardToken(), aePayInfo);
        this.getAttributes().setAEPayInfo(aePayInfoMap);
        userRepository.updateUser(this);
    }

    /**
     * 获取卡列表信息
     */
    public List<UserPaymentCard> getUserPaymentCardList() {
        Map<String, AEPayInfo> aePayInfoMap = attributes.getAEPayInfo();

        List<AEPayInfo> sortedList = Lists.newArrayList(aePayInfoMap.values().iterator());

        sortedList.sort(Comparator.comparing(AEPayInfo::getUpdateTime).reversed());
        return sortedList.stream()
                .map(aePayInfo -> UserPaymentCard.builder()
                        .funding(aePayInfo.getFunding())
                        .cardNo(aePayInfo.getCardNo())
                        .cardBrand(aePayInfo.getCardBrand())
                        .encryptedCardToken(Md5Utils.getPaymentTokenMd5Value(aePayInfo.getCardToken()))
                        .build()
                )
                .collect(Collectors.toList());
    }

    /**
     * 根据卡ID查询卡信息
     *
     * @param cardId 卡ID
     * @return 卡信息
     */
    public AEPayInfo getPayCardInfoByCardId(String cardId) {
        if (StrUtil.isEmpty(cardId)) {
            return null;
        }

        return Optional.ofNullable(attributes.getAEPayInfo())
                .map(Map::values)
                .flatMap(collection -> collection.stream()
                        .filter(card -> cardId.equals(Md5Utils.getPaymentTokenMd5Value(card.getCardToken())))
                        .findFirst()
                )
                .orElse(null);
    }

    /**
     * 获取最新的卡token值
     *
     * @return 卡token值
     */
    public AEPayInfo getLatestPayCardInfo() {
        Map<String, AEPayInfo> aePayInfoMap = attributes.getAEPayInfo();
        return aePayInfoMap.values()
                .stream()
                .max(Comparator.comparing(AEPayInfo::getUpdateTime))
                .orElse(null);
    }
}
