package com.alibaba.copilot.enabler.domain.user.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户卡信息
 */
@Slf4j
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPaymentCard {

    /**
     * 卡类型
     * （CREDIT、DEBIT）
     */
    private String funding;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 卡token
     */
    private String encryptedCardToken;

    /**
     * 卡品牌
     */
    private String cardBrand;
}
