package com.alibaba.copilot.enabler.domain.payment.factory;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentChannel;
import com.alibaba.copilot.enabler.client.payment.dto.*;
import com.alibaba.copilot.enabler.client.payment.request.CashierPayRequest;
import com.alibaba.copilot.enabler.client.payment.request.PreparePayRequest;
import com.alibaba.copilot.enabler.domain.payment.dto.PaymentAsyncResultDTO;
import com.alibaba.copilot.enabler.domain.payment.dto.PaymentSyncResultDTO;
import com.alibaba.copilot.enabler.domain.payment.gateway.AepayGateway;
import com.alibaba.copilot.enabler.domain.subscription.request.InitiatePaymentRequest;
import com.alibaba.copilot.enabler.domain.subscription.request.InquiryPaymentRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * AE金融支付
 */
@Component
public class AeFinanceChannel extends IPaymentChannelStrategy {

    @Autowired
    private AepayGateway aepayGateway;

    @Override
    public PaymentChannel getPaymentChannel(){
        return PaymentChannel.AE_FINANCE;
    }

    @Override
    public SingleResult preparePay(PreparePayRequest request) {
        return null;
    }

    @Override
    public SingleResult<PaymentSessionDTO> createPaymentSession(CreatePaymentSessionDTO sessionDTO) {
        return aepayGateway.createPaymentSession(sessionDTO);
    }

    @Override
    public SingleResult<PaymentAsyncResultDTO> initiatePay(InitiatePaymentRequest request) {
        return aepayGateway.initiatePay(request);
    }

    @Override
    public SingleResult<PaymentAsyncResultDTO> cashierPay(InitiatePaymentRequest request) {
        return aepayGateway.cashierPay(request);
    }

    @Override
    public SingleResult<PaymentSyncResultDTO> inquiryPayment(InquiryPaymentRequest request) {
        return aepayGateway.inquiryPayment(request);
    }

    @Override
    public SingleResult<RefundResultDTO> refund(RefundDTO refundDTO) {
        return aepayGateway.refund(refundDTO);
    }

    @Override
    public SingleResult<QueryRefundResultDTO> queryRefundResult(QueryRefundDTO queryRefundDTO) {
        return aepayGateway.queryRefundResult(queryRefundDTO);
    }
}