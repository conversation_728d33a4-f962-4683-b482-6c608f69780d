package com.alibaba.copilot.enabler.domain.subscription.repository;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionOrderQuery;
import com.google.common.collect.Lists;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * subscription_order repository
 */
public interface SubscriptionOrderRepository {

    /**
     * 查询订单
     *
     * @return
     */
    List<SubscriptionOrder> querySubscriptionOrders(SubscriptionOrderQuery query);

    /**
     * 查询已订阅开始时间为当天周期的订单
     *
     * @param query
     * @return
     */
    List<SubscriptionOrder> queryStartSubscriptionOrders(SubscriptionOrderQuery query);

    /**
     * 查询结束订阅知时间周期的订单
     *
     * @param query
     * @return
     */
    List<SubscriptionOrder> queryCancelSubscriptionOrders(SubscriptionOrderQuery query);

    /**
     * 扫描订单
     *
     * @return
     */
    List<SubscriptionOrder> scanSubscriptionOrders(SubscriptionOrderQuery query);

    /**
     * 保存订单
     *
     * @return
     */
    int saveSubscriptionOrder(SubscriptionOrder order);

    /**
     * 删除下一笔套餐的信息
     *
     * @param orderId 订单ID
     * @return 操作影响的数据行
     */
    int clearNextPlan(Long orderId);

    /**
     * 查询用户对应App的历史订单
     *
     * @param userId  用户ID
     * @param appCode 应用标识
     * @return 历史订单
     */
    List<SubscriptionOrder> getHistoryOrders(Long userId, String appCode);

    /**
     * 根据订单ID查询订单信息
     *
     * @param orderId 订单ID
     * @return 订单信息
     */
    SubscriptionOrder getByOrderId(Long orderId);

    /**
     * 根据订单ID查询订单信息
     *
     * @param orderId 订单ID
     * @return 订单信息
     */
    SubscriptionOrder getByOrderId(Long userId, Long orderId);

    /**
     * 根据外部订阅ID查询订单信息
     *
     * @param outerSubscriptionId
     * @return
     */
    SubscriptionOrder getByOuterSubscriptionId(Long userId, String outerSubscriptionId);

    /**
     * 查询当前生效的订单
     *
     * @param appCode 应用标识
     * @param userId  用户ID
     * @return 生效的订单
     */
    SubscriptionOrder queryEffectOrder(String appCode, Long userId);

    /**
     * 查询指定订单ID列表对应的订单信息
     *
     * @param orderIds 订单ID列表
     * @return 订单信息
     */
    List<SubscriptionOrder> queryByOrderIds(List<Long> orderIds);

    /**
     * 查询指定 Shopify 订阅 ID 对应的订单信息
     *
     * @param shopifySubscriptionId Shopify 订阅 ID
     * @return
     */
    SubscriptionOrder queryByShopifySubscriptionId(Long shopifySubscriptionId);

    /**
     * 根据用户ID查询订单信息
     *
     * @param userIds    用户ID
     * @param statusList 订单状态
     * @return 订单信息
     */
    Map<Long, SubscriptionOrder> queryOrderByUserIds(List<Long> userIds, List<SubscriptionOrderStatus> statusList);

    /**
     * 根据用户ID查询订单信息
     *
     * @param appCode
     * @param userIds
     * @param statusList
     * @return
     */
    Map<Long, List<SubscriptionOrder>> queryUserId2OrderByUserIds(String appCode, List<Long> userIds, List<SubscriptionOrderStatus> statusList);

    /**
     * 查询对账订单
     *
     * @param appCode   应用标识
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 返回在该月份生效过的订单列表
     */
    default List<SubscriptionOrder> queryListForBill(String appCode, Date startTime, Date endTime) {
        return queryListForBill(Lists.newArrayList(appCode), startTime, endTime);
    }

    /**
     * 查询对账订单
     *
     * @param appCodeList 应用标识
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 返回在该月份生效过的订单列表
     */
    List<SubscriptionOrder> queryListForBill(List<String> appCodeList, Date startTime, Date endTime);
}
