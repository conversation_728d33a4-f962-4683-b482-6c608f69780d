package com.alibaba.copilot.enabler.domain.marketing.model;

import com.alibaba.copilot.boot.basic.data.Attributes;
import com.alibaba.copilot.enabler.client.marketing.constant.DiscountWay;
import com.alibaba.copilot.enabler.client.marketing.constant.Language;

import java.math.BigDecimal;

/**
 * 折扣规则拓展属性
 *
 * <AUTHOR>
 * @version 2024/2/26
 */
public class DiscountRuleAttributes extends Attributes {

    /**
     * 语言
     */
    private static final String ATTR_KEY_LANGUAGE = "language";

    /**
     * 折扣方式
     */
    private static final String ATTR_KEY_DISCOUNT_WAY = "discountWay";

    /**
     * 折扣值
     */
    private static final String ATTR_KEY_DISCOUNT_VALUE = "discountValue";

    public DiscountRuleAttributes(String json) {
        super(json);
    }

    public DiscountRuleAttributes() {
    }

    public DiscountRuleAttributes setLanguage(Language language) {
        put(ATTR_KEY_LANGUAGE, language);
        return this;
    }

    public Language getLanguage() {
        return get(ATTR_KEY_LANGUAGE, Language.class);
    }

    public DiscountRuleAttributes setDiscountWay(DiscountWay discountWay) {
        put(ATTR_KEY_DISCOUNT_WAY, discountWay);
        return this;
    }

    public DiscountWay getDiscountWay() {
        return get(ATTR_KEY_DISCOUNT_WAY, DiscountWay.class);
    }

    public DiscountRuleAttributes setDiscountValue(BigDecimal discountValue) {
        put(ATTR_KEY_DISCOUNT_VALUE, discountValue);
        return this;
    }

    public BigDecimal getDiscountValue() {
        return get(ATTR_KEY_DISCOUNT_VALUE, BigDecimal.class);
    }
}
