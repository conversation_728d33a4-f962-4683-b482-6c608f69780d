package com.alibaba.copilot.enabler.domain.payment.model;

import com.alibaba.copilot.enabler.client.payment.constant.message.MessageDirectionEnum;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageStatusEnum;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageTypeEnum;
import lombok.Builder;
import lombok.Data;

/**
 * 消息信息
 */
@Data
@Builder
public class MessageInfo {

    /**
     * 消息id
     */
    private String msgId;

    /**
     * 消息内容或请求参数
     */
    private String notifyContentOrRequestParam;

    /**
     * 响应
     */
    private String response;

    /**
     * 消息类型
     */
    private MessageTypeEnum typeEnum;

    /**
     * 实体id
     */
    private String entityId;

    /**
     * 消息状态
     */
    private MessageStatusEnum statusEnum;

    /**
     * 消息方向
     */
    private MessageDirectionEnum directionEnum;

}
