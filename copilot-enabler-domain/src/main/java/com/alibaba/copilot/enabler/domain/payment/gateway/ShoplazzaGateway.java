package com.alibaba.copilot.enabler.domain.payment.gateway;

import com.alibaba.copilot.enabler.domain.payment.dto.ShoplazzaSubscriptionResponse;
import com.alibaba.copilot.enabler.domain.payment.request.ShoplazzaSubscriptionRequest;

/**
 * 店匠gateway
 *
 * <AUTHOR>
 * @date 2024/9/24 下午2:58
 */
public interface ShoplazzaGateway {

    /**
     * 订阅
     *
     * @param shoplazzaDomain
     * @param accessToken
     * @param request
     * @return
     */
    ShoplazzaSubscriptionResponse subscribePlan(String shoplazzaDomain, String accessToken, ShoplazzaSubscriptionRequest request);

    /**
     * 取消订阅
     *
     * @param shoplazzaDomain
     * @param accessToken
     * @param chargeId
     * @return
     */
    void cancelSubscribedPlan(String shoplazzaDomain, String accessToken, String chargeId);
}
