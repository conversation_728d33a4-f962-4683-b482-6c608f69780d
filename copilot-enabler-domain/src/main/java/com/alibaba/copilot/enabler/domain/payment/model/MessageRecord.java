package com.alibaba.copilot.enabler.domain.payment.model;

import com.alibaba.copilot.enabler.client.payment.constant.message.MessageDirectionEnum;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageStatusEnum;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageTypeEnum;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * 付费记录表
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@Slf4j
public class MessageRecord {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 事件的唯一标识（对应eventId，一般使用UUID）
     */
    private String messageId;

    /**
     * 消息方向（值为in或者out，即发送的消息or接收的消息）
     */
    private MessageDirectionEnum direction;

    /**
     * App code 业务身份
     */
    private AppEnum appCode;

    /**
     * 实体id（表明本事件是由那个具体的实体发出的）
     */
    private String entityId;

    /**
     * 用户ID（冗余字段，方便快速筛选）
     */
    private String userId;

    /**
     * 渠道（同一个功能涉及多渠道时，指具体的渠道）
     */
    private String channel;

    /**
     * 事件类型（描述了业务的具体行为，如发起支付）
     */
    private MessageTypeEnum messageType;

    /**
     * 事件数据（消息内容的具体数据或rpc/http调用的param）
     */
    private String messageData;

    /**
     * 事件状态（待发送、发送/接收成功、发送/接收失败）
     */
    private MessageStatusEnum status;

    /**
     * 重试次数，记录该事件已经尝试发送的次数。
     */
    private Integer retryCount;

    /**
     * rpc/http调用信息（url、httpMethod或bean、method）
     */
    private String callInfo;

    /**
     * rpc/http响应内容
     */
    private String callResponse;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 属性（JSON格式）
     */
    private MessageRecordAttributes attributes = new MessageRecordAttributes(null);
}
