
package com.alibaba.copilot.enabler.domain.base.config;


import com.alibaba.copilot.boot.config.diamond.annotation.DiamondKey;
import com.alibaba.copilot.boot.config.diamond.annotation.DiamondValue;

import lombok.Data;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 * @date 2023/8/31 2:37 PM
 */
@Data
@Component
@EnableTransactionManagement
public class BaseDataConfig {

    @DiamondValue("project.name")
    String projectName;

    @DiamondKey("com.copilot.enabler.demo")
    String test;
}
