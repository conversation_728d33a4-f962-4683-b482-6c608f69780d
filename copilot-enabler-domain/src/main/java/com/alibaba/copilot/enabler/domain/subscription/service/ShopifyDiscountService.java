package com.alibaba.copilot.enabler.domain.subscription.service;

import com.alibaba.copilot.enabler.domain.subscription.dto.ShopifyDiscountDTO;
import com.alibaba.copilot.enabler.domain.subscription.dto.ShopifyDiscountResultDTO;

/**
 * 针对Shopify的折扣服务
 *
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/10/30
 */
public interface ShopifyDiscountService {

    /**
     * 计算折扣信息
     *
     * @param discountDTO 参数信息
     * @return 折扣结果
     */
    ShopifyDiscountResultDTO computeDiscount(ShopifyDiscountDTO discountDTO);
}
