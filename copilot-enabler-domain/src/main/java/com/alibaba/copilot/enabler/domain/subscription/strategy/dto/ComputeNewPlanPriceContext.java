package com.alibaba.copilot.enabler.domain.subscription.strategy.dto;

import com.alibaba.copilot.enabler.client.marketing.dto.FirstMonthDiscountDTO;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.subscription.dto.FinalDiscountDTO;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.annotation.Nullable;
import java.util.Date;
import java.util.List;

/**
 * 计算价格包含了巨量的参数逻辑, 为便于函数调用, 单独抽取了计算价格逻辑的上下文类
 *
 * <AUTHOR>
 * @version 2023/10/25
 */
@Data
@AllArgsConstructor
public class ComputeNewPlanPriceContext {

    /**
     * 应用标识
     */
    private String appCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 折扣码
     */
    private String shareCode;

    /**
     * 当前时间
     */
    private Date now;

    /**
     * 待订阅的套餐
     */
    private SubscriptionPlan newPlan;

    /**
     * 该用户的历史订单信息
     */
    private List<SubscriptionOrder> historyOrders;

    /**
     * 折扣信息
     */
    private FinalDiscountDTO finalDiscountDTO;

    /**
     * 已生效订单
     */
    @Nullable
    private SubscriptionOrder oldOrder;

    /**
     * 已生效套餐
     */
    @Nullable
    private SubscriptionPlan oldPlan;

    /**
     * 已生效订单的支付流水
     */
    @Nullable
    private TradeRecord oldPayRecord;

    /**
     * 首月减免折扣信息
     */
    @Nullable
    private FirstMonthDiscountDTO firstMonthDiscountDTO;
}
