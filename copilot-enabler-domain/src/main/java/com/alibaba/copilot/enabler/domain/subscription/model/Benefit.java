package com.alibaba.copilot.enabler.domain.subscription.model;

import com.alibaba.copilot.boot.basic.data.Attributes;

/**
 * Benefit-值对象
 */
public class Benefit extends Attributes {
    private static final String ATTR_QUOTA = "quota";

    private static final String ATTR_DURATION = "duration";

    private static final String ATTR_DURATION_UNIT = "duration_unit";

    public Benefit(String json) {
        super(json);
    }

    public void setQuota(Long quota) {
        put(ATTR_QUOTA, quota);
    }

    public Integer getQuota() {
        return getAsInteger(ATTR_QUOTA);
    }

    public void setDuration(Integer duration) {
        put(ATTR_DURATION, duration);
    }

    public Integer getDuration() {
        return getAsInteger(ATTR_DURATION);
    }

    public void setDurationUnit(String durationUnit) {
        put(ATTR_DURATION_UNIT, durationUnit);
    }

    public String getDurationUnit() {
        return getAsString(ATTR_DURATION_UNIT);
    }

}
