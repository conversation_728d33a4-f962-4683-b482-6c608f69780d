package com.alibaba.copilot.enabler.domain.payment.strategy;


import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * CreateCashierPay Strategy工厂
 */
public class CreateCashierPayStrategyFactory {

    /**
     * key: 业务身份
     *
     * @see AppEnum
     * <p>
     * value:支付逻辑实现
     */
    private static final Map<AppEnum, CreateCashierPayStrategy> MAP = new ConcurrentHashMap<>();

    public static void register(AppEnum app, CreateCashierPayStrategy strategy) {
        if (app != null && Objects.nonNull(strategy)) {
            MAP.put(app, strategy);
        }
    }


    public static CreateCashierPayStrategy getStrategy(AppEnum app) {
        return MapUtils.getObject(MAP, app);
    }

}
