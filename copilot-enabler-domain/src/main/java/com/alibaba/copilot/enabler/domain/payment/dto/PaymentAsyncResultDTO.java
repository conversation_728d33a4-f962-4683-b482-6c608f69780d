package com.alibaba.copilot.enabler.domain.payment.dto;

import com.alibaba.aepay.fund.business.api.payment.dto.RedirectActionForm;
import com.alibaba.global.money.Money;
import lombok.Data;

/**
 * 支付结果（异步）
 */
@Data
public class PaymentAsyncResultDTO {

    /**
     * 结果码
     */
    private String resultCode;
    /**
     * 结果状态类型
     */
    private String resultStatus;
    /**
     * 结果信息
     */
    private String resultMessage;

    /**
     * 支付幂等号（交易流水号id）
     */
    String paymentRequestId;

    /**
     * 支付幂等号（支付机构生成）
     */
    String paymentId;

    /**
     * 支付金额
     */
    Money paymentAmount;

    /**
     * 支付创建时间
     */
    Long paymentCreateTime;

    /**
     * 失效时间
     */
    Long authExpiryTime;

    /**
     * 跳转表单内容
     */
    RedirectActionForm redirectActionForm;

    /**
     * 收银台地址
     */
    private String normalUrl;
}
