package com.alibaba.copilot.enabler.domain.payment.factory;

import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * {@link PaymentChannelFactory}的初始化器
 */
@Component
public class PaymentChannelFactoryInitializer {

    @Resource
    private AeFinanceChannel AeFinanceChannel;

    @PostConstruct
    public void init() {
        // 设置默认支付渠道
        PaymentChannelFactory.setDefaultBindMap(AeFinanceChannel);
        PaymentChannelFactory.register(AppEnum.EDGE_SHOP_OFFICIAL, AeFinanceChannel);
        PaymentChannelFactory.register(AppEnum.PIC_COPILOT, AeFinanceChannel);
    }
}
