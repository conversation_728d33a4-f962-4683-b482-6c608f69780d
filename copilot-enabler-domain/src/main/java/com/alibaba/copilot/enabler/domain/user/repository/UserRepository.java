package com.alibaba.copilot.enabler.domain.user.repository;

import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.request.UserQuery;

import java.util.List;


/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/30
 */
public interface UserRepository {
    User getUser(Long userId);

    User getUser(String appCode, Long userId);

    User createUser(User user);

    User updateUser(User user);

    User queryUser(UserQuery query);

    User queryUserByParams(UserQuery query);

    List<User> allUsers();
}
