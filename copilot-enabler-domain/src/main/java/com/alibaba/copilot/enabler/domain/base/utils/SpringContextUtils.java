package com.alibaba.copilot.enabler.domain.base.utils;

import lombok.Getter;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.io.Resource;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/31
 */
@Component
public class SpringContextUtils implements ApplicationContextInitializer<ConfigurableApplicationContext> {
    @Getter
    private static ApplicationContext context;

    public static <T> T getBean(Class<T> beanClass) {
        return (T) context.getBean(beanClass);
    }

    public static <T> T getBean(String name, Class<T> beanClass) {
        return context.getBean(name, beanClass);
    }

    public static Object getBean(String name) {
        return context.getBean(name);
    }

    public static <T> Map<String, T> getBeans(Class<T> type) {
        return context.getBeansOfType(type);
    }

    public static Resource getResource(String location) {
        return context.getResource(location);
    }

    public static Map<String, Object> getBeansWithAnnotation(Class<? extends Annotation> annotationType) {
        return context.getBeansWithAnnotation(annotationType);
    }

    @Override
    public void initialize(ConfigurableApplicationContext applicationContext) {
        SpringContextUtils.context = applicationContext;
    }
}
