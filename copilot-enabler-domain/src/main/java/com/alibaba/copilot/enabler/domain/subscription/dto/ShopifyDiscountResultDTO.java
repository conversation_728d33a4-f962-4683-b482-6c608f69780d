package com.alibaba.copilot.enabler.domain.subscription.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/10/30
 */
@Data
@Accessors(chain = true)
public class ShopifyDiscountResultDTO implements Serializable {

    /**
     * 是否有折扣信息
     */
    private Boolean hasDiscount;

    /**
     * 折扣率 (8折对应值为0.8)
     */
    private BigDecimal discountRate;

    /**
     * 折扣价格
     */
    private BigDecimal discountPrice;

    /**
     * 有效折扣码
     */
    private String effectiveShareCode;

    /**
     * Attributes中的优惠价格,使用折扣码时的原价,折扣周期之后需要变成原价也就是这个价格
     */
    private BigDecimal originDiscountPrice;

    /**
     * 折扣结束时间 (可能为空, 为空时表示折扣不限时)
     */
    private Date discountEndTime;
}
