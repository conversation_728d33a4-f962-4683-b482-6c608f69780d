package com.alibaba.copilot.enabler.domain.subscription.repository;

import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyFeatureQuery;
import com.alibaba.copilot.enabler.domain.subscription.model.FeatureUsage;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionPlanQuery;

import java.math.BigDecimal;
import java.util.List;

/**
 * subscription_plan repository
 */
public interface SubscriptionPlanRepository {

    /**
     * 查询订阅计划
     *
     * @return
     */
    List<SubscriptionPlan> querySubscriptionPlans(SubscriptionPlanQuery query);

    /**
     * 根据套餐ID查询套餐信息
     *
     * @param planId          套餐ID
     * @param fillFeatureInfo 是否填充feature信息
     * @return 套餐信息
     */
    SubscriptionPlan queryByPlanId(Long planId, boolean fillFeatureInfo);

    /**
     * 查询免费套餐
     *
     * @param appCode         应用标识
     * @param fillFeatureInfo 是否填充feature信息
     * @return 免费套餐
     */
    SubscriptionPlan queryFreePlan(String appCode, boolean fillFeatureInfo);

    /**
     * 查询下一个同周期推荐套餐
     * @param appCode
     * @param fillFeatureInfo
     * @param planDurationUnit
     * @param price
     * @return
     */
    SubscriptionPlan queryNextPlan(String appCode, boolean fillFeatureInfo, String planDurationUnit, BigDecimal price);

    /**
     * 写 FeatureUsage 表
     * @param featureUsage
     * @return
     */
    int saveFeatureUsage(FeatureUsage featureUsage);

    /**
     * 查询特性使用
     * @param shopifyFeatureQuery
     * @return
     */
    FeatureUsage queryFeatureUsage(ShopifyFeatureQuery shopifyFeatureQuery);


    /**
     * 查询某用户所有的特性使用
     * @param shopifyFeatureQuery
     * @return
     */
    public List<FeatureUsage> queryFeatureUsages(ShopifyFeatureQuery shopifyFeatureQuery);

    List<SubscriptionPlan> queryByIds(List<Long> planIds);
}
