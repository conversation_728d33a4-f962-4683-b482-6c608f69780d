package com.alibaba.copilot.enabler.domain.base.dingtalk.impl;

import com.alibaba.copilot.enabler.domain.base.dingtalk.DingTalkRequest;
import com.alibaba.copilot.enabler.domain.base.dingtalk.DingTalkService;
import com.alibaba.copilot.enabler.domain.base.dingtalk.dto.DingMessage;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/10/22
 */
@Slf4j
public abstract class AbstractDingTalkService implements DingTalkService {


    /**
     * 机器人 Webhook 地址
     *
     * @return
     */
    protected abstract String getRobotWebhookUrl();

    /**
     * 机器人加签密钥
     * <a href="https://open.dingtalk.com/document/robots/customize-robot-security-settings">文档</a>
     *
     * @return
     */
    protected abstract String getRobotSignSecret();


    @Override
    public boolean sendMessage(DingTalkRequest request) {
        DingMessage msg = DingMessage.buildTextMessage(request.getContent());
        int result = sendMessage(msg);
        return result != 0;
    }


    private int sendMessage(Object obj) {
        try {
            HttpURLConnection conn = getHttpURLConnection();
            OutputStream out = conn.getOutputStream();
            String jsonMessage = JSONObject.toJSONString(obj);
            byte[] data = jsonMessage.getBytes();
            // 发送请求参数
            out.write(data);
            // flush输出流的缓冲
            out.flush();
            out.close();
            //开始获取数据
            InputStream in = conn.getInputStream();
            byte[] content = new byte[in.available()];
            // 输出返回结果
            return in.read(content);
        } catch (Exception e) {
            log.error("error_send_dingtalk:{}", JSON.toJSONString(obj), e);
            return 0;
        }
    }

    @NotNull
    private HttpURLConnection getHttpURLConnection() throws IOException {
        String urlStr = getRobotWebhookUrl();
        if (StringUtils.isNotBlank(getRobotSignSecret())) {
            Long timestamp = System.currentTimeMillis();
            String sign = getSign(timestamp);
            urlStr += "&timestamp=" + timestamp + "&sign=" + sign;
        }
        URL url = new URL(urlStr);
        //打开连接
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        // 发送POST请求必须设置如下两行
        conn.setDoOutput(true);
        conn.setDoInput(true);
        // Post 请求不能使用缓存
        conn.setUseCaches(false);
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Charset", "UTF-8");
        conn.setRequestProperty("Content-Type", "application/Json; charset=UTF-8");
        conn.connect();
        return conn;
    }

    private String getSign(Long timestamp) {
        String stringToSign = timestamp + "\n" + getRobotSignSecret();
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(getRobotSignSecret().getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
            return URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF8");
        } catch (Exception e) {
            throw new IllegalArgumentException("Can't calculate the signature, hmacSHA256 is not supported.");
        }
    }



}