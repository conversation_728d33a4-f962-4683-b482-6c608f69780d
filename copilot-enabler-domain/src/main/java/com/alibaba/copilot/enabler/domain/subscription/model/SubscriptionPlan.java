package com.alibaba.copilot.enabler.domain.subscription.model;

import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPlanName;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 订阅计划-计划实体
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@Slf4j
public class SubscriptionPlan {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 产品唯一code
     */
    private String appCode;

    /**
     * 计划名称
     */
    private String name;

    /**
     * 计划描述
     */
    private String description;

    /**
     * 计划价格（原价）
     */
    private BigDecimal price;

    /**
     * 计划周期
     */
    private Long duration;

    /**
     * 周期单位
     */
    private DurationUnit durationUnit;

    /**
     * 是否可以试用
     */
    private Boolean isHasTrial;

    /**
     * 试用周期
     */
    private Long trialDuration;

    /**
     * 试用周期单位
     */
    private String trialDurationUnit;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 属性
     */
    private SubscriptionPlanAttributes attributes = new SubscriptionPlanAttributes(null);

    /**
     * 当前计划关联的属性
     */
    private List<SubscriptionFeature> features;

    /**
     * 设置Attribute
     *
     * @param subscriptionPlanAttributes
     */
    public void setAttributes(SubscriptionPlanAttributes subscriptionPlanAttributes) {
        this.attributes = subscriptionPlanAttributes;
    }

    /**
     * 设置feature
     *
     * @param features
     */
    public void setFeatures(List<SubscriptionFeature> features) {
        this.features = features;
    }

    /**
     * @return 判断是否是免费套餐
     */
    public boolean isFree() {
        if (SubscriptionPlanName.FREE.name().equals(name)) {
            return true;
        }
        return price != null && price.compareTo(BigDecimal.ZERO) <= 0;
    }
}
