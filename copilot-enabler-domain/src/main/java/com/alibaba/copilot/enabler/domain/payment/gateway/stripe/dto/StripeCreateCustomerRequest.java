package com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/07
 */
@Data
public class StripeCreateCustomerRequest {

    /**
     * 客户名称
     */
    private String name;

    /**
     * 客户Email
     */
    private String email;

    /**
     * 客户描述（用于区分业务）
     */
    private String description;
    /**
     * Metadata
     */
    private Map<String, String> metadata;


    public void add(String key, String value) {
        if (Objects.isNull(metadata)) {
            metadata = new HashMap<>();
        }
        if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)) {
            metadata.put(key, value);
        }
    }
}