package com.alibaba.copilot.enabler.domain.payment.gateway.geoip;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.payment.dto.CreatePaymentSessionDTO;
import com.alibaba.copilot.enabler.client.payment.dto.PaymentSessionDTO;
import com.alibaba.copilot.enabler.domain.payment.gateway.geoip.dto.GetAddressInfoByIpResponse;
import com.alibaba.copilot.enabler.domain.payment.model.IpAddressInfo;

/**
 * Alipay网关接口 (通过AE金融进行代理)
 *
 * <AUTHOR>
 * @version 2023/9/27
 */
public interface GeoIpGateway {

    /**
     * 根据IP解析地址信息
     */
    GetAddressInfoByIpResponse getAddressInfoByIp(String ip);

}
