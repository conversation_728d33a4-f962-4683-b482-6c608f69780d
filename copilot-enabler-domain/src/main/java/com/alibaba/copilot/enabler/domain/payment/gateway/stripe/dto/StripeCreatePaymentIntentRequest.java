package com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto;

import com.alibaba.copilot.enabler.domain.payment.constant.PaymentConst;
import lombok.*;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/14
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StripeCreatePaymentIntentRequest {

    private Long amount;
    private String currency = PaymentConst.CURRENCY_USD;
    private String customerId;
    private String paymentMethodId;
    private Boolean offSession;
    private boolean confirm;
    private StripeEventMetadata metadata;

}