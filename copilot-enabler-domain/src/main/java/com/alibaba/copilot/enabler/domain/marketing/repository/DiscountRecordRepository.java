package com.alibaba.copilot.enabler.domain.marketing.repository;

import com.alibaba.copilot.enabler.domain.marketing.model.DiscountRecord;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2024/2/26
 */
public interface DiscountRecordRepository {

    /**
     * 创建
     */
    DiscountRecord create(DiscountRecord discountRecord);

    /**
     * 删除
     */
    void deleteById(Long id);

    /**
     * 判断用户是否享受过该折扣
     */
    boolean exists(String appCode, Long userId, List<Long> discountRuleIds);
}
