package com.alibaba.copilot.enabler.domain.marketing.repository;

import com.alibaba.copilot.enabler.client.marketing.constant.DiscountRuleStatus;
import com.alibaba.copilot.enabler.domain.marketing.model.DiscountRule;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2024/2/26
 */
public interface DiscountRuleRepository {

    /**
     * 创建折扣规则
     */
    DiscountRule create(DiscountRule discountRule);

    /**
     * 根据ID查询 (注: 未启动的也会被查询到)
     */
    DiscountRule queryById(Long id);

    /**
     * 更新
     */
    DiscountRule update(DiscountRule discountRule);

    /**
     * 更新状态
     */
    void updateStatus(Long id, DiscountRuleStatus status);

    /**
     * 查询指定应用下的所有首月减免折扣
     */
    List<DiscountRule> queryFirstMonthRulesByAppCode(String appCode);

    /**
     * 根据套餐ID查询首月减免的折扣规则
     */
    Map<Long, DiscountRule> queryFirstMonthRules(List<Long> planIds);

    /**
     * 根据套餐ID和状态查询规则列表
     */
    Map<Long, List<DiscountRule>> queryByPlanIds(List<Long> planIds, @Nullable DiscountRuleStatus status);

    /**
     * 根据套餐ID查询当前启用的折扣规则
     */
    Map<Long, DiscountRule> queryEnabledByPlanIds(List<Long> planIds);
}
