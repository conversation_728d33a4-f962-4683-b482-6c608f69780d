package com.alibaba.copilot.enabler.domain.subscription.strategy.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2023/10/25
 */
@Data
@Accessors(chain = true)
public class ComputeNewPlanPriceResultDTO implements Serializable {

    /**
     * 是否需要支付
     */
    private Boolean needPay;

    /**
     * 套餐的金额
     */
    private BigDecimal planAmount;

    /**
     * 折扣后的金额
     */
    @Nullable
    private BigDecimal discountAmount;

    /**
     * 上个套餐抵扣的金额
     */
    private BigDecimal deductedAmountOfLastPlan;

    /**
     * 支付的金额
     */
    private BigDecimal payAmount;
}
