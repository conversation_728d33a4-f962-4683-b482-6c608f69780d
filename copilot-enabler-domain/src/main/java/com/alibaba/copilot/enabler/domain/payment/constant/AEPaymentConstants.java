package com.alibaba.copilot.enabler.domain.payment.constant;

/**
 * 接入AE金融支付相关的常量配置
 *
 * <AUTHOR>
 * @version 2023/9/26
 */
public interface AEPaymentConstants {

    /**
     * 信用卡产品码
     */
    String PRODUCT_CODE_FOR_CASHIER = "CASHIER_PAYMENT";

    /**
     * Alipay产品码
     */
    String PRODUCT_CODE_FOR_AGREEMENT = "AGREEMENT_PAYMENT";

    /**
     * Alipay场景码
     */
    String PRODUCT_SCENE_FOR_ALIPAY = "EASY_PAY";

    /**
     * 币种
     */
    String CURRENCY_CODE = "USD";

    /**
     * 商品交货方式
     */
    String GOODS_DELIVERY_METHOD_TYPE = "DIGITAL";

    /**
     * 支付方式
     */
    @Deprecated
    String DEFAULT_PAYMENT_METHOD_TYPE = "CARD";

    /**
     * 信用卡支付方式
     */
    @Deprecated
    String PAYMENT_METHOD_TYPE_FOR_CARD = "CARD";

    /**
     * Alipay支付方式
     */
    @Deprecated
    String PAYMENT_METHOD_TYPE_FOR_ALIPAY = "ALIPAY_CN";

    /**
     * 终端类型
     */
    String DEFAULT_TERMINAL_TYPE = "WEB";

    /**
     * HSF接口结果码-成功
     */
    String RESULT_STATUS_SUCCESS = "S";

    /**
     * HSF接口结果码-失败
     */
    String RESULT_STATUS_FAILURE = "F";

    /**
     * HSF接口结果码-未知
     */
    String RESULT_STATUS_UNKNOWN = "U";

    /**
     * 退款状态码-正在退款
     */
    String REFUND_RESULT_CODE_IN_PROCESS = "REFUND_IN_PROCESS";

    /**
     * 扣款周期类型
     */
    String RECURRING_TYPE = "SCHEDULED";

    /**
     * 支付授权Metaq通知类型
     */
    String AUTH_PAYMENT_RESULT_NOTIFY_TYPE = "PAYMENT_RESULT";

    /**
     * 支付请款Metaq通知类型
     */
    String CAPTURE_PAYMENT_RESULT_NOTIFY_TYPE = "CAPTURE_RESULT";

    /**
     * 退款Metaq通知类型
     */
    String REFUND_PAYMENT_RESULT_NOTIFY_TYPE = "REFUND_RESULT";

    /**
     * 拒付Metaq通知类型
     */
    String DISPUTE_PAYMENT_RESULT_NOTIFY_TYPE = "DISPUTE_CREATED";

    /**
     * 支付通知成功标记
     */
    String PAYMENT_RESULT_NOTIFY_SUCCESS = "SUCCESS";

    /**
     * 退款通知成功标记
     */
    String REFUND_RESULT_NOTIFY_SUCCESS = "SUCCESS";

    /**
     * redirectUrl中paymentRequestId的参数Key
     */
    String KEY_PAYMENT_REQUEST_ID = "paymentRequestId";

    /**
     * redirectUrl中planId的参数Key
     */
    String KEY_PLAN_ID = "planId";

    String KEY_GOODS_ID = "goodsId";

    /**
     * 卡token进行md5的盐值
     */
    String PAYMENT_TOKEN_MD5_SALT_VALUE = "Sd45Gh23Jk78Lm90Op12AI";

    /**
     * dsCopilot独立支付的年套餐默认减免优惠的百分比值
     */
    Integer DSC_ALONE_ANNUAL_PLAN_DEFAULT_REDUCE_DISCOUNT = 40;
}
