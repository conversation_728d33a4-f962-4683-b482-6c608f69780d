package com.alibaba.copilot.enabler.domain.subscription.strategy;

import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.base.utils.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 策略工厂类, 主要为了解决不同应用在订阅逻辑上差异化的问题
 *
 * <AUTHOR>
 * @version 2023/10/12
 */
@Slf4j
@Component
public class SubscriptionStrategyFactory {

    /**
     * `App`->`订阅支付类型`与`订阅策略实现类` 的映射关系
     */
    private static final Map<AppEnum, Map<SubscriptionPayType, SubscriptionStrategy>> APP_STRATEGY_MAPPING = new ConcurrentHashMap<>();

    @PostConstruct
    private void init() {
        SpringContextUtils.getBeans(SubscriptionStrategy.class).forEach((beanName, bean) -> {
            ApplicationContext applicationContext = SpringContextUtils.getContext();
            SubscriptionStrategyConfig strategyConfig = applicationContext.findAnnotationOnBean(beanName, SubscriptionStrategyConfig.class);
            Assertor.assertNonNull(strategyConfig, "No @SubscriptionStrategyConfig found on " + beanName);

            Arrays.stream(strategyConfig.value()).forEach(appEnum -> {
                // `App`->`订阅支付类型`与`订阅策略实现类`对应关系
                Map<SubscriptionPayType, SubscriptionStrategy> payTypeAndStrategyMap = APP_STRATEGY_MAPPING.getOrDefault(appEnum, new ConcurrentHashMap<>());
                payTypeAndStrategyMap.put(strategyConfig.payType(), bean);

                // `订阅支付类型`与`订阅策略实现类`对应关系
                APP_STRATEGY_MAPPING.put(appEnum, payTypeAndStrategyMap);
            });
        });
    }

    /**
     * 根于指定的应用, 获取到相对应的订阅策略实现类
     *
     * @param appCode             应用标识
     * @param subscriptionPayType 订阅支付类型
     * @return 该应用对应的策略实现类
     */
    public static SubscriptionStrategy getStrategy(String appCode, SubscriptionPayType subscriptionPayType) {
        Assertor.assertNotBlank(appCode, "appCode is blank");
        AppEnum appEnum = AppEnum.getAppByCode(appCode);
        return getStrategy(appEnum, subscriptionPayType);
    }

    /**
     * 根于指定的应用, 获取到相对应的订阅策略实现类
     *
     * @param appEnum             应用标识
     * @param subscriptionPayType 订阅支付类型
     * @return 该应用对应的策略实现类
     */
    public static SubscriptionStrategy getStrategy(AppEnum appEnum, SubscriptionPayType subscriptionPayType) {
        if (subscriptionPayType == null) {
            subscriptionPayType = appEnum.getDefaultSubscriptionPayType();
        }

        Map<SubscriptionPayType, SubscriptionStrategy> subscriptionPayTypeSubscriptionStrategyMap = APP_STRATEGY_MAPPING.get(appEnum);
        SubscriptionStrategy strategy = subscriptionPayTypeSubscriptionStrategyMap.get(subscriptionPayType);

        Assertor.assertNonNull(strategy, "No subscription strategy for " + appEnum.getName());

        if (strategy instanceof AppEnumHolder) {
            ((AppEnumHolder) strategy).setAppEnum(appEnum);
        }
        return strategy;
    }
}
