package com.alibaba.copilot.enabler.domain.subscription.model;

import com.alibaba.copilot.boot.basic.data.Attributes;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * SubscriptionOrderAttributes
 */
public class SubscriptionOrderAttributes extends Attributes {

    /**
     * 状态流转原因
     */
    private static final String ATTR_STATUS_FLOW_REASON = "statusFlowReason";

    /**
     * 折扣码
     */
    private static final String ATTR_SHARE_CODE = "shareCode";

    /**
     * 试用期天数
     */
    private static final String ATTR_TRIAL_DAYS = "trialDays";

    /**
     * 套餐天数
     */
    private static final String ATTR_PLAN_DAYS = "planDays";

    /**
     * 是否已经发送过扣款通知的邮件
     */
    private static final String ATTR_WHETHER_SENT_DEDUCTION_NOTIFY_EMAIL = "whetherSentDeductionNotifyEmail";

    /**
     * 已经发送过扣款通知的邮件标识值
     */
    public static final Integer ATTR_WHETHER_SENT_DEDUCTION_NOTIFY_EMAIL_VALUE = 1;

    /**
     * 是否为零元购订单
     */
    private static final String ATTR_IS_FREE_ORDER = "isFreeOrder";

    /**
     * 首月折扣的规则ID
     */
    private static final String ATTR_IS_FIRST_MONTH_DISCOUNT_RULE_ID = "firstMonthDiscountRuleId";

    private static final String STRIPE_SUBSCRIPTION_ID = "stripeSubscriptionId";
    private static final String STRIPE_PAYMENT_METHOD_ID = "stripePaymentMethodId";
    private static final String STRIPE_PAYMENT_INTENT_ID = "stripePaymentIntentId";

    /**
     * payment_token 表的主键id
     */
    private static final String PAYMENT_TOKEN_ID = "paymentTokenId";

    /**
     * 是否为计划取消（用于Antom等不主动发送订阅结束通知的支付方式）
     */
    private static final String ATTR_SCHEDULED_CANCEL = "scheduledCancel";

    public SubscriptionOrderAttributes() {
    }

    public SubscriptionOrderAttributes(String json) {
        super(json);
    }

    public void addStatusFlowReason(String statusFlowReason) {
        Map<String, String> statusFlowReasonMap = getStatusFlowReason();
        statusFlowReasonMap.put(String.valueOf(System.currentTimeMillis()), statusFlowReason);
        put(ATTR_STATUS_FLOW_REASON, statusFlowReasonMap);
    }

    public Map<String, String> getStatusFlowReason() {
        Map<String, String> map = get(ATTR_STATUS_FLOW_REASON, Map.class);
        if (map == null) {
            map = Maps.newHashMap();
            put(ATTR_STATUS_FLOW_REASON, map);
            return map;
        }

        return map;
    }

    public void setShareCode(String shareCode) {
        put(ATTR_SHARE_CODE, shareCode);
    }

    public String getShareCode() {
        return getAsString(ATTR_SHARE_CODE);
    }

    public Long getTrialDays() {
        return getAsLong(ATTR_TRIAL_DAYS);
    }

    public void setTrialDays(Long trialDays) {
        put(ATTR_TRIAL_DAYS, trialDays);
    }

    public Long getPlanDays() {
        return getAsLong(ATTR_PLAN_DAYS);
    }

    public void setPlanDays(Long planDays) {
        put(ATTR_PLAN_DAYS, planDays);
    }

    public Boolean getWhetherSentDeductionNotifyEmail() {
        Integer flag = getAsInteger(ATTR_WHETHER_SENT_DEDUCTION_NOTIFY_EMAIL);
        return ATTR_WHETHER_SENT_DEDUCTION_NOTIFY_EMAIL_VALUE.equals(flag);
    }

    public void setWhetherSentDeductionNotifyEmail(Integer whetherSentDeductionNotifyEmail) {
        put(ATTR_WHETHER_SENT_DEDUCTION_NOTIFY_EMAIL, whetherSentDeductionNotifyEmail);
    }

    public boolean isFreeOrder() {
        return Boolean.TRUE.equals(get(ATTR_IS_FREE_ORDER, Boolean.class));
    }

    public void setFreeOrder(boolean freeOrder) {
        put(ATTR_IS_FREE_ORDER, freeOrder);
    }

    public Long getFirstMonthDiscountRuleId() {
        return getAsLong(ATTR_IS_FIRST_MONTH_DISCOUNT_RULE_ID);
    }

    public void setFirstMonthDiscountRuleId(Long firstMonthDiscountRuleId) {
        put(ATTR_IS_FIRST_MONTH_DISCOUNT_RULE_ID, firstMonthDiscountRuleId);
    }

    public void setStripeSubscriptionId(String stripeSubscriptionId) {
        put(STRIPE_SUBSCRIPTION_ID, stripeSubscriptionId);
    }

    public String getStripeSubscriptionId() {
        return getAsString(STRIPE_SUBSCRIPTION_ID);
    }

    public void setStripePaymentMethodId(String stripePaymentMethodId) {
        put(STRIPE_PAYMENT_METHOD_ID, stripePaymentMethodId);
    }

    public String getStripePaymentMethodId() {
        return getAsString(STRIPE_PAYMENT_METHOD_ID);
    }

    public void setStripePaymentIntentId(String stripePaymentMethodId) {
        put(STRIPE_PAYMENT_INTENT_ID, stripePaymentMethodId);
    }

    public String getStripePaymentIntentId() {
        return getAsString(STRIPE_PAYMENT_INTENT_ID);
    }

    public void setPaymentTokenId(String paymentTokenId) {
        put(PAYMENT_TOKEN_ID, paymentTokenId);
    }

    public String getPaymentTokenId() {
        return getAsString(PAYMENT_TOKEN_ID);
    }

    public Boolean getScheduledCancel() {
        return get(ATTR_SCHEDULED_CANCEL, Boolean.class);
    }

    public void setScheduledCancel(Boolean scheduledCancel) {
        put(ATTR_SCHEDULED_CANCEL, scheduledCancel);
    }

}
