package com.alibaba.copilot.enabler.domain.base.utils;

import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import org.apache.commons.lang3.time.DateUtils;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;

public class TimeUtils {

    /**
     * 计划到期时间
     *
     * @param now          当前时间
     * @param duration     周期
     * @param durationUnit 周期单位
     * @return 到期的时间
     */
    public static Date getEndTime(Date now, Long duration, String durationUnit) {
        if (DurationUnit.YEAR.name().equals(durationUnit)) {
            return DateUtils.addDays(now, duration.intValue() * 365);
        } else if (DurationUnit.MONTH.name().equals(durationUnit)) {
            return DateUtils.addDays(now, duration.intValue() * 30);
        } else {
            return DateUtils.addDays(now, duration.intValue());
        }
    }

    public static long calculateDayCount(long duration, String durationUnit) {
        if (DurationUnit.YEAR.name().equals(durationUnit)) {
            return duration * 365;
        } else if (DurationUnit.MONTH.name().equals(durationUnit)) {
            return duration * 30;
        }
        return duration;
    }

    public static long calculateDayCount(long duration, DurationUnit durationUnit) {
        if (durationUnit == DurationUnit.YEAR) {
            return duration * 365;
        } else if (durationUnit == DurationUnit.MONTH) {
            return duration * 30;
        }
        return duration;
    }

    public static Date min(Date... dates) {
        return Arrays.stream(dates)
                .min(Date::compareTo)
                .orElseThrow(() -> new IllegalArgumentException("dates can't be empty"));
    }

    public static Date max(Date... dates) {
        return Arrays.stream(dates)
                .max(Date::compareTo)
                .orElseThrow(() -> new IllegalArgumentException("dates can't be empty"));
    }

    public static Date getStartOfDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 今天的结束时间（自然日）
     *
     * @return
     */
    public static Date getEndOfDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

}
