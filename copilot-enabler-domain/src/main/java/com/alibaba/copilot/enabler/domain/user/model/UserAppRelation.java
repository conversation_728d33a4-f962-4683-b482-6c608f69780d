package com.alibaba.copilot.enabler.domain.user.model;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/30
 */
@Slf4j
@Data
@Builder
public class UserAppRelation {
    /**
     * 主键
     */
    private Long id;

    /**
     * 账号 id
     */
    private Long userId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 产品名称
     */
    private String appName;

    /**
     * 产品编码
     */
    private String appCode;

    /**
     * 产品类型
     */
    private String appType;

    /**
     * 产品绑定状态
     */
    private String bindStatus;

    /**
     * 产品绑定来源
     */
    private String bindSource;
}
