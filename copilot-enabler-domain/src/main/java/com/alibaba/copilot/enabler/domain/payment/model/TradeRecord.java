package com.alibaba.copilot.enabler.domain.payment.model;

import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 付费记录表
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@Slf4j
public class TradeRecord {

    /**
     * id
     */
    private Long id;

    /**
     * 唯一流水号
     */
    private String tradeNo;

    /**
     * 外部流水号
     */
    private String outTradeNo;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 订单ID
     */
    private Long subscriptionOrderId;

    /**
     * App code 业务身份
     */
    private String appCode;

    /**
     * 支付类型（用户主动支付、系统定期代扣）
     */
    private PaymentTypeEnum paymentType;

    /**
     * 交易金额
     */
    private BigDecimal tradeAmount;

    /**
     * 币种
     */
    private String tradeCurrency;

    /**
     * 交易方向
     */
    private TradeDirection tradeDirection;

    /**
     * 支付方式（Credit Card、Google Pay、Paypal、Alipay）
     */
    private String paymentMethod;

    /**
     * 交易时间
     */
    private Date tradeTime;

    /**
     * 状态
     */
    private TradeRecordStatus status;

    /**
     * 税费
     */
    private BigDecimal taxAmount;

    /**
     * 税费币种
     */
    private String taxCurrency;

    /**
     * 手续费
     */
    private BigDecimal transactionAmount;

    /**
     * 手续费币种
     */
    private String transactionCurrency;

    /**
     * 是否已发起支付
     * （指是否已请求第三方支付平台）
     */
    private Boolean hadInitiatePay;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 属性（JSON格式）
     */
    @Builder.Default
    private TradeRecordAttributes attributes = new TradeRecordAttributes(null);
}
