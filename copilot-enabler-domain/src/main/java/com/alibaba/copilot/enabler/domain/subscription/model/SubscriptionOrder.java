package com.alibaba.copilot.enabler.domain.subscription.model;

import cn.hutool.core.date.DateUtil;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订阅计划-用户订阅关系实体
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Slf4j
@Data
public class SubscriptionOrder {

    /**
     * id
     */
    @Setter
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 产品唯一code
     */
    private String appCode;

    /**
     * 用户产品关系ID
     */
    private Long userAppRelationId;

    /**
     * 计划ID
     */
    private Long subscriptionPlanId;

    /**
     * 计划名称
     */
    private String subscriptionPlanName;

    /**
     * 下次执行的计划ID
     */
    private Long nextPlanId;

    /**
     * 下次执行的计划名称
     */
    private String nextPlanName;

    /**
     * 订单状态
     */
    @Setter
    private SubscriptionOrderStatus status;

    /**
     * 折扣tag
     */
    private String subscriptionDiscountTag;

    /**
     * 是否续定（否取消订阅）
     */
    private Boolean autoRenew;

    /**
     * 下次自动续费的时间
     */
    private Date nextRenewalTime;

    /**
     * 是否包含试用期
     */
    private Boolean isIncludeTrial;

    /**
     * 计划价格（计划的原价）
     */
    private BigDecimal planPrice;

    /**
     * 真实付款费用
     */
    private BigDecimal actualFee;

    /**
     * 订单履约开始时间
     */
    private Date performStartTime;

    /**
     * 订单履约结束时间
     */
    private Date performEndTime;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 已发起下一次续订
     * (防止生成支付流水任务再起被扫描到)
     */
    private Boolean hadNextRenew;

    /**
     * Shopify订阅ID
     */
    private Long shopifySubscriptionId;

    /**
     * 订阅支付类型
     */
    private SubscriptionPayType subscriptionPayType;

    /**
     * 外部订阅ID
     */
    private String outerSubscriptionId;

    /**
     * 属性
     */
    @Builder.Default
    private SubscriptionOrderAttributes attributes = new SubscriptionOrderAttributes(null);

    /**
     * 是否是免费订单
     */
    public boolean freeOrder() {
        return planPrice.compareTo(BigDecimal.ZERO) <= 0;
    }

    /**
     * 判断当前是否在试用期
     */
    public boolean currentIsInTrial() {
        if (isIncludeTrial) {
            Long trialDays = attributes.getTrialDays();
            if (trialDays != null && trialDays > 0) {
                Date trialEndTime = DateUtils.addDays(performStartTime, Math.toIntExact(trialDays));
                return DateUtil.compare(new Date(), trialEndTime) < 0;
            }
        }

        return false;
    }
}
