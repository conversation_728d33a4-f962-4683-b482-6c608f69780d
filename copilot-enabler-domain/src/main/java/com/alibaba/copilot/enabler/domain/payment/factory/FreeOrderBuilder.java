package com.alibaba.copilot.enabler.domain.payment.factory;

import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.dto.CreateFreeOrderDTO;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrderAttributes;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.user.model.UserAppRelation;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2024/1/18
 */
@AllArgsConstructor
public class FreeOrderBuilder implements Builder<SubscriptionOrder> {

    private final CreateFreeOrderDTO dto;
    private final Integer month;
    private final SubscriptionPlan plan;
    private final UserAppRelation userAppRelation;

    @Override
    public SubscriptionOrder build() {
        String appCode = dto.getAppCode();
        Long userId = dto.getUserId();
        Long planId = dto.getPlanId();

        // 计算开始和结束时间
        Date now = new Date();
        int orderDays = month * 30;
        Date endTime = new Date(now.getTime() + Duration.ofDays(orderDays).toMillis());

        // 创建新订单
        SubscriptionOrderAttributes attributes = new SubscriptionOrderAttributes();
        attributes.setTrialDays((long) orderDays);
        attributes.setPlanDays(0L);
        attributes.setFreeOrder(true);
        return SubscriptionOrder.builder()
                .userId(userId)
                .email("")
                .appCode(appCode)
                .userAppRelationId(userAppRelation.getId())
                .subscriptionPlanId(planId)
                .subscriptionPlanName(plan.getName())
                .status(SubscriptionOrderStatus.IN_EFFECT)
                .subscriptionDiscountTag(null)
                .autoRenew(false)
                .nextRenewalTime(endTime)
                .isIncludeTrial(false)
                .planPrice(BigDecimal.ZERO)
                .performStartTime(now)
                .performEndTime(endTime)
                .deleted(false)
                .hadNextRenew(false)
                .attributes(attributes)
                .build();
    }
}
