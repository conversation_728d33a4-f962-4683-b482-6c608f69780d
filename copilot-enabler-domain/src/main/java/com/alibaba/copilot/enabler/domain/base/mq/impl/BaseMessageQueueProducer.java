package com.alibaba.copilot.enabler.domain.base.mq.impl;

import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.domain.base.mq.DelayTimeEnum;
import com.alibaba.copilot.enabler.domain.base.mq.MessageQueueProducer;
import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.taobao.metaq.client.MetaProducer;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 消息队列生产者基类
 *
 * <AUTHOR>
 * @date 2024/11/12
 */
@Slf4j
public abstract class BaseMessageQueueProducer implements MessageQueueProducer {

    private volatile MetaProducer metaProducer;
    private static final Map<DelayTimeEnum, Integer> METAQ_DELAY_TIME_2_LEVEL_MAP = new HashMap<>();

    static {
        // init metaq delay time to level mapping. https://www.atatech.org/articles/160715
        METAQ_DELAY_TIME_2_LEVEL_MAP.put(DelayTimeEnum.S_1, 1);
        METAQ_DELAY_TIME_2_LEVEL_MAP.put(DelayTimeEnum.S_5, 2);
        METAQ_DELAY_TIME_2_LEVEL_MAP.put(DelayTimeEnum.S_10, 3);
        METAQ_DELAY_TIME_2_LEVEL_MAP.put(DelayTimeEnum.S_30, 4);
        METAQ_DELAY_TIME_2_LEVEL_MAP.put(DelayTimeEnum.M_1, 5);
        METAQ_DELAY_TIME_2_LEVEL_MAP.put(DelayTimeEnum.M_2, 6);
        METAQ_DELAY_TIME_2_LEVEL_MAP.put(DelayTimeEnum.M_3, 7);
        METAQ_DELAY_TIME_2_LEVEL_MAP.put(DelayTimeEnum.M_4, 8);
        METAQ_DELAY_TIME_2_LEVEL_MAP.put(DelayTimeEnum.M_5, 9);
        METAQ_DELAY_TIME_2_LEVEL_MAP.put(DelayTimeEnum.M_6, 10);
        METAQ_DELAY_TIME_2_LEVEL_MAP.put(DelayTimeEnum.M_7, 11);
        METAQ_DELAY_TIME_2_LEVEL_MAP.put(DelayTimeEnum.M_8, 12);
        METAQ_DELAY_TIME_2_LEVEL_MAP.put(DelayTimeEnum.M_9, 13);
        METAQ_DELAY_TIME_2_LEVEL_MAP.put(DelayTimeEnum.M_10, 14);
        METAQ_DELAY_TIME_2_LEVEL_MAP.put(DelayTimeEnum.M_20, 15);
        METAQ_DELAY_TIME_2_LEVEL_MAP.put(DelayTimeEnum.M_30, 16);
        METAQ_DELAY_TIME_2_LEVEL_MAP.put(DelayTimeEnum.H_1, 17);
        METAQ_DELAY_TIME_2_LEVEL_MAP.put(DelayTimeEnum.H_2, 18);
    }

    @PostConstruct
    public void init() {
        try {
            metaProducer = new MetaProducer(getProducerGroup());
            metaProducer.setUnitName(getProducerUnitName());
            metaProducer.start();
        } catch (Exception e) {
            log.error("init producer error, metaQProducerGroup={}", getProducerGroup(), e);
        }
    }


    @Override
    public <T> boolean send(String topic, String tags, T event) {
        MessageExt msg = buildMessage(topic, tags, event);
        return internalSend(msg, event);
    }


    @Override
    public <T> boolean send(String topic, String tags, T event, DelayTimeEnum delayTimeEnum) {
        MessageExt msg = buildMessage(topic, tags, event);
        msg.setDelayTimeLevel(METAQ_DELAY_TIME_2_LEVEL_MAP.get(delayTimeEnum));
        return internalSend(msg, event);
    }

    private MessageExt buildMessage(String topic, String tags, Object event) {
        Assertor.assertNotBlank(topic, "topic should not be blank");
        Assertor.assertNotBlank(tags, "tags should not be blank");
        Assertor.assertNonNull(event, "event should not be null");

        MessageExt messageExt = new MessageExt();
        messageExt.setTopic(topic);
        messageExt.setTags(tags);
        if (event instanceof String) {
            messageExt.setBody(((String) event).getBytes());
        } else {
            messageExt.setBody(JSON.toJSONString(event).getBytes());
        }
        return messageExt;
    }

    private <T> boolean internalSend(MessageExt message, T event) {
        SendResult sendResult = null;
        try {
            sendResult = metaProducer.send(message);
            return true;
        } catch (MQClientException e) {
            log.error("send message error, topic={}", message.getTopic(), e);
            if (e.getResponseCode() == 13) {
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("send message error, topic={}", message.getTopic(), e);
            return false;
        } finally {
            traceLogMessage(message, event, sendResult);
        }
    }

    private void traceLogMessage(MessageExt message, Object msgBody, SendResult sendResult) {
        log.warn("{}||{}||{}||{}", message.getTopic(), message.getTags(), JSON.toJSONString(msgBody), JSON.toJSONString(sendResult));
    }



    /**
     * get config group
     *
     * @return
     */
    protected abstract String getProducerGroup();

    protected abstract String getProducerUnitName();


}
