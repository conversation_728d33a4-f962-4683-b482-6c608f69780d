package com.alibaba.copilot.enabler.domain.payment.factory;


import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.client.subscription.dto.CreateFreeOrderDTO;
import com.alibaba.copilot.enabler.domain.base.utils.PaymentUtils;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecordAttributes;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@AllArgsConstructor
public class FreeTradeRecordBuilder implements Builder<TradeRecord> {
    private final CreateFreeOrderDTO dto;
    private final SubscriptionOrder subscriptionOrder;

    @Override
    public TradeRecord build() {
        TradeRecord payRecord = TradeRecord.builder()
                .userId(dto.getUserId())
                .subscriptionOrderId(subscriptionOrder.getId())
                .appCode(dto.getAppCode())
                .paymentType(PaymentTypeEnum.INITIATED_PAYMENT)
                .tradeAmount(new BigDecimal(0))
                .tradeCurrency(AEPaymentConstants.CURRENCY_CODE)
                .tradeDirection(TradeDirection.FORWARD)
                .paymentMethod(dto.getTradeRecord().getPaymentMethod())
                .outTradeNo(dto.getTradeRecord().getOutTradeNo())
                .status(TradeRecordStatus.SUCC)
                .tradeTime(new Date())
                .taxAmount(null)
                .taxCurrency(null)
                .transactionAmount(null)
                .transactionCurrency(null)
                .tradeNo(PaymentUtils.generateTradeNo(dto.getUserId()))
                .hadInitiatePay(true)
                .deleted(false)
                .attributes(new TradeRecordAttributes(null))
                .build();

        return payRecord;
    }
}
