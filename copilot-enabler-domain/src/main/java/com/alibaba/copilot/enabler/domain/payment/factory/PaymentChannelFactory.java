package com.alibaba.copilot.enabler.domain.payment.factory;

import com.alibaba.copilot.boot.basic.enums.IEnum;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 创建支付渠道工厂
 */
public class PaymentChannelFactory {

    /**
     * App与对应支付渠道bean 的映射关系
     */
    private static final Map<AppEnum, IPaymentChannelStrategy> APP_AND_PAYMENT_CHANNEL_MAP = new ConcurrentHashMap<>();

    /**
     * 默认支付渠道
     */
    private static IPaymentChannelStrategy defaultPaymentChannel;

    /**
     * 根据支付渠道enum获取对应的支付渠道bean
     *
     * @param appEnum 应用
     * @return 支付渠道bean
     */
    public static IPaymentChannelStrategy getPaymentByApp(AppEnum appEnum) {
        IPaymentChannelStrategy abstractPayment = APP_AND_PAYMENT_CHANNEL_MAP.get(appEnum);
        if (abstractPayment == null) {
            return defaultPaymentChannel;
        }

        return abstractPayment;
    }

    public static IPaymentChannelStrategy getPaymentByApp(String appCode) {
        return getPaymentByApp(IEnum.of(AppEnum.class, appCode));
    }

    /**
     * 设置默认支付渠道
     *
     * @param abstractPayment 默认支付渠道
     */
    public static void setDefaultBindMap(IPaymentChannelStrategy abstractPayment) {
        defaultPaymentChannel = abstractPayment;
    }

    /**
     * 对指定app并设置支付渠道
     *
     * @param appEnum         应用标识
     * @param abstractPayment 对应支付渠道bean
     */
    public static void register(AppEnum appEnum, IPaymentChannelStrategy abstractPayment) {
        APP_AND_PAYMENT_CHANNEL_MAP.put(appEnum, abstractPayment);
    }
}