package com.alibaba.copilot.enabler.domain.subscription.strategy.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/10/25
 */
@Data
@Accessors(chain = true)
public class ComputeNewPlanPriceDTO implements Serializable {

    /**
     * 应用标识
     */
    private String appCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 计划ID
     */
    private Long planId;

    /**
     * 折扣码 (用来作为推广域标识)
     */
    private String shareCode;

    /**
     * 折扣码 (目前仅PicCopilot场景使用)
     */
    private String discountCode;
}
