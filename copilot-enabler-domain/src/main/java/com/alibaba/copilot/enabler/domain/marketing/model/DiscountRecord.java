package com.alibaba.copilot.enabler.domain.marketing.model;

import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 折扣记录
 */
@Data
@Accessors(chain = true)
public class DiscountRecord implements Serializable {

    /**
     * ID主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 应用标识
     */
    private AppEnum appCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 折扣规则ID
     */
    private Long discountRuleId;

    /**
     * 套餐原价
     */
    private BigDecimal planOriginPrice;

    /**
     * 折扣价
     */
    private BigDecimal discountPrice;

    /**
     * 扩展属性
     */
    private DiscountRecordAttributes attributes = new DiscountRecordAttributes(null);
}
