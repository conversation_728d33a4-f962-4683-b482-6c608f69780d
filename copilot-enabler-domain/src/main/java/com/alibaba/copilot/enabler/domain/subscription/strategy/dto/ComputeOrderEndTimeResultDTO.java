package com.alibaba.copilot.enabler.domain.subscription.strategy.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2023/10/24
 */
@Data
@Accessors(chain = true)
public class ComputeOrderEndTimeResultDTO implements Serializable {

    /**
     * 试用期天数
     */
    private long trialDays;

    /**
     * 套餐天数
     */
    private long planDays;

    /**
     * 订单结束时间
     */
    private Date orderEndTime;
}
