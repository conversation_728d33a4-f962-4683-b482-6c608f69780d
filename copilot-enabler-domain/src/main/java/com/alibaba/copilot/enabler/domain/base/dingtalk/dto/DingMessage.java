package com.alibaba.copilot.enabler.domain.base.dingtalk.dto;


import lombok.Data;

@Data
public class DingMessage {

    /**
     * 消息文本类型 目前只支持文本
     */
    private String msgtype = "text";

    /**
     * 文本消息
     */
    private MessageContent text;


    @Data
    public static class MessageContent {

        /**
         * 文本内容
         */
        private String content;

    }

    public static DingMessage buildTextMessage(String content) {
        DingMessage dingMessage = new DingMessage();
        MessageContent mc = new MessageContent();
        mc.setContent(content);
        dingMessage.setText(mc);
        return dingMessage;
    }
}
