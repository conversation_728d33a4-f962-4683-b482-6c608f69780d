package com.alibaba.copilot.enabler.domain.payment.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 店匠订阅Request
 *
 * <AUTHOR>
 * @date 2024/9/19 4:47 下午
 */
@Data
public class ShoplazzaSubscriptionRequest {

    private String name;

    private String price;

    @JSONField(name = "return_url")
    private String returnUrl;

    @JSONField(name = "trial_days")
    private Integer trialDays;

    private Boolean test;
}