package com.alibaba.copilot.enabler.domain.payment.model;

import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 支付令牌表
 *
 * <AUTHOR>
 * @version 2024/1/11
 */
@Data
@Accessors(chain = true)
public class PaymentToken implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 业务身份
     */
    private AppEnum appCode;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 支付方式 (如: ALIPAY_CN、ALIPAY_HK)
     */
    private PaymentMethodEnum paymentMethod;

    /**
     * 授权标识 (关联外部支付钱包的授权上下文标识)
     */
    private String authState;

    /**
     * 获取授权时间
     */
    private Date authStartTime;

    /**
     * 结束授权时间
     */
    private Date authEndTime;

    /**
     * 支付令牌
     */
    private String token;

    /**
     * 支付令牌标识
     */
    private String tokenId;

    /**
     * 拓展字段
     */
    private PaymentTokenAttributes attributes = new PaymentTokenAttributes(null);
}
