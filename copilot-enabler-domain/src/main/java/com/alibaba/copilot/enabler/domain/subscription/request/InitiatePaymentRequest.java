package com.alibaba.copilot.enabler.domain.subscription.request;

import com.alibaba.aepay.fund.business.api.payment.dto.*;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.global.money.Money;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
public class InitiatePaymentRequest {

    /**
     * 业务身份
     */
    private AppEnum appEnum;

    /**
     * 支付方式
     */
    private PaymentMethodEnum paymentMethod;

    /**
     *支付幂等号（支付流水id）
     */
    @NotNull
    private String paymentRequestId;

    /**
     * 订单信息，如买方、商家、货物、数量、运输信息和购买环境。这个字段有不同的用途:
     *
     * 在支付过程中，该领域主要被支付宝用于风险控制或反洗钱。
     * 付款完成后，此字段用于记录和报告目的，如采购跟踪和监管报告。
     */
    @NotNull
    @Valid
    private Order order;

    /**
     * 支付金额
     */
    @NotNull
    private Money paymentAmount;

    /**
     * 支付过期时间
     */
    private Long paymentExpiryTime;

    /**
     * 页面跳转地址
     */
    @NotNull
    private String paymentRedirectUrl;

    /**
     * 卡Token (仅用于信用卡支付)
     */
    private String cardToken;

    /**
     * 卡组交易ID (仅用于信用卡支付)
     */
    private String networkTransactionId;

    /**
     * Alipay Token (仅用于Alipay支付, 已授权时传递)
     */
    private String alipayToken;
}
