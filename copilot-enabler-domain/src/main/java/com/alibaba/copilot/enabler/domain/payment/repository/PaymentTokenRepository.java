package com.alibaba.copilot.enabler.domain.payment.repository;

import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentToken;
import com.alibaba.copilot.enabler.domain.payment.model.PaymentTokenAttributes;

import java.util.List;
import java.util.Map;

/**
 * 支付令牌 repository
 *
 * <AUTHOR>
 * @version 2024/1/11
 */
public interface PaymentTokenRepository {

    /**
     * 根据用户和authState创建
     */
    PaymentToken create(String appCode, Long userId, PaymentMethodEnum paymentMethod, String authState);

    /**
     * 根据用户和 token 创建
     */
    PaymentToken createByToken(String appCode, Long userId, PaymentMethodEnum paymentMethod, String token, PaymentTokenAttributes attributes);

    /**
     * 根据用户和 token 创建
     */
    PaymentToken createByToken(String appCode, Long userId, PaymentMethodEnum paymentMethod, String token);

    /**
     * 更新token信息
     */
    boolean updateToken(Long id, String token);

    /**
     * 根据tokenId查询
     */
    PaymentToken queryByTokenId(String tokenId);

    /**
     * 根据authState查询
     */
    PaymentToken queryByAuthState(String authState);

    /**
     * 根据token查询
     */
    PaymentToken queryByToken(String token);

    /**
     * 根据token删除
     */
    boolean deleteById(Long id);

    /**
     * 查询指定用户的已授权的支付令牌 (注: 每类支付方式, 只查询生效的最新一条)
     */
    Map<PaymentMethodEnum, PaymentToken> queryAuthedTokensByUser(String appCode, Long userId);


    /**
     * 查询指定支付方式的支付令牌
     */
    List<PaymentToken> queryTokenByPaymentMethod(String appCode, Long userId, PaymentMethodEnum paymentMethod);
}
