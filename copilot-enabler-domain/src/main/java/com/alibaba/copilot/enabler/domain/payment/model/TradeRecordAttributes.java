package com.alibaba.copilot.enabler.domain.payment.model;

import com.alibaba.copilot.boot.basic.data.Attributes;
import com.alibaba.copilot.enabler.client.payment.dto.OrderDTO;
import com.alibaba.copilot.enabler.domain.user.model.StripeInfo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.Optional;

/**
 * TradeRecordAttributes
 */
public class TradeRecordAttributes extends Attributes {

    /**
     * 订阅计划id
     */
    private static final String ATTR_SUBSCRIPTION_PLAN_ID = "subscriptionPlanId";

    /**
     * 订阅计划名称
     */
    private static final String ATTR_SUBSCRIPTION_PLAN_NAME = "subscriptionPlanName";

    /**
     * 状态流转原因
     */
    private static final String ATTR_STATUS_FLOW_REASON = "statusFlowReason";

    /**
     * 首月折扣的规则ID
     */
    private static final String ATTR_IS_FIRST_MONTH_DISCOUNT_RULE_ID = "firstMonthDiscountRuleId";

    /**
     * 支付账单发票文件名
     */
    private static final String ATTR_PAY_BILL_INVOICE_FILE_NAME = "payBillInvoiceFileName";

    /**
     * 支付产品码
     */
    private static final String ATTR_PAY_PRODUCT_CODE = "payProductCode";

    private static final String ATTR_ORDER = "order";

    private static final String ATTR_REDIRECT_URL = "cashierPayRedirectUrl";

    private static final String ATTR_NORMAL_URL = "cashierPayNormalUrl";

    private static final String ATTR_PAYMENT_SESSION_ID = "paymentSessionId";
    private static final String ATTR_PAYMENT_SESSION_DATA = "paymentSessionData";
    private static final String ATTR_ENV = "environment";
    private static final String ATTR_PAYMENT_SESSION_EXPIRE_TIME = "paymentSessionExpiryTime";

    /**
     * 使用WEB_SDK对接Stripe创建Session的clientSecret
     */
    private static final String ATTR_STRIPE_SESSION_CLIENT_SECRET = "clientSecret";

    /**
     * IP 地址信息
     */
    public static final String IP_ADDRESS_INFO = "ipAddressInfo";

    public static final String BILLING_DETAIL = "billingDetail";

    public static final String PAYMENT_METHOD_DETAIL = "paymentMethodDetail";


    public TradeRecordAttributes(String json) {
        super(json);
    }

    public String getPaymentSessionId() {
        return getAsString(ATTR_PAYMENT_SESSION_ID);
    }

    public void setPaymentSessionId(String paymentSessionId) {
        put(ATTR_PAYMENT_SESSION_ID, paymentSessionId);
    }

    public String getPaymentSessionData() {
        return getAsString(ATTR_PAYMENT_SESSION_DATA);
    }

    public void setPaymentSessionData(String paymentSessionData) {
        put(ATTR_PAYMENT_SESSION_DATA, paymentSessionData);
    }

    public String getEnv() {
        return getAsString(ATTR_ENV);
    }

    public void setEnv(String env) {
        put(ATTR_ENV, env);
    }

    public Long getPaymentSessionExpiryTime() {
        return getAsLong(ATTR_PAYMENT_SESSION_EXPIRE_TIME);
    }

    public void setPaymentSessionExpiryTime(Long paymentSessionExpiryTime) {
        put(ATTR_PAYMENT_SESSION_EXPIRE_TIME, paymentSessionExpiryTime);
    }

    public void setCashierPayRedirectUrl(String redirectUrl) {
        put(ATTR_REDIRECT_URL, redirectUrl);
    }

    public String getCashierPayRedirectUrl() {
        return getAsString(ATTR_REDIRECT_URL);
    }

    public void setCashierPayNormalUrl(String normalUrl) {
        put(ATTR_NORMAL_URL, normalUrl);
    }

    public String getCashierPayNormalUrl() {
        return getAsString(ATTR_NORMAL_URL);
    }

    public void setOrder(OrderDTO order) {
        put(ATTR_ORDER, order);
    }

    public OrderDTO getOrder() {
        return get(ATTR_ORDER, OrderDTO.class);
    }

    public void setProductCode(String productCode) {
        put(ATTR_PAY_PRODUCT_CODE, productCode);
    }

    public String getProductCode() {
        return get(ATTR_PAY_PRODUCT_CODE, String.class);
    }

    public void setSubscriptionPlanId(Long planId) {
        put(ATTR_SUBSCRIPTION_PLAN_ID, planId);
    }

    public Long getSubscriptionPlanId() {
        return getAsLong(ATTR_SUBSCRIPTION_PLAN_ID);
    }

    public void setSubscriptionPlanName(String planName) {
        put(ATTR_SUBSCRIPTION_PLAN_NAME, planName);
    }

    public String getSubscriptionPlanName() {
        return getAsString(ATTR_SUBSCRIPTION_PLAN_NAME);
    }

    public void setStatusFlowReason(String statusFlowReason) {
        put(ATTR_STATUS_FLOW_REASON, statusFlowReason);
    }

    public Long getFirstMonthDiscountRuleId() {
        return getAsLong(ATTR_IS_FIRST_MONTH_DISCOUNT_RULE_ID);
    }

    public void setFirstMonthDiscountRuleId(Long firstMonthDiscountRuleId) {
        put(ATTR_IS_FIRST_MONTH_DISCOUNT_RULE_ID, firstMonthDiscountRuleId);
    }

    public String getPayBillInvoiceFileName() {
        return getAsString(ATTR_PAY_BILL_INVOICE_FILE_NAME);
    }

    public void setPayBillInvoiceFileName(String payBillInvoiceFileName) {
        put(ATTR_PAY_BILL_INVOICE_FILE_NAME, payBillInvoiceFileName);
    }

    public String getStripeSessionClientSecret() {
        return getAsString(ATTR_STRIPE_SESSION_CLIENT_SECRET);
    }

    public void setStripeSessionClientSecret(String stripeSessionClientSecret) {
        put(ATTR_STRIPE_SESSION_CLIENT_SECRET, stripeSessionClientSecret);
    }


    public IpAddressInfo getIpAddressInfoNotNull() {
        return Optional.ofNullable(getAttributes())
                .map(map -> map.get(IP_ADDRESS_INFO))
                .map(JSONObject::toJSONString)
                .map(json -> {
                    Type mapActualType = new TypeToken<IpAddressInfo>() {
                    }.getType();
                    return (IpAddressInfo) JSON.parseObject(json, mapActualType);
                })
                .orElseGet(IpAddressInfo::new);
    }

    public void setIpAddressInfo(IpAddressInfo ipAddressInfo) {
        getAttributes().put(IP_ADDRESS_INFO, ipAddressInfo);
    }

    public void setBillingDetail(BillingDetail billingDetail) {
        getAttributes().put(BILLING_DETAIL, billingDetail);
    }

    public void setPaymentMethodDetail(PaymentMethodDetail paymentMethodDtail) {
        getAttributes().put(PAYMENT_METHOD_DETAIL, paymentMethodDtail);
    }

    public BillingDetail getBillingDetailNotNull() {
        return Optional.ofNullable(getAttributes())
                .map(map -> map.get(BILLING_DETAIL))
                .map(JSONObject::toJSONString)
                .map(json -> {
                    Type mapActualType = new TypeToken<BillingDetail>() {
                    }.getType();
                    return (BillingDetail) JSON.parseObject(json, mapActualType);
                })
                .orElseGet(BillingDetail::new);
    }
}
