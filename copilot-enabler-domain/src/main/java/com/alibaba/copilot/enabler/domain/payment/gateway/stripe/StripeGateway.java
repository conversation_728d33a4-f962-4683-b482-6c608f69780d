package com.alibaba.copilot.enabler.domain.payment.gateway.stripe;

import com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto.*;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/06
 */
public interface StripeGateway {


    StripeCreateSessionResponse createCheckoutSession(StripeCreateSessionRequest request);

    StripeCreateCustomerResponse createCustomer(StripeCreateCustomerRequest request);

    StripeRetrieveCustomerResponse retrieveCustomer(String customerId);

    void createPaymentIntent(StripeCreatePaymentIntentRequest request);

    void createRefund(StripeCreateRefundRequest request);

    void checkWebhookEvent(String payload, String sigHeader);

    StripePaymentMethodResponse retrievePaymentMethod(String paymentMethodId);
}