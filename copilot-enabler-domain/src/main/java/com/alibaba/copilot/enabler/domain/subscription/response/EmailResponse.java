package com.alibaba.copilot.enabler.domain.subscription.response;

import lombok.Data;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/10/22
 */
@Data
public class EmailResponse {

    private Boolean success;
    private String errorMessage;


    public static EmailResponse buildSuccessRes() {
        EmailResponse e = new EmailResponse();
        e.setSuccess(true);
        return e;
    }

    public static EmailResponse buildErrorRes(String errorMessage) {
        EmailResponse e = new EmailResponse();
        e.setSuccess(false);
        e.setErrorMessage(errorMessage);
        return e;
    }

}