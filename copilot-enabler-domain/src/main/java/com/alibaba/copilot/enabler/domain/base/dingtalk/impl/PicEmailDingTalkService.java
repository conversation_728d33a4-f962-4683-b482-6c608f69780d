package com.alibaba.copilot.enabler.domain.base.dingtalk.impl;

import org.springframework.stereotype.Service;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/10/22
 */
@Service("picEmailDingTalkService")
public class PicEmailDingTalkService extends AbstractDingTalkService {


    @Override
    protected String getRobotWebhookUrl() {
        return "https://oapi.dingtalk.com/robot/send?access_token=f880739a69f3dbf804d747e133c93bef15f4d0b34220c4d3437ce350af778d05";
    }

    @Override
    protected String getRobotSignSecret() {
        return "SECcc4add6cfc958cfd9797b4c8485557713128f60696476ba9637ccef258f115ba";
    }

}