package com.alibaba.copilot.enabler.domain.subscription.request;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 查询订单
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class SubscriptionOrderQuery {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * APP CODE
     */
    private String appCode;

    /**
     * 订单ID
     */
    private List<Long> orderIds;

    /**
     * 订单状态
     */
    private List<SubscriptionOrderStatus> status;

    /**
     * 已发起下一次续订
     */
    private Boolean hadNextRenew;

    /**
     * 是否续定（否取消订阅）
     */
    private Boolean autoRenew;

    /**
     * 下次自动续费的开始时间
     */
    private Date nextRenewalStartTime;

    /**
     * 下次自动续费的结束时间
     */
    private Date nextRenewalEndTime;

    /**
     * 限制条数
     */
    private Integer limit;

    /**
     * 订单履约开始时间
     */
    private Date startTime;

    /**
     * 订单履约结束时间
     */
    private Date endTime;
}
