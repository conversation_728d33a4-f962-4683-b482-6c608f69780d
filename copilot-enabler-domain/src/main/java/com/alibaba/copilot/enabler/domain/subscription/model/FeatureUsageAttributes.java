package com.alibaba.copilot.enabler.domain.subscription.model;

import com.alibaba.copilot.boot.basic.data.Attributes;

/**
 * @ClassName FeatureUsageAttributes
 * <AUTHOR>
 * @Date 2023/9/5 16:57
 */
public class FeatureUsageAttributes extends Attributes {
    public FeatureUsageAttributes(String json) {
        super(json);
    }

    private static final String ATTR_FEATURE_TYPE = "featureType";

    private static final String ATTR_PLAN_NAME = "planName";


    public void setFeatureType(String featureType) {
        put(ATTR_FEATURE_TYPE, featureType);
    }

    public String getFeatureType() {
        return getAsString(ATTR_FEATURE_TYPE);
    }

    public void setPlanName(String planName) {
        put(ATTR_PLAN_NAME, planName);
    }

    public String getPlanName() {
        return getAsString(ATTR_PLAN_NAME);
    }
}
