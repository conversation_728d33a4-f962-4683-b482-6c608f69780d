package com.alibaba.copilot.enabler.domain.subscription.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 推广域折扣信息-响应
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class DiscountInfoDTO {

    /**
     * 折扣
     */
    private Integer discount;

    /**
     * 折扣周期
     */
    private Long discountDuration;

    /**
     * 折扣码
     */
    private String DiscountTag;


    /**
     * 折扣截止日
     */
    private Date discountDeadline;
}
