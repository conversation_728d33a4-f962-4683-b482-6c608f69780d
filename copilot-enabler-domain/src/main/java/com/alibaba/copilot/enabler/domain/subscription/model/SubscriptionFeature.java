package com.alibaba.copilot.enabler.domain.subscription.model;

import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * 特性-值对象
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@Slf4j
public class SubscriptionFeature {

    /**
     * id
     */
    private Long id;

    /**
     * 计划-特性关联ID（subscription_plan_feature表主键）
     */
    private Long planFeatureId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 产品唯一code
     */
    private String appCode;

    /**
     * 特性名称
     */
    private String name;

    /**
     * 特性描述
     */
    private String description;

    /**
     * 特性类型
     */
    private String type;

    /**
     * 是否消耗型特性
     */
    private Boolean isDepletion;

    /**
     * 权益信息（json）
     */
    private Benefit benefit;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 属性
     */
    private SubscriptionFeatureAttributes attributes = new SubscriptionFeatureAttributes(null);

    /**
     * 设置Attribute
     *
     * @param subscriptionFeatureAttributes
     */
    public void setAttributes(SubscriptionFeatureAttributes subscriptionFeatureAttributes) {
        this.attributes = subscriptionFeatureAttributes;
    }

    /**
     * 设置planFeatureId
     *
     * @param planFeatureId
     */
    public void setPlanFeatureId(Long planFeatureId) {
        this.planFeatureId = planFeatureId;
    }
}
