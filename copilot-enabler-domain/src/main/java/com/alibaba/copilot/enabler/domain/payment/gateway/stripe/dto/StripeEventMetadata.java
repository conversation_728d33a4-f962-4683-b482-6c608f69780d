package com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto;

import com.alibaba.copilot.enabler.domain.payment.constant.StripeCheckoutSessionMode;
import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Data
public class StripeEventMetadata {

    // ===== 必传参数 =====

    private String sessionMode;
    private String appCode;
    private String tradeNo;

    // ===== 选填 ======

    private String userId;
    private String subscriptionOrderId;
    private String refundTradeId;

    private String goodsName;


    public static StripeEventMetadata of(StripeCheckoutSessionMode mode, String appCode, String tradeNo) {
        StripeEventMetadata metadata = new StripeEventMetadata();
        metadata.setSessionMode(mode.name());
        metadata.setAppCode(appCode);
        metadata.setTradeNo(tradeNo);
        return metadata;
    }


    public Map<String, String> toMap() {
        Map<String, String> result = new HashMap<>(8);
        JSON.parseObject(JSON.toJSONString(this))
                .forEach((k, v) -> {
                    if (Objects.nonNull(v)) {
                        result.put(k, String.valueOf(v));
                    }
                });
        return result;
    }

    public boolean whetherSetupMode() {
        return StripeCheckoutSessionMode.SETUP.name().equals(sessionMode);
    }

    public boolean whetherPaymentMode() {
        return StripeCheckoutSessionMode.PAYMENT.name().equals(sessionMode);
    }

    public boolean whetherSubscriptionMode() {
        return StripeCheckoutSessionMode.SUBSCRIPTION.name().equals(sessionMode);
    }


}