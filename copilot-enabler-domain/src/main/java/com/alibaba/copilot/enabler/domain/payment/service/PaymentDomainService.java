package com.alibaba.copilot.enabler.domain.payment.service;

import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.base.event.DomainEventJsonProducer;
import com.alibaba.copilot.enabler.client.payment.constant.StatusFlowReasonEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.payment.constant.dispute.DisputeResult;
import com.alibaba.copilot.enabler.client.payment.constant.dispute.DisputeStatus;
import com.alibaba.copilot.enabler.client.payment.dto.DisputeEventDTO;
import com.alibaba.copilot.enabler.client.payment.dto.PaymentResultEventDTO;
import com.alibaba.copilot.enabler.client.payment.request.CashierPayRequest;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.base.utils.PaymentUtils;
import com.alibaba.copilot.enabler.domain.payment.dto.PaymentAsyncResultDTO;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecordAttributes;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.payment.strategy.CreateCashierPayStrategy;
import com.alibaba.copilot.enabler.domain.payment.strategy.CreateCashierPayStrategyFactory;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 支付领域服务
 * 7/20/24
 *
 * <AUTHOR> href="mailto:<EMAIL>">ouzhencong</a>
 */
@Slf4j
@Service
public class PaymentDomainService {

    @Resource
    private TradeRecordRepository tradeRecordRepository;
    @Resource
    private DomainEventJsonProducer domainEventJsonProducer;
    @Resource
    private UserRepository userRepository;

    public TradeRecord createCashierPay(CashierPayRequest request) {
        AppEnum app = AppEnum.getAppByCode(request.getAppCode());
        CreateCashierPayStrategy strategy = CreateCashierPayStrategyFactory.getStrategy(app);
        Assertor.assertNonNull(strategy, "App[" + app.name() + "] createCashierPay strategy not found.");
        return strategy.create(request);
    }

    public void updateCashierPayStatus(PaymentAsyncResultDTO result) {
        // 1. 更新TradeRecord记录的支付结果
        TradeRecord tradeRecord = tradeRecordRepository.queryByTradeNo(result.getPaymentRequestId());
        if (tradeRecord == null) {
            log.error("PaymentDomainService.updateCashierPayStatus, tradeRecord not found, result={}", JSON.toJSONString(result));
            throw new BizException(ErrorCode.SYS_ERROR, "trade record not found.");
        }
        String resultStatus = result.getResultStatus();
        if (resultStatus.equals("S")) {
            tradeRecord.setStatus(TradeRecordStatus.SUCC);
        } else if (resultStatus.equals("F")) {
            tradeRecord.setStatus(TradeRecordStatus.FAIL);
        } else {
            log.warn("PaymentDomainService.updateCashierPayStatus, unknown resultStatus, result={}", JSON.toJSONString(result));
        }
        if (StringUtils.isNotBlank(result.getPaymentId())) {
            tradeRecord.setOutTradeNo(result.getPaymentId());
        }
        tradeRecord.setTradeTime(new Date());
        tradeRecordRepository.createOrUpdateTradeRecord(tradeRecord);

        // 2. 发送支付结果通知
        PaymentResultEventDTO notifyDTO = buildPaymentResultNotifyDTO(tradeRecord);
        domainEventJsonProducer.publish(notifyDTO);
        log.info("PaymentDomainService.updateCashierPayStatus, notifyDTO={}", JSON.toJSONString(notifyDTO));
    }

    public void updateOutTradeNo(String tradeNo, String outTradeNo) {
        // 1. 更新TradeRecord记录的支付结果
        TradeRecord tradeRecord = tradeRecordRepository.queryByTradeNo(tradeNo);
        if (tradeRecord == null) {
            log.error("PaymentDomainService.updateOutTradeNo, tradeRecord not found, tradeNo={} outTradeNo={}", tradeNo, outTradeNo);
            throw new BizException(ErrorCode.SYS_ERROR, "trade record not found.");
        }

        tradeRecord.setOutTradeNo(outTradeNo);
        tradeRecord.setGmtModified(new Date());
        tradeRecordRepository.createOrUpdateTradeRecord(tradeRecord);
    }

    public TradeRecord queryByOutTradeNoAndTradeDirection(String outTradeNo, TradeDirection tradeDirection) {
        return tradeRecordRepository.queryByOutTradeNoAndTradeDirection(outTradeNo, tradeDirection);
    }

    public TradeRecord queryByTradeNo(String tradeNo) {
        return tradeRecordRepository.queryByTradeNo(tradeNo);
    }

    private PaymentResultEventDTO buildPaymentResultNotifyDTO(TradeRecord tradeRecord) {
        PaymentResultEventDTO notifyDTO = new PaymentResultEventDTO();
        notifyDTO.setAppCode(tradeRecord.getAppCode());
        notifyDTO.setTradeNo(tradeRecord.getTradeNo());
        notifyDTO.setOutTradeNo(tradeRecord.getOutTradeNo());
        notifyDTO.setTradeStatus(tradeRecord.getStatus().name());
        notifyDTO.setUserId(tradeRecord.getUserId());
        notifyDTO.setTradeTime(tradeRecord.getTradeTime());
        notifyDTO.setOrder(tradeRecord.getAttributes().getOrder());
        return notifyDTO;
    }

    public void dealDisputeRefund(TradeRecord tradeRecord, DisputeResult disputeResult) {
        // 1.创建退款流水
        if (DisputeResult.LOST.equals(disputeResult)) {
            TradeRecord refundRecord = new TradeRecord();
            refundRecord.setTradeNo(PaymentUtils.generateTradeNo(tradeRecord.getUserId()));
            refundRecord.setOutTradeNo(tradeRecord.getOutTradeNo());
            refundRecord.setGmtCreate(new Date());
            refundRecord.setGmtModified(new Date());
            refundRecord.setUserId(tradeRecord.getUserId());
            refundRecord.setAppCode(tradeRecord.getAppCode());
            refundRecord.setPaymentType(tradeRecord.getPaymentType());
            refundRecord.setTradeAmount(tradeRecord.getTradeAmount());
            refundRecord.setTradeCurrency(tradeRecord.getTradeCurrency());
            refundRecord.setTradeDirection(TradeDirection.REFUND);
            refundRecord.setPaymentMethod(tradeRecord.getPaymentMethod());
            refundRecord.setTradeTime(new Date());
            refundRecord.setStatus(TradeRecordStatus.SUCC);
            refundRecord.setHadInitiatePay(true);
            refundRecord.setDeleted(false);

            TradeRecordAttributes attributes = tradeRecord.getAttributes();
            if (attributes == null) {
                attributes = new TradeRecordAttributes(null);
            }
            attributes.setStatusFlowReason(StatusFlowReasonEnum.USER_HAD_DISPUTE.name());

            refundRecord.setAttributes(attributes);

            tradeRecordRepository.createOrUpdateTradeRecord(refundRecord);
        }

        // 2.发送消息
        publishDisputeEvent(tradeRecord, DisputeStatus.CLOSED.name(), disputeResult.name());
    }

    public void publishDisputeEvent(TradeRecord tradeRecord, String disputeStatus, String disputeResult) {
        DisputeEventDTO disputeEventDTO = buildDisputeEventDTO(tradeRecord, disputeStatus, disputeResult);
        domainEventJsonProducer.publish(disputeEventDTO);
        log.info("PaymentDomainService.publishDisputeEvent, disputeEventDTO={}", JSON.toJSONString(disputeEventDTO));
    }

    private DisputeEventDTO buildDisputeEventDTO(TradeRecord tradeRecord, String disputeStatus, String disputeResult) {
        DisputeEventDTO disputeEventDTO = new DisputeEventDTO();
        disputeEventDTO.setTradeNo(tradeRecord.getTradeNo());
        disputeEventDTO.setOutTradeNo(tradeRecord.getOutTradeNo());
        disputeEventDTO.setUserId(tradeRecord.getUserId());
        disputeEventDTO.setOrder(tradeRecord.getAttributes().getOrder());
        disputeEventDTO.setStatus(disputeStatus);
        disputeEventDTO.setResult(disputeResult);
        return disputeEventDTO;
    }
}
