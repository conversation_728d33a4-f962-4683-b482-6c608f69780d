package com.alibaba.copilot.enabler.domain.subscription.service;

import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.domain.subscription.dto.DiscountInfoDTO;
import com.alibaba.copilot.enabler.domain.subscription.dto.FinalDiscountDTO;
import com.alibaba.copilot.enabler.domain.subscription.gateway.SpreadGateway;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.request.DiscountInfoRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 折扣相关的领域服务
 */
@Service
@Slf4j
public class DiscountService {

    @Autowired
    private SpreadGateway spreadGateway;

    /**
     * 计算最终折扣信息
     *
     * @param appCode
     * @param discountTag
     * @param activatedPlan
     * @param historyOrders
     * @return
     */
    public FinalDiscountDTO getFinalDiscountInfo(String appCode, String discountTag, SubscriptionPlan activatedPlan,
                                                 List<SubscriptionOrder> historyOrders, boolean isOrderRenewal) {

        if (StringUtils.isEmpty(discountTag)) {
            return null;
        }
        //SEO_COPILOT_SITE 不走muyuan折扣逻辑
        if (AppEnum.SEO_COPILOT_SITE.getCode().equals(appCode)) {
            return null;
        }

        // DsCopilot产品的逻辑
        if (AppEnum.DS_COPILOT.getCode().equals(appCode)) {
            return getDSCFinalDiscountInfo(discountTag, activatedPlan, isOrderRenewal);
        }

        // 折扣
        DiscountInfoRequest discountInfoRequest = DiscountInfoRequest.builder()
                .appCode(appCode)
                .discountTag(discountTag)
                .build();

        // 折扣周期-单位默认是月
        Long remainDiscountDiscount = 0L;
        DiscountInfoDTO discountInfo = spreadGateway.getDiscountInfo(discountInfoRequest);
        Date discountDeadline = discountInfo.getDiscountDeadline();
        if (discountDeadline != null && discountDeadline.compareTo(new Date()) < 0) {
            remainDiscountDiscount = 0L;
        }
        String durationUnit = activatedPlan.getDurationUnit().name();

        List<SubscriptionOrder> planOrders = historyOrders.stream()
                .filter(order ->
                        order.getSubscriptionPlanId().equals(activatedPlan.getId()) &&
                                !order.freeOrder()
                                && StringUtils.isNotBlank(order.getSubscriptionDiscountTag())
                ).collect(Collectors.toList());

        if (StringUtils.equals(durationUnit, DurationUnit.YEAR.name())) {
            remainDiscountDiscount = CollectionUtils.isEmpty(planOrders) ? 1L : 0L;
        } else if (StringUtils.equals(durationUnit, DurationUnit.MONTH.name())) {
            long remainDuration = CollectionUtils.isEmpty(planOrders) ? discountInfo.getDiscountDuration() :
                    discountInfo.getDiscountDuration() - (long) planOrders.size();
            remainDiscountDiscount = Math.max(remainDuration, 0L);
        }
        // build
        BigDecimal discountPrice = remainDiscountDiscount > 0L ?
                activatedPlan.getPrice()
                        .multiply(BigDecimal.valueOf(discountInfo.getDiscount()))
                        .divide(BigDecimal.valueOf(100))
                        .setScale(2, RoundingMode.HALF_UP)
                : null;

        return FinalDiscountDTO.builder()
                .isDiscount(remainDiscountDiscount > 0L)
                .discountPrice(discountPrice)
                .discountInfoDTO(discountInfo)
                .discountTag(discountTag)
                .remainDiscountDuration(remainDiscountDiscount)
                .discountDurationUnit(durationUnit)
                .discountInfoDTO(discountInfo)
                .build();
    }

    /**
     * 获取DsCopilot的折扣信息
     * * 使用优惠券时，优惠券截止日期结束了则折扣不可用
     * * 订单续订时，无论优惠券截止日期是否已过期则折扣可继续使用
     *
     * @param discountTag    折扣码
     * @param activatedPlan  被激活的计划
     * @param isOrderRenewal 是否为订单续订
     * @return 最终折扣信息
     */
    private FinalDiscountDTO getDSCFinalDiscountInfo(String discountTag, SubscriptionPlan activatedPlan, boolean isOrderRenewal) {
        // 折扣
        DiscountInfoRequest discountInfoRequest = DiscountInfoRequest.builder()
                .appCode(AppEnum.DS_COPILOT.getCode())
                .discountTag(discountTag)
                .build();

        boolean isDiscount = true;

        DiscountInfoDTO discountInfo = spreadGateway.getDiscountInfo(discountInfoRequest);
        Date discountDeadline = discountInfo.getDiscountDeadline();
        if (!isOrderRenewal && discountDeadline != null && discountDeadline.compareTo(new Date()) < 0) {
            isDiscount = false;
        }

        BigDecimal price = activatedPlan.getPrice();
        // 年套餐默认是减免40%，如果折扣码优惠减免小于40%，则不使用折扣码，否则才使用折扣码的减免优惠
        if (isDiscount && DurationUnit.YEAR == activatedPlan.getDurationUnit()) {
            if (discountInfo.getDiscount() > (100 - AEPaymentConstants.DSC_ALONE_ANNUAL_PLAN_DEFAULT_REDUCE_DISCOUNT)) {
                isDiscount = false;
            } else {
                // 年套餐默认是减免40%，先用除以的方式恢复原价，然后再进行折扣计算出最终价格
                price = price.divide(new BigDecimal(1 - AEPaymentConstants.DSC_ALONE_ANNUAL_PLAN_DEFAULT_REDUCE_DISCOUNT * 0.01), 3, RoundingMode.HALF_UP);
            }
        }

        BigDecimal discountPrice = isDiscount ? price
                .multiply(BigDecimal.valueOf(discountInfo.getDiscount()))
                .divide(BigDecimal.valueOf(100), 3, RoundingMode.HALF_UP)
                .setScale(2, RoundingMode.HALF_UP) : null;

        return FinalDiscountDTO.builder()
                .isDiscount(isDiscount)
                .discountPrice(discountPrice)
                .discountInfoDTO(discountInfo)
                .discountTag(discountTag)
                .remainDiscountDuration(discountInfo.getDiscountDuration())
                .discountDurationUnit(activatedPlan.getDurationUnit().name())
                .discountInfoDTO(discountInfo)
                .build();
    }
}
