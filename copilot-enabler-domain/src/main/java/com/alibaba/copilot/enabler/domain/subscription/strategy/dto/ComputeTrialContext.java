package com.alibaba.copilot.enabler.domain.subscription.strategy.dto;

import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/10/13
 */
@Data
@AllArgsConstructor
public class ComputeTrialContext implements Serializable {

    /**
     * 应用标识
     */
    private String appCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 待激活的套餐
     */
    private SubscriptionPlan targetPlan;

    /**
     * 历史订单信息 (可传null, 为null时方法体内部查询)
     */
    @Nullable
    private List<SubscriptionOrder> historyOrders;
}
