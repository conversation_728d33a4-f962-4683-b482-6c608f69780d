package com.alibaba.copilot.enabler.domain.subscription.request;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName FeatureUsageRequest
 * <AUTHOR>
 * @Date 2023/9/5 17:00
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class FeatureUsageQuery {

    /**
     * id
     */
    private Long id;


    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 特性ID
     */
    private Long subscriptionFeatureId;

    /**
     * 状态
     */
    private String status;
}

