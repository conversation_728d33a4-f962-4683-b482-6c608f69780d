package com.alibaba.copilot.enabler.domain.marketing.model;

import com.alibaba.copilot.enabler.client.marketing.constant.DiscountRuleEntityType;
import com.alibaba.copilot.enabler.client.marketing.constant.DiscountRuleStatus;
import com.alibaba.copilot.enabler.client.marketing.constant.DiscountType;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 折扣规则
 */
@Data
@Accessors(chain = true)
public class DiscountRule implements Serializable {

    /**
     * ID主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 应用标识
     */
    private AppEnum appCode;

    /**
     * 状态
     */
    private DiscountRuleStatus status;

    /**
     * 折扣类型
     */
    private DiscountType discountType;

    /**
     * 实体类型
     */
    private DiscountRuleEntityType entityType;

    /**
     * 实体ID
     */
    private Long entityId;

    /**
     * 扩展属性
     */
    private DiscountRuleAttributes attributes = new DiscountRuleAttributes(null);
}
