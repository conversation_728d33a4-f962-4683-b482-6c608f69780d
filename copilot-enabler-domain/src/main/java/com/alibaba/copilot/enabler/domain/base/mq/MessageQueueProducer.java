package com.alibaba.copilot.enabler.domain.base.mq;

/**
 * 消息队列生产者
 *
 * <AUTHOR>
 * @date 2024/11/12
 */
public interface MessageQueueProducer {

    /**
     * 发送消息事件
     *
     * @param topic topic name
     * @param tags tags
     * @param event event
     * @param <T> event type
     * @return true/false
     */
    <T> boolean send(String topic, String tags, T event);

    /**
     * 发送延迟消息
     *
     * @param topic topic name
     * @param tags tags
     * @param event event
     * @param delayTimeEnum delay time
     * @param <T> event type
     * @return true/false
     */
    <T> boolean send(String topic, String tags, T event, DelayTimeEnum delayTimeEnum);


}
