package com.alibaba.copilot.enabler.domain.subscription.strategy.dto;

import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 2023/10/13
 */
@Data
@Accessors(chain = true)
public class CreateOrderAndTradeResultDTO {

    /**
     * 新订单
     */
    private SubscriptionOrder newOrder;

    /**
     * 支付流水
     */
    private TradeRecord payRecord;
}
