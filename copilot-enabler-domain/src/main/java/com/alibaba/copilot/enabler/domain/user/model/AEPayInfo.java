package com.alibaba.copilot.enabler.domain.user.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AEPayInfo {

    /**
     * 卡类型
     * （CREDIT、DEBIT）
     */
    private String funding;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 卡token
     */
    private String cardToken;

    /**
     * 卡品牌
     */
    private String cardBrand;

    /**
     * 发卡国/地区
     */
    private String issuingCountry;

    /**
     * 付款方式的国家地区码
     */
    private String paymentMethodRegion;

    /**
     * 卡组交易ID
     */
    private String networkTransactionId;

    /**
     * 更新时间
     */
    private Long updateTime;
}