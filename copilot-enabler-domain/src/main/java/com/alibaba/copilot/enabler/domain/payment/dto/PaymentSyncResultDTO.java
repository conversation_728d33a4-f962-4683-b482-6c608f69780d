package com.alibaba.copilot.enabler.domain.payment.dto;

import com.alibaba.copilot.enabler.client.payment.constant.PaymentStatusEnum;
import com.alibaba.copilot.enabler.client.payment.dto.PaymentResultDTO;
import com.alibaba.global.money.Money;
import lombok.Data;

/**
 * 支付结果（同步）
 */
@Data
public class PaymentSyncResultDTO {

    /**
     * 支付结果码
     */
    private String paymentResultCode;

    /**
     * 支付结果信息
     */
    private String paymentResultMessage;

    /**
     * 支付幂等号（交易流水号id）
     */
    private String paymentRequestId;

    /**
     * 支付幂等号（支付机构生成）
     */
    private String paymentId;

    /**
     * 支付金额
     */
    private Money paymentAmount;

    /**
     * 支付创建时间
     */
    private Long paymentCreateTime;

    /**
     * 支付成功时间
     */
    private Long paymentTime;

    /**
     * 支付状态
     */
    private PaymentStatusEnum paymentStatus;

    /**
     * 支付结果附加信息
     */
    private PaymentResultDTO paymentResultDTO;
}
