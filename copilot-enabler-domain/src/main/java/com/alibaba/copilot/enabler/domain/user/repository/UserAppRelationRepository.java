package com.alibaba.copilot.enabler.domain.user.repository;

import com.alibaba.copilot.enabler.client.user.request.UserAppRelationQuery;
import com.alibaba.copilot.enabler.domain.user.model.UserAppRelation;

import java.util.List;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-08-31
 **/
public interface UserAppRelationRepository {

    UserAppRelation getUserAppRelationById(Long id);

    UserAppRelation getUserAppRelation(Long userId, String appCode, String email);

    List<UserAppRelation> queryUserAppRelationList(UserAppRelationQuery userAppRelationQuery);

    UserAppRelation saveUserAppRelation(UserAppRelation relationDomain);

    Boolean updateUserAppRelation(UserAppRelation userAppRelation);
}
