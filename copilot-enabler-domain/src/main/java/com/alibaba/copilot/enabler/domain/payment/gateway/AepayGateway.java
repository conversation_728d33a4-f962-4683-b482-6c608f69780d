package com.alibaba.copilot.enabler.domain.payment.gateway;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.payment.dto.*;
import com.alibaba.copilot.enabler.client.payment.request.CashierPayRequest;
import com.alibaba.copilot.enabler.domain.payment.dto.PaymentAsyncResultDTO;
import com.alibaba.copilot.enabler.domain.payment.dto.PaymentSyncResultDTO;
import com.alibaba.copilot.enabler.domain.subscription.request.InitiatePaymentRequest;
import com.alibaba.copilot.enabler.domain.subscription.request.InquiryPaymentRequest;

/**
 * Alipay网关接口 (通过AE金融进行代理)
 *
 * <AUTHOR>
 * @version 2023/9/27
 */
public interface AepayGateway {

    /**
     * 创建支付会话
     *
     * @param sessionDTO 支付请求信息
     * @return 支付会话信息
     */
    SingleResult<PaymentSessionDTO> createPaymentSession(CreatePaymentSessionDTO sessionDTO);

    /**
     * 发起支付
     *
     * @param request 支付请求信息
     * @return 支付结果
     */
    SingleResult<PaymentAsyncResultDTO> initiatePay(InitiatePaymentRequest request);

    /**
     * 收银台支付
     * @param request
     * @return
     */
    SingleResult<PaymentAsyncResultDTO> cashierPay(InitiatePaymentRequest request);

    /**
     * 支付结果主动查询
     *
     * @param request 支付结果查询
     * @return 支付结果
     */
    SingleResult<PaymentSyncResultDTO> inquiryPayment(InquiryPaymentRequest request);

    /**
     * 发起退款
     *
     * @param refundDTO 退款请求信息
     * @return 退款结果
     */
    SingleResult<RefundResultDTO> refund(RefundDTO refundDTO);

    /**
     * 退款结果查询
     *
     * @param queryRefundDTO 退款结果查询
     * @return 退款结果
     */
    SingleResult<QueryRefundResultDTO> queryRefundResult(QueryRefundDTO queryRefundDTO);
}
