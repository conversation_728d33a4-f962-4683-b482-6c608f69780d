package com.alibaba.copilot.enabler.domain.base.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.UUID;

/**
 * 支付util
 */
public class PaymentUtils {

    /**
     * 生成支付流水号
     *
     * @param userId 用户id
     * @return 支付流水号
     */
    public static String generateTradeNo(String userId) {
        if (StringUtils.isBlank(userId)) {
            throw new IllegalArgumentException("userId cannot be blank.");
        }

        String date = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
        String random = RandomUtil.randomNumbers(4);
        return date + userId + random;
    }

    /**
     * 生成支付流水号
     *
     * @param userId 用户id
     * @return 支付流水号
     */
    public static String generateTradeNo(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("userId cannot be null.");
        }

        return generateTradeNo(String.valueOf(userId));
    }

    /**
     * @return 生成Alipay的授权请求码
     */
    public static String generateAlipayAuthState() {
        return UUID.randomUUID().toString().replace("-", "").toLowerCase();
    }
}
