package com.alibaba.copilot.enabler.domain.base.utils;

import com.alibaba.normandy.credential.Credential;
import com.alibaba.normandy.credential.CredentialProvider;
import com.alibaba.normandy.credential.ResourceNames;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.params.SetParams;

import javax.annotation.PostConstruct;

@Component
public class RedisUtils {

    @Value("${redis.instance-id}")
    private String REDIS_INSTANCE_ID;

    @Value("${redis.endpoint}")
    private String REDIS_HOST;

    private static CredentialProvider credentialProvider;
    private static JedisPool jedisPool = null;

    @Autowired
    public RedisUtils(CredentialProvider credentialProvider) {
        RedisUtils.credentialProvider = credentialProvider;
    }

    @PostConstruct
    void init() {
        String resourceName = ResourceNames.ofAliyunKvStoreInstanceId(REDIS_INSTANCE_ID);
        Credential credential = credentialProvider.getCredential(resourceName);
        String password = credential.getUsername() + ":" + credential.getPassword();
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(32);
        config.setMaxIdle(4);
        config.setMinIdle(2);
        config.setBlockWhenExhausted(true);
        jedisPool = new JedisPool(config, REDIS_HOST, 6379, 3000, password);
    }

    public static String get(String key) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.get(key);
        }
    }

    public static String set(String key, String value) {
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.set(key, value);
            return value;
        }
    }

    public static String setex(String key, long seconds, String value) {
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.setex(key, seconds, value);
            return value;
        }
    }

    public static boolean delete(String key) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.del(key) > 0;
        }
    }

    public static boolean exist(String key) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.exists(key);
        }
    }

    public static JedisPool getJedisPool() {
        return jedisPool;
    }
}
