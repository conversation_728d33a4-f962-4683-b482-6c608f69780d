package com.alibaba.copilot.enabler.domain.subscription.dto;

import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/10/30
 */
@Data
@Accessors(chain = true)
public class ShopifyDiscountDTO implements Serializable {

    /**
     * 应用标识
     */
    private String appCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 套餐ID
     */
    private Long planId;
    /**
     * 折扣码 (用来作为推广域标识)
     */
    private String shareCode;

    /**
     * 套餐实体 (该值存在时不使用planId)
     */
    private SubscriptionPlan plan;
}
