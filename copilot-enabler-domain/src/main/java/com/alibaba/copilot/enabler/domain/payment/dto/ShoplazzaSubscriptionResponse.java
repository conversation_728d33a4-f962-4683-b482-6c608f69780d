package com.alibaba.copilot.enabler.domain.payment.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 店匠订阅Response
 *
 * <AUTHOR>
 * @date 2024/9/19 4:47 下午
 */
@Data
public class ShoplazzaSubscriptionResponse {

    private String id;

    @JSONField(name = "application_id")
    private String applicationId;

    private String name;

    private String price;

    @JSONField(name = "return_url")
    private String returnUrl;

    @JSONField(name = "confirmation_url")
    private String confirmationUrl;

    private String status;

    private Boolean test;
}
