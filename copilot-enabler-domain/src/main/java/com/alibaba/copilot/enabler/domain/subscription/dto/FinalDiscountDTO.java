package com.alibaba.copilot.enabler.domain.subscription.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 最终折扣信息
 */
@Data
@Builder
public class FinalDiscountDTO {
    /**
     * 是否享受折扣
     */
    Boolean isDiscount;

    /**
     * 折扣价格
     */
    BigDecimal discountPrice;

    /**
     * 折扣码
     */
    String discountTag;

    /**
     * 剩余折扣周期
     */
    Long remainDiscountDuration;

    /**
     * 折扣期单位
     */
    String discountDurationUnit;

    /**
     * 推广域返回折扣信息
     */
    DiscountInfoDTO discountInfoDTO;
}