package com.alibaba.copilot.enabler.domain.subscription.strategy.dto;

import com.alibaba.copilot.enabler.client.subscription.dto.CycleFeeDetail;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/10/26
 */
@Data
@Accessors(chain = true)
public class ComputeCycleFeeResultDTO implements Serializable {

    /**
     * 套餐周期费用
     */
    private List<CycleFeeDetail> cycleFeeDetails;

    /**
     * 最终需要支付的费用
     */
    private BigDecimal finalPayAmount;
}
