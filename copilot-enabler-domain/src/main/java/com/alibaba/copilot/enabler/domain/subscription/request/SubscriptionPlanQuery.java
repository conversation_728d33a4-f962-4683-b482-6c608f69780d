package com.alibaba.copilot.enabler.domain.subscription.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 查询计划
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class SubscriptionPlanQuery {

    /**
     * APP CODE
     */
    private String appCode;

    /**
     * plan表主键
     */
    private Long planId;

    /**
     * plan名称
     */
    private String planName;

    /**
     * 关联数据
     */
    private List<SubscriptionPlanQueryInfo> queryInfos = new ArrayList<>();
}
