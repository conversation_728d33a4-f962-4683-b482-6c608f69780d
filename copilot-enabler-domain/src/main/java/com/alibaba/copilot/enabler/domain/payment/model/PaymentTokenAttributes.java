package com.alibaba.copilot.enabler.domain.payment.model;

import com.alibaba.copilot.boot.basic.data.Attributes;
import com.alibaba.copilot.enabler.domain.user.model.StripeInfo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 2024/1/11
 */
public class PaymentTokenAttributes extends Attributes {

    public PaymentTokenAttributes(String json) {
        super(json);
    }

    /**
     *
     */
    private static final String STRIPE_INFO = "stripeInfo";

    public StripeInfo getStripeInfoNotNull() {
        return Optional.ofNullable(getAttributes())
                .map(map -> map.get(STRIPE_INFO))
                .map(JSONObject::toJSONString)
                .map(json -> {
                    Type mapActualType = new TypeToken<StripeInfo>() {
                    }.getType();
                    return (StripeInfo) JSON.parseObject(json, mapActualType);
                })
                .orElseGet(StripeInfo::new);
    }

    public void setStripeInfo(StripeInfo stripeInfo) {
        getAttributes().put(STRIPE_INFO, stripeInfo);
    }
}
