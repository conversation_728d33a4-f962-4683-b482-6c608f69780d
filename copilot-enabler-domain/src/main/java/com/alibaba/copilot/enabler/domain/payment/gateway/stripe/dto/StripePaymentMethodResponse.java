package com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto;

import lombok.Data;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/07
 */
@Data
public class StripePaymentMethodResponse {

    /**
     * customer id
     */
    private String customerId;

    /**
     * payment method id
     */
    private String id;

    /**
     * type
     */
    private String type;

    /**
     * funding
     */
    private String funding;

    /**
     * brand
     */
    private String brand;

    /**
     * last 4
     */
    private String last4;

    /**
     * allow_redisplay
     */
    private String allowRedisplay;


    public Boolean isAllowRedisplay() {
        return "always".equals(allowRedisplay);
    }
}