package com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSONPath;
import org.apache.commons.collections4.MapUtils;

import java.util.Objects;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/13
 */
public class StripeEvent {

    private JSONObject value;

    public static StripeEvent of(JSONObject value) {
        StripeEvent se = new StripeEvent();
        se.value = value;
        return se;
    }

//  {
//    "id": "evt_1Ep24XHssDVaQm2PpwS19Yt0",
//        "object": "event",
//        "api_version": "2019-03-14",
//        "created": 1561420781,
//        "data": {
//        "object": {
//            "id": "cs_test_MlZAaTXUMHjWZ7DcXjusJnDU4MxPalbtL5eYrmS2GKxqscDtpJq8QM0k",
//                    "object": "checkout.session",
//                    "billing_address_collection": null,
//                    "client_reference_id": null,
//                    "customer": "",
//                    "customer_email": null,
//                    "display_items": [],
//            "mode": "setup",
//                    "setup_intent": "seti_1EzVO3HssDVaQm2PJjXHmLlM",
//                    "submit_type": null,
//                    "subscription": null,
//                    "success_url": "https://example.com/success"
//        }
//    },
//        "livemode": false,
//            "pending_webhooks": 1,
//            "request": {
//        "id": null,
//                "idempotency_key": null
//    },
//        "type": "checkout.session.completed"
//    }

    public String fetchSetupIntentId() {
        JSONObject dataObject = parseDataObject();
        return MapUtils.getString(dataObject, "setup_intent");
    }

    private JSONObject parseDataObject() {
        if (Objects.nonNull(value)) {
            JSONObject data = value.getJSONObject("data");
            if (Objects.nonNull(data)) {
                return data.getJSONObject("object");
            }
        }
        return null;
    }

    public String fetchEventId() {
        return MapUtils.getString(value, "id");
    }

    public Long fetchEventCreatedTimeMillis() {
        return MapUtils.getLong(value, "created", 0L) * 1000;
    }


    public String fetchPaymentIntentId() {
        JSONObject dataObject = parseDataObject();
        return MapUtils.getString(dataObject, "id");
    }

    public String fetchDisputeStatus(){
        JSONObject dataObject = parseDataObject();
        return MapUtils.getString(dataObject, "status");
    }

    public String fetchPaymentMethodId() {
        JSONObject dataObject = parseDataObject();
        return MapUtils.getString(dataObject, "payment_method");
    }

    public String fetch(String key) {
        JSONObject dataObject = parseDataObject();
        return MapUtils.getString(dataObject, key);
    }

    public String fetchJsonPath(String jsonPath) {
        JSONObject dataObject = parseDataObject();
        Object eval = JSONPath.eval(dataObject, jsonPath);
        if (eval == null) {
            return null;
        }
        return String.valueOf(eval);
    }
}