package com.alibaba.copilot.enabler.domain.payment.repository;

import com.alibaba.copilot.enabler.client.base.dto.PageDTO;
import com.alibaba.copilot.enabler.client.payment.request.TradeRecordPageQuery;
import com.alibaba.copilot.enabler.client.payment.request.TradeRecordsQuery;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * payment repository
 */
public interface TradeRecordRepository {

    /**
     * 创建或更新交易流水
     *
     * @return 支付流水号
     */
    String createOrUpdateTradeRecord(TradeRecord tradeRecord);

    /**
     * 查询交易流水
     */
    List<TradeRecord> queryTradeRecords(TradeRecordsQuery query);

    /**
     * 是否有待退款的交易流水
     */
    boolean hasRefundRecordWithTodoStatus(String appCode, Long userId);

    /**
     * 查询交易流水分页信息
     */
    PageDTO<TradeRecord> queryTradeRecordsPage(TradeRecordPageQuery query);

    /**
     * 查询交易流水分页信息
     */
    PageDTO<TradeRecord> queryTradeRecordsPageWithStripe(TradeRecordPageQuery query);

    /**
     * 扫描交易流水
     */
    List<TradeRecord> scanTradeRecords(TradeRecordsQuery query);

    /**
     * 根据指定流水号查询流水数据
     */
    TradeRecord queryByTradeNo(String tradeNo);

    /**
     * 根据外部流水号查询流水数据
     */
    TradeRecord queryByOutTradeNoAndTradeDirection(String outTradeNo, TradeDirection tradeDirection);

    /**
     * 根据主键 id 查询流水数据
     *
     * @param id
     * @return
     */
    TradeRecord queryById(Long id);

    /**
     * 根据指定订单号查询流水数据
     */
    TradeRecord queryByOrderId(Long orderId, TradeDirection tradeDirection);

    /**
     * 判断用户是否支付过
     *
     * @param appCode 应用标识
     * @param userId  用户ID
     * @return 查询结果
     */
    Boolean isPaid(String appCode, Long userId);

    /**
     * 根据订单Id查询支付成功的流水
     *
     * @param orderId 订单ID
     * @return 支付流水
     */
    TradeRecord queryPaySuccessRecordByOrderId(Long orderId);

    /**
     * 查询对账单的流水
     */
    List<TradeRecord> queryListForBill(String appCode, Date startTime, Date endTime);

    /**
     * 根据订单Id批量查询流水
     */
    List<TradeRecord> queryRefundRecordsForBill(Collection<Long> orderIds);

    /**
     * 查询支付成功的流水
     */
    List<TradeRecord> queryPaySuccessRecordsForBill(Collection<Long> orderIds);
}
