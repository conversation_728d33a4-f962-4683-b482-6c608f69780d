package com.alibaba.copilot.enabler.domain.subscription.event;

import com.alibaba.copilot.enabler.client.base.event.DomainEventJsonProducer;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionCompleteReason;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPlanChangedType;
import com.alibaba.copilot.enabler.client.subscription.event.SubscriptionPlanChangedEvent;
import com.alibaba.copilot.enabler.domain.payment.model.TradeRecord;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * 套餐变更事件生产器, 为了统一收口套餐变更事件发送逻辑
 *
 * <AUTHOR>
 * @version 2023/10/22
 */
@Slf4j
@Component
public class SubscriptionPlanChangedEventProducer {

    @Resource
    private DomainEventJsonProducer domainEventJsonProducer;

    @Resource
    private TradeRecordRepository tradeRecordRepository;

    @Resource
    private SubscriptionPlanRepository subscriptionPlanRepository;

    /**
     * 发送试用期退订事件
     *
     * @param currentOrder 当前订单 (取消的订单)
     */
    public void sendCancelWhenTrialEvent(SubscriptionOrder currentOrder) {
        log.info("sendCancelEvent, currentOrder={}", JSON.toJSONString(currentOrder));

        Long planId = currentOrder.getSubscriptionPlanId();
        SubscriptionPlan plan = subscriptionPlanRepository.queryByPlanId(planId, false);

        SubscriptionPlanChangedEvent event = new SubscriptionPlanChangedEvent()
                .setAppCode(currentOrder.getAppCode())
                .setUserId(currentOrder.getUserId())
                .setCurrentOrderId(currentOrder.getId())
                .setChangedType(SubscriptionPlanChangedType.CANCEL_WHEN_TRIAL)
                .setLastPlanId(planId)
                .setLastPlanDurationUnit(plan.getDurationUnit());

        injectPlanStartAndEndTime(event, currentOrder, null);

        log.info("sendCancelEvent, event={}", JSON.toJSONString(event));
        domainEventJsonProducer.publish(event);
    }

    /**
     * 发送订单完结事件
     *
     * @param currentOrder 当前订单 (取消的订单)
     */
    public void sendCompleteEvent(SubscriptionOrder currentOrder, SubscriptionCompleteReason reason) {
        log.info("sendCompleteEvent, currentOrder={}", JSON.toJSONString(currentOrder));

        TradeRecord tradeRecord = tradeRecordRepository.queryPaySuccessRecordByOrderId(currentOrder.getId());
        String tradeNo = Optional.ofNullable(tradeRecord).map(TradeRecord::getTradeNo).orElse(null);

        Long planId = currentOrder.getSubscriptionPlanId();
        SubscriptionPlan plan = subscriptionPlanRepository.queryByPlanId(planId, false);

        String reasonStr = Optional.ofNullable(reason).map(Enum::name).orElse(null);

        SubscriptionPlanChangedEvent event = new SubscriptionPlanChangedEvent()
                .setAppCode(currentOrder.getAppCode())
                .setUserId(currentOrder.getUserId())
                .setCurrentOrderId(currentOrder.getId())
                .setCurrentTradeNo(tradeNo)
                .setChangedType(SubscriptionPlanChangedType.COMPLETE)
                .setSubscriptionCompleteReason(reasonStr)
                .setLastPlanId(planId)
                .setLastPlanDurationUnit(plan.getDurationUnit());

        injectPlanStartAndEndTime(event, currentOrder, null);

        log.info("sendCompleteEvent, event={}", JSON.toJSONString(event));
        domainEventJsonProducer.publish(event);
    }

    /**
     * 发送新订或切换事件
     *
     * @param userId   用户ID
     * @param oldPlan  老套餐 (可能为空, 如新订场景)
     * @param oldOrder 老订单 (可能为空, 如新订场景)
     * @param newPlan  新套餐 (不为空)
     * @param newOrder 新订单 (不为空)
     */
    public void sendNewOrSwitchEvent(@Nonnull Long userId,
                                     @Nullable SubscriptionPlan oldPlan,
                                     @Nullable SubscriptionOrder oldOrder,
                                     @Nonnull SubscriptionPlan newPlan,
                                     @Nonnull SubscriptionOrder newOrder,
                                     String newTradeNo) {
        log.info("sendNewOrSwitchEvent, oldPlan={}, newPlan={}", JSON.toJSONString(oldPlan), JSON.toJSONString(newPlan));

        SubscriptionPlanChangedType changedType = oldPlan == null
                ? SubscriptionPlanChangedType.NEW : SubscriptionPlanChangedType.SWITCH;

        SubscriptionPlanChangedEvent event = new SubscriptionPlanChangedEvent()
                .setAppCode(newPlan.getAppCode())
                .setUserId(userId)
                .setCurrentOrderId(newOrder.getId())
                .setCurrentTradeNo(newTradeNo)
                .setChangedType(changedType)
                .setLastPlanId(oldPlan == null ? null : oldPlan.getId())
                .setLastPlanDurationUnit(oldPlan == null ? null : oldPlan.getDurationUnit())
                .setCurrentPlanId(newPlan.getId())
                .setCurrentPlanDurationUnit(newPlan.getDurationUnit());

        injectPlanStartAndEndTime(event, oldOrder, newOrder);

        log.info("sendNewOrSwitchEvent, event={}", JSON.toJSONString(event));
        domainEventJsonProducer.publish(event);
    }

    /**
     * 向事件中注入套餐开始和结束时间的信息
     */
    private static void injectPlanStartAndEndTime(@Nonnull SubscriptionPlanChangedEvent event,
                                                  @Nullable SubscriptionOrder oldOrder,
                                                  @Nullable SubscriptionOrder newOrder) {
        if (oldOrder != null) {
            event.setLastPlanStartTime(oldOrder.getPerformStartTime());
            event.setLastPlanEndTime(oldOrder.getPerformEndTime());
        }
        if (newOrder != null) {
            event.setCurrentPlanStartTime(newOrder.getPerformStartTime());
            event.setCurrentPlanEndTime(newOrder.getPerformEndTime());
        }
    }
}
