package com.alibaba.copilot.enabler.domain.payment.factory;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentChannel;
import com.alibaba.copilot.enabler.client.payment.dto.*;
import com.alibaba.copilot.enabler.client.payment.request.CashierPayRequest;
import com.alibaba.copilot.enabler.client.payment.request.PreparePayRequest;
import com.alibaba.copilot.enabler.domain.payment.dto.PaymentAsyncResultDTO;
import com.alibaba.copilot.enabler.domain.payment.dto.PaymentSyncResultDTO;
import com.alibaba.copilot.enabler.domain.subscription.request.InitiatePaymentRequest;
import com.alibaba.copilot.enabler.domain.subscription.request.InquiryPaymentRequest;

/**
 * 第三方支付渠道基类
 */
public abstract class IPaymentChannelStrategy {

    /**
     * 获取渠道类型
     */
    public abstract PaymentChannel getPaymentChannel();

    /**
     * 支付前置准备
     * （用于拉起收银台前置链路的请求，如用于渲染确认订单页时使用的支付数据）
     */
    public abstract SingleResult<PreparePayResultDTO> preparePay(PreparePayRequest request);

    /**
     * 创建支付会话
     */
    public abstract SingleResult<PaymentSessionDTO> createPaymentSession(CreatePaymentSessionDTO sessionDTO);

    /**
     * 发起支付
     */
    public abstract SingleResult<PaymentAsyncResultDTO> initiatePay(InitiatePaymentRequest request);

    /**
     * 收银台支付
     * @param request
     * @return
     */
    public abstract SingleResult<PaymentAsyncResultDTO> cashierPay(InitiatePaymentRequest request);

    /**
     * 询问支付结果
     */
    public abstract SingleResult<PaymentSyncResultDTO> inquiryPayment(InquiryPaymentRequest request);

    /**
     * 发起退款
     */
    public abstract SingleResult<RefundResultDTO> refund(RefundDTO refundDTO);

    /**
     * 询问退款结果
     */
    public abstract SingleResult<QueryRefundResultDTO> queryRefundResult(QueryRefundDTO queryRefundDTO);
}