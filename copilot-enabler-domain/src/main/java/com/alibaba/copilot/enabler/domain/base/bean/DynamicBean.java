package com.alibaba.copilot.enabler.domain.base.bean;

import com.alibaba.copilot.enabler.domain.base.utils.SpringContextUtils;

/**
 * 动态Bean (用于非Component方式的注入)
 *
 * <AUTHOR>
 * @version 2023/10/19
 */
public class DynamicBean<T> {

    private final Class<T> beanType;

    private T beanInstance;

    public static <T> DynamicBean<T> of(Class<T> beanType) {
        return new DynamicBean<>(beanType);
    }

    private DynamicBean(Class<T> beanType) {
        this.beanType = beanType;
    }

    public T get() {
        if (beanInstance == null) {
            beanInstance = SpringContextUtils.getBean(beanType);
        }
        return beanInstance;
    }
}
