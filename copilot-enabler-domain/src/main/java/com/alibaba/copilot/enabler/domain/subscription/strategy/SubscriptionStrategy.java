package com.alibaba.copilot.enabler.domain.subscription.strategy;

import com.alibaba.copilot.enabler.client.email.annotation.EmailTemplate;
import com.alibaba.copilot.enabler.client.subscription.dto.NeedPayToSubscribePlanDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.TrialDurationDTO;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.response.EmailResponse;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.*;

import javax.annotation.Nonnull;

/**
 * 订阅策略接口 (主要针对不同AppCode的订阅策略做逻辑隔离)
 *
 * <AUTHOR>
 * @version 2023/10/12
 */
public interface SubscriptionStrategy {

    /**
     * 对试用期订单进行退款
     *
     * @param order 待退款订单
     */
    void refundForTrialOrder(SubscriptionOrder order);

    /**
     * 计算试用期信息
     *
     * @param context 计算上下文逻辑
     * @return 计算结果
     */
    TrialDurationDTO computeTrialDuration(ComputeTrialContext context);

    /**
     * 创建订单和流水
     *
     * @param context 订阅请求体
     * @return 处理结果
     */
    CreateOrderAndTradeResultDTO createOrderAndTrade(SubscribeContext context);

    /**
     * 计算订单结束时间
     *
     * @param context 订阅上下文
     * @return 处理结果
     */
    ComputeOrderEndTimeResultDTO computeOrderEndTime(SubscribeContext context);

    /**
     * 判断订阅指定套餐是否需要付款
     *
     * @param dto 参数信息
     * @return true: 需要付款; false: 无需付款;
     */
    Boolean needPayToSubscribePlan(NeedPayToSubscribePlanDTO dto);

    /**
     * 当套餐变更时, 处理老订单的退款逻辑
     *
     * @param oldEffectOrder 变更前的生效订单
     */
    void handleRefundLogicWhenOldOrderCompleted(@Nonnull SubscriptionOrder oldEffectOrder);

    /**
     * 发送邮件
     *
     * @param userId       用户ID
     * @param emailInfoObj 邮件对象 (需要添加{@link EmailTemplate}注解)
     * @return
     */
    EmailResponse sendEmail(Long userId, Object emailInfoObj);

    /**
     * 根据计算价格的参数, 构建计算价格所需要用到的上下文信息
     *
     * @param dto 参数信息
     * @return 计算价格的上下文信息
     */
    ComputeNewPlanPriceContext buildComputeNewPlanPriceContext(ComputeNewPlanPriceDTO dto);

    /**
     * 计算新套餐的价格
     *
     * @param context 计算上下文
     * @return 计算结果
     */
    ComputeNewPlanPriceResultDTO computeNewPlanPrice(ComputeNewPlanPriceContext context);

    /**
     * 计算账单的周期费用
     *
     * @param context 上下文信息
     * @return 周期费用列表
     */
    ComputeCycleFeeResultDTO computeCycleFee(ComputeNewPlanPriceContext context);
}
