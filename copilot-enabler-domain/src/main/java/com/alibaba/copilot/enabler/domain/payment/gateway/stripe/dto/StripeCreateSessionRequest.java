package com.alibaba.copilot.enabler.domain.payment.gateway.stripe.dto;

import com.alibaba.copilot.enabler.client.payment.dto.GoodsDTO;
import com.alibaba.copilot.enabler.domain.payment.constant.PaymentConst;
import com.alibaba.copilot.enabler.domain.payment.constant.StripeCheckoutSessionMode;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/06
 */
@Data
public class StripeCreateSessionRequest {

    /**
     * Stripe Checkout Session Mode
     */
    private StripeCheckoutSessionMode mode;

    /**
     * 币种，默认美元
     */
    private String currency = PaymentConst.CURRENCY_USD;
    /**
     * 重定向 Url
     */
    private String returnUrl;

    /**
     * metadata
     */
    private StripeEventMetadata metadata;

    /**
     * Goods
     */
    private List<GoodsDTO> goodsList = new ArrayList<>();

    /**
     * customer id
     */
    private String stripeCustomerId;

    /**
     * expire at (s) 多长时间后过期
     */
    private Long timeout;

    /**
     * clientReferenceId
     */
    private String clientReferenceId;

}