package com.alibaba.copilot.enabler.domain.payment.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.copilot.boot.basic.enums.IEnum;
import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.tools.lock.Lock;
import com.alibaba.copilot.boot.tools.lock.RedisLock;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.base.event.DomainEventJsonProducer;
import com.alibaba.copilot.enabler.client.email.dto.*;
import com.alibaba.copilot.enabler.client.marketing.dto.FirstMonthDiscountDTO;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.constant.StatusFlowReasonEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageDirectionEnum;
import com.alibaba.copilot.enabler.client.payment.constant.message.MessageStatusEnum;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionCompleteReason;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import com.alibaba.copilot.enabler.client.subscription.dto.CreateFreeOrderDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.CreateTradeRecordDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.TrialDurationDTO;
import com.alibaba.copilot.enabler.client.subscription.event.SubscriptionAppliedEvent;
import com.alibaba.copilot.enabler.client.subscription.event.SubscriptionCanceledEvent;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.client.user.dto.UserAppRelationDTO;
import com.alibaba.copilot.enabler.domain.base.email.EdmService;
import com.alibaba.copilot.enabler.domain.base.utils.PaymentUtils;
import com.alibaba.copilot.enabler.domain.base.utils.RedisUtils;
import com.alibaba.copilot.enabler.domain.base.utils.TimeUtils;
import com.alibaba.copilot.enabler.domain.marketing.model.DiscountRecord;
import com.alibaba.copilot.enabler.domain.marketing.repository.DiscountRecordRepository;
import com.alibaba.copilot.enabler.domain.payment.constant.AEPaymentConstants;
import com.alibaba.copilot.enabler.domain.payment.constant.PaymentConst;
import com.alibaba.copilot.enabler.domain.payment.factory.FreeOrderBuilder;
import com.alibaba.copilot.enabler.domain.payment.factory.FreeTradeRecordBuilder;
import com.alibaba.copilot.enabler.domain.payment.factory.IPaymentChannelStrategy;
import com.alibaba.copilot.enabler.domain.payment.factory.PaymentChannelFactory;
import com.alibaba.copilot.enabler.domain.payment.gateway.geoip.GeoIpGateway;
import com.alibaba.copilot.enabler.domain.payment.gateway.geoip.dto.GetAddressInfoByIpResponse;
import com.alibaba.copilot.enabler.domain.payment.model.*;
import com.alibaba.copilot.enabler.domain.payment.repository.MessageRecordRepository;
import com.alibaba.copilot.enabler.domain.payment.repository.PaymentTokenRepository;
import com.alibaba.copilot.enabler.domain.payment.repository.TradeRecordRepository;
import com.alibaba.copilot.enabler.domain.subscription.dto.FinalDiscountDTO;
import com.alibaba.copilot.enabler.domain.subscription.event.SubscriptionPlanChangedEventProducer;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrder;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionOrderAttributes;
import com.alibaba.copilot.enabler.domain.subscription.model.SubscriptionPlan;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionOrderRepository;
import com.alibaba.copilot.enabler.domain.subscription.repository.SubscriptionPlanRepository;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionPlanQuery;
import com.alibaba.copilot.enabler.domain.subscription.request.SubscriptionPlanQueryInfo;
import com.alibaba.copilot.enabler.domain.subscription.service.DiscountService;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategy;
import com.alibaba.copilot.enabler.domain.subscription.strategy.SubscriptionStrategyFactory;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeNewPlanPriceContext;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.ComputeOrderEndTimeResultDTO;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.CreateOrderAndTradeResultDTO;
import com.alibaba.copilot.enabler.domain.subscription.strategy.dto.SubscribeContext;
import com.alibaba.copilot.enabler.domain.user.model.User;
import com.alibaba.copilot.enabler.domain.user.model.UserAppRelation;
import com.alibaba.copilot.enabler.domain.user.model.UserAttributes;
import com.alibaba.copilot.enabler.domain.user.repository.UserAppRelationRepository;
import com.alibaba.copilot.enabler.domain.user.repository.UserRepository;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 订单领域服务
 */
@Service
@Slf4j
public class OrderDomainService {

    public static final Long FIRST_SUBSCRIBE_EMAIL_ID = 323L;
    @Autowired
    private TradeRecordRepository tradeRecordRepository;
    @Autowired
    private SubscriptionOrderRepository orderRepository;
    @Autowired
    private DomainEventJsonProducer eventPublisher;
    @Autowired
    private MessageRecordRepository messageRecordRepository;
    @Autowired
    private SubscriptionPlanRepository subscriptionPlanRepository;
    @Autowired
    private DiscountService discountService;
    @Autowired
    private SubscriptionPlanChangedEventProducer subscriptionPlanChangedEventProducer;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private UserAppRelationRepository userAppRelationRepository;
    @Autowired
    private PaymentTokenRepository paymentTokenRepository;
    @Autowired
    private DiscountRecordRepository discountRecordRepository;
    @Autowired
    private EdmService edmService;
    @Resource
    private GeoIpGateway geoIpGateway;


    /**
     * 创建准备支付的订单
     * （用户主动付款）
     *
     * @return 新付款流水号
     */
    @Monitor(name = "创建准备支付的订单（用户主动付款）", level = Monitor.Level.P1, layer = Monitor.Layer.SERVICE)
    @Transactional(rollbackFor = Exception.class)
    public CreateOrderAndTradeResultDTO createPreparePayOrder(SubscribeContext context) {
        // paymentMethod here
        PaymentMethodEnum paymentMethod = context.getPaymentMethod();
        UserAppRelationDTO userAppRelationDTO = context.getUserAppRelationDTO();
        Long userId = userAppRelationDTO.getUserId();
        SubscriptionPlan activatedPlan = context.getNewPlan();
        SubscriptionOrder oldEffcOrder = context.getOldOrder();

        // 1.计算新订单待支付的金额
        BigDecimal payAmount = getForwardPriceForInitiatedPayment(context);
        boolean needPay = payAmount.compareTo(BigDecimal.ZERO) > 0;

        // 2.不需要付款的完结掉已生效的订单
        if (!needPay) {
            completedInEffectOrder(true, oldEffcOrder);
        }

        // 3.生成新的订单
        SubscriptionOrder newOrder = createNewOrder(context, needPay);
        log.info("createPreparePayOrder newOrderId:{},needPay:{}", newOrder.getId(), needPay);

        // 4.生成付款交易流水
        // ip 解析
        IpAddressInfo ipAddressInfo = null;
        if (StringUtils.isNotBlank(context.getUserClientIp())) {
            GetAddressInfoByIpResponse addressInfoByIp = geoIpGateway.getAddressInfoByIp(context.getUserClientIp());
            ipAddressInfo = new IpAddressInfo();
            BeanUtils.copyProperties(addressInfoByIp, ipAddressInfo);
        }
        TradeRecord payRecord = needPay ? createPayRecord(activatedPlan, userId, payAmount, newOrder.getId(),
                PaymentTypeEnum.INITIATED_PAYMENT, paymentMethod, context.getFirstMonthDiscountDTO(), ipAddressInfo) : null;

        String tradeNo = Optional.ofNullable(payRecord).map(TradeRecord::getTradeNo).orElse(null);

        // 5. 发送套餐变化事件 (仅针对套餐立即生效的场景)
        if (newOrder.getStatus() == SubscriptionOrderStatus.IN_EFFECT) {
            subscriptionPlanChangedEventProducer.sendNewOrSwitchEvent(
                    userId, context.getOldPlan(), oldEffcOrder, activatedPlan, newOrder, tradeNo);
        }

        CreateOrderAndTradeResultDTO result = new CreateOrderAndTradeResultDTO()
                .setNewOrder(newOrder)
                .setPayRecord(payRecord);
        log.info("createPreparePayOrder invoke finished, result={}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 创建准备支付的订单
     * （系统自动扣款）
     *
     * @return 新付款流水号
     */
    @Monitor(name = "创建准备支付的订单（系统自动扣款）", level = Monitor.Level.P1, layer = Monitor.Layer.SERVICE)
    @Transactional(rollbackFor = Exception.class)
    public TradeRecord createPreparePayOrder(SubscriptionOrder oldOrder, String appCode, Long userId) {
        // 1.查询订阅计划 (优先取nextPlanId, 取不到再取当前的planId)
        Long nextPlanId = Optional.ofNullable(oldOrder.getNextPlanId())
                .orElse(oldOrder.getSubscriptionPlanId());
        SubscriptionPlanQuery effcPlanQuery = SubscriptionPlanQuery.builder()
                .appCode(oldOrder.getAppCode())
                .planId(nextPlanId)
                .build();
        SubscriptionPlan activatedPlan = querySubscriptionPlan(effcPlanQuery);
        if (activatedPlan == null) {
            return null;
        }

        Date endTime = TimeUtils.getEndTime(oldOrder.getNextRenewalTime(),
                activatedPlan.getDuration(), activatedPlan.getDurationUnit().name());
        String discountTag = oldOrder.getSubscriptionDiscountTag();

        // 2.生成新的订单
        Long newSubscriptionOrderId = createNewSubscriptionOrder(oldOrder, activatedPlan, appCode, endTime);

        // 3.计算优惠后的实付金额
        BigDecimal payAmount = calculateDiscount(activatedPlan, appCode, userId, discountTag, true);

        // 4.生成付款交易流水
        // 查询之前记录的 ip 信息
        TradeRecord oldTradeRecord = tradeRecordRepository.queryPaySuccessRecordByOrderId(oldOrder.getId());
        IpAddressInfo ipAddressInfo = oldTradeRecord.getAttributes().getIpAddressInfoNotNull();

        PaymentMethodEnum paymentMethodOfLastOrder = queryPaymentMethodOfLastOrder(oldOrder);
        TradeRecord payRecord = createPayRecord(activatedPlan, userId, payAmount, newSubscriptionOrderId,
                PaymentTypeEnum.PERIODIC_DEDUCT, paymentMethodOfLastOrder, null, ipAddressInfo);

        // 5.更改旧订单的状态，防止再起被扫描到
        oldOrder.setHadNextRenew(true);
        orderRepository.saveSubscriptionOrder(oldOrder);
        return payRecord;
    }

    /**
     * 查询上一笔订单的支付方式
     */
    private PaymentMethodEnum queryPaymentMethodOfLastOrder(SubscriptionOrder oldOrder) {
        TradeRecord lastPayRecord = tradeRecordRepository.queryByOrderId(oldOrder.getId(), TradeDirection.FORWARD);
        return Optional.ofNullable(lastPayRecord)
                .map(TradeRecord::getPaymentMethod)
                .map(PaymentMethodEnum::ofName)
                .orElse(null);
    }

    /**
     * 取消待支付的订单
     * （用户主动触发）
     */
    @Monitor(name = "取消待支付的订单（用户主动触发）", level = Monitor.Level.P1, layer = Monitor.Layer.SERVICE)
    public void cancelPendingPaymentOrder(SubscriptionOrder order, StatusFlowReasonEnum reasonEnum) {
        if (order == null) {
            return;
        }

        // 1.校验订单
        if (order.getStatus() != SubscriptionOrderStatus.PENDING_PAYMENT) {
            return;
        }

        // 2.更新订单
        order.setStatus(SubscriptionOrderStatus.PAY_CANCELLED);
        order.setAutoRenew(false);
        order.setPerformEndTime(DateUtil.date());
        order.getAttributes().addStatusFlowReason(reasonEnum.name());
        orderRepository.saveSubscriptionOrder(order);

        // 3.更新交易流水
        TradeRecord payRecord = tradeRecordRepository.queryByOrderId(order.getId(), TradeDirection.FORWARD);
        if (payRecord != null) {
            payRecord.setStatus(TradeRecordStatus.CANCELLED);
            payRecord.getAttributes().setStatusFlowReason(reasonEnum.name());
            tradeRecordRepository.createOrUpdateTradeRecord(payRecord);
        }
    }

    /**
     * 取消待支付的订单
     * （系统自动触发）
     */
    @Monitor(name = "取消待支付的订单（系统自动触发）", level = Monitor.Level.P1, layer = Monitor.Layer.SERVICE)
    public void cancelPendingPaymentOrder(TradeRecord tradeRecord, MessageInfo messageInfo, PaymentInfo paymentInfo,
                                          StatusFlowReasonEnum reasonEnum) {
        // 1.保存消息记录
        saveMessageRecord(messageInfo, tradeRecord.getAppCode(), tradeRecord.getUserId());

        // 2.更新订单状态
        SubscriptionOrder order = querySubscriptionOrder(tradeRecord, SubscriptionOrderStatus.PENDING_PAYMENT);
        if (order == null) {
            return;
        }

        order.setStatus(SubscriptionOrderStatus.PAY_CANCELLED);
        order.setAutoRenew(false);
        order.setPerformEndTime(DateUtil.date());
        order.getAttributes().addStatusFlowReason(reasonEnum.name());
        orderRepository.saveSubscriptionOrder(order);

        // 3.更新交易流水状态
        TradeRecordStatus tradeRecordStatus =
                (reasonEnum == StatusFlowReasonEnum.ORDER_OVERDUE_4_PAY)
                        ? TradeRecordStatus.CANCELLED : TradeRecordStatus.FAIL;
        tradeRecord.setStatus(tradeRecordStatus);
        tradeRecord.getAttributes().setStatusFlowReason(reasonEnum.name());

        // 4.支付相关信息更新
        Optional.ofNullable(paymentInfo)
                .ifPresent(info -> {
                    Optional.ofNullable(info.getOutTradeNo()).ifPresent(tradeRecord::setOutTradeNo);
                    Optional.ofNullable(info.getTradeTime()).ifPresent(e -> tradeRecord.setTradeTime(new Date(e)));
                });

        tradeRecordRepository.createOrUpdateTradeRecord(tradeRecord);
    }

    /**
     * 处理退款成功
     * （系统自动触发：监听 退款成功消息、拒付消息 或 主动查询退款成功 后执行的逻辑）
     */
    @Monitor(name = "处理退款成功（系统自动触发）", level = Monitor.Level.P1, layer = Monitor.Layer.SERVICE)
    public void refundSuccess(TradeRecord payRecord, MessageInfo messageInfo, PaymentInfo paymentInfo,
                              StatusFlowReasonEnum reasonEnum) {

        // 1.保存消息记录
        saveMessageRecord(messageInfo, payRecord.getAppCode(), payRecord.getUserId());

        // 2.更新订单状态
        SubscriptionOrder order = orderRepository.getByOrderId(payRecord.getSubscriptionOrderId());
        if (order == null) {
            return;
        }

        if (order.getStatus() == SubscriptionOrderStatus.IN_EFFECT) {
            order.setStatus(SubscriptionOrderStatus.UNSUBSCRIBE);
            order.setPerformEndTime(DateUtil.date());
            order.getAttributes().addStatusFlowReason(reasonEnum.name());
        }
        order.setAutoRenew(false);
        orderRepository.saveSubscriptionOrder(order);

        // 3.更新交易流水
        if (payRecord.getStatus() == TradeRecordStatus.TODO) {
            payRecord.setStatus(TradeRecordStatus.SUCC);
            payRecord.setTradeDirection(TradeDirection.REFUND);
            payRecord.setOutTradeNo(payRecord.getOutTradeNo());
            payRecord.getAttributes().setStatusFlowReason(reasonEnum.name());
            Optional.ofNullable(paymentInfo)
                    .ifPresent(info -> {
                        payRecord.setTradeTime(
                                Optional.ofNullable(paymentInfo.getTradeTime())
                                        .map(Date::new)
                                        .orElse(DateUtil.date()));
                    });
            tradeRecordRepository.createOrUpdateTradeRecord(payRecord);
        }

        // 4.发送取消订阅事件
        sendCancelOrderEvent(order);
    }

    /**
     * 处理支付成功
     * （用户主动支付或系统定期代扣后执行逻辑）
     */
    @Monitor(name = "处理支付成功", level = Monitor.Level.P1, layer = Monitor.Layer.SERVICE)
    public void paymentSuccess(TradeRecord newPayRecord, MessageInfo messageInfo, PaymentInfo paymentInfo) {
        String appCode = newPayRecord.getAppCode();
        Long userId = newPayRecord.getUserId();

        SubscriptionOrder newOrder = orderRepository.getByOrderId(newPayRecord.getSubscriptionOrderId());
        if (newOrder == null) {
            log.warn("paymentSuccess, order is null, payRecord={}", JSON.toJSONString(newPayRecord));
            return;
        }
        SubscriptionOrderStatus status = newOrder.getStatus();
        boolean statusIsInvalid = Arrays.asList(
                SubscriptionOrderStatus.IN_EFFECT,
                SubscriptionOrderStatus.UNSUBSCRIBE
        ).contains(status);
        if (statusIsInvalid) {
            log.warn("paymentSuccess, order status is invalid, order={}", JSON.toJSONString(newOrder));
            return;
        }

        // 1.保存消息记录
        saveMessageRecord(messageInfo, appCode, userId);

        // 2.更新交易流水
        newPayRecord.setStatus(TradeRecordStatus.SUCC);
        Optional.ofNullable(paymentInfo).ifPresent(info -> {
            newPayRecord.setOutTradeNo(paymentInfo.getOutTradeNo());
            newPayRecord.setTradeTime(new Date(paymentInfo.getTradeTime()));
        });
        tradeRecordRepository.createOrUpdateTradeRecord(newPayRecord);

        // 3.完结掉正在生效的订单(人工支付的要发起退款)
        SubscriptionOrder oldEffectOrder = orderRepository.queryEffectOrder(appCode, userId);
        // 支付宝那边扣款消息通知和主动查询支付宝消息通知重复执行本逻辑时，避免下面的逻辑重复执行
        if (oldEffectOrder != null && Objects.equals(oldEffectOrder.getId(), newOrder.getId())) {
            log.info("paymentSuccess repeatedExecute,paymentInfo:{}", JSON.toJSONString(paymentInfo));
            return;
        }
        boolean isInitiatePayment = newPayRecord.getPaymentType() == PaymentTypeEnum.INITIATED_PAYMENT;
        SubscriptionPlan newPlan = subscriptionPlanRepository.queryByPlanId(newOrder.getSubscriptionPlanId(), false);
        completedInEffectOrder(isInitiatePayment, oldEffectOrder);

        // 4.将待支付的订单变成生效中
        newOrder.setStatus(SubscriptionOrderStatus.IN_EFFECT);
        newOrder.setAutoRenew(true);
        newOrder.setActualFee(newPayRecord.getTradeAmount());
        orderRepository.saveSubscriptionOrder(newOrder);

        // 5.保存首月减免记录
        saveFirstMonthDiscountRecord(newOrder, newPayRecord);

        // 6.用户主动支付的case发送申请订阅事件
        if (isInitiatePayment) {
            // 主动支付场景, 发送申请订阅事件
            sendSubscriptionAppliedEvent(newPayRecord, newOrder);
        }

        // 7. 发送支付成功的邮件通知
        sendPaySuccessEmail(newPayRecord, newOrder, newPlan, isInitiatePayment);

        // 8. 发送套餐变化事件
        SubscriptionPlan oldPlan = Optional.ofNullable(oldEffectOrder)
                .map(SubscriptionOrder::getSubscriptionPlanId)
                .map(planId -> subscriptionPlanRepository.queryByPlanId(planId, false))
                .orElse(null);
        subscriptionPlanChangedEventProducer.sendNewOrSwitchEvent(userId, oldPlan, oldEffectOrder, newPlan, newOrder, newPayRecord.getTradeNo());

        // 释放创建订单为了保证幂等加的锁
        Lock lock = RedisLock.of(RedisUtils.getJedisPool(), PaymentConst.LOCK_SPACE,
                String.format(PaymentConst.LOCK_ID_FORMAT, newOrder.getAppCode(), newOrder.getUserId()));
        lock.unlock();
    }

    /**
     * 保存首月减免记录
     */
    private void saveFirstMonthDiscountRecord(SubscriptionOrder newOrder, TradeRecord newPayRecord) {
        Long discountRuleId = newPayRecord.getAttributes().getFirstMonthDiscountRuleId();
        if (discountRuleId != null) {
            DiscountRecord discountRecord = new DiscountRecord()
                    .setAppCode(AppEnum.PIC_COPILOT)
                    .setUserId(newPayRecord.getUserId())
                    .setDiscountRuleId(discountRuleId)
                    .setPlanOriginPrice(newOrder.getPlanPrice())
                    .setDiscountPrice(newPayRecord.getTradeAmount());
            discountRecordRepository.create(discountRecord);
            log.info("saveFirstMonthDiscountRecord, discountRecord={}", JSON.toJSONString(discountRecord));
        }
    }

    /**
     * 发送「代扣成功」的邮件
     */
    private void sendPaySuccessEmail(TradeRecord newPayRecord, SubscriptionOrder newOrder, SubscriptionPlan newPlan, boolean isInitiatePayment) {
        BasePaySuccessEmailDTO emailDTO = new BasePaySuccessEmailDTO();
        String appCode = newPayRecord.getAppCode();
        if (isInitiatePayment) {
            if (Objects.equals(appCode, AppEnum.DS_COPILOT.getCode())) {
                if (isFirstOrder(newOrder.getUserId(), AppEnum.DS_COPILOT.getCode()) && Objects.isNull(newOrder.getShopifySubscriptionId())) {
                    // 首次订阅通知邮件
//                    emailDTO = new DscFirstSubscribeEmailDTO();
                    User user = userRepository.getUser(newOrder.getUserId());
                    if (user != null) {
                        Map<String, Object> params = new HashMap<>();
                        if (user.getEmail() != null) {
                            params.put(EdmService.USER_NAME_KEY, user.getEmail());
                            edmService.triggerEmail(FIRST_SUBSCRIBE_EMAIL_ID, user.getEmail(), params);
                        }
                    }

                    return;
                }
            } else if (Objects.equals(appCode, AppEnum.SEO_COPILOT_SITE.getCode())) {
                //SEO_COPILOT_SITE 主动付费成功
                SeoDeductSuccessEmailDTO seoDeductSuccessEmailDTO = new SeoDeductSuccessEmailDTO();
                emailDTO = seoDeductSuccessEmailDTO;
            } else {
                if (isAutoRenew(newOrder, newPayRecord)) {
                    // 连续订阅的通知邮件
                    emailDTO = new SubscribeWithAutoRenewEmailDTO();
                } else {
                    // 单次订阅的通知邮件
                    emailDTO = new SubscribeOnlyOnceEmailDTO();
                }
            }
        } else {
            // 续费成功的通知邮件
            if (Objects.equals(appCode, AppEnum.SEO_COPILOT_SITE.getCode())) {
                //SEO_COPILOT_SITE 续费成功
                SeoRenewSuccessEmailDTO seoRenewSuccessEmailDTO = new SeoRenewSuccessEmailDTO();
                emailDTO = seoRenewSuccessEmailDTO;
            } else {
                emailDTO = new DeductSuccessEmailDTO();
            }
        }

        Long userId = newPayRecord.getUserId();
        AppEnum appEnum = AppEnum.getAppByCode(appCode);
        BigDecimal payAmount = newPayRecord.getTradeAmount();
        emailDTO
                .setOrderTime(DateUtils.format(newOrder.getPerformStartTime(), "yyyy-MM-dd"))
                .setRealPay("$" + payAmount.setScale(2, RoundingMode.HALF_UP))
                .setOrderNo(newPayRecord.getTradeNo())
                .setAppName(appEnum.getName())
                .setUserName(newOrder.getEmail())
                .setPlanName(newPlan.getName());
        SubscriptionStrategyFactory.getStrategy(appCode, newOrder.getSubscriptionPayType()).sendEmail(userId, emailDTO);
    }


    private boolean isFirstOrder(Long userId, String appCode) {
        List<SubscriptionOrder> historyOrders = orderRepository.getHistoryOrders(userId, appCode);
        return historyOrders.size() <= 1;
    }

    /**
     * 是否自动自动续费<hr/>
     * 判断条件: <br/>
     * 1. 信用卡支付, 开启自动续费, 且包含卡Token<br/>
     * 2. Alipay支付, 开启自动续费, 且包含支付Token
     */
    private boolean isAutoRenew(SubscriptionOrder order, TradeRecord newPayRecord) {
        if (order.getAutoRenew()) {
            String appCode = order.getAppCode();
            Long userId = order.getUserId();
            PaymentMethodEnum paymentMethod = PaymentMethodEnum.ofName(newPayRecord.getPaymentMethod());
            switch (paymentMethod) {
                case CREDIT_CARD:
                    User user = userRepository.getUser(userId);
                    UserAttributes attributes = user.getAttributes();
                    return CollectionUtil.isNotEmpty(attributes.getAEPayInfo());
                case ALIPAY_CN:
                case ALIPAY_HK:
                    Map<PaymentMethodEnum, PaymentToken> method2Token = paymentTokenRepository.queryAuthedTokensByUser(appCode, userId);
                    return method2Token.get(paymentMethod) != null;
                case STRIPE:
                    return true;
                default:
                    log.warn("Unknown payment method: {}", paymentMethod);
                    break;
            }
        }
        return false;
    }

    /**
     * 取消当前生效的订单
     *
     * @param appCode     应用标识
     * @param userId      用户ID
     * @param forceRefund 是否强制退款 (针对业务上过了试用期仍然要退款的诉求)
     * @return 是否处理成功
     */
    @Monitor(name = "取消当前生效的订单", level = Monitor.Level.P1, layer = Monitor.Layer.SERVICE)
    public boolean cancelCurrentEffectOrder(String appCode, Long userId, boolean forceRefund) {
        log.info("cancelCurrentEffectOrder, userId={}, forceRefund={}", userId, forceRefund);

        SubscriptionOrder effectOrder = orderRepository.queryEffectOrder(appCode, userId);
        log.info("cancelCurrentEffectOrder, queryEffectOrder finished, result={}", JSON.toJSONString(effectOrder));
        if (effectOrder == null) {
            return true;
        }

        // 更改订单状态
        effectOrder.setStatus(SubscriptionOrderStatus.UNSUBSCRIBE);
        effectOrder.setPerformEndTime(DateUtil.date());
        effectOrder.setAutoRenew(false);
        effectOrder.getAttributes().addStatusFlowReason(StatusFlowReasonEnum.USER_CANCEL_SUBSCRIBE.name());
        orderRepository.saveSubscriptionOrder(effectOrder);
        // 清除下一笔订单的信息
        orderRepository.clearNextPlan(effectOrder.getId());

        // 试用单
        if (forceRefund || effectOrder.currentIsInTrial()) {
            // 退款试用期订单
            SubscriptionStrategyFactory.getStrategy(appCode, effectOrder.getSubscriptionPayType()).refundForTrialOrder(effectOrder);
        }

        return true;
    }

    /**
     * 取消当前生效订单的自动续费
     *
     * @param appCode 应用标识
     * @param userId  用户ID
     * @return 是否处理成功
     */
    @Monitor(name = "取消当前生效订单的自动续费", level = Monitor.Level.P1, layer = Monitor.Layer.SERVICE)
    public Boolean cancelAutoRenewForCurrentEffectOrder(String appCode, Long userId) {
        log.info("cancelAutoRenewForCurrentEffectOrder, appCode={},userId={}", appCode, userId);

        SubscriptionOrder effectOrder = orderRepository.queryEffectOrder(appCode, userId);
        log.info("cancelAutoRenewForCurrentEffectOrder, queryEffectOrder finished, result={}", JSON.toJSONString(effectOrder));
        if (effectOrder == null) {
            return true;
        }

        effectOrder.setAutoRenew(false);
        boolean result = orderRepository.saveSubscriptionOrder(effectOrder) == 1;

        // 清除下一笔订单的信息
        orderRepository.clearNextPlan(effectOrder.getId());

        return result;
    }

    /**
     * 查询订阅订单
     */
    public SubscriptionOrder querySubscriptionOrder(TradeRecord tradeRecord, SubscriptionOrderStatus orderStatus) {
        SubscriptionOrder subscriptionOrder = orderRepository.getByOrderId(tradeRecord.getSubscriptionOrderId());
        return Optional.ofNullable(subscriptionOrder)
                .filter(order -> order.getStatus() == orderStatus)
                .orElse(null);
    }

    /**
     * 获取交易流水
     */
    public TradeRecord getTradeRecord(String tradeNo) {
        return tradeRecordRepository.queryByTradeNo(tradeNo);
    }

    /**
     * 保存消息记录
     */
    public MessageRecord saveMessageRecord(MessageInfo messageInfo, String appCode, Long userId) {
        if (messageInfo == null) {
            return null;
        }

        IPaymentChannelStrategy paymentChannelStrategy = PaymentChannelFactory.getPaymentByApp(appCode);

        MessageRecord messageRecord = MessageRecord.builder()
                .gmtCreate(DateUtil.date())
                .gmtModified(DateUtil.date())
                .messageId(messageInfo.getMsgId())
                .direction(MessageDirectionEnum.IN)
                .appCode(IEnum.of(AppEnum.class, appCode))
                .entityId(messageInfo.getEntityId())
                .userId(String.valueOf(userId))
                .channel(paymentChannelStrategy.getPaymentChannel().name())
                .messageType(messageInfo.getTypeEnum())
                .messageData(messageInfo.getNotifyContentOrRequestParam())
                .status(messageInfo.getStatusEnum() != null ? messageInfo.getStatusEnum() : MessageStatusEnum.SUCCESS)
                .retryCount(1)
                .callResponse(messageInfo.getResponse())
                .deleted(false)
                .attributes(new MessageRecordAttributes(null))
                .build();

        messageRecordRepository.createOrUpdateMessageRecord(messageRecord);
        return messageRecord;
    }

    /**
     * 完结掉不再续约但履约已结束的订单
     */
    public void handlePerformDueOrder(SubscriptionOrder order) {
        if (order == null) {
            return;
        }

        if (SubscriptionOrderStatus.IN_EFFECT != order.getStatus()) {
            return;
        }

        if (order.getAutoRenew()) {
            return;
        }

        if (System.currentTimeMillis() < order.getPerformEndTime().getTime()) {
            return;
        }

        order.setStatus(SubscriptionOrderStatus.COMPLETED);
        orderRepository.saveSubscriptionOrder(order);

        subscriptionPlanChangedEventProducer.sendCompleteEvent(order, SubscriptionCompleteReason.NOT_AUTO_RENEW);
    }

    /**
     * 完结正在生效的订单, 对应两种场景:<br/>
     * 1. 订阅的套餐不需要支付时, 直接完结掉<br/>
     * 2. 订阅的套餐需要支付时, 收到支付成功的通知后, 完结掉<br/>
     */
    private void completedInEffectOrder(boolean needInitiateRefund, SubscriptionOrder oldEffectOrder) {
        if (oldEffectOrder == null) {
            return;
        }

        oldEffectOrder.setStatus(SubscriptionOrderStatus.COMPLETED);
        oldEffectOrder.setAutoRenew(false);
        oldEffectOrder.setPerformEndTime(DateUtil.date());
        oldEffectOrder.setGmtModified(DateUtil.date());

        orderRepository.saveSubscriptionOrder(oldEffectOrder);
        if (needInitiateRefund) {
            // 对于主动支付的场景, 操作老订单的退款逻辑, 需要分场景处理
            SubscriptionStrategy strategy = SubscriptionStrategyFactory.getStrategy(oldEffectOrder.getAppCode(), oldEffectOrder.getSubscriptionPayType());
            strategy.handleRefundLogicWhenOldOrderCompleted(oldEffectOrder);
        }
    }

    /**
     * 发送申请订阅事件
     */
    private void sendSubscriptionAppliedEvent(TradeRecord tradeRecord, SubscriptionOrder subscriptionOrder) {
        SubscriptionPlanQuery subscriptionPlanQuery = SubscriptionPlanQuery.builder()
                .appCode(tradeRecord.getAppCode())
                .planId(subscriptionOrder.getSubscriptionPlanId())
                .queryInfos(Collections.singletonList((SubscriptionPlanQueryInfo.FEATURE)))
                .build();
        SubscriptionPlan subscribePlan = querySubscriptionPlan(subscriptionPlanQuery);

        Assertor.asserts(subscribePlan != null, "subscribePlan is empty");

        SubscriptionAppliedEvent subscriptionAppliedEvent = SubscriptionAppliedEvent.builder()
                .appCode(tradeRecord.getAppCode())
                .userCode(subscriptionOrder.getAttributes().getShareCode())
                .userId(String.valueOf(tradeRecord.getUserId()))
                .email(StringUtils.EMPTY)
                .subName(subscribePlan.getName())
                .subCycle(subscribePlan.getDurationUnit().getDays())
                .trialCycle(subscribePlan.getTrialDuration().intValue())
                .actualFee(new cn.hutool.core.math.Money(tradeRecord.getTradeAmount()).getCent())
                .subId(subscriptionOrder.getId())
                .unix(System.currentTimeMillis())
                .build();

        eventPublisher.publish(subscriptionAppliedEvent);
    }

    /**
     * 发生取消订单事件
     *
     * @param order 订单
     */
    private void sendCancelOrderEvent(SubscriptionOrder order) {
        SubscriptionCanceledEvent subscriptionCanceledEvent = SubscriptionCanceledEvent.builder()
                .appCode(order.getAppCode())
                .subId(order.getId())
                .unix(System.currentTimeMillis())
                .build();
        eventPublisher.publish(subscriptionCanceledEvent);
    }

    /**
     * 生成新的订单
     */
    private Long createNewSubscriptionOrder(SubscriptionOrder oldSubscriptionOrder, SubscriptionPlan activatedPlan, String appCode, Date endTime) {
        Long planId = oldSubscriptionOrder.getSubscriptionPlanId();
        String planName = oldSubscriptionOrder.getSubscriptionPlanName();
        if (oldSubscriptionOrder.getNextPlanId() != null) {
            planId = oldSubscriptionOrder.getNextPlanId();
            planName = oldSubscriptionOrder.getNextPlanName();
        }

        SubscriptionOrder newSubscriptionOrder = SubscriptionOrder.builder()
                .userId(oldSubscriptionOrder.getUserId())
                .email(oldSubscriptionOrder.getEmail())
                .appCode(appCode)
                .userAppRelationId(oldSubscriptionOrder.getUserAppRelationId())
                .subscriptionPlanId(planId)
                .subscriptionPlanName(planName)
                .status(SubscriptionOrderStatus.PENDING_PAYMENT)
                .subscriptionDiscountTag(oldSubscriptionOrder.getSubscriptionDiscountTag())
                .autoRenew(oldSubscriptionOrder.getAutoRenew())
                .nextRenewalTime(endTime)
                .isIncludeTrial(false)
                .planPrice(activatedPlan.getPrice())
                .performStartTime(oldSubscriptionOrder.getPerformEndTime())
                .performEndTime(endTime)
                .deleted(false)
                .hadNextRenew(false)
                .attributes(oldSubscriptionOrder.getAttributes())
                .subscriptionPayType(oldSubscriptionOrder.getSubscriptionPayType())
                .build();

        if (newSubscriptionOrder.getAttributes() == null) {
            newSubscriptionOrder.setAttributes(new SubscriptionOrderAttributes());
        }
        newSubscriptionOrder.getAttributes().setWhetherSentDeductionNotifyEmail(0);

        orderRepository.saveSubscriptionOrder(newSubscriptionOrder);
        return newSubscriptionOrder.getId();
    }

    /**
     * 创建订单
     */
    private SubscriptionOrder createNewOrder(SubscribeContext context, boolean needPay) {
        Date now = context.getNow();
        SubscriptionPlan activatedPlan = context.getNewPlan();
        UserAppRelationDTO userAppRelationDTO = context.getUserAppRelationDTO();
        String appCode = userAppRelationDTO.getAppCode();
        FinalDiscountDTO finalDiscountDTO = context.getFinalDiscountDTO();
        TrialDurationDTO trialDurationDTO = context.getTrialDurationDTO();
        Boolean isTrial = trialDurationDTO.getIsTrial();
        SubscriptionOrderAttributes attributes = new SubscriptionOrderAttributes();
        attributes.setShareCode(context.getShareCode());

        // 添加首月折扣标记
        Optional.ofNullable(context.getFirstMonthDiscountDTO())
                .map(FirstMonthDiscountDTO::getDiscountRuleId)
                .ifPresent(attributes::setFirstMonthDiscountRuleId);

        // 计算订单结束时间
        SubscriptionStrategy strategy = SubscriptionStrategyFactory.getStrategy(appCode, context.getSubscriptionPayType());
        ComputeOrderEndTimeResultDTO endTimeResult = strategy.computeOrderEndTime(context);
        Date orderEndTime = endTimeResult.getOrderEndTime();
        attributes.setPlanDays(endTimeResult.getPlanDays());
        attributes.setTrialDays(endTimeResult.getTrialDays());

        // 对于不需要支付的订单, 直接更改为生效状态
        SubscriptionOrderStatus status = needPay
                ? SubscriptionOrderStatus.PENDING_PAYMENT : SubscriptionOrderStatus.IN_EFFECT;

        SubscriptionOrder newOrder = SubscriptionOrder.builder()
                .subscriptionPlanId(activatedPlan.getId())
                .gmtCreate(now)
                .gmtModified(now)
                .userId(userAppRelationDTO.getUserId())
                .email(userAppRelationDTO.getEmail())
                .userAppRelationId(userAppRelationDTO.getId())
                .appCode(activatedPlan.getAppCode())
                .subscriptionPlanName(activatedPlan.getName())
                // 不享受折扣的不存储该折扣码
                .subscriptionDiscountTag(finalDiscountDTO != null && finalDiscountDTO.getIsDiscount() ? finalDiscountDTO.getDiscountTag() : null)
                .planPrice(activatedPlan.getPrice())
                .autoRenew(true)
                .performStartTime(now)
                .status(status)
                .deleted(false)
                .isIncludeTrial(isTrial)
                .performEndTime(orderEndTime)
                .nextRenewalTime(orderEndTime)
                .hadNextRenew(false)
                .attributes(attributes)
                .subscriptionPayType(context.getSubscriptionPayType())
                .build();
        orderRepository.saveSubscriptionOrder(newOrder);
        return newOrder;
    }

    /**
     * 生成付款交易流水
     */
    private TradeRecord createPayRecord(SubscriptionPlan activatedPlan, Long userId, BigDecimal payAmount, Long orderId,
                                        PaymentTypeEnum paymentTypeEnum, PaymentMethodEnum paymentMethod,
                                        FirstMonthDiscountDTO firstMonthDiscountDTO, IpAddressInfo ipAddressInfo) {
        Assertor.assertNonNull(paymentMethod, "paymentMethod is null");
        TradeRecord payRecord = TradeRecord.builder()
                .userId(userId)
                .subscriptionOrderId(orderId)
                .appCode(activatedPlan.getAppCode())
                .paymentType(paymentTypeEnum)
                .tradeAmount(payAmount)
                .tradeCurrency(AEPaymentConstants.CURRENCY_CODE)
                .tradeDirection(TradeDirection.FORWARD)
                .paymentMethod(paymentMethod.name())
                .status(TradeRecordStatus.TODO)
                .taxAmount(null)
                .taxCurrency(null)
                .transactionAmount(null)
                .transactionCurrency(null)
                .tradeNo(PaymentUtils.generateTradeNo(userId))
                .hadInitiatePay(paymentTypeEnum == PaymentTypeEnum.INITIATED_PAYMENT)
                .deleted(false)
                .attributes(new TradeRecordAttributes(null))
                .build();

        TradeRecordAttributes attributes = payRecord.getAttributes();
        attributes.setSubscriptionPlanId(activatedPlan.getId());
        attributes.setSubscriptionPlanName(activatedPlan.getName());

        if (Objects.nonNull(ipAddressInfo)) {
            attributes.setIpAddressInfo(ipAddressInfo);
        }

        // 添加首月折扣标记
        Optional.ofNullable(firstMonthDiscountDTO)
                .map(FirstMonthDiscountDTO::getDiscountRuleId)
                .ifPresent(attributes::setFirstMonthDiscountRuleId);

        tradeRecordRepository.createOrUpdateTradeRecord(payRecord);
        return payRecord;
    }

    /**
     * 生成shopify支付的交易流水
     */
    public TradeRecord createShopifyTradeRecord(TradeDirection tradeDirection, SubscriptionOrder newOrder,
                                                PaymentTypeEnum paymentTypeEnum, SubscriptionPlan subscriptionPlan, String clientIp) {
        if (newOrder == null || subscriptionPlan == null) {
            return null;
        }

        if (newOrder.getActualFee() == null || newOrder.getActualFee().doubleValue() <= 0) {
            return null;
        }

        TradeRecord payRecord = TradeRecord.builder()
                .userId(newOrder.getUserId())
                .subscriptionOrderId(newOrder.getId())
                .appCode(newOrder.getAppCode())
                .paymentType(paymentTypeEnum)
                .tradeAmount(newOrder.getActualFee())
                .tradeCurrency(AEPaymentConstants.CURRENCY_CODE)
                .tradeDirection(tradeDirection)
                .paymentMethod(PaymentMethodEnum.SHOPIFY.getValue())
                .tradeTime(new Date())
                .status(TradeRecordStatus.SUCC)
                .taxAmount(null)
                .taxCurrency(null)
                .transactionAmount(null)
                .transactionCurrency(null)
                .tradeNo(PaymentUtils.generateTradeNo(newOrder.getUserId()))
                .hadInitiatePay(paymentTypeEnum == PaymentTypeEnum.INITIATED_PAYMENT)
                .deleted(false)
                .attributes(new TradeRecordAttributes(null))
                .build();

        TradeRecordAttributes attributes = payRecord.getAttributes();
        attributes.setSubscriptionPlanId(subscriptionPlan.getId());
        attributes.setSubscriptionPlanName(subscriptionPlan.getName());
        if (StringUtils.isNotBlank(clientIp)) {
            GetAddressInfoByIpResponse addressInfoByIp = geoIpGateway.getAddressInfoByIp(clientIp);
            if (addressInfoByIp != null) {
                IpAddressInfo ipAddressInfo = new IpAddressInfo();
                BeanUtils.copyProperties(addressInfoByIp, ipAddressInfo);
                attributes.setIpAddressInfo(ipAddressInfo);
            }
        }

        tradeRecordRepository.createOrUpdateTradeRecord(payRecord);
        return payRecord;
    }

    private SubscriptionPlan querySubscriptionPlan(SubscriptionPlanQuery subscriptionPlanQuery) {
        List<SubscriptionPlan> subscriptionPlans = subscriptionPlanRepository.querySubscriptionPlans(subscriptionPlanQuery);
        return CollectionUtil.getFirst(subscriptionPlans);
    }

    /**
     * 计算折扣价格
     */
    private BigDecimal calculateDiscount(SubscriptionPlan activatedPlan, String appCode, Long userId, String discountTag, boolean isOrderRenewal) {
        List<SubscriptionOrder> historyOrders = orderRepository.getHistoryOrders(userId, appCode);

        FinalDiscountDTO finalDiscountInfo = discountService.getFinalDiscountInfo(appCode, discountTag, activatedPlan, historyOrders, isOrderRenewal);
        return getForwardPriceForPeriodicDeduct(finalDiscountInfo, activatedPlan);
    }

    /**
     * 被动代扣的支付金额计算
     */
    private BigDecimal getForwardPriceForPeriodicDeduct(FinalDiscountDTO finalDiscountInfo, SubscriptionPlan activatedPlan) {
        BigDecimal activatedPlanPrice = activatedPlan.getPrice();
        if (StringUtils.equals(AppEnum.SEO_COPILOT_SITE.getCode(), activatedPlan.getAppCode())
                && activatedPlan.getAttributes() != null
                && activatedPlan.getAttributes().containsKey("discountPrice")) {
            //SEO_COPILOT_SITE 的原价
            activatedPlanPrice = BigDecimal.valueOf(activatedPlan.getAttributes().getAsDouble("discountPrice"));
        }
        if (activatedPlanPrice.compareTo(BigDecimal.ZERO) <= 0) {
            // 原价免费的套餐, 不需要走折扣逻辑
            return BigDecimal.ZERO;
        }

        if (finalDiscountInfo != null && finalDiscountInfo.getIsDiscount()) {
            return finalDiscountInfo.getDiscountPrice();
        }
        return activatedPlanPrice;
    }

    /**
     * 主动支付的支付金额计算
     */
    private BigDecimal getForwardPriceForInitiatedPayment(SubscribeContext context) {
        UserAppRelationDTO userAppRelationDTO = context.getUserAppRelationDTO();
        String appCode = userAppRelationDTO.getAppCode();

        ComputeNewPlanPriceContext computeNewPlanPriceContext = new ComputeNewPlanPriceContext(
                appCode,
                userAppRelationDTO.getUserId(),
                context.getShareCode(),
                context.getNow(),
                context.getNewPlan(),
                context.getHistoryOrders(),
                context.getFinalDiscountDTO(),
                context.getOldOrder(),
                context.getOldPlan(),
                context.getOldPayRecord(),
                context.getFirstMonthDiscountDTO()
        );

        SubscriptionStrategy strategy = SubscriptionStrategyFactory.getStrategy(appCode, context.getSubscriptionPayType());
        return strategy.computeNewPlanPrice(computeNewPlanPriceContext).getPayAmount();
    }

    /**
     * 创建零元购订单, 给用户创建不需要支付的套餐订阅
     */
    @Transactional(rollbackFor = Throwable.class)
    public void createFreeOrder(CreateFreeOrderDTO dto) {
        String appCode = dto.getAppCode();
        Long userId = dto.getUserId();
        Long planId = dto.getPlanId();
        Integer month = dto.getMonth();

        // 参数校验
        checkCreateFreeOrderParams(appCode, userId, planId, month);

        // 检查新套餐的有效性
        SubscriptionPlan plan = checkNewPlan(planId);

        // 检查当前生效订单
        checkCurrentEffectOrder(appCode, userId);

        // 查询用户关系
        UserAppRelation userAppRelation = queryUserAppRelation(appCode, userId);

        // 创建订单
        SubscriptionOrder newOrder = new FreeOrderBuilder(dto, month, plan, userAppRelation).build();

        // 保存DB
        orderRepository.saveSubscriptionOrder(newOrder);

        // 如果没有传外部账单，则不创建0元账单流水
        if (dto.getTradeRecord() != null) {

            // 参数校验
            checkCreateFreeTradeRecord(dto.getTradeRecord());
            // 创建0元账单
            TradeRecord tradeRecord = new FreeTradeRecordBuilder(dto, newOrder).build();
            // 保存账单到数据库
            tradeRecordRepository.createOrUpdateTradeRecord(tradeRecord);
        }


        // 发送成功创建的MQ消息
        subscriptionPlanChangedEventProducer.sendNewOrSwitchEvent(userId, null, null, plan, newOrder, null);
    }

    private void checkCreateFreeTradeRecord(CreateTradeRecordDTO tradeRecord) {
        Assertor.assertNotBlank(tradeRecord.getPaymentMethod(), "paymentMethod is blank");
        Assertor.asserts(tradeRecord.getPaymentMethod().equals(PaymentMethodEnum.PIC_REDEEM_CODE.getValue()),
                "only support PIC_REDEEM_CODE");
        Assertor.assertNotBlank(tradeRecord.getOutTradeNo(), "outTradeNo is blank");
    }

    private static void checkCreateFreeOrderParams(String appCode, Long userId, Long planId, Integer month) {
        Assertor.assertNotBlank(appCode, "appCode is blank");
        Assertor.asserts(userId != null && userId > 0, "userId is invalid");
        Assertor.asserts(planId != null && planId > 0, "planId is invalid");
        Assertor.asserts(month != null && month > 0, "month is invalid");
    }

    @NotNull
    private SubscriptionPlan checkNewPlan(Long planId) {
        SubscriptionPlan plan = subscriptionPlanRepository.queryByPlanId(planId, false);
        if (plan == null) {
            // 无效套餐
            log.warn("createFreeOrder, plan is null, planId={}", planId);
            throw new BizException(ErrorCode.SYS_ERROR, "No plan found.");
        }
        return plan;
    }

    private void checkCurrentEffectOrder(String appCode, Long userId) {
        SubscriptionOrder effectOrder = orderRepository.queryEffectOrder(appCode, userId);
        if (effectOrder != null) {
            // 当前有生效套餐的用户, 不允许设置
            String message = "Disallow to create free order for user who already has effect order.";
            log.warn("createFreeOrder, {}", message);
            throw new BizException(ErrorCode.SYS_ERROR, message);
        }
    }

    @NotNull
    private UserAppRelation queryUserAppRelation(String appCode, Long userId) {
        UserAppRelation userAppRelation = userAppRelationRepository.getUserAppRelation(userId, appCode, null);
        if (userAppRelation == null) {
            // 缺失用户关系
            throw new BizException(ErrorCode.SYS_ERROR, "No user relation found.");
        }
        return userAppRelation;
    }
}
