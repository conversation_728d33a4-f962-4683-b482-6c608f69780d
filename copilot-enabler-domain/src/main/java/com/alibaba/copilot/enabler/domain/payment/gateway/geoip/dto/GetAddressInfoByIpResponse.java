package com.alibaba.copilot.enabler.domain.payment.gateway.geoip.dto;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/11/07
 */
@Data
public class GetAddressInfoByIpResponse {
    /**
     * IP 地址
     */
    private String ip;

    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 地区代码
     */
    private String regionCode;

    /**
     * 地区名称
     */
    private String regionName;

    /**
     * 城市代码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 县代码
     */
    private String countyCode;

    /**
     * 县名称
     */
    private String countyName;
}