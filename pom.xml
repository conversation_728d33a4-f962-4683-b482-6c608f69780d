<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.taobao</groupId>
        <artifactId>parent</artifactId>
        <version>2.0.0</version>
    </parent>
    <groupId>com.alibaba.copilot</groupId>
    <artifactId>copilot-enabler</artifactId>
    <version>1.0.39-RELEASE</version>
    <packaging>pom</packaging>
    <name>copilot-enabler</name>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <java.version>1.8</java.version>
        <java_source_version>1.8</java_source_version>
        <java_target_version>1.8</java_target_version>
        <file_encoding>UTF-8</file_encoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven-antrun.version>1.8</maven-antrun.version>
        <spring-boot.version>2.5.12</spring-boot.version>
        <pandora-boot.version>2023-02-release</pandora-boot.version>
        <pandora-boot-maven-plugin.version>2.1.18.9</pandora-boot-maven-plugin.version>
        <pandora-hsf-spring-boot-starter.version>2023-02-release</pandora-hsf-spring-boot-starter.version>
        <pandora-metaq-spring-boot-starter.version>2023-02-release</pandora-metaq-spring-boot-starter.version>
        <metaq-client.version>4.2.6.Final</metaq-client.version>
        <pandora-ons-spring-boot-starter.version>2023-02-release</pandora-ons-spring-boot-starter.version>
        <maven-surefire-plugin.version>2.22.2</maven-surefire-plugin.version>

        <copilot.boot.version>0.1.20231106-RELEASE</copilot.boot.version>
        <copilot.client.version>1.0.39-RELEASE</copilot.client.version>


        <mybatis-plus.version>3.5.0</mybatis-plus.version>
        <fastjson.version>2.0.30</fastjson.version>
        <jacoco-plugin.version>0.8.8</jacoco-plugin.version>
        <lombok.version>1.18.26</lombok.version>
        <mapstruct.version>1.5.3.Final</mapstruct.version>
        <guava.version>31.1-jre</guava.version>
        <swagger.version>2.5.0</swagger.version>
        <swagger-ui.version>1.9.4</swagger-ui.version>
        <spring-security-crypto.version>5.3.9.RELEASE</spring-security-crypto.version>
        <tbsession.version>4.0.10-fix</tbsession.version>
        <logback-mdc-ttl.version>1.0.2</logback-mdc-ttl.version>
        <transmittable-thread-local.version>2.5.1</transmittable-thread-local.version>
        <pandora-sentinel-spring-boot-starter.version>2023-04-release</pandora-sentinel-spring-boot-starter.version>
        <hutool.version>5.8.12</hutool.version>
        <oss-client.version>3.10.2</oss-client.version>
    </properties>

    <modules>
        <module>copilot-enabler-client</module>
        <module>copilot-enabler-domain</module>
        <module>copilot-enabler-infrastructure</module>
        <module>copilot-enabler-service</module>
        <module>copilot-enabler-starter</module>
    </modules>
    <dependencyManagement>
        <dependencies>
            <!-- 依赖子模块 -->
            <dependency>
                <groupId>com.alibaba.copilot</groupId>
                <artifactId>copilot-enabler-client</artifactId>
                <version>${copilot.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.copilot</groupId>
                <artifactId>copilot-enabler-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.copilot</groupId>
                <artifactId>copilot-enabler-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.copilot</groupId>
                <artifactId>copilot-enabler-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 中间件 -->
            <dependency>
                <groupId>com.taobao.pandora</groupId>
                <artifactId>pandora-boot-starter-bom</artifactId>
                <version>${pandora-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>pandora-metaq-spring-boot-starter</artifactId>
                <version>${pandora-metaq-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.middleware</groupId>
                <artifactId>demo-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.csp</groupId>
                <artifactId>switchcenter</artifactId>
                <version>2.1.0.4.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.csp</groupId>
                        <artifactId>courier</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 二方包 -->
            <dependency>
                <groupId>com.alibaba.copilot</groupId>
                <artifactId>copilot-boot-basic</artifactId>
                <version>${copilot.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.copilot</groupId>
                <artifactId>copilot-boot-tools</artifactId>
                <version>${copilot.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.copilot</groupId>
                <artifactId>copilot-boot-config</artifactId>
                <version>${copilot.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.copilot</groupId>
                <artifactId>copilot-boot-monitor-starter</artifactId>
                <version>${copilot.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.copilot</groupId>
                <artifactId>copilot-boot-event-base</artifactId>
                <version>${copilot.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.copilot</groupId>
                <artifactId>copilot-boot-shopify</artifactId>
                <version>${copilot.boot.version}</version>
            </dependency>
            <!-- 三方包 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-autoconfigure</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.zaxxer</groupId>
                        <artifactId>HikariCP</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>8.0.33</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>pandora-tddl-spring-boot-starter</artifactId>
                <version>2023-02-release</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jcl</artifactId>
                <version>999-not-exist</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>1.7.26</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>999-not-exist-v3</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>servlet-api</artifactId>
                <version>99.0-does-not-exist</version>
            </dependency>
            <dependency>
                <groupId>servlet-api</groupId>
                <artifactId>servlet-api</artifactId>
                <version>999-not-exist</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>999-not-exist</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <optional>true</optional>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>2.9.9</version>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.1.Final</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mapstruct</groupId>
                        <artifactId>mapstruct</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>swagger-bootstrap-ui</artifactId>
                <version>${swagger-ui.version}</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.tbsession</groupId>
                <artifactId>tbsession</artifactId>
                <version>${tbsession.version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.tbsession</groupId>
                <artifactId>tbsession-springboot-starter</artifactId>
                <version>${tbsession.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ofpay</groupId>
                <artifactId>logback-mdc-ttl</artifactId>
                <version>${logback-mdc-ttl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>pandora-sentinel-spring-boot-starter</artifactId>
                <version>${pandora-sentinel-spring-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>pandora-hsf-spring-boot-starter</artifactId>
                <version>${pandora-hsf-spring-boot-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>hsf-sdk</artifactId>
                        <groupId>com.alibaba.middleware</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.aliyun.schedulerx</groupId>
                <artifactId>schedulerx2-spring-boot-starter</artifactId>
                <version>1.9.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.aepay</groupId>
                <artifactId>fund-business-api</artifactId>
                <version>1.0.3-AUTO-DEBIT-20240130</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.middleware</groupId>
                <artifactId>hsf-sdk</artifactId>
                <version>3.1.8--2023-02-release</version>
            </dependency>

            <!-- 发送邮件 -->
            <dependency>
                <groupId>com.alibaba.global.midplatform</groupId>
                <artifactId>global-message-client-starter</artifactId>
                <version>2.0.6-fatigue</version>
                <exclusions>
                    <exclusion>
                        <artifactId>global-landlord-sdk</artifactId>
                        <groupId>com.alibaba.global</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.alibaba.edm</groupId>
                <artifactId>copilot-edm-client</artifactId>
                <version>1.0.0</version>
            </dependency>

            <!-- stripe -->
            <dependency>
                <groupId>com.stripe</groupId>
                <artifactId>stripe-java</artifactId>
                <version>28.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.9.1</version>
            </dependency>

            <!-- redis -->
            <dependency>
                <groupId>com.alibaba.normandy.credential</groupId>
                <artifactId>normandy-credential-spring-boot-starter</artifactId>
                <version>1.0.10</version>
            </dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>4.3.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.11.1</version>
            </dependency>

            <!-- Excel表格制作 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>5.2.3</version>
            </dependency>

            <!-- 绘制PDF -->
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itext7-core</artifactId>
                <version>7.1.9</version>
                <type>pom</type>
            </dependency>

            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${oss-client.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>${maven-antrun.version}</version>
                </plugin>
                <plugin>
                    <groupId>com.taobao.pandora</groupId>
                    <artifactId>pandora-boot-maven-plugin</artifactId>
                    <version>${pandora-boot-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <argLine>${argLine} -Dfile.encoding=UTF-8</argLine>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco-plugin.version}</version>
                    <configuration>
                        <propertyName>argLine</propertyName>
                    </configuration>
                    <executions>
                        <execution>
                            <id>default-prepare-agent</id>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>default-report</id>
                            <phase>test</phase>
                            <goals>
                                <goal>report</goal>
                                <goal>report-aggregate</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
            </plugin>
            <!--				build 二方包-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.1.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <attach>true</attach>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
