package com.alibaba.copilot.enabler.starter;

import com.alibaba.copilot.enabler.domain.base.utils.SpringContextUtils;
import com.alibaba.copilot.enabler.infra.base.metaq.MetaqMessageProperties;
import com.alibaba.copilot.enabler.infra.base.switchs.SwitchConfig;
import com.alibaba.security.SecurityUtil;
import com.taobao.csp.switchcenter.core.SwitchManager;
import com.taobao.pandora.boot.PandoraBootstrap;
import com.taobao.pandora.boot.loader.SarLoaderUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableAsync
@EnableAspectJAutoProxy
@SpringBootApplication(scanBasePackages = {"com.alibaba.copilot.enabler", "com.alibaba.global.midplatform"})
@EnableConfigurationProperties(MetaqMessageProperties.class)
@EnableRetry
@Slf4j
public class Application {

    public static void main(String[] args) {
        SecurityUtil.initialize();
        PandoraBootstrap.run(args);
        new SpringApplicationBuilder(Application.class)
                .initializers(new SpringContextUtils())
                .run(args);
        SwitchManager.init("copilot-enabler", SwitchConfig.class);
        PrintStartUpTime();
        PandoraBootstrap.markStartupAndWait();
    }

    /**
     * 打印启动耗时
     */
    private static void PrintStartUpTime() {
        long durationInSeconds = (System.nanoTime() - SarLoaderUtils.t1()) / 1000000000;
        String message = String.format("=============== 应用启动成功！耗时：%ds ===============", durationInSeconds);
        log.info(message);
    }
}
