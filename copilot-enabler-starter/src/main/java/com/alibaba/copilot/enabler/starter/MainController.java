package com.alibaba.copilot.enabler.starter;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @description Spring MVC 的根路径、健康检查等。
 * @email <EMAIL>
 * @date 2023/4/28
 */
@ApiIgnore
@Controller
public class MainController {
    /**
     * 健康检查，系统部署需要
     * 请不要删除！！
     */
    @GetMapping("/checkpreload.htm")
    public @ResponseBody String checkPreload() {
        return "success";
    }

    @GetMapping("/status.taobao")
    public @ResponseBody String statusTaobao() {
        return "success";
    }

    @GetMapping("/taobao.status")
    public @ResponseBody String taobaoStatus() {
        return "success";
    }

}
