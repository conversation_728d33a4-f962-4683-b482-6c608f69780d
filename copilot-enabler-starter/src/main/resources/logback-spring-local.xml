<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- https://github.com/spring-projects/spring-boot/blob/v2.5.12/spring-boot-project/spring-boot/src/main/resources/org/springframework/boot/logging/logback/defaults.xml -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <include resource="copilot-boot-monitor-log.xml"/>

    <property name="APP_NAME" value="copilot-enabler" />
    <property name="LOG_PATH" value="${user.home}/${APP_NAME}/logs" />
    <property name="LOG_FILE" value="${LOG_PATH}/application.log" />
    <property name="FILE_LOG_PATTERN" value="%d - %p - ${PID:- } - %c{1.}:%M - [%t] | %m%n"/>

    <appender name="APPLICATION"
        class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <!--任务日志-->
    <appender name="taskAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/task.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/task.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>15</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>
                <![CDATA[%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger{35} - %m%n%ex{10}]]>
            </pattern>
        </layout>
    </appender>

    <appender name="taskAsyncAppender" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize><!--队列大小-->
        <neverBlock>true</neverBlock><!--offer还是put，true代表使用offer，不挂线程，永远不会阻塞-->
        <maxFlushTime>1000</maxFlushTime><!--flush时间，默认1s-->
        <discardingThreshold>0</discardingThreshold>
        <!--丢弃阈值，如果队列大小剩余小于阈值，就会丢弃比info（包含）级别低的日志，error 和warn等依然会进队列，除非设置nerverblock参数-->
        <appender-ref ref="taskAppender"/>
    </appender>

    <logger name="taskLogger" level="INFO" additivity="false">
        <appender-ref ref="taskAsyncAppender"/>
    </logger>

    <root level="DEBUG">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="APPLICATION" />
    </root>
</configuration>
