### 线上环境
# 是否启用swagger
swagger.enable=false
spring.hsf.version=1.0.0
# 日志配置
logging.config=classpath:logback-spring.xml
# eventBus配置(hession协议)
copilot.boot.eventbus.enabled=true
copilot.boot.eventbus.message.topic=copilot-outer-domain-topic
copilot.boot.eventbus.message.consumerGroup=CID_copilot_outer_domain_event
copilot.boot.eventbus.message.producerGroup=PID_copilot_outer_domain_event
copilot.boot.eventbus.message.producerUnitName=aliyun-region-vpc-ap-southeast-1
copilot.boot.eventbus.message.producerInstanceName=aliyun-region-vpc-ap-southeast-1
copilot.boot.eventbus.message.consumerUnitName=aliyun-region-vpc-ap-southeast-1
copilot.boot.eventbus.message.consumerInstanceName=aliyun-region-vpc-ap-southeast-1
# metaq配置
copilot.metaq.message.topic=copilot-outer-domain-json-topic
copilot.metaq.message.consumerGroup=CID_copilot_outer_domain_json_event
copilot.metaq.message.producerGroup=PID_copilot_outer_domain_json_event
copilot.metaq.message.producerUnitName=aliyun-region-vpc-ap-southeast-1
copilot.metaq.message.producerInstanceName=aliyun-region-vpc-ap-southeast-1
copilot.metaq.message.consumerUnitName=aliyun-region-vpc-ap-southeast-1
copilot.metaq.message.consumerInstanceName=aliyun-region-vpc-ap-southeast
inner.metaq.message.consumerUnitName=aliyun-region-vpc-ap-southeast-1
inner.metaq.message.producerUnitName=aliyun-region-vpc-ap-southeast-1
# 推广回流 Http 服务
commissionHttpServiceUrlPrefix=https://aidc-commission.alibaba-inc.com

# stripe
stripe.webhook.secret=whsec_4m4GYED23fRMvfGKgZX1LAIuCyeztDQG
stripe.api.key=***********************************************************************************************************

# redis配置(copilot-enabler应用独享)
redis.instance-id=r-t4n355784db61654
redis.endpoint=r-t4n355784db61654.redis.singapore.rds.aliyuncs.com
# schedulerx2配置
spring.schedulerx2.enabled=true
spring.schedulerx2.domainName=sg.schedulerx2.alibaba-inc.com
spring.schedulerx2.namespace=4cf5afd1-5f17-4733-9280-741be3a395f6
spring.schedulerx2.groupId=copilot-enabler
spring.schedulerx2.appKey=uFJI+tdojy2VlEXq+2wipA==
#Aepay 服务
hsf.aepay.version=1.0.0.aepay
hsf.aepay.unit=
# ip ??HSF
hsf.ae.geoip.unit=aliyun-region-vpc-ap-southeast-1
# ?????
spring.hsf.configserverCenter=aliyun-region-vpc-ap-southeast-1,lazada-sg-2