<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>账单令牌生成</title>
</head>
<script type="text/javascript">
    function copy(id) {
        const element = document.getElementById(id);

        const tempTextarea = document.createElement("textarea");
        tempTextarea.value = element.textContent;
        document.body.appendChild(tempTextarea);
        tempTextarea.select();
        document.execCommand("copy");
        document.body.removeChild(tempTextarea);

        alert("已复制");
    }
</script>
<body>
<h1 style="color:red;">！！！每次刷新页面，信息都会发生变化，请务必保存好！！！</h1>

<p>1. （<b style="color:red;">可分享，给到开发同学</b>）请将 <span id="publicToken" style="color:red;">${publicToken}</span> 给到开发同学进行配置；
    <span><button onclick="copy('publicToken')">复制令牌</button></span>
</p>

<p>2.（<b style="color:red;">不可分享，不给任何人</b>）
    请保存该地址：<span id="link" style="color:red;">https://pre-copilot-enabler.alibaba-inc.com/api/bill/${privateToken}/download?appCode=PIC_COPILOT&targetDate=2024-3</span>
    <span><button onclick="copy('link')">复制链接</button></span>
</p>

</body>
</html>
