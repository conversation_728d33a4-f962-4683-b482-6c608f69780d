### 预发环境

# 是否启用swagger
swagger.enable=true
spring.hsf.version=1.0.0

# 日志配置
logging.config=classpath:logback-spring.xml

# eventBus配置
copilot.boot.eventbus.enabled=true
copilot.boot.eventbus.message.topic=copilot-outer-domain-topic
copilot.boot.eventbus.message.consumerGroup=CID_copilot_outer_domain_event
copilot.boot.eventbus.message.producerGroup=PID_copilot_outer_domain_event
copilot.boot.eventbus.message.producerUnitName=aliyun-region-vpc-ap-southeast-1-pre
copilot.boot.eventbus.message.producerInstanceName=aliyun-region-vpc-ap-southeast-1-pre
copilot.boot.eventbus.message.consumerUnitName=aliyun-region-vpc-ap-southeast-1-pre
copilot.boot.eventbus.message.consumerInstanceName=aliyun-region-vpc-ap-southeast-1-pre

# metaq配置
copilot.metaq.message.topic=copilot-outer-domain-json-topic
copilot.metaq.message.consumerGroup=CID_copilot_outer_domain_json_event
copilot.metaq.message.producerGroup=PID_copilot_outer_domain_json_event
copilot.metaq.message.producerUnitName=aliyun-region-vpc-ap-southeast-1-pre
copilot.metaq.message.producerInstanceName=aliyun-region-vpc-ap-southeast-1-pre
copilot.metaq.message.consumerUnitName=aliyun-region-vpc-ap-southeast-1-pre
copilot.metaq.message.consumerInstanceName=aliyun-region-vpc-ap-southeast-1-pre
inner.metaq.message.consumerUnitName=lazada-sg-2-pre
inner.metaq.message.producerUnitName=lazada-sg-2-pre

# 推广回流 Http 服务
commissionHttpServiceUrlPrefix=https://pre-commission.ai.alibaba-inc.com

# stripe
stripe.webhook.secret=whsec_zxftXz8lWxd1KXwKhOSyXzTtz6TS6Q0p
stripe.api.key=sk_test_51PEB93BUuFYymNc8YYpUAZoTzCdhjqh3iOUYiRuG7t1sjVjoWNloajSiLVddNLqBfg8hlis2eenhkDDTmZzPRSmj00N4YsiQtQ

# redis配置(copilot-enabler应用独享)
redis.instance-id=r-t4n355784db61654
redis.endpoint=r-t4n355784db61654.redis.singapore.rds.aliyuncs.com

# schedulerx2配置
spring.schedulerx2.enabled=true
spring.schedulerx2.domainName=pre-sg-schedulerx2.alibaba-inc.com
spring.schedulerx2.namespace=baeaa0d7-da05-4ab6-9bbc-0ea88ae3601b
spring.schedulerx2.groupId=copilot-enabler
spring.schedulerx2.appKey=2lfw+yg14RG/F7eGR19FXQ==

#Aepay 服务
hsf.aepay.version=1.0.0.aepay
hsf.aepay.unit=

# ip ??HSF
hsf.ae.geoip.unit=aliyun-region-vpc-ap-southeast-1-pre

# ?????
spring.hsf.configserverCenter=aliyun-region-vpc-ap-southeast-1-pre,lazada-sg-2-pre