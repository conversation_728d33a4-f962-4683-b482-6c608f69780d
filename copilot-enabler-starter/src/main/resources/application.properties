### 公共配置
project.name=copilot-enabler

# http服务器端口
server.port=7001
# endpoint配置
management.server.port=7002
# management.endpoints.web.exposure.include=*

# hsf配置，详见 http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-hsf
spring.hsf.group=HSF
spring.hsf.version=1.0.0.DAILY
spring.hsf.timeout=2000

# tddl配置，详见 https://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-tddl
spring.tddl.app=PB_TEST_APP
spring.tddl.sharding=true
# mybatis
mybatis.config-location=classpath:/mybatis/mybatis-config.xml

# tair配置，详见 https://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-tair
spring.tair.user-name=9ed882569b0c433a
spring.tair.dynamic-config=true

# 是否启用swagger
swagger.enable=true

# 日志配置
logging.config=classpath:logback-spring-local.xml
logging.level.org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener=OFF
logging.level.org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider=OFF
logging.level.io.netty.util.internal.PlatformDependent0=OFF
logging.level.springfox.documentation.swagger.readers=OFF
logging.level.springfox.documentation.schema=OFF
logging.level.springfox.documentation.spring.web=OFF

# eventBus配置
copilot.boot.eventbus.enabled=true
copilot.boot.eventbus.message.topic=copilot-outer-domain-topic
copilot.boot.eventbus.message.consumerGroup=CID_copilot_outer_domain_event
copilot.boot.eventbus.message.producerGroup=PID_copilot_outer_domain_event

# metaq配置(json协议)
copilot.metaq.message.topic=copilot-outer-domain-json-topic
copilot.metaq.message.consumerGroup=CID_copilot_outer_domain_json_event
copilot.metaq.message.producerGroup=PID_copilot_outer_domain_json_event
copilot.metaq.message.serializeType=fastjson
inner.metaq.message.consumerUnitName=aliyun-region-vpc-ap-southeast-1

# 登录态
#tbsession.filter.enabled=true
tbsession.filter.url-patterns = /*
tbsession.filter.name=sessionFilter
tbsession.filter.session-config-group=lazada-buyer-session_AI_GLOBAL
tbsession.filter.filter-intercept-url-excludes=/checkpreload.htm,/status.taobao,/taobao.status,/doc.html,/user/*

#tbsession.authorization.enabled=true
tbsession.authorization.order=2
tbsession.authorization.exclude-paths=/checkpreload.htm,/status.taobao,/taobao.status,/doc.html,/user/*
# 登录域名
tbsession.authorization.login-url=https://pre-www.dscopilot.ai/app/aefashion/login/login?redirectURL=
#tbsession.authorization.interceptor-class=com.alibaba.copilot.enabler.service.base.interceptor.CopilotAuthorizationInterceptor

# UIC 注册 MetaQ 消息
spring.metaq.consumers[0].topic=GLOBAL-USER-BIZ-REGISTER-TOPIC
spring.metaq.consumers[0].consumerGroup=CID-AI-COPILOT-ENABLER
spring.metaq.consumers[0].messageListenerRef=globalUserRegisterConsumer
spring.metaq.consumers[0].subExpression=REGISTER

## 支付基建消息监听
# 支付授权结果通知
spring.metaq.consumers[1].topic=aepay_fund_business_payment_notify_topic_PIC_COPILOT
spring.metaq.consumers[1].subExpression=payment_result_tag
spring.metaq.consumers[1].consumerGroup=CID_copilot_enabler_payment_auth
spring.metaq.consumers[1].messageListenerRef=paymentAuthNotifyListener
spring.metaq.consumers[1].consumeMessageBatchMaxSize=1

# 支付请款结果通知
spring.metaq.consumers[2].topic=aepay_fund_business_capture_notify_topic_PIC_COPILOT
spring.metaq.consumers[2].subExpression=capture_result_tag
spring.metaq.consumers[2].consumerGroup=CID_copilot_enabler_payment_capture
spring.metaq.consumers[2].messageListenerRef=paymentCaptureNotifyListener
spring.metaq.consumers[2].consumeMessageBatchMaxSize=1

# 支付退款结果通知
spring.metaq.consumers[3].topic=aepay_fund_business_refund_notify_topic_PIC_COPILOT
spring.metaq.consumers[3].subExpression=refund_result_tag
spring.metaq.consumers[3].consumerGroup=CID_copilot_enabler_payment_refund
spring.metaq.consumers[3].messageListenerRef=paymentRefundNotifyListener
spring.metaq.consumers[3].consumeMessageBatchMaxSize=1

# 支付拒付通知
spring.metaq.consumers[4].topic=aepay_fund_business_dispute_notify_topic_PIC_COPILOT
spring.metaq.consumers[4].subExpression=dispute_result_tag
spring.metaq.consumers[4].consumerGroup=CID_copilot_enabler_payment_dispute
spring.metaq.consumers[4].messageListenerRef=paymentDisputeNotifyListener
spring.metaq.consumers[4].consumeMessageBatchMaxSize=1

# Alipay Auth Message From AEPay
spring.metaq.consumers[5].topic=aepay_fund_business_authorization_notify_topic_PIC_COPILOT
spring.metaq.consumers[5].subExpression=authorization_result_tag
spring.metaq.consumers[5].consumerGroup=CID_copilot_enabler_alipay_auth
spring.metaq.consumers[5].messageListenerRef=alipayAuthNotifyListener
spring.metaq.consumers[5].consumeMessageBatchMaxSize=1

# 支付授权结果通知
spring.metaq.consumers[6].topic=aepay_fund_business_payment_notify_topic_EDGE_SHOP
spring.metaq.consumers[6].subExpression=payment_result_tag
spring.metaq.consumers[6].consumerGroup=CID_copilot_enabler_payment_auth_EDGE_SHOP
spring.metaq.consumers[6].messageListenerRef=paymentAuthNotifyListener
spring.metaq.consumers[6].consumeMessageBatchMaxSize=1

# 支付请款结果通知
spring.metaq.consumers[7].topic=aepay_fund_business_capture_notify_topic_EDGE_SHOP
spring.metaq.consumers[7].subExpression=capture_result_tag
spring.metaq.consumers[7].consumerGroup=CID_copilot_enabler_payment_capture_EDGE_SHOP
spring.metaq.consumers[7].messageListenerRef=paymentCaptureNotifyListener
spring.metaq.consumers[7].consumeMessageBatchMaxSize=1

# 支付退款结果通知
spring.metaq.consumers[8].topic=aepay_fund_business_refund_notify_topic_EDGE_SHOP
spring.metaq.consumers[8].subExpression=refund_result_tag
spring.metaq.consumers[8].consumerGroup=CID_copilot_enabler_payment_refund_EDGE_SHOP
spring.metaq.consumers[8].messageListenerRef=paymentRefundNotifyListener
spring.metaq.consumers[8].consumeMessageBatchMaxSize=1

# 支付拒付通知
spring.metaq.consumers[9].topic=aepay_fund_business_dispute_notify_topic_EDGE_SHOP
spring.metaq.consumers[9].subExpression=dispute_result_tag
spring.metaq.consumers[9].consumerGroup=CID_copilot_enabler_payment_dispute_EDGE_SHOP
spring.metaq.consumers[9].messageListenerRef=paymentDisputeNotifyListener
spring.metaq.consumers[9].consumeMessageBatchMaxSize=1

# schedulerx2配置
spring.schedulerx2.enabled=true
spring.schedulerx2.groupId=copilot-enabler.defaultGroup
spring.schedulerx2.appKey=wmPjc8ZPpotVvRxikn9BBg==
spring.schedulerx2.domainName=schedulerx2.taobao.net
spring.schedulerx2.namespace=system_namespace

# OSS
algorithm_image_oss_service_endpoint=oss-ap-southeast-1.aliyuncs.com
algorithm_image_oss_service_bucket_name=t-selection-algorithms-image
normandy.credential.provider.local.assumed.app=copilot-app

#Aepay 服务
hsf.aepay.version=1.0.0.aepay
hsf.aepay.unit=

# redis配置(copilot-enabler应用独享)
redis.instance-id=r-8vb6f9ebdfbfae44
redis.endpoint=r-8vb6f9ebdfbfae44.redis.zhangbei.rds.aliyuncs.com

global.message.imUnits=
