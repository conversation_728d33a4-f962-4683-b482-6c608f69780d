<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.alibaba.copilot</groupId>
		<artifactId>copilot-enabler</artifactId>
		<version>1.0.39-RELEASE</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<artifactId>copilot-enabler-starter</artifactId>
	<packaging>jar</packaging>
	<name>copilot-enabler-starter</name>

	<dependencies>
		<!-- 依赖子模块 -->
		<dependency>
			<groupId>com.alibaba.copilot</groupId>
			<artifactId>copilot-enabler-service</artifactId>
		</dependency>
		<!-- 中间件 -->
		<dependency>
			<groupId>com.taobao.pandora</groupId>
			<artifactId>pandora-boot-bootstrap</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.pandora</groupId>
			<artifactId>taobao-hsf.sar</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.pandora</groupId>
			<artifactId>pandora-boot-test</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- 二方包 -->
		<!-- 三方包 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-actuator-autoconfigure</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-test</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- FreeMarker依赖 -->
		<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jcl-over-slf4j</artifactId>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>8</source>
					<target>8</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>com.taobao.pandora</groupId>
				<artifactId>pandora-boot-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>pandora-boot-maven-plugin</id>
						<phase>package</phase>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>maven-antrun-plugin</id>
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<tasks>
								<unzip src="${project.build.directory}/${project.build.finalName}.${project.packaging}"
									   dest="${project.build.directory}/copilot-enabler"/>
							</tasks>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
