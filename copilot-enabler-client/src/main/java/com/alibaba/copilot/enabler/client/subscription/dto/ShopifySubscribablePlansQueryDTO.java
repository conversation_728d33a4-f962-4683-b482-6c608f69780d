package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/10/29
 */
@Data
@Accessors(chain = true)
public class ShopifySubscribablePlansQueryDTO implements Serializable {
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * App Code
     */
    private String appCode;

    /**
     * 折扣码 (用来作为推广域标识)
     */
    private String shareCode;
}
