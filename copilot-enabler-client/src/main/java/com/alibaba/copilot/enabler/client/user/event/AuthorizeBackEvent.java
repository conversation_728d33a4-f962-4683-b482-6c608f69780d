package com.alibaba.copilot.enabler.client.user.event;

import lombok.*;

/**
 * @description  shopify店铺安装应用
 * <AUTHOR>
 * @date 2024/1/24
 */
@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AuthorizeBackEvent {
    /**
     * 用户 id (invitee_id)
     */
    private String userId;

    /**
     * 邀请码 (tag)
     */
    private String userCode;

    /**
     * appCode (app)
     */
    private String appCode;

    /**
     * email
     */
    private String email;

    /**
     * 注册时间
     */
    private Long unix;

    //以下是shopify店铺安装应用

    /**
     * shopify显示的域名
     */
    private String shopDomain;
    /**
     * shopifyShopDomain
     */
    private String shopifyShopDomain;
}
