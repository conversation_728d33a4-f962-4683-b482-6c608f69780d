package com.alibaba.copilot.enabler.client.subscription.dto;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/9/28
 */
@Data
@Accessors(chain = true)
public class SubscribePlanResultDTO implements Serializable {

    /**
     * 是否需要支付 (false时, 不包含支付信息)
     */
    private boolean needPay;

    /***
     * 支付请求唯一id
     */
    private String paymentRequestId;

    /**
     * 订单ID
     */
    private Long orderId;

    /***
     * 支付要素信息
     */
    private String paymentSessionData;

    /***
     * 支付会话过期时间
     */
    private Long paymentSessionExpiryTime;

    /***
     * 支付会话id
     */
    private String paymentSessionId;

    /***
     * 支付环境标
     * sandbox：沙箱环境
     * prod：生产环境
     */
    private String environment;

    /**
     * 订阅url
     */
    private String subscriptionUrl;

    /**
     * 订阅支付类型
     *
     * @see SubscriptionPayType
     */
    private String subscriptionPayTypeName;
    /**
     * 客户端密钥（针对 Stripe 支付场景）
     */
    private String clientSecret;
}
