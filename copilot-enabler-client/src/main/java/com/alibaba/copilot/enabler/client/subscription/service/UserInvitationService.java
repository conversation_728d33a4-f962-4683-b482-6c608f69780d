package com.alibaba.copilot.enabler.client.subscription.service;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.dto.SourceRewardDTO;
import com.alibaba.copilot.enabler.client.subscription.enums.UserInvitationSourceType;

/**
 * 用户邀请服务接口
 *
 * <AUTHOR>
 * @date 2025/2/21
 */
public interface UserInvitationService {

    /**
     * 处理用户邀请并发放奖励
     *
     * @param inviterId 邀请人ID
     * @param inviteeId 被邀请人ID
     * @return 处理结果
     */
    SingleResult<Boolean> processInvitationAndReward(Long inviterId, Long inviteeId);

    /**
     * 获取用户邀请统计信息
     *
     * @param userId 用户ID
     * @return 用户的总邀请数
     */
    SingleResult<Integer> getUserInvitationStats(Long userId);

    /**
     * 处理特定来源的福利发放
     * 
     * @param rewardDTO 福利DTO，包含用户ID和奖励项列表
     * @param sourceType 福利来源类型，如Discord欢迎福利等
     * @return 处理结果，true表示成功发放，false表示未发放（可能是已领取过）
     */
    SingleResult<Boolean> processSourceReward(SourceRewardDTO rewardDTO, UserInvitationSourceType sourceType);
} 