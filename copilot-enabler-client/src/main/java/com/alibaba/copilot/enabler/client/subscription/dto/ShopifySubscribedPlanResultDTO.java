package com.alibaba.copilot.enabler.client.subscription.dto;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPlanName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/10/29
 */
@Data
@Accessors(chain = true)
public class ShopifySubscribedPlanResultDTO implements Serializable {
    /**
     * 当前订单Id
     */
    private Long orderId;

    /**
     * 当前planId
     */
    private Long planId;

    /**
     * 当前plan名称
     */
    private String planName;

    /**
     * 是否自动续费
     */
    private Boolean isAutoRenew;

    /**
     * 下次自动续费的时间
     */
    private Date nextRenewTime;

    /**
     * 订单履约开始时间
     */
    private Date performStartTime;

    /**
     * 订单履约结束时间
     */
    private Date performEndTime;

    /**
     * 应用标识
     */
    private String appCode;

    /**
     * 计划价格（原价）
     */
    private BigDecimal price;

    /**
     * @return 判断是否是免费套餐
     */
    public boolean isFree() {
        if (SubscriptionPlanName.FREE.name().equals(planName)) {
            return true;
        }
        return price != null && price.compareTo(BigDecimal.ZERO) <= 0;
    }
}
