package com.alibaba.copilot.enabler.client.subscription.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 来源福利DTO
 * 用于传递来源福利的相关参数
 *
 * <AUTHOR>
 * @date 2025/3/19
 */
public class SourceRewardDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 福利项列表
     */
    private List<RewardItem> rewardItems = new ArrayList<>();

    /**
     * 奖励项
     */
    public static class RewardItem implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 特性类型
         */
        private String featureType;

        /**
         * 奖励数量
         */
        private Integer amount;

        public RewardItem() {
        }

        public RewardItem(String featureType, Integer amount) {
            this.featureType = featureType;
            this.amount = amount;
        }

        public String getFeatureType() {
            return featureType;
        }

        public void setFeatureType(String featureType) {
            this.featureType = featureType;
        }

        public Integer getAmount() {
            return amount;
        }

        public void setAmount(Integer amount) {
            this.amount = amount;
        }
    }

    /**
     * 添加奖励项
     *
     * @param featureType 特性类型
     * @param amount 奖励数量
     * @return this
     */
    public SourceRewardDTO addRewardItem(String featureType, Integer amount) {
        rewardItems.add(new RewardItem(featureType, amount));
        return this;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public List<RewardItem> getRewardItems() {
        return rewardItems;
    }

    public void setRewardItems(List<RewardItem> rewardItems) {
        this.rewardItems = rewardItems;
    }
} 