package com.alibaba.copilot.enabler.client.robot.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2023/11/9
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderDTO extends BaseDTO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 产品唯一code
     */
    private String appCode;

    /**
     * 用户产品关系ID
     */
    private Long userAppRelationId;

    /**
     * 计划ID
     */
    private Long subscriptionPlanId;

    /**
     * 计划名称
     */
    private String subscriptionPlanName;

    /**
     * 下次执行的计划ID
     */
    private Long nextPlanId;

    /**
     * 下次执行的计划名称
     */
    private String nextPlanName;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 折扣tag
     */
    private String subscriptionDiscountTag;

    /**
     * 是否续定（否取消订阅）
     */
    private Boolean autoRenew;

    /**
     * 下次自动续费的时间
     */
    private Date nextRenewalTime;

    /**
     * 是否包含试用期
     */
    private Boolean isIncludeTrial;

    /**
     * 计划价格
     */
    private BigDecimal planPrice;

    /**
     * 真实付款费用
     */
    private BigDecimal actualFee;

    /**
     * 订单履约开始时间
     */
    private Date performStartTime;

    /**
     * 订单履约结束时间
     */
    private Date performEndTime;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 已发起下一次续订
     */
    private Boolean hadNextRenew;

    /**
     * 环境
     */
    private String env;

    /**
     * Shopify订阅ID
     */
    private Long shopifySubscriptionId;

    /**
     * 状态流转原因
     */
    private Map<String, String> statusFlowReason;

    /**
     * 折扣码
     */
    private String shareCode;

    /**
     * 试用期天数
     */
    private Long trialDays;

    /**
     * 套餐天数
     */
    private Long planDays;

    /**
     * 是否已经发送过扣款通知的邮件
     */
    private Boolean whetherSentDeductionNotifyEmail;
}
