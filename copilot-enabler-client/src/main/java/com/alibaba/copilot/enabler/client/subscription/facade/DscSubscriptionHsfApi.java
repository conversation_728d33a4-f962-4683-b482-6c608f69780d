package com.alibaba.copilot.enabler.client.subscription.facade;

import java.util.List;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.dto.DscSubscriptionInfoDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.DscSubscriptionUsageInfoDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.UseSubscriptionFeatureDTO;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2024/1/30
 */
public interface DscSubscriptionHsfApi {
    /**
     * 当前订阅信息
     *
     * @param userId
     * @return
     */
    SingleResult<DscSubscriptionUsageInfoDTO> currentSubscriptionInfo(Long userId);

    /**
     * 扣减权益
     *
     * @param featureLimitDTO
     * @return
     */
    SingleResult<DscSubscriptionInfoDTO> useSubscriptionFeature(UseSubscriptionFeatureDTO featureLimitDTO);

    /**
     * 归还订阅特性
     *
     * @param userId
     * @param featureId
     * @param featureType
     * @return
     */
    SingleResult<Long> returnSubscriptionFeature(Long userId, Long featureId, String featureType);

    /**
     * 获取新订阅第7天的用户id
     *
     * @param
     * @return
     */
    SingleResult<List<Long>> querySubscribeToSeventhDayUsers();

    /**
     * 获取新订阅第14天的用户id
     *
     * @param
     * @return
     */
    SingleResult<List<Long>> querySubscribeToFourteenDayUsers();

    /**
     * 查询未连续订阅距离到期5天的订阅用户
     * @return
     */
    SingleResult<List<Long>> queryRenewSubscribeFiveDayUsers();


    /**
     * 查询退订到期3天后的用户
     * @return
     */
    SingleResult<List<Long>> queryCancelSubscribeThreeDayUsers();
}
