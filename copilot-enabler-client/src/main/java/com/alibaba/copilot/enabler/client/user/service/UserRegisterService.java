package com.alibaba.copilot.enabler.client.user.service;

import com.alibaba.copilot.enabler.client.user.request.SeoRegisterRequest;
import com.alibaba.copilot.enabler.client.user.request.UserAndAppRelationRegisterRequest;
import com.alibaba.copilot.enabler.client.user.request.UserRegisterRequest;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/31
 */
public interface UserRegisterService {

    /**
     * UIC 用户注册
     *
     * @param registerRequest
     */
    void register(UserRegisterRequest registerRequest);

    /**
     * UIC 用户注册 (用作订阅场景)
     *
     * @param registerRequest 注册请求
     */
    void registerForSubscription(UserRegisterRequest registerRequest);

    /**
     * 检查邮箱是否注册过账号
     *
     * @param email
     * @return
     */
    Boolean checkEmailExist(String email);

    /**
     * updateRegisterData
     *
     * @param seoRegisterRequest
     */
    void updateRegisterData(SeoRegisterRequest seoRegisterRequest);

    Boolean registerUserAndAppRelation(UserAndAppRelationRegisterRequest registerRequest);
}
