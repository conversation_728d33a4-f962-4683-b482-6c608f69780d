package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/10/9
 */
@Data
@Accessors(chain = true)
public class UserPaymentCardDTO implements Serializable {

    /**
     * 卡类型
     * （CREDIT、DEBIT）
     */
    private String funding;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 卡token
     */
    private String encryptedCardToken;

    /**
     * 卡品牌
     */
    private String cardBrand;
}
