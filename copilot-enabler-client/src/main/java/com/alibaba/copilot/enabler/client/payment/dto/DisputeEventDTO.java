package com.alibaba.copilot.enabler.client.payment.dto;

import com.alibaba.copilot.boot.event.eventbus.annotation.Event;
import lombok.Data;

import java.io.Serializable;

/**
 * 争议事件
 */
@Data
@Event(id = "disputeEvent")
public class DisputeEventDTO implements Serializable {

    /**
     * 应用Code
     */
    private String appCode;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 交易流水号
     */
    private String tradeNo;
    /**
     * 渠道交易流水号
     */
    private String outTradeNo;

    /**
     * 争议状态
     */
    private String status;

    /**
     * 争议结果
     */
    private String result;

    /**
     * 订单
     */
    private OrderDTO order;

}
