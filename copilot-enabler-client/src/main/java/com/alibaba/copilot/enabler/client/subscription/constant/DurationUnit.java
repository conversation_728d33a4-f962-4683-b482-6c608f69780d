package com.alibaba.copilot.enabler.client.subscription.constant;

import com.alibaba.copilot.boot.basic.enums.IEnum;
import lombok.Getter;

/**
 * 周期单位
 */
@Getter
public enum DurationUnit implements IEnum<DurationUnit> {
    /**
     * 日（天）
     */
    DAY(1),

    /**
     * 月
     */
    MONTH(30),

    /**
     * 年
     */
    YEAR(365);

    private final Integer days;

    DurationUnit(Integer days) {
        this.days = days;
    }
}
