package com.alibaba.copilot.enabler.client.subscription.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SubscribePlanRequest {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * App Code
     */
    @NotNull
    private String appCode;

    /**
     * 计划ID
     */
    @NotNull
    private Long planId;

    /**
     * 折扣码
     */
    private String shareCode;
}
