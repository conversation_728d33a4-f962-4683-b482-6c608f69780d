package com.alibaba.copilot.enabler.client.subscription.dto;

import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/10/26
 */
@Data
@Accessors(chain = true)
public class QueryTradeRecordResultDTO implements Serializable {

    /**
     * 交易流水状态
     */
    private TradeRecordStatus status;
}
