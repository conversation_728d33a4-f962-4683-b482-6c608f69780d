package com.alibaba.copilot.enabler.client.subscription.service;

import java.util.List;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.dto.DscSubscriptionUsageInfoDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyFeatureQuery;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2024/1/30
 */
public interface DscSubscriptionService {
    /**
     * 当前生效的订阅信息
     *
     * @param userId
     * @return
     */
    DscSubscriptionUsageInfoDTO currentSubscriptionInfo(Long userId);

    /**
     * 使用权益
     *
     * @param featureType
     * @return
     */
    void updateSubscriptionFeature(Long userId, String featureType, String planName, Boolean isNew);

    void rollbackFeatureUsage(Long userId, Long featureId, String featureType);

    /**
     * 查询订阅第七天的订阅用户
     * @return
     */
    List<Long> querySubscribeToSeventhDayUsers();

    /**
     * 查询订阅第十四天的订阅用户
     * @return
     */
    List<Long> querySubscribeToFourteenDayUsers();

    /**
     * 查询未连续订阅距离到期5天的订阅用户
     * @return
     */
    List<Long> queryRenewSubscribeFiveDayUsers();

    /**
     * 查询退订到期3天前的用户
     * @return
     */
    List<Long> queryCancelSubscribeThreeDayUsers();
}
