package com.alibaba.copilot.enabler.client.email.dto;

import com.alibaba.copilot.enabler.client.email.annotation.EmailTemplate;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 由于支付超时取消服务的通知邮件: https://gmc.alibaba-inc.com/#/template/create?templateName=PicCopilot_buyer_CancelNotice
 *
 * <AUTHOR>
 * @version 2023/10/24
 */
@Data
@Accessors(chain = true)
@EmailTemplate(id = "56", name = "PicCopilot_buyer_CancelNotice")
public class CancelSubscribeForPayTimeoutEmailDTO implements Serializable {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 计划名称
     */
    private String planName;
}
