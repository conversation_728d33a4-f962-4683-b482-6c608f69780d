package com.alibaba.copilot.enabler.client.payment.constant;

import com.alibaba.copilot.boot.basic.enums.IEnum;

/**
 * 支付状态
 *
 * <AUTHOR>
 * @version 2023/10/8
 */
public enum PaymentStatusEnum implements IEnum<PaymentStatusEnum> {

    /**
     * Indicates that the payment succeeds
     */
    SUCCESS,

    /**
     * Indicates that the payment fails
     */
    FAIL,

    /**
     * Indicates that the payment is under processing
     */
    PROCESSING,

    /**
     * Indicates that the payment is completed. Wait for the final payment result
     */
    PENDING,
    ;

    public static PaymentStatusEnum of(String name) {
        return IEnum.of(PaymentStatusEnum.class, name);
    }
}
