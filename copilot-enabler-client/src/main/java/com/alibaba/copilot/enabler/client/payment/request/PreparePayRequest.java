package com.alibaba.copilot.enabler.client.payment.request;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 支付前置准备Request
 */
@Builder
@Data
public class PreparePayRequest extends PayRequest {
    private String orderId;
    private String userId;
    private BigDecimal amount;
    private String currency;
    private String description;
    private String payMethod;
}