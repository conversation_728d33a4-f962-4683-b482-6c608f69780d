package com.alibaba.copilot.enabler.client.subscription.constant;

import com.alibaba.copilot.boot.basic.enums.IEnum;
import lombok.Getter;

/**
 * 订单状态enum
 */
@Getter
public enum SubscriptionOrderStatus implements IEnum<SubscriptionOrderStatus> {

    /**
     * 待支付（待生效）
     */
    PENDING_PAYMENT("待支付"),

    /**
     * 生效中（完成支付或者协议签署）
     */
    IN_EFFECT("生效中"),

    /**
     * 已取消订阅
     */
    UNSUBSCRIBE("已取消订阅"),

    /**
     * 已取消支付
     */
    PAY_CANCELLED("已取消支付"),

    /**
     * 已完结
     */
    COMPLETED("已完结"),

    /**
     * 已过期（已失效）
     */
    EXPIRED("已失效"),
    ;

    private final String description;

    SubscriptionOrderStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public static  SubscriptionOrderStatus of(String name) {
        // 兼容老数据
        if ("CANCELLED".equals(name)) {
            return PAY_CANCELLED;
        }
        return IEnum.of(SubscriptionOrderStatus.class, name);
    }
}
