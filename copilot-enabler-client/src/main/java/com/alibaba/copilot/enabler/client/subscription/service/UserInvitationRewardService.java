package com.alibaba.copilot.enabler.client.subscription.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.alibaba.copilot.enabler.client.subscription.dto.UserInvitationRewardDTO;

/**
 * 用户邀请奖励服务接口
 *
 * <AUTHOR>
 * @date 2025/2/21
 */
public interface UserInvitationRewardService {

    /**
     * 根据用户ID查询有效的奖励类型列表
     *
     * @param userId 用户ID
     * @param currentTime 当前时间
     * @return 奖励类型列表
     */
    List<String> getValidRewardTypes(Long userId, Date currentTime);

    /**
     * 根据用户ID和奖励类型查询有效的奖励总量
     *
     * @param userId 用户ID
     * @param rewardType 奖励类型
     * @param currentTime 当前时间
     * @return 奖励总量
     */
    Integer getValidRewardAmount(Long userId, String rewardType, Date currentTime);
    
    /**
     * 根据用户ID和奖励类型查询有效的奖励剩余可用量
     *
     * @param userId 用户ID
     * @param rewardType 奖励类型
     * @param currentTime 当前时间
     * @return 奖励剩余可用量
     */
    Integer getValidRemainingAmount(Long userId, String rewardType, Date currentTime);

    /**
     * 根据用户ID和奖励类型查询有效的奖励记录列表
     *
     * @param userId 用户ID
     * @param rewardType 奖励类型
     * @param currentTime 当前时间
     * @return 奖励记录列表
     */
    List<UserInvitationRewardDTO> getValidRewards(Long userId, String rewardType, Date currentTime);

    /**
     * 更新奖励记录
     *
     * @param rewardId 奖励ID
     * @param newAmount 新的奖励额度
     * @return 是否更新成功
     */
    @Deprecated
    boolean updateRewardAmount(Long rewardId, Integer newAmount);
    
    /**
     * 增加奖励使用量
     *
     * @param rewardId 奖励ID
     * @param usageAmount 使用量
     * @return 是否更新成功
     */
    boolean increaseRewardUsage(Long rewardId, Integer usageAmount);
    
    /**
     * 获取奖励的当前使用量
     *
     * @param rewardId 奖励ID
     * @return 当前使用量
     */
    Integer getRewardUsage(Long rewardId);
    
    /**
     * 获取奖励的剩余可用量（总量减去已使用量）
     *
     * @param rewardId 奖励ID
     * @return 剩余可用量
     */
    Integer getRemainingAmount(Long rewardId);
    
    /**
     * 获取用户所有有效的奖励记录（未过期）
     *
     * @param userId 用户ID
     * @param currentTime 当前时间
     * @return 有效的奖励记录列表
     */
    List<UserInvitationRewardDTO> getAllValidRewards(Long userId, Date currentTime);
    
    /**
     * 获取用户特定类型的所有有效奖励记录（未过期）
     *
     * @param userId 用户ID
     * @param featureTypes 特性类型列表
     * @param currentTime 当前时间
     * @return 有效的奖励记录列表
     */
    List<UserInvitationRewardDTO> getAllValidRewardsByTypes(Long userId, List<String> featureTypes, Date currentTime);
} 