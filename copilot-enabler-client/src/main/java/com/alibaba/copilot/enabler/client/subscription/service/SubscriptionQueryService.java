package com.alibaba.copilot.enabler.client.subscription.service;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.dto.*;
import com.alibaba.copilot.enabler.client.subscription.request.*;

import java.util.List;
import java.util.Map;

/**
 * 订阅管理服务 <hr/>
 * 原SubscriptionService类随着业务复杂度的增加，越来越膨胀 <br/>
 * 因此拆分为 {@link SubscriptionQueryService} 和 {@link SubscriptionManageService}，分别提供订阅查询和订阅管理的能力
 */
public interface SubscriptionQueryService {

    /**
     * 订阅中心-用户订阅的App
     *
     * @param query
     * @return
     */
    List<SubscribedAppDTO> getSubscribedAppList(SubscribedAppInfoQuery query);

    /**
     * 订阅计划可选列表
     *
     * @param query
     * @return
     */
    List<SubscribablePlanDTO> getSubscribablePlanList(SubscribablePlanQuery query);

    /**
     * 已选的订阅计划信息
     *
     * @param query
     * @return
     */
    SelectedPlanInfoDTO getSelectedPlanInfo(SelectedPlanInfoQuery query);

    /**
     * 通过支付的交易号查询试用期信息
     *
     * @param payTradeNo 支付的交易号
     * @return 试用期信息
     */
    TrialDurationDTO getTrialDurationInfoByPayTradeNo(String payTradeNo);

    /**
     * 判断订阅指定套餐是否需要付款
     *
     * @param dto 参数信息
     * @return true: 需要付款; false: 无需付款;
     */
    Boolean needPayToSubscribePlan(NeedPayToSubscribePlanDTO dto);

    /**
     * 查询订阅主页的信息 (由于上层业务显示较复杂, 因此单独包装一个Profile接口)
     *
     * @param dto 参数信息
     * @return 订阅主页的信息
     */
    GetSubscribeProfileResultDTO getSubscribeProfile(GetSubscribeProfileDTO dto);

    /**
     * 根据订单ID查询订单信息
     *
     * @param userId
     * @param id
     * @return
     */
    SubscriptionOrderDTO queryOrderById(Long userId, Long id);

    /**
     * 根据外部订阅ID查询订单信息
     *
     * @param userId
     * @param outerSubscriptionId
     * @return
     */
    SubscriptionOrderDTO queryOrderByOuterSubscriptionId(Long userId, String outerSubscriptionId);

    /**
     * 根据用户ID查询订单信息
     *
     * @param userIds    用户ID
     * @param statusList 订单状态
     * @return 订单信息
     */
    Map<Long, SubscriptionOrderDTO> queryOrderByUserIds(List<Long> userIds, List<SubscriptionOrderStatus> statusList);

    /**
     * 根据用户ID查询订单信息
     *
     * @param userIds    用户ID
     * @param statusList 订单状态
     * @return 订单信息
     */
    Map<Long, List<SubscriptionOrderDTO>> queryUserId2OrderByUserIds(String appCode, List<Long> userIds, List<SubscriptionOrderStatus> statusList);

    SubscriptionOrderResult querySubscribeOrder(SubscriptionOrderQueryDTO subscriptionOrderQueryDTO);

    InvoiceResult queryInvoice(InvoiceQuery query);
}
