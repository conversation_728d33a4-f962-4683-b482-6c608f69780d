package com.alibaba.copilot.enabler.client.user.facade;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.user.dto.UserAppRelationDTO;
import com.alibaba.copilot.enabler.client.user.request.AppBindingRequest;
import com.alibaba.copilot.enabler.client.user.request.UserAppRelationQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/11
 */
public interface UserAppRelationHsfApi {
    /**
     * 条件查询用户和指定产品的关系
     *
     * @param userAppRelationQuery
     * @return
     */
    SingleResult<UserAppRelationDTO> queryUserAppRelation(UserAppRelationQuery userAppRelationQuery);

    /**
     * 查询用户产品绑定关系
     *
     * @param userAppRelationQuery
     * @return
     */
    SingleResult<List<UserAppRelationDTO>> queryUserAppRelationList(UserAppRelationQuery userAppRelationQuery);

    /**
     * 账号绑定 app
     *
     * @param userId
     * @param appBindingRequest
     * @return
     */
    SingleResult<UserAppRelationDTO> createUserAppRelation(Long userId, AppBindingRequest appBindingRequest);

    /**
     * updateUserAppRelationById
     *
     * @param id
     * @param userAppRelationDTO
     * @return
     */
    SingleResult<Boolean> updateUserAppRelation(Long id, UserAppRelationDTO userAppRelationDTO);
}
