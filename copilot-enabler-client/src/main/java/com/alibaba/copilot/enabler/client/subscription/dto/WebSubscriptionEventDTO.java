package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 订阅回调dto
 *
 * <AUTHOR>
 * @date 2024/9/24 下午4:42
 */
@Data
public class WebSubscriptionEventDTO {

    private String topic;

    private String systemDomain;

    private String deduplicationId;

    private Date activatedOn;

    private String applicationId;

    private Date billingOn;

    private Date cancelledOn;

    private String cappedAmount;

    private String id;

    private String name;

    private String price;

    private String status;

    private Long storeId;

    private Boolean test;

    private int trialDays;

    private Date trialEndsOn;

    private Date updatedAt;

    private String subscriptionSource;

    private String webhookBody;

    private Map<String, Object> attributes = new HashMap<>();
}
