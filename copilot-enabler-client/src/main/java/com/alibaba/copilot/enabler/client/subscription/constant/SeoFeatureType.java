package com.alibaba.copilot.enabler.client.subscription.constant;


import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum SeoFeatureType {
    UNLIMITED_SEO_DIAGNOSIS("Unlimited SEO diagnosis"),
    VIEW_SITE_WIDE_OPTIMIZATION_ADVICE("View site-wide optimization advice"),
    MANUAL_CONTENT_EDITING("Manual content editing"),
    /**
     * 全托管
     */
    SUPPORT_FULL_MANAGEMENT("Support full management"),

    AUTOMATIC_OPTIMIZATION_FIXING_SEO_TECHNICAL_ISSUES("Automatic optimization & fixing of SEO technical issues"),
    KEYWORD_RECOMMENDATIONS("Keyword recommendations"),

    INTELLIGENT_GENERATION_OPTIMIZATION_SITE_WIDE_IMAGE_ALT_TAGS("Intelligent generation & optimization of site-wide image Alt tags"),
    INTELLIGENT_GENERATION_OPTIMIZATION_SITE_WIDE_TDK("Intelligent generation & optimization of site-wide TDK"),
    INTERNAL_LINK_GENERATION_OPTIMIZATION("Internal link generation & optimization"),

    INTELLIGENT_BLOG_CONTENT("Intelligent generation & optimization of blog content"),
//    INTELLIGENT_GENERATION_OPTIMIZATION_BLOG_CONTENT_20_ARTICLES_DAY("Intelligent generation & optimization of blog content - 20 articles/day"),
//    INTELLIGENT_GENERATION_OPTIMIZATION_BLOG_CONTENT_30_ARTICLES_DAY("Intelligent generation & optimization of blog content - 30 articles/day"),

    KEYWORD_VIEW("Keyword view");
//    KEYWORD_VIEW_1000("Keyword view - 1,000"),
//    KEYWORD_VIEW_FULL("Keyword view - Full");

    private final String serviceDescription;
}
