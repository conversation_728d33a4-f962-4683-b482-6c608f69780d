package com.alibaba.copilot.enabler.client.robot.facade;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.robot.dto.*;

/**
 * 仅提供给内部开发测试&问题排查使用, 业务开发切勿调用
 *
 * <AUTHOR>
 * @version 2023/11/9
 */
public interface RobotHsfApi {

    /**
     * 查询用户信息
     *
     * @param dto 查询信息
     * @return 查询结果
     */
    SingleResult<QueryResultDTO<UserDTO>> queryUser(QueryUserDTO dto);

    /**
     * 查询订单信息
     *
     * @param dto 查询信息
     * @return 查询结果
     */
    SingleResult<QueryResultDTO<OrderDTO>> queryOrder(QueryOrderDTO dto);

    /**
     * 查询流水信息
     *
     * @param dto 查询信息
     * @return 查询结果
     */
    SingleResult<QueryResultDTO<TradeRecordDTO>> queryTradeRecord(QueryTradeRecordDTO dto);

    /**
     * 查询套餐信息
     *
     * @param dto 查询信息
     * @return 查询结果
     */
    SingleResult<QueryResultDTO<PlanDTO>> queryPlan(QueryPlanDTO dto);

    /**
     * 查询用量信息
     *
     * @param dto 查询信息
     * @return 查询结果
     */
    SingleResult<QueryResultDTO<FeatureUsageDTO>> queryFeatureUsage(QueryFeatureUsageDTO dto);
}
