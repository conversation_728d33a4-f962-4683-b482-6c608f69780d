package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2023/10/30
 */
@Data
@Accessors(chain = true)
public class ShopifyAppSubscriptionDTO implements Serializable {

    /**
     * Shopify 订阅 ID
     */
    private String adminGraphqlApiId;

    private String name;

    private String status;

    private String adminGraphqlApiShopId;

    private Date createdAt;

    private Date updatedAt;

    private String currency;

    private double cappedAmount;
}
