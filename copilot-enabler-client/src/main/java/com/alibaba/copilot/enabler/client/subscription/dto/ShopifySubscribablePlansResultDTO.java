package com.alibaba.copilot.enabler.client.subscription.dto;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPlanName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/10/29
 */
@Data
@Accessors(chain = true)
public class ShopifySubscribablePlansResultDTO implements Serializable {
    private List<PlanDTO> list;

    @Data
    @Accessors(chain = true)
    public static class PlanDTO {
        /**
         * app name
         */
        private String appName;

        /**
         * 订阅计划ID
         */
        private Long planId;

        /**
         * 计划周期（按年/按月）
         */
        private String planDurationUnit;

        /**
         * 计划时长
         */
        private Long planDuration;

        /**
         * 计划名称
         */
        private String planName;

        /**
         * 计划描述
         */
        private String planDescription;

        /**
         * 划线价（年度才有，取值为月度X12)
         */
        private BigDecimal crossedPrice;

        /**
         * 折扣信息（折扣数据）
         */
        private BigDecimal discount;

        /**
         * 当前套餐的ID
         */
        private Long currentPlanId;

        /**
         * 当前套餐结束的时间
         */
        private Date currentPlanEndTime;

        /**
         * 计划原价（subscription_plan price字段）
         */
        private BigDecimal originPlanPrice;

        /**
         * 计划折扣价 （考虑推广折扣）
         */
        private BigDecimal discountPlanPrice;

        /**
         * 折扣周期（区别推广域返回的周期）
         */
        private Long discountDuration;

        /**
         * 折扣周期单位
         */
        private String discountDurationUnit;

        /**
         * 折扣结束时间 (可能为空, 为空时表示折扣不限时)
         */
        private Date discountEndTime;

        /**
         * 特性名称集合
         */
        private List<String> featureNames;

        /**
         * @return 判断是否是免费套餐
         */
        public boolean isFree() {
            if (SubscriptionPlanName.FREE.name().equals(planName)) {
                return true;
            }
            return originPlanPrice != null && originPlanPrice.compareTo(BigDecimal.ZERO) <= 0;
        }
    }
}

