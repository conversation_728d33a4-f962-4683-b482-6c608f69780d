package com.alibaba.copilot.enabler.client.user.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/29
 */
@Data
public class AppBindingRequest implements Serializable {
    /**
     * 邮箱
     */
    @NotBlank(message = "must not be null")
    private String email;

    /**
     * 产品服务编码
     */
    @NotBlank(message = "must not be null")
    private String appCode;

    /**
     * 产品服务编码
     */
    @NotBlank(message = "must not be null")
    private String bindSource;
}
