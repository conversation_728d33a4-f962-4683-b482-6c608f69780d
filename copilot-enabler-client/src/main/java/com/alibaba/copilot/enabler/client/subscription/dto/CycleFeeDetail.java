package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
public class CycleFeeDetail {

    /**
     * 时间
     */
    String date;

    /**
     * 周期费用
     */
    BigDecimal cycleFee;

    /**
     * 费用描述
     */
    String feeDescription;

    /**
     * 结算周期
     */
    Long duration;

    /**
     * 周期单位
     */
    String durationUnit;

    /**
     * 折扣描述
     */
    String discountDescription;
}
