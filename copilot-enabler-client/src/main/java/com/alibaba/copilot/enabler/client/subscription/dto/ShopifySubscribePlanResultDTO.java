package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/10/29
 */
@Data
@Accessors(chain = true)
public class ShopifySubscribePlanResultDTO implements Serializable {

    /**
     * 订阅确认 URL
     */
    private String confirmationUrl;
    /**
     * shopifySubscriptionId
     */
    private Long shopifySubscriptionId;
}
