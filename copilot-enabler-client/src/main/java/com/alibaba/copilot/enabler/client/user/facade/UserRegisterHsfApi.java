package com.alibaba.copilot.enabler.client.user.facade;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.user.request.SeoRegisterRequest;
import com.alibaba.copilot.enabler.client.user.request.UserAndAppRelationRegisterRequest;
import com.alibaba.copilot.enabler.client.user.request.UserRegisterRequest;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/11
 */
public interface UserRegisterHsfApi {
    /**
     * 检查邮箱是否注册过账号
     *
     * @param email
     * @return
     */
    SingleResult<Boolean> checkEmailExist(String email);

    /**
     * UIC 用户注册
     *
     * @param registerRequest
     */
    void register(UserRegisterRequest registerRequest);

    /**
     * updateRegisterData
     *
     * @param seoRegisterRequest
     */
    void updateRegisterData(SeoRegisterRequest seoRegisterRequest);

    SingleResult<Boolean> registerUserAndAppRelation(UserAndAppRelationRegisterRequest registerRequest);
}
