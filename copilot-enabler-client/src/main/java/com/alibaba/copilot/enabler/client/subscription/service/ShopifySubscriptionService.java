package com.alibaba.copilot.enabler.client.subscription.service;

import com.alibaba.copilot.enabler.client.subscription.dto.*;

/**
 * <AUTHOR>
 * @version 2023/10/30
 */
public interface ShopifySubscriptionService {

    /**
     * 订阅 (新增或变更)
     *
     * @param dto 订阅信息
     * @return 订阅结果
     */
    ShopifySubscribePlanResultDTO subscribePlan(ShopifySubscribePlanDTO dto);

    /**
     * 查询可用的订阅计划列表
     *
     * @param dto 查询参数
     * @return 查询结果
     */
    ShopifySubscribablePlansResultDTO getSubscribablePlans(ShopifySubscribablePlansQueryDTO dto);

    /**
     * 查询当前的订阅计划
     *
     * @param dto 查询参数
     * @return 查询结果
     */
    ShopifySubscribedPlanResultDTO getSubscribedPlan(ShopifySubscribedPlanQueryDTO dto);

    /**
     * 提供hsf接口给各个app使用，判断当次用户对某个特性的请求是否有资格以及用量
     * @param shopifyFeatureQuery
     * @return
     */
    ShopifyFeatureDTO getFeature(ShopifyFeatureQuery shopifyFeatureQuery);


    /**
     * 类似getFeature接口，提供更多信息
     * @param shopifyFeatureQuery
     * @return
     */
    ShopifyFeatureAllDTO getFeatureAll(ShopifyFeatureQuery shopifyFeatureQuery);

    /**
     * 获取用户待订阅的下一个套餐的特性
     * @param shopifyFeatureQuery
     * @return
     */
    ShopifyPlanFeatureDTO getFeatureMappingNextPlan(ShopifyFeatureQuery shopifyFeatureQuery);

    /**
     * 提供当前的用户的所有特性的资格和用量
     * @param shopifyFeatureQuery
     * @return
     */
    ShopifyPlanFeatureDTO getFeatureMapping(ShopifyFeatureQuery shopifyFeatureQuery);

    /**
     * 每当消耗型特性使用1次后需要写入表 feature_usage ，周期内第一次insert，后面update
     * @param shopifyFeatureQuery
     * @return
     */
    Boolean incFeatureUsage(ShopifyFeatureQuery shopifyFeatureQuery);


    /**
     * 为了防止用户并发请求，权益修改为如果生成失败，就回退权益
     * @param shopifyFeatureQuery
     * @return
     */
    Boolean rollbackFeatureUsage(ShopifyFeatureQuery shopifyFeatureQuery);



    /**
     * 临时使用，刷新用户的权益
     * @param shopifyFeatureQuery
     * @return
     */
    Boolean refreshFeatureUsage(ShopifyFeatureQuery shopifyFeatureQuery);

    /**
     * 查询订阅计划（根据planId）
     * @param shopifySubscribablePlanQueryDTO
     * @return
     */
    ShopifySubscriptionDTO getShopifySubscriptionDTO(ShopifySubscribablePlanQueryDTO shopifySubscribablePlanQueryDTO);


}
