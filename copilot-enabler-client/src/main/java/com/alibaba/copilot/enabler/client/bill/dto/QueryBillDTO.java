package com.alibaba.copilot.enabler.client.bill.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2024/3/21
 */
@Data
@Accessors(chain = true)
public class QueryBillDTO implements Serializable {

    /**
     * 应用标识
     */
    private String appCode;

    private List<String> appCodeList;
    /**
     * 目标日期 (注: 仅取yyyy和MM的部分, 其他部分会被忽略掉)
     */
    private Date targetDate;

    private List<String> payType;

    private List<String> excludePayType;
    /**
     * 支付类型
     * @see com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum
     */
    private String paymentType;
}
