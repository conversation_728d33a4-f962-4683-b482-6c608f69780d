package com.alibaba.copilot.enabler.client.payment.request;

import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 查询交易流水
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class TradeRecordsQuery {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * app code
     */
    private String appCode;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 交易流水号
     */
    private String tradeNo;

    /**
     * 交易方向
     */
    private TradeDirection tradeDirection;

    /**
     * 支付类型（用户主动支付、系统定期代扣）
     */
    private PaymentTypeEnum paymentType;

    /**
     * 状态
     */
    private TradeRecordStatus status;

    /**
     * 是否已发起支付
     */
    private Boolean hadInitiatePay;

    /**
     * 交易流水创建的开始时间
     */
    private Date startCreateTime;

    /**
     * 交易流水创建的结束时间
     */
    private Date endCreateTime;

    /**
     * 环境标识
     */
    private String env;

    /**
     * 外部流水号
     */
    private String outTradeNo;
}
