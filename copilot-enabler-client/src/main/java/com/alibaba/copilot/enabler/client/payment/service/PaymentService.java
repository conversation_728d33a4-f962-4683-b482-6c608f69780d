package com.alibaba.copilot.enabler.client.payment.service;

import com.alibaba.copilot.boot.basic.result.PageResult;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.payment.dto.*;
import com.alibaba.copilot.enabler.client.payment.request.CashierPayRequest;
import com.alibaba.copilot.enabler.client.payment.request.TradeRecordPageQuery;
import com.alibaba.copilot.enabler.client.subscription.dto.QueryTradeRecordDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.QueryTradeRecordResultDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.UserPaymentCardDTO;

import java.io.IOException;
import java.util.List;

/**
 * 订阅服务
 */
public interface PaymentService {

    /**
     * 收银台支付
     * @param request
     * @return
     */
    SingleResult<CashierPayResultDTO> cashierPay(CashierPayRequest request);
    /**
     * 查询交易流水
     *
     * @param orderId
     * @return
     */
    List<PaymentRecordDTO> queryRecordsByOrderId(Long orderId);

    /**
     * 支付结果主动查询
     *
     * @param paymentDTO 支付结果查询
     * @return 支付结果
     */
    SingleResult<InquiryPaymentResultDTO> inquiryPayment(InquiryPaymentDTO paymentDTO);

    /**
     * 退款
     *
     * @param refundDTO 退款请求信息
     * @return 退款结果
     */
    SingleResult<RefundResultDTO> refund(RefundDTO refundDTO);

    /**
     * 查询交易流水分页信息
     *
     * @param query 查询参数
     * @return 分页信息
     */
    PageResult<TradeRecordDetailDTO> queryTradeRecordsByPage(TradeRecordPageQuery query);

    /**
     * 查询交易流水分页信息
     *
     * @param query 查询参数
     * @return 分页信息
     */
    PageResult<TradeRecordDetailDTO> queryTradeRecordsByPageWithStripe(TradeRecordPageQuery query);

    /**
     * 查询交易流水
     *
     * @param dto 参数信息
     * @return 交易流水
     */
    SingleResult<QueryTradeRecordResultDTO> queryTradeRecord(QueryTradeRecordDTO dto);

    /**
     * 查询账单票据
     *
     * @param dto 参数信息
     * @return 账单票据url
     */
    SingleResult<String> queryBillInvoice(QueryTradeRecordDTO dto) throws IOException;
}
