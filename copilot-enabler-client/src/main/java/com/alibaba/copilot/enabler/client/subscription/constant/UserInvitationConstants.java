package com.alibaba.copilot.enabler.client.subscription.constant;

/**
 * 用户邀请相关常量
 *
 * <AUTHOR>
 * @date 2025/3/19
 */
public class UserInvitationConstants {

    /**
     * 来源福利类型
     */
    public static class SourceRewardType {
        /**
         * Discord欢迎福利
         */
        public static final String DISCORD_WELCOME = "DISCORD_WELCOME";
    }
    
    /**
     * 特性类型
     */
    public static class FeatureType {
        /**
         * AI检测额度
         */
        public static final String AI_DETECTION_LIMIT = "AI_DETECTION_LIMIT";
        
        /**
         * 每月总词数
         */
        public static final String TOTAL_WORDS_PER_MONTH = "TOTAL_WORDS_PER_MONTH";
    }
    
    /**
     * Discord欢迎福利数量
     */
    public static class DiscordWelcomeAmount {
        /**
         * AI检测额度（次数）
         */
        public static final int AI_DETECTION_LIMIT = 3;
        
        /**
         * 每月总词数
         */
        public static final int TOTAL_WORDS_PER_MONTH = 500;
    }
    
    /**
     * 奖励有效期（年）
     */
    public static class RewardValidity {
        /**
         * 永久有效（1000年）
         */
        public static final int PERMANENT = 1000;
        
        /**
         * 一年有效期
         */
        public static final int ONE_YEAR = 1;
    }
} 