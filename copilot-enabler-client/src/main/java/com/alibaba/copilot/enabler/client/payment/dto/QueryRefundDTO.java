package com.alibaba.copilot.enabler.client.payment.dto;

import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/10/7
 */
@Data
@Accessors(chain = true)
public class QueryRefundDTO implements Serializable {

    /**
     * 业务身份
     */
    private AppEnum appEnum;

    /**
     * 调用退款后，机构返回的退款id
     */
    private String refundId;

    /**
     * 请求退款时的退款请求id
     */
    private String refundRequestId;
}
