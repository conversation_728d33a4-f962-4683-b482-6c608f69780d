package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;

@Data
public class SubscriptionPreviewResultDTO {
    /**
     * now / periodEnd
     */
    private String payType;
    private String currentBillingCycle;
    private Long immediateCharge;
    private Long nextCompleteBillingCycle;
    private Long currentAmount;
    private Long nextAmount;
    private String currency;
    private String currentInterval;
    private Long currentIntervalCount;
    private String newInterval;
    private Long newIntervalCount;
    private String curPlanName;
    private String newPlanName;

    private Long currentPeriodStart;
    private Long currentPeriodEnd;
}
