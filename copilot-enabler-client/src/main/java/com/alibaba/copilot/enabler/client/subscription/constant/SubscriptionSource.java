package com.alibaba.copilot.enabler.client.subscription.constant;

/**
 * 订阅来源
 *
 * <AUTHOR>
 * @date 2024/9/29 上午11:30
 */
public enum SubscriptionSource {

    /**
     * alpharank网页
     */
    Web,

    /**
     * Shopify
     */
    Shopify,


    /**
     * 店匠
     */
    Shoplazza;


    public static SubscriptionSource fromName(String name) {
        for (SubscriptionSource e : SubscriptionSource.values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e;
            }
        }
        return null;
    }
}
