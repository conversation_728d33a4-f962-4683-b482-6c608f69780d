package com.alibaba.copilot.enabler.client.subscription.callback;

import com.alibaba.copilot.enabler.client.subscription.dto.AuthorizeEventDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyAppUninstalledEventDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscriptionEventDTO;

/**
 * <AUTHOR>
 * @version 2023/10/30
 */
public interface ShopifyCallback {

    /**
     * 订阅更新
     * @param event
     * @param shareCode
     */
    void onSubscriptionEvent(SubscriptionEventDTO event,String shareCode);

    /**
     * App卸载
     *
     * @param event 事件
     */
    void onAppUninstalledEvent(ShopifyAppUninstalledEventDTO event);

    /**
     * App 安装
     * @param event 事件
     */
    void onAuthorizeEvent(AuthorizeEventDTO event);
}
