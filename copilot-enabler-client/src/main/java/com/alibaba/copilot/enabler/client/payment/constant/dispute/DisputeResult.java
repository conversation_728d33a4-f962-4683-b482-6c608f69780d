package com.alibaba.copilot.enabler.client.payment.constant.dispute;

import lombok.Getter;

@Getter
public enum DisputeResult {


    WON("won"),

    LOST("lost"),

    WARNING_CLOSED("warning_closed");

    private final String value;

    DisputeResult(String value) {
        this.value = value;
    }

    public static DisputeResult getByValue(String value) {
        for (DisputeResult disputeResult : DisputeResult.values()) {
            if (disputeResult.getValue().equals(value)) {
                return disputeResult;
            }
        }
        return null;
    }
}
