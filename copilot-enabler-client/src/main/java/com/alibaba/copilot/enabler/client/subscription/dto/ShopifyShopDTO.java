package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2024/01/24
 */
@Data
@Accessors(chain = true)
public class ShopifyShopDTO implements Serializable {

    /**
     *  The ID for the shop. A 64-bit unsigned integer.
     */
    private Long id;
    /**
     * The name of the shop.
     */
    private String name;
    /**
     * The contact email used for communication between Shopify and the shop owner.
     */
    private String email;
    /**
     * The shop'Files domain.
     */
    private String domain;
    private String province;
    private String country;
    private String address1;
    private String zip;
    private String city;
    private String source;
    private String phone;
    private Double latitude;
    private Double longitude;
    private String primaryLocale;
    private String address2;
    private Date createdAt;
    private Date updatedAt;
    private String countryCode;
    private String countryName;
    private String currency;
    private String customerEmail;
    private String timezone;
    private String ianaTimezone;
    private String shopOwner;
    private String moneyFormat;
    private String moneyWithCurrencyFormat;
    private String weightUnit;
    private String provinceCode;
    private Boolean taxesIncluded;
    private Boolean autoConfigureTaxInclusivity;
    private Boolean taxShipping;
    private Boolean countyTaxes;
    private String planDisplayName;
    private String planName;
    private Boolean hasDiscounts;
    private Boolean hasGiftCards;
    private String myshopifyDomain;
    private String googleAppsDomain;
    private Boolean googleAppsLoginEnabled;
    private String moneyInEmailsFormat;
    private String moneyWithCurrencyInEmailsFormat;
    private Boolean eligibleForPayments;
    private Boolean requiresExtraPaymentsAgreement;
    private Boolean passwordEnabled;
    private Boolean hasStorefront;
    private Boolean finances;
    private Long primaryLocationId;
    private Boolean checkoutApiSupported;
    private Boolean multiLocationEnabled;
    private Boolean setupRequired;
    private Boolean preLaunchEnabled;
    private List<String> enabledPresentmentCurrencies;
    private Boolean transactionalSmsDisabled;
    private Boolean marketingSmsConsentEnabledAtCheckout;

}
