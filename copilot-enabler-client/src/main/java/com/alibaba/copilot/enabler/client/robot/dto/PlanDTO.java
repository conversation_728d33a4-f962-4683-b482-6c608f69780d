package com.alibaba.copilot.enabler.client.robot.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2023/11/9
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PlanDTO extends BaseDTO {

    /**
     * 产品唯一code
     */
    private String appCode;

    /**
     * 计划名称
     */
    private String name;

    /**
     * 计划描述
     */
    private String description;

    /**
     * 计划价格（原价）
     */
    private BigDecimal price;

    /**
     * 计划周期
     */
    private Long duration;

    /**
     * 周期单位
     */
    private String durationUnit;

    /**
     * 是否可以试用
     */
    private Boolean isHasTrial;

    /**
     * 试用周期
     */
    private Long trialDuration;

    /**
     * 试用周期单位
     */
    private String trialDurationUnit;

    /**
     * 删除标记
     */
    private Boolean deleted;
}
