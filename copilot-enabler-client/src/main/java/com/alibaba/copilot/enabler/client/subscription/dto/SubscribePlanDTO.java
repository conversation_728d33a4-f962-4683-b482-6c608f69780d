package com.alibaba.copilot.enabler.client.subscription.dto;

import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class SubscribePlanDTO implements Serializable {

    /**
     * 已过时，请设置 paymentMethodName 字段
     * 支付方式
     *
     */
    @Deprecated
    private PaymentMethodEnum paymentMethod;

    /**
     * 支付方式
     */
    private String paymentMethodName;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * App Code
     */
    private String appCode;

    /**
     * 计划ID
     */
    private Long planId;

    /**
     * 付款方式那边的 key
     */
    private String paymentPlanKey;

    /**
     * 不维护订阅计划了。
     */
    private String planName;

    /**
     * 折扣码 (用来作为推广域标识)
     */
    private String shareCode;

    /***
     * 重定向地址URI (用来支付成功后的前端页面重定向)
     */
    private String redirectUrl;

    /***
     * 客户端Ip (用来支付时的风控使用)
     */
    private String clientIp;

    /**
     * 邮箱 (用来给用户发送订阅支付相关的邮件通知)
     */
    private String email;

    /**
     * 用户注册时间 (用来支付时的风控使用)
     */
    private Long userRegistrationTime;

    /**
     * <b style="color:red">弃用, 统一迁移为 {@link #paymentTokenId}</b><hr/>
     * 卡标识 (用来决定前端拉起的收银台是否再次输入卡信息)
     */
    @Deprecated
    private String cardId;

    /**
     * 支付Token标识 (用来决定是否需要输入支付信息)
     */
    private String paymentTokenId;

    /**
     * 折扣码
     */
    private String discountCode;

    /**
     * （已过时，请设置 subscriptionPayTypeName 字段）
     * 订阅支付类型
     * （建议传该字段。若没有传这个字段，使用{@link com.alibaba.copilot.enabler.client.user.constants.AppEnum}的defaultSubscriptionPayType字段）
     */
    @Deprecated
    private SubscriptionPayType subscriptionPayType;

    /**
     * 订阅支付类型
     *
     * @see SubscriptionPayType
     */
    private String subscriptionPayTypeName;

    /**
     * 店铺域名
     */
    private String shopDomain;

    /**
     * 鉴权访问token
     */
    private String accessToken;

    /**
     * 订阅来源
     */
    private String subscriptionSource;

    /**
     * 客户IP地址
     */
    private String userClientIp;

    /**
     * Stripe ClientReferenceId
     */
    private String clientReferenceId;

    /**
     * UiMode-默认
     */
    private String stripeUiMode;
}
