package com.alibaba.copilot.enabler.client.user.constants;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2023/10/16
 */
@Data
@Accessors(chain = true)
public class UserDTO implements Serializable {

    /**
     * 账号 id
     */
    private Long userId;

    /**
     * appCode
     */
    private String appCode;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户数据创建时间 (for db 记录时间)
     */
    private Date gmtCreate;

    /**
     * 用户注册时间
     */
    private Date registerTime;
}
