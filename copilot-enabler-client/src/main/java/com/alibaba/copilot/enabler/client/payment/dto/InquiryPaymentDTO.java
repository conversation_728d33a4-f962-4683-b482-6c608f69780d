package com.alibaba.copilot.enabler.client.payment.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/10/7
 */
@Data
@Accessors(chain = true)
public class InquiryPaymentDTO implements Serializable {

    /**
     * 支付幂等号（商户自行生成）
     */
    String paymentRequestId;

    /**
     * 支付幂等号（支付机构生成）
     */
    String paymentId;

}
