package com.alibaba.copilot.enabler.client.email.dto;

import com.alibaba.copilot.enabler.client.email.annotation.EmailTemplate;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 扣费失败的通知邮件: https://gmc.alibaba-inc.com/#/template/create?templateName=PicCopilot_buyer_Unpaid
 *
 * <AUTHOR>
 * @version 2023/10/24
 */
@Data
@Accessors(chain = true)
@EmailTemplate(id = "54", name = "Pic<PERSON>op<PERSON>t_buyer_Unpaid")
public class DeductFailureEmailDTO implements Serializable {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 计划名称
     */
    private String planName;
}
