package com.alibaba.copilot.enabler.client.subscription.facade;

import com.alibaba.copilot.boot.basic.result.Result;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.robot.dto.TradeRecordDTO;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.dto.*;
import com.alibaba.copilot.enabler.client.subscription.request.*;

import java.util.List;
import java.util.Map;

/**
 * 订阅域提供给Top/MTop的HSF接口声明
 * * 需要使用 {@link Result} 包装返回值
 */
public interface SubscriptionHsfApi {

    /**
     * edgeshop订阅中心-用户订阅的App
     *
     * @param query 查询参数
     * @return
     */
    SingleResult<List<SubscribedAppDTO>> getSubscribedAppList(SubscribedAppInfoQuery query);

    /**
     * 订阅计划可选列表
     *
     * @param query 查询请求
     * @return
     */
    SingleResult<List<SubscribablePlanDTO>> getSubscribablePlanList(SubscribablePlanQuery query);

    /**
     * 已选的订阅计划信息
     *
     * @param query 查询请求
     * @return
     */
    SingleResult<SelectedPlanInfoDTO> getSelectedPlanInfo(SelectedPlanInfoQuery query);

    /**
     * 订阅计划
     *
     * @param subscribePlanDTO 订阅请求信息
     * @return 订阅结果
     */
    SingleResult<SubscribePlanResultDTO> subscribePlan(SubscribePlanDTO subscribePlanDTO);


    SingleResult<SubscriptionPreviewResultDTO> previewSubscribe(SubscribePlanDTO subscribePlanDTO);

    /**
     * 取消已订阅的计划
     *
     * @param request 查询条件
     * @param userId  查询条件
     * @return 是否订阅成功
     */
    SingleResult<Boolean> cancelSubscribedPlan(CancelSubscribedPlanRequest request, Long userId);

    /**
     * 取消当前生效订单的自动续费
     *
     * @param dto 参数信息
     * @return 是否处理成功
     */
    SingleResult<Boolean> cancelAutoRenew(CancelAutoRenewDTO dto);

     /**
     * 通过支付的交易号查询试用期信息
     *
     * @param payTradeNo 支付的交易号
     * @return 试用期信息
     */
    SingleResult<TrialDurationDTO> getTrialDurationInfoByPayTradeNo(String payTradeNo);

    /**
     * 判断订阅指定套餐是否需要付款 (已废弃, 建议直接使用{@link #getSelectedPlanInfo}接口, 更能保证逻辑一致性)
     *
     * @param dto 参数信息
     * @return true: 需要付款; false: 无需付款;
     */
    @Deprecated
    SingleResult<Boolean> needPayToSubscribePlan(NeedPayToSubscribePlanDTO dto);

    /**
     * 查询订阅主页的信息 (由于上层业务显示较复杂, 因此单独包装一个Profile接口)
     *
     * @param dto 参数信息
     * @return 查询订阅主页的信息
     */
    SingleResult<GetSubscribeProfileResultDTO> getSubscribeProfile(GetSubscribeProfileDTO dto);

    /**
     * 根据用户ID查询订单信息
     *
     * @param userIds    用户ID
     * @param statusList 订单状态
     * @return 订单信息
     */
    SingleResult<Map<Long, SubscriptionOrderDTO>> queryOrderByUserIds(List<Long> userIds, List<SubscriptionOrderStatus> statusList);

    /**
     * 根据用户ID查询订单信息
     *
     * @param userIds    用户ID
     * @param statusList 订单状态
     * @return 订单信息
     */
    SingleResult<Map<Long, List<SubscriptionOrderDTO>>> queryUserId2OrderByUserIds(String appCode, List<Long> userIds, List<SubscriptionOrderStatus> statusList);

    SingleResult<SubscriptionOrderResult> querySubscribeOrder(SubscriptionOrderQueryDTO subscriptionOrderQueryDTO);

    /**
     * 创建零元购订单, 给用户创建不需要支付的套餐订阅
     *
     * @param dto 创建信息
     */
    SingleResult<Void> createFreeOrder(CreateFreeOrderDTO dto);

    /**
     * 创建订单
     *
     * @param subscriptionOrderDTO
     * @return
     */
    SingleResult<Long>
    createSubscriptionOrder(SubscriptionOrderDTO subscriptionOrderDTO);

    /**
     * 更新订单
     *
     * @param subscriptionOrderDTO
     * @return
     */
    SingleResult<Boolean> updateSubscriptionOrder(SubscriptionOrderDTO subscriptionOrderDTO);

    /**
     * 处理订阅回调
     *
     * @param webSubscriptionEventDTO
     * @return
     */
    SingleResult<Boolean> handleSubscriptionEvent(WebSubscriptionEventDTO webSubscriptionEventDTO);

    /**
     * 更新订单
     *
     * @param subscriptionOrderDTO
     * @return
     */
    SingleResult<Boolean> switchSubscriptionOrder(SubscriptionOrderDTO subscriptionOrderDTO);

    /**
     * 创建流水
     *
     * @param tradeRecordDTO
     * @return
     */
    SingleResult<Long> createTradeRecord(TradeRecordDTO tradeRecordDTO);

    SingleResult<InvoiceResult> queryInvoice(InvoiceQuery invoiceQuery);

    SingleResult<StripePortalResult> createPortalSession(StripePortalQuery stripePortalQuery);

    SingleResult<CheckoutSessionResult> queryCheckoutSession(String sessionId);

    SingleResult<Boolean> updateBillingCycle(Long orderId);
}
