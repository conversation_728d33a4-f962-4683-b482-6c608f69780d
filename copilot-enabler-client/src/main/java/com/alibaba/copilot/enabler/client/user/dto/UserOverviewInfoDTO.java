package com.alibaba.copilot.enabler.client.user.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserOverviewInfoDTO implements Serializable {
    /**
     * 账户 id
     */
    private Long userId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 推广信息
     */
    private ShareInfo shareInfo;

    /**
     * 是否是推广用户
     */
    private Boolean spreadUser;

    /**
     * 用户使用本产品的推广信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShareInfo {
        /**
         * 推广链接
         */
        private String url;

        /**
         * 推广文案
         */
        private String shareText;

        /**
         * 注册量
         */
        private Integer registered;

        /**
         * 转化量
         */
        private Integer converted;
    }
}
