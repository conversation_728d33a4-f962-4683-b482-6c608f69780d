package com.alibaba.copilot.enabler.client.marketing.constant;

import com.alibaba.copilot.boot.basic.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 折扣规则状态
 *
 * <AUTHOR>
 * @version 2024/2/26
 */
@Getter
@AllArgsConstructor
public enum DiscountRuleStatus implements IEnum<DiscountRuleStatus> {

    /**
     * 启用
     */
    ENABLED,

    /**
     * 禁用
     */
    DISABLED,
    ;

    public static DiscountRuleStatus of(String name) {
        return IEnum.of(DiscountRuleStatus.class, name);
    }
}
