package com.alibaba.copilot.enabler.client.email.dto;

import com.alibaba.copilot.enabler.client.email.annotation.EmailTemplate;

/**
 * 单次订阅的通知邮件: https://gmc.alibaba-inc.com/#/template/create?templateName=PicCopilot_buyer_OnceOrder
 *
 * <AUTHOR>
 * @version 2023/10/24
 */
@EmailTemplate(id = "49", name = "PicCopilot_buyer_OnceOrder")
public class SubscribeOnlyOnceEmailDTO extends BasePaySuccessEmailDTO {

}
