package com.alibaba.copilot.enabler.client.subscription.event;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionType;
import lombok.*;

/**
 * 订阅取消事件
 */
@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SubscriptionCanceledEvent {

    /**
     * 产品唯一code
     */
    private String appCode;

    /**
     * 订阅订单id
     */
    private Long subId;

    /**
     * 取消订阅时间
     */
    private Long unix;

    /**
     * 申请订阅 or 取消订阅
     */
    public SubscriptionType getType() {
        return SubscriptionType.cancel;
    }
}
