package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2024/1/30
 */
@Data
public class DscSubscriptionUsageInfoDTO implements Serializable {
    private Long userId;
    private String appCode;
    private String email;
    private Long subscriptionPlanId;
    private String subscriptionPlanName;
    private String status;
    private List<SubscriptionFeatureUsageInfo> featureInfoList;

    @Data
    public static class SubscriptionFeatureUsageInfo {
        private Long featureId;
        private String appCode;
        private String name;
        private String description;
        private String type;

        /**
         * 是否耗尽
         */
        private Boolean isDepletion;
        /**
         * 原始额度
         */
        private Long quota;

        /**
         * 当前周期内已经使用的次数，周期内使用额度，递增
         */
        private Long usageCount;
    }
}
