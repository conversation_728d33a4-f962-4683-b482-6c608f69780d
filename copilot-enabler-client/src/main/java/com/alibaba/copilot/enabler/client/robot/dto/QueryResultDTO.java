package com.alibaba.copilot.enabler.client.robot.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/11/9
 */
@Data
@Accessors(chain = true)
public class QueryResultDTO<T> {

    /**
     * 查询到的列表数据
     */
    private List<T> list;

    public static <T> QueryResultDTO<T> of(List<T> list) {
        return new QueryResultDTO<T>()
                .setList(list);
    }
}
