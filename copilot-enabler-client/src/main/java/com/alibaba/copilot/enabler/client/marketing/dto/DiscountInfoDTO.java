package com.alibaba.copilot.enabler.client.marketing.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2024/2/27
 */
@Data
@Accessors(chain = true)
public class DiscountInfoDTO implements Serializable {

    /**
     * 应用标识
     */
    private String appCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 套餐ID
     */
    private Long planId;

    /**
     * 折扣规则ID
     */
    private Long discountRuleId;
}
