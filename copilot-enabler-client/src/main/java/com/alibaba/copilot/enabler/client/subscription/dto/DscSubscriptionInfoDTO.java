package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2024/2/1
 */
@Data
public class DscSubscriptionInfoDTO implements Serializable {
    private Long planId;
    private String planName;
    private String featureType;
    private String featureName;
    private Long featureId;
    private String usage;
    private String usageCount;
    private String quota;
    private Boolean isDepletion;
}
