package com.alibaba.copilot.enabler.client.payment.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/9/27
 */
@Data
@Accessors(chain = true)
public class BuyerDTO implements Serializable {

    /**
     * 买家Id (必传)
     */
    private String buyerId;

    /**
     * 买家注册时间 (必传)
     */
    private Long buyerRegistrationTime;
}
