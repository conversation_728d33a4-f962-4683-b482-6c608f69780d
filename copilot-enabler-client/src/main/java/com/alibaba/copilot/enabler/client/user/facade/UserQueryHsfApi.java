package com.alibaba.copilot.enabler.client.user.facade;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.user.constants.UserDTO;
import com.alibaba.copilot.enabler.client.user.dto.UserOverviewInfoDTO;
import com.alibaba.copilot.enabler.client.user.dto.UserSourceDTO;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/11
 */
public interface UserQueryHsfApi {
    /**
     * 用户信息查询
     *
     * @param userId
     * @param appCode
     * @return
     */
    SingleResult<UserOverviewInfoDTO> queryUserOverviewInfo(Long userId, String appCode);

    /**
     * 查询用户信息
     *
     * @param userId  用户ID
     * @param appCode 应用标识
     * @return 用户信息
     */
    SingleResult<UserDTO> queryUserDTO(Long userId, String appCode);

    /**
     * 查询用户来源
     *
     * @param userId
     * @param email
     * @return
     */
    SingleResult<UserSourceDTO> queryUserSource(Long userId, String email);
}
