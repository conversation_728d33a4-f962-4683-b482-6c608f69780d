package com.alibaba.copilot.enabler.client.payment.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/9/27
 */
@Data
@Accessors(chain = true)
public class OrderDTO implements Serializable {

    /**
     * 订单金额 (必传)
     */
    private BigDecimal orderAmount;

    /**
     * 订单号 (必传)
     */
    private String referenceOrderId;

    /**
     * 订单描述 (必传)
     */
    private String orderDescription;

    /**
     * 扩展信息 (选传)
     */
    private String extendInfo;

    /**
     * 买家信息 (必传)
     */
    private BuyerDTO buyer;

    /**
     * 买家信息 (必传)
     */
    private List<GoodsDTO> goodsList;
}
