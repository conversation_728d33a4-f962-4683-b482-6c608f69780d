package com.alibaba.copilot.enabler.client.subscription.dto;

import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2023/10/18
 */
@Data
@Accessors(chain = true)
public class GetSubscribeProfileResultDTO implements Serializable {

    /**
     * 当前订单Id
     */
    private Long orderId;

    /**
     * 当前planId
     */
    private Long planId;

    /**
     * 当前plan名称
     */
    private String planName;

    /**
     * 是否在试用期
     */
    private Boolean isTrial;

    /**
     * 包含的试用期天数
     * （套餐总试用期天数-之前订单已试用的天数）
     */
    private Long includedTrialDay;

    /**
     * 是否自动续费
     */
    private Boolean isAutoRenew;

    /**
     * 下次自动续费的时间
     */
    private Date nextRenewTime;

    /**
     * 订单履约开始时间
     */
    private Date performStartTime;

    /**
     * 订单履约结束时间
     */
    private Date performEndTime;

    /**
     * 计划周期（按年/按月）
     */
    private DurationUnit planDurationUnit;

    /**
     * 计划时长
     */
    private Long planDuration;

    /**
     * 是否支付过
     */
    private Boolean paid;

    /**
     * 是否为零元购订单
     */
    private Boolean isFreeOrder;

    /**
     * 是否有待退款状态的流水
     */
    private Boolean hasRefundRecordWithTodoStatus;

    /**
     * 订阅支付类型
     */
    private SubscriptionPayType subscriptionPayType;

    /**
     * 交易金额
     */
    private BigDecimal actualFee;
}
