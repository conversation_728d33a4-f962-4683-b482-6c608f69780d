package com.alibaba.copilot.enabler.client.user.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/12/18
 */
@Data
@Accessors(chain = true)
public class UserSourceDTO implements Serializable {
    private String firstChannel;
    private String lastChannel;
    private String email;
    private Long userId;
}
