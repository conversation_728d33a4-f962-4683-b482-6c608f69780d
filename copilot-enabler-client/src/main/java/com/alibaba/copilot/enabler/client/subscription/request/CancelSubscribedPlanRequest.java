package com.alibaba.copilot.enabler.client.subscription.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(description = "取消订阅request")
public class CancelSubscribedPlanRequest {

    @ApiModelProperty("App Code")
    @NotBlank
    private String appCode;

    /**
     * 订阅来源
     */
    private String subscriptionSource;

    /**
     * 店铺域名
     */
    private String shopDomain;

    /**
     * 鉴权访问token
     */
    private String accessToken;

    /**
     * 客户IP地址
     */
    private String clientIpAddress;
}
