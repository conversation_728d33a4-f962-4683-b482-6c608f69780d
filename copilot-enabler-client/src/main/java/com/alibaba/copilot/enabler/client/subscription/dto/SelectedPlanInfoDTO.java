package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 待订阅计划详情
 */
@Data
@Accessors(chain = true)
public class SelectedPlanInfoDTO {

    /**
     * 产品名称
     */
    private String appName;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 计划描述
     */
    private String planDescription;

    /**
     * 特性名称集合
     */
    private List<String> featureNames;

    /**
     * 第一期费用
     */
    private BigDecimal currentPayFee;

    /**
     * 周期费用详情
     */
    private List<CycleFeeDetail> cycleFeeDetails;

    /**
     * 计划原价（subscription_plan price字段）
     */
    private BigDecimal originPlanPrice;

    /**
     * 计划周期
     */
    private Long duration;

    /**
     * 周期单位
     */
    private String durationUnit;

    /**
     * 计划折扣价 （考虑推广折扣）
     */
    private BigDecimal discountPlanPrice;

    /**
     * 折扣周期（区别推广域返回的周期）
     */
    private Long discountDuration;

    /**
     * 折扣周期单位
     */
    private String discountDurationUnit;

    /**
     * 信用卡信息
     */
    private List<UserPaymentCardDTO> cardList;

    /**
     * 支付方式和支付TokenId的映射关系
     */
    private Map<String, String> paymentMethodToTokenId;
}
