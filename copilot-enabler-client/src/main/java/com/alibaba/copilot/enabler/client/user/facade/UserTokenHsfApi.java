package com.alibaba.copilot.enabler.client.user.facade;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.payment.dto.UserAccountDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.client.user.dto.UserDetailDTO;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2024/1/7
 */
public interface UserTokenHsfApi {

    /**
     * 生成 Token
     *
     * @param userId
     * @param shopDomain
     * @return
     */
    SingleResult<String> genToken(Long userId, String shopDomain);

    /**
     * 校验 Token 合法性
     *
     * @param token
     * @return
     */
    SingleResult<UserDetailDTO> checkToken(String token);

    /**
     * 生成支付使用的用户令牌
     *
     * @param appEnum 业务身份
     * @param userId  userId
     * @param email  email
     * @return 用户令牌
     */
    SingleResult<String> generatePayUserToken(AppEnum appEnum, Long userId, String email);

    /**
     * 查询支付使用的用户令牌
     *
     * @param appEnum   业务身份
     * @param userToken 用户令牌
     * @return 明文userId
     */
    SingleResult<UserAccountDTO> queryPayUserToken(AppEnum appEnum, String userToken);

    /**
     * 获取用户token
     * @param userId
     * @param appCode
     * @return
     */
    SingleResult<UserAccountDTO> getUserToken( Long userId, String appCode);
}
