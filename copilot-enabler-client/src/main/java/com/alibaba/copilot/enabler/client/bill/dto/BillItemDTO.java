package com.alibaba.copilot.enabler.client.bill.dto;

import com.alibaba.copilot.enabler.client.bill.annotation.BillItemField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2024/3/21
 */
@Data
@Accessors(chain = true)
public class BillItemDTO implements Serializable {

    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 创建时间
     */
    @BillItemField("创建时间")
    @JsonFormat(pattern = DATE_FORMAT)
    private Date createTime;

    /**
     * 账单月份 (注: 该时间不包含[dd HH mm ss SSS]信息)
     */
    @BillItemField("账单月份")
    private String billMonth;

    @BillItemField("支付方式")
    private String paymentMethod;

    /**
     * 用户ID A
     */
    @BillItemField("用户ID")
    private Long userId;


    /**
     * 订单编号 B
     */
    @BillItemField("订单编号")
    private String tradeNo;

    /**
     * 会员档位 C
     */
    @BillItemField("会员档位")
    private String vipLevel;

    /**
     * 主体账号 D
     */
    @BillItemField("主体账号")
    private String ownerAccount;

    /**
     * 货币单位 E
     */
    @BillItemField("货币单位")
    private String currencyUnit;

    /**
     * 交易金额 (含税) F
     */
    @BillItemField("交易金额 (含税)")
    private BigDecimal tradeAmount;

    /**
     * 税率 G
     */
    @BillItemField("税率")
    private BigDecimal taxRate;

    /**
     * 税金 H=F*(1-G)*G
     */
    @BillItemField("税金")
    private BigDecimal taxAmount;

    /**
     * 交易金额 (不含税) I=F-H
     */
    @BillItemField("交易金额 (不含税)")
    private BigDecimal tradeAmountWithoutTax;

    /**
     * 平均每天交易金额 J=I/N
     */
    @BillItemField("平均每天交易金额")
    private BigDecimal amountPerDay;

    /**
     * 交易时间 K
     */
    @BillItemField("交易时间")
    @JsonFormat(pattern = DATE_FORMAT)
    private Date tradeTime;

    /**
     * 会员开始时间 L
     */
    @BillItemField("会员开始时间")
    @JsonFormat(pattern = DATE_FORMAT)
    private Date vipStartTime;

    /**
     * 会员结束时间 M
     */
    @BillItemField("会员结束时间")
    @JsonFormat(pattern = DATE_FORMAT)
    private Date vipEndTime;

    /**
     * 会员天数 N=M-L
     */
    @BillItemField("会员天数")
    private Long vipDays;

    /**
     * 截止本月末, 是否在免费退订期内 O
     */
    @BillItemField("截止本月末, 是否在免费退订期内")
    private Boolean canUnsubscribeAtCurrentMonthEnd;

    /**
     * 免费退订结束时间 P=L+7
     */
    @BillItemField("免费退订结束时间")
    @JsonFormat(pattern = DATE_FORMAT)
    private Date unsubscribeEndTime;

    /**
     * 是否发生了退款行为 Q
     */
    @BillItemField("是否发生了退款行为")
    private Boolean unsubscribed;

    /**
     * 退款时间 (未退款时为空)
     */
    @BillItemField("退款时间")
    @JsonFormat(pattern = DATE_FORMAT)
    private Date unsubscribedTime;

    /**
     * 当月收款金额 (含税) U
     */
    @BillItemField("当月收款金额")
    private BigDecimal totalTradeAmountAtCurrentMonth;

    /**
     * 截止上月末, 累计已使用天数 R
     */
    @BillItemField("截止上月末, 累计已使用天数")
    private Integer usedDaysUntilLastMonthEnd;

    /**
     * 截止本月末, 累计已使用天数 S
     */
    @BillItemField("截止本月末, 累计已使用天数")
    private Integer usedDaysUtilCurrentMonthEnd;

    /**
     * 当月计费天数 T=if(and(O="是",Q="是"),S-R,0)
     */
    @BillItemField("当月计费天数")
    private Integer usedDaysForCurrentMonth;

    /**
     * 截止上月末, 累计已确认收入 (不含税) V=R*J
     */
    @BillItemField("截止上月末, 累计已确认收入")
    private BigDecimal totalTradeAmountUntilLastMonthEnd;

    /**
     * 本月收入 (不含税) W=T*J
     */
    @BillItemField("本月收入 (不含税)")
    private BigDecimal totalTradeAmountCurrentMonth;

    /**
     * 截止本月末, 累计已确认收入 (不含税) X=V+W
     */
    @BillItemField("截止本月末, 累计已确认收入")
    private BigDecimal totalTradeAmountUntilCurrentMonthEnd;

    /**
     * 待确收 Y=I-X
     */
    @BillItemField("待确收")
    private BigDecimal willReceiveAmount;

    @BillItemField("城市")
    private String cityName;

    @BillItemField("国家")
    private String countryName;
}
