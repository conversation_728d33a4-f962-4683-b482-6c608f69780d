package com.alibaba.copilot.enabler.client.subscription.request;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class UpdateOrderStatusRequest {
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * App Code
     */
    private String appCode;

    /**
     * 订单状态更新
     * key：订单ID
     * value：{@link SubscriptionOrderStatus#name()}
     */
    private Map<Long, String> orderStatusMap;
}
