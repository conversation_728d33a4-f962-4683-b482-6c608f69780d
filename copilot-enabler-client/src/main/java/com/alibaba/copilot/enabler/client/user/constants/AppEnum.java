package com.alibaba.copilot.enabler.client.user.constants;

import com.alibaba.copilot.boot.basic.enums.IEnum;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/5
 */
@Getter
@AllArgsConstructor
public enum AppEnum implements IEnum<AppEnum> {

    /**
     * 官网
     */
    EDGE_SHOP_OFFICIAL("edge shop official", "website", SubscriptionPayType.AEPay, AEPayIdentityCodeEnum.EDGE_SHOP),

    /**
     * DsCopilot
     */
    DS_COPILOT("DS Copilot", "website", SubscriptionPayType.Shopify, AEPayIdentityCodeEnum.EDGE_SHOP),

    /**
     * PicCopilot
     */
    PIC_COPILOT("Pic Copilot", "website", SubscriptionPayType.AEPay, AEPayIdentityCodeEnum.PIC_COPILOT),

    /**
     * seo alpharank
     */
    SEO_COPILOT("Seo Copilot", "website", SubscriptionPayType.Shopify, AEPayIdentityCodeEnum.EDGE_SHOP),

    /**
     * seo SEO_COPILOT_SITE
     */
    SEO_COPILOT_SITE("AlphaRank", "website", SubscriptionPayType.AEPay, AEPayIdentityCodeEnum.EDGE_SHOP),

    /**
     * AIB 官网
     */
    AIDGE("Aidge", "website", SubscriptionPayType.STRIPE, null),

    /**
     * text2go.ai
     */
    TEXT2GO("Text2Go", "website", SubscriptionPayType.STRIPE, null),

    ADIC("ADIC", "website", SubscriptionPayType.STRIPE, null),

    ;

    private final String name;
    private final String type;
    /**
     * 缺省情况下默认的订阅支付类型
     * （历史包袱，兼容老逻辑）
     */
    private final SubscriptionPayType defaultSubscriptionPayType;

    /**
     * AE支付对应的钱包code
     */
    private final AEPayIdentityCodeEnum aePayIdentityCode;

    public static AppEnum getAppByCode(String code) {
        if (StringUtils.isBlank(code)) {
            throw new RuntimeException("app enum code illegal!");
        }

        AppEnum appEnum = Arrays.stream(AppEnum.values())
                .filter(item -> item.name().equals(code))
                .findFirst()
                .orElse(null);
        if (appEnum == null) {
            throw new RuntimeException("app enum code illegal!");
        }

        return appEnum;
    }

    public String getCode() {
        return this.name();
    }
}
