package com.alibaba.copilot.enabler.client.payment.dto;

import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 付费记录流水(Query)
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@Slf4j
public class PaymentRecordDTO {

    /**
     * 订单ID
     */
    private Long subscriptionOrderId;


    /**
     * 本流水中交易最终金额
     */
    private BigDecimal tradeAmount;

    /**
     * 交易方向
     */
    private String tradeDirection;

    /**
     * 外部交易号
     */
    private String outTradeNo;

    /**
     * 交易时间
     */
    private Date tradeTime;
}
