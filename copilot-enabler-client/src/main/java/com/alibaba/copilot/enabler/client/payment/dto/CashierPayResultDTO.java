package com.alibaba.copilot.enabler.client.payment.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 发起收银台支付的返回结果，要考虑两种情况：
 * <ul>
 *     <li>支付宝支付：使用 normalUrl 进行跳转</li>
 *     <li>银行卡支付：使用 paymentSessionData等参数唤起前端SDK 进行跳转</li>
 * </ul>
 * <AUTHOR>
 */
@Data
public class CashierPayResultDTO implements Serializable {
    /**
     * The trade number associated with the payment.
     */
    private String tradeNo;

    /**
     * The external trade number associated with the payment.
     */
    private String outTradeNo;

    /**
     * The amount of the payment.
     */
    private BigDecimal paymentAmount;

    /**
     * The timestamp when the payment was created.
     */
    private Long paymentCreateTime;

    /**
     * 这两个参数是用于直接前端对接Web跳转的参数
     */
    /**
     * The URL to redirect to after the payment is processed.
     */
    private String redirectUrl;

    /**
     * The normal URL to redirect to after the payment is processed.
     */
    private String normalUrl;

    /**
     * 下面的参数是针对通过卡支付的情况下，返回用于唤起收银台SDK的参数
     */
    /**
     * The session data for the payment.
     */
    private String paymentSessionData;

    /**
     * The expiry time of the payment session.
     */
    private Long paymentSessionExpiryTime;

    /**
     * The session ID for the payment.
     */
    private String paymentSessionId;

    /**
     * The environment in which the payment was processed.
     */
    private String environment;

    /**
     * 这个参数主要是用在支付渠道是stripe且clientType是WEB_SDK的情况下，前端通过SDK调用
     */
    private String clientSecret;
}
