package com.alibaba.copilot.enabler.client.marketing.constant;

import com.alibaba.copilot.boot.basic.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 折扣规则实体类型
 *
 * <AUTHOR>
 * @version 2024/2/26
 */
@Getter
@AllArgsConstructor
public enum DiscountRuleEntityType implements IEnum<DiscountRuleEntityType> {

    /**
     * 套餐
     */
    PLAN,
    ;

    public static DiscountRuleEntityType of(String name) {
        return IEnum.of(DiscountRuleEntityType.class, name);
    }
}
