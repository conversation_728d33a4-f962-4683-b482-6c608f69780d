package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;

import java.util.Date;

/**
 * 订阅的App信息
 */
@Data
public class SubscribedAppDTO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * App名称
     */
    private String appName;

    /**
     * logo
     */
    private String appLogo;

    /**
     * go to跳转链接
     */
    private String appLink;

    /**
     * 交互图的 chrome plugin
     */
    String appType;

    /**
     * App Code
     */
    private String appCode;

    /**
     * 当前订阅计划
     */
    private String currentPlan;

    /**
     * 订阅计划过期时间
     */
    private Date endTime;

    /**
     * 订阅状态
     */
    private String status;

    /**
     * 用户订阅关系ID
     */
    private Long ordeId;
}
