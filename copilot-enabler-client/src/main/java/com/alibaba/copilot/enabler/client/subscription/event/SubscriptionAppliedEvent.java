package com.alibaba.copilot.enabler.client.subscription.event;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionType;
import lombok.*;

/**
 * 订阅申请事件
 */
@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SubscriptionAppliedEvent {

    /**
     * 产品唯一code
     */
    private String appCode;

    /**
     * 邀请码
     */
    private String userCode;

    /**
     * 受邀者UIC ID
     */
    private String userId;

    /**
     * 受邀者UIC邮箱
     */
    private String email;

    /**
     * 订阅名称
     */
    private String subName;

    /**
     * 订阅周期 30 or 365等
     */
    private Integer subCycle;

    /**
     * 试用周期" 大于0表示试用天数，发起支付之后才有正式单，再发一条消息
     */
    private Integer trialCycle;

    /**
     * 实际支付的费用，最小单位 美分
     */
    private Long actualFee;

    /**
     * 订阅订单id
     */
    private Long subId;

    /**
     * 订阅时间
     */
    private Long unix;

    /**
     * 申请订阅 or 取消订阅
     */
    public SubscriptionType getType() {
        return SubscriptionType.active;
    }
}
