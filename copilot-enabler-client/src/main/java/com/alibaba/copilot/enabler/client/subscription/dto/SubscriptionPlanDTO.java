package com.alibaba.copilot.enabler.client.subscription.dto;

import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2023/10/18
 */
@Data
@Accessors(chain = true)
public class SubscriptionPlanDTO implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 产品唯一code
     */
    private String appCode;

    /**
     * 计划名称
     */
    private String name;

    /**
     * 计划描述
     */
    private String description;

    /**
     * 计划价格（原价）
     */
    private BigDecimal price;

    /**
     * 计划周期
     */
    private Long duration;

    /**
     * 周期单位
     */
    private DurationUnit durationUnit;

    /**
     * 是否可以试用
     */
    private Boolean isHasTrial;

    /**
     * 试用周期
     */
    private Long trialDuration;

    /**
     * 试用周期单位
     */
    private String trialDurationUnit;

    /**
     * 删除标记
     */
    private Boolean deleted;
}
