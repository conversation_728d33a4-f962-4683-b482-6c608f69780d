package com.alibaba.copilot.enabler.client.payment.constant;

import com.alibaba.copilot.boot.basic.enums.IEnum;

/**
 * 交易流水状态enum
 */
public enum TradeRecordStatus implements IEnum<TradeRecordStatus> {

    /**
     * 待支付/待退款
     */
    TODO("待支付/待退款"),

    /**
     * 支付成功/退款成功
     */
    SUCC("支付成功/退款成功"),

    /**
     * 支付失败/退款失败
     */
    FAIL("支付失败/退款失败"),

    /**
     * 已取消
     */
    CANCELLED("已取消");

    private String description;

    TradeRecordStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
