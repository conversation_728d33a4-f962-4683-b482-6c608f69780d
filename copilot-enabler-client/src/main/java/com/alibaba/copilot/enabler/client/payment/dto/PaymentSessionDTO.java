package com.alibaba.copilot.enabler.client.payment.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/9/26
 */
@Data
@Accessors(chain = true)
public class PaymentSessionDTO implements Serializable {

    /***
     * 支付要素信息
     */
    private String paymentSessionData;

    /***
     * 支付会话过期时间
     */
    private Long paymentSessionExpiryTime;

    /***
     * 支付会话id
     */
    private String paymentSessionId;

    /***
     * 支付环境标
     * sandbox：沙箱环境
     * prod：生产环境
     */
    private String environment;
}
