package com.alibaba.copilot.enabler.client.robot.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 2023/11/9
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserDTO extends BaseDTO {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 注册来源
     */
    private String appCode;

    /**
     * 注册渠道
     */
    private String signUpChannel;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 注册时间
     */
    private Date registerTime;
}
