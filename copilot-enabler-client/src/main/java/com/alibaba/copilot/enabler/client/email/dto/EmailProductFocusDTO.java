package com.alibaba.copilot.enabler.client.email.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2024/1/24
 */
@Data
public class EmailProductFocusDTO implements Serializable {
    private String email;
    private String saleCountry;
    private String category;
    private String categoryName;
    private String exclusiveOpportunities;
    private List<ProductsFocusDTO> recommendProductFocusList = new ArrayList<>();
    private List<ProductsFocusDTO> moreProducts = new ArrayList<>();

    private String templateId;
    private String templateName;
    private String templateInstanceId;

    @Data
    public static class ProductsFocusDTO {
        /**
         * itemId
         */
        private String itemId;

        /**
         * 图片url
         */
        private String productMainUrl;

        /**
         * 商品标题
         */
        private String title;

        /**
         * 价格
         */
        private String price;

        /**
         * ds折扣
         */
        private Double dsDiscount;

        /**
         * 总销量
         */
        private String orderTotal;

        /**
         * 14天总销售额
         */
        private String fortnightOrder;

        /**
         * 商品评分
         */
        private String productScore;

        /**
         * Ads 数据分
         */
        private String adsView;

        /**
         * 排序字段
         */
        private Integer sort;
    }
}
