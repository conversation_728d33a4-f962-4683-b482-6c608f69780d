package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户特性奖励数据传输对象
 *
 * <AUTHOR>
 * @date 2025/2/21
 */
@Data
@NoArgsConstructor
public class UserFeatureRewardsDTO implements Serializable {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 查询时间
     */
    private Date queryTime;
    
    /**
     * 各类型奖励额度详情列表
     */
    private List<FeatureRewardDetail> rewardDetails;
    
    /**
     * 特性奖励详情
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FeatureRewardDetail implements Serializable {
        
        /**
         * 特性类型
         */
        private String featureType;
        
        /**
         * 特性名称
         */
        private String featureName;
        
        /**
         * 总额度
         */
        private Long totalQuota;
        
        /**
         * 已使用额度
         */
        private Long usedQuota;
        
        /**
         * 剩余额度
         */
        private Long remainingQuota;
        
        /**
         * 过期时间
         */
        private Date expiryTime;
        
        /**
         * 是否永久有效
         */
        private Boolean isPermanent;
        
        /**
         * 其他属性
         */
        private Map<String, Object> attributes;
    }
} 