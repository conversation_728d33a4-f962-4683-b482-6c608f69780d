package com.alibaba.copilot.enabler.client.email.dto;

import java.io.Serializable;

import com.alibaba.copilot.enabler.client.email.annotation.EmailTemplate;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @ClassName DsCopilotDeductFailureEmailDTO
 * <AUTHOR>
 * @Date 2024/3/7 15:11
 */
@Data
@Accessors(chain = true)
@EmailTemplate(id = "xx", name = "xx")
public class DsCopilotDeductFailureEmailDTO implements Serializable {

    /**
     * 用户名字
     */
    private String userName;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 计划名称
     */
    private String planName;
}
