package com.alibaba.copilot.enabler.client.payment.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2023/10/8
 */
@Data
@Accessors(chain = true)
public class RefundResultDTO implements Serializable {

    /**
     * 支付机构在支付时生成的唯一id，可用于这一笔支付的退款
     */
    private String paymentId;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 调用退款后，机构返回的退款id
     */
    private String refundId;

    /**
     * 退款请求id
     */
    private String refundRequestId;

    /**
     * 退款达到最终成功状态的日期和时间
     */
    private String refundTime;
}
