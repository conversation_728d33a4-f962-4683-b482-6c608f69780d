package com.alibaba.copilot.enabler.client.marketing.dto;

import com.alibaba.copilot.enabler.client.marketing.constant.Language;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribablePlanDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2024/2/27
 */
@Data
@Accessors(chain = true)
public class QueryFirstMonthDiscountDTO implements Serializable {

    /**
     * 应用标识
     */
    private String appCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 语言
     */
    private Language language;

    /**
     * 待查询的套餐列表
     */
    private List<SubscribablePlanDTO> plans;
}
