package com.alibaba.copilot.enabler.client.subscription.service;

import com.alibaba.copilot.enabler.client.robot.dto.TradeRecordDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscriptionOrderDTO;

/**
 * 订阅基础服务
 *
 * <AUTHOR>
 * @date 2024/9/27 上午11:16
 */
public interface SubscriptionService {

    Long createSubscriptionOrderNoPlan(SubscriptionOrderDTO subscriptionOrderDTO);

    /**
     * 创建订单
     *
     * @param subscriptionOrderDTO
     * @return
     */
    Long createSubscriptionOrder(SubscriptionOrderDTO subscriptionOrderDTO);

    /**
     * 更新订单
     *
     * @param subscriptionOrderDTO
     * @return
     */
    Boolean updateSubscriptionOrder(SubscriptionOrderDTO subscriptionOrderDTO);

    /**
     * 创建流水
     *
     * @param tradeRecordDTO
     * @return
     */
    Long createTradeRecord(TradeRecordDTO tradeRecordDTO);
}
