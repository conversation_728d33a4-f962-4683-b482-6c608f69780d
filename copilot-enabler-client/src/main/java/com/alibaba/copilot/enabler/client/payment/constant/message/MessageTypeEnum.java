package com.alibaba.copilot.enabler.client.payment.constant.message;

import com.alibaba.copilot.boot.basic.enums.IEnum;

/**
 * 消息类型enum
 */
public enum MessageTypeEnum implements IEnum<MessageTypeEnum> {

    /**
     * 创建支付会话
     */
    CREATE_PAYMENT_SESSION,

    /**
     * 定期发起代扣
     */
    PERIODIC_INITIATE_DEDUCTION,

    /**
     * 支付授权通知
     */
    PAYMENT_AUTH_NOTIFY,

    /**
     * Alipay授权通知
     */
    ALIPAY_AUTH_NOTIFY,

    /**
     * 支付请款通知
     */
    PAYMENT_CAPTURE_NOTIFY,

    /**
     * 询问请款结果
     */
    INQUIRY_CAPTURE_RESULT,

    /**
     * 退款通知
     */
    PAYMENT_REFUND_NOTIFY,

    /**
     * 询问退款结果
     */
    INQUIRY_REFUND_RESULT,

    /**
     * 拒付通知
     */
    PAYMENT_DISPUTE_NOTIFY,
}
