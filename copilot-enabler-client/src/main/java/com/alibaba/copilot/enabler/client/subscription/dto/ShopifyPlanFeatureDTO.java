package com.alibaba.copilot.enabler.client.subscription.dto;

import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import com.alibaba.copilot.enabler.client.subscription.constant.SeoFeatureType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;


@Data
@Accessors(chain = true)
public class ShopifyPlanFeatureDTO implements Serializable {
    private Map<SeoFeatureType, ShopifyFeatureDTO> featureDTOMap;

//    private SubscribablePlanDTO planDTO;
    private Long planId;

    private ShopifyPlanSimpleDTO planDTO;

    @Data
    @Accessors(chain = true)
    public static class ShopifyPlanSimpleDTO {
        /**
         * id
         */
        private Long id;

        /**
         * 产品唯一code
         */
        private String appCode;

        /**
         * 计划名称
         */
        private String name;

        /**
         * 计划描述
         */
        private String description;

        /**
         * 计划价格（原价）
         */
        private BigDecimal price;

        /**
         * 计划周期
         */
        private Long duration;

        /**
         * 周期单位
         */
        private DurationUnit durationUnit;

        /**
         * 是否可以试用
         */
        private Boolean isHasTrial;

        /**
         * 试用周期
         */
        private Long trialDuration;

        /**
         * 试用周期单位
         */
        private String trialDurationUnit;
    }
}
