package com.alibaba.copilot.enabler.client.subscription.service;

import com.alibaba.copilot.enabler.client.subscription.dto.DscSubscriptionUsageInfoDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyFeatureAllDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyFeatureQuery;
import com.alibaba.copilot.enabler.client.subscription.dto.TrialDurationDTO;

/**
 * @ClassName Text2GoSubscriptionService
 * <AUTHOR>
 * @Date 2025/2/21 14:24
 */
public interface Text2GoSubscriptionService {
    /**
     * 当前生效的订阅信息
     *
     * @param userId
     * @return
     */
    DscSubscriptionUsageInfoDTO currentSubscriptionInfo(Long userId);

    /**
     * 使用权益
     *
     * @param userId user ID
     * @param featureType feature type
     * @param planName plan name
     * @param isNew whether this is a new record
     * @param usageCount usage count
     */
    void updateSubscriptionFeature(Long userId, String featureType, String planName, Boolean isNew, Long usageCount);
    
    /**
     * 使用权益（支持自定义有效期）
     *
     * @param userId user ID
     * @param featureType feature type
     * @param planName plan name
     * @param isNew whether this is a new record
     * @param usageCount usage count
     * @param yearsValid number of years until expiration
     */
    void updateSubscriptionFeature(Long userId, String featureType, String planName, Boolean isNew, Long usageCount, Integer yearsValid);

    /**
     * 归还订阅特性
     * @param userId
     * @param featureId
     * @param featureType
     */
    void rollbackFeatureUsage(Long userId, Long featureId, String featureType, Long usageCount);

    /**
     * 创建带初始配额的订阅特性使用记录
     *
     * @param userId 用户ID
     * @param featureType 特性类型
     * @param planName 计划名称
     * @param isNew 是否为新记录
     * @param usageCount 使用量
     * @param initialQuota 初始配额
     */
    void createSubscriptionFeatureWithQuota(Long userId, String featureType, String planName, Boolean isNew, Long usageCount, Long initialQuota);

    /**
     * 类似getFeature接口，提供更多信息
     * @param shopifyFeatureQuery
     * @return
     */
    ShopifyFeatureAllDTO getFeatureAll(ShopifyFeatureQuery shopifyFeatureQuery);
    
    /**
     * Check if a user is eligible for trial period
     * 
     * @param userId the user ID to check
     * @return trial duration information including eligibility status and remaining trial days
     */
    TrialDurationDTO subscriptionIsTrial(Long userId);
}
