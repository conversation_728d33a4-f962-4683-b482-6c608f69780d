package com.alibaba.copilot.enabler.client.robot.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 2023/11/9
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FeatureUsageDTO extends BaseDTO {

    /**
     * 产品唯一code
     */
    private String appCode;

    /**
     * 用户ID 或 shopifyShopId
     */
    private Long userId;

    /**
     * 特性类型
     */
    private String featureType;

    /**
     * 原始额度，不变
     */
    private Long quota;
    /**
     * 当前周期内已经使用的次数，周期内使用额度，递增
     */
    private Long usageCount;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 删除标记
     */
    private Boolean deleted;
}
