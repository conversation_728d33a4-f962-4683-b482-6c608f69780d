package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/10/17
 */
@Data
@Accessors(chain = true)
public class NeedPayToSubscribePlanDTO implements Serializable {

    /**
     * 应用标识
     */
    private String appCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 目标套餐Id
     */
    private Long planId;
}
