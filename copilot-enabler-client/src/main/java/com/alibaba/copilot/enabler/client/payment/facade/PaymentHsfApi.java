package com.alibaba.copilot.enabler.client.payment.facade;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.payment.dto.CashierPayResultDTO;
import com.alibaba.copilot.enabler.client.payment.request.CashierPayRequest;

/**
 * 支付服务
 *
 * <AUTHOR>
 */
public interface PaymentHsfApi {

    SingleResult<CashierPayResultDTO> cashierPay(CashierPayRequest request);

}
