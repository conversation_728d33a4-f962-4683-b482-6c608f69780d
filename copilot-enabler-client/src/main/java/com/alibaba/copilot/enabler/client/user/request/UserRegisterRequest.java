package com.alibaba.copilot.enabler.client.user.request;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/31
 */
@Data
public class UserRegisterRequest {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date registerTime;

    /**
     * 账号 id
     */
    private Long userId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 注册来源 tag
     */
    private String signUpSource;

    /**
     * 注册渠道
     */
    private String signUpChannel;


    /**
     * 产品名称
     */
    private String appName;

    /**
     * 产品编码
     */
    private String appCode;

    /**
     * 产品类型
     */
    private String appType;

    /**
     * 产品绑定状态
     */
    private String bindStatus;

    /**
     * 产品绑定来源
     */
    private String bindSource;

    /**
     * 推广码
     */
    private String shareCode;

    /**
     * firstChannel
     */
    private String firstChannel;

    /**
     * lastChannel
     */
    private String lastChannel;
}
