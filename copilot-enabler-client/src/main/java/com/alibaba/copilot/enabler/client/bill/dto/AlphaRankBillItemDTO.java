package com.alibaba.copilot.enabler.client.bill.dto;

import com.alibaba.copilot.enabler.client.bill.annotation.BillItemField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class AlphaRankBillItemDTO extends BillItemDTO {
    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @BillItemField("试用期开始时间")
    @JsonFormat(pattern = DATE_FORMAT)
    private Date trialStartTime;

    @BillItemField("试用期结束时间")
    @JsonFormat(pattern = DATE_FORMAT)
    private Date trialEndTime;

    @BillItemField("试用期内是否退款")
    private Boolean unsubscribedInTrial;

    @BillItemField("订单来源")
    private String orderSource;
}
