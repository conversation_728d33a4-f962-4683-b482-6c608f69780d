package com.alibaba.copilot.enabler.client.subscription.dto;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPlanName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 可选择的订阅计划
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class SubscribablePlanDTO implements Comparable<SubscribablePlanDTO> {

    /**
     * app name
     */
    private String appName;

    /**
     * 订阅计划ID
     */
    private Long planId;

    /**
     * 计划周期（按年/按月）
     */
    private String planDurationUnit;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 计划描述
     */
    private String planDescription;

    /**
     * 划线价（年度才有，取值为月度X12)
     */
    private BigDecimal crossedPrice;

    /**
     * 折扣信息（折扣数据）
     */
    private BigDecimal discount;

    /**
     * 当前套餐的ID
     */
    private Long currentPlanId;

    /**
     * 当前套餐结束的时间
     */
    private Date currentPlanEndTime;

    /**
     * 当前套餐结束后的下一笔planId
     */
    private Long nextPlanId;

    /**
     * 当前套餐结束后的下一笔planName
     */
    private String nextPlanName;

    /**
     * 计划原价（subscription_plan price字段）
     */
    private BigDecimal originPlanPrice;

    /**
     * 计划折扣价 （考虑推广折扣）
     */
    private BigDecimal discountPlanPrice;

    /**
     * 折扣周期（区别推广域返回的周期）
     */
    private Long discountDuration;

    /**
     * 折扣周期单位
     */
    private String discountDurationUnit;

    /**
     * 特性名称集合
     */
    private List<String> featureNames;

    /**
     * @return 判断是否是免费套餐
     */
    public boolean isFree() {
        if (SubscriptionPlanName.FREE.name().equals(planName)) {
            return true;
        }
        return originPlanPrice != null && originPlanPrice.compareTo(BigDecimal.ZERO) <= 0;
    }

    /**
     * 这里将排序逻辑内聚到类的内部, 作为该对象的默认排序规则
     */
    @Override
    public int compareTo(SubscribablePlanDTO target) {
        // 先根据免费排序, 免费的在前
        boolean thisIsFree = this.isFree();
        boolean targetIsFree = target.isFree();
        if (thisIsFree ^ targetIsFree) {
            return thisIsFree ? -1 : 1;
        }

        // 都免费或都收费时, 再根据套餐价格排, 价格低的在前
        return this.getOriginPlanPrice().compareTo(target.getOriginPlanPrice());
    }
}
