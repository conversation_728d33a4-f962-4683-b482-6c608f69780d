package com.alibaba.copilot.enabler.client.payment.dto;

import com.alibaba.copilot.enabler.client.payment.constant.PaymentStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/10/7
 */
@Data
@Accessors(chain = true)
public class InquiryPaymentResultDTO implements Serializable {

    /**
     * 支付结果码
     */
    private String paymentResultCode;

    /**
     * 支付结果信息
     */
    private String paymentResultMessage;

    /**
     * 支付幂等号（交易流水号id）
     */
    private String paymentRequestId;

    /**
     * 支付幂等号（支付机构生成）
     */
    private String paymentId;

    /**
     * 支付金额
     */
//    private Money paymentAmount;

    /**
     * 支付创建时间
     */
    private Long paymentCreateTime;

    /**
     * 支付成功时间
     */
    private Long paymentTime;

    /**
     * 支付状态
     */
    private PaymentStatusEnum paymentStatus;

    /**
     * 支付结果附加信息
     */
    private PaymentResultDTO paymentResultInfo;
}
