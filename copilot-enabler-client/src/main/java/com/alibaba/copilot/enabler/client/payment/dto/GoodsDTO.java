package com.alibaba.copilot.enabler.client.payment.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/9/27
 */
@Data
@Accessors(chain = true)
public class GoodsDTO implements Serializable {

    /**
     * 商品Id (必传)
     */
    private String goodsId;

    /**
     * 商品名称 (必传)
     */
    private String goodsName;

    /**
     * stripe 商品描述
     */
    private String description;

    /**
     * Stripe 单价
     */
    private Long unitAmount;

    /**
     * Stripe 数量
     */
    private Long quantity;
}
