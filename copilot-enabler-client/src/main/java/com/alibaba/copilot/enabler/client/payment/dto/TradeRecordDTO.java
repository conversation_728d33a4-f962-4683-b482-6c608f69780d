package com.alibaba.copilot.enabler.client.payment.dto;

import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * 付费记录流水
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@Slf4j
public class TradeRecordDTO {

    /**
     * 订单ID
     */
    private Long subscriptionOrderId;

    /**
     * AppCode
     */
    private String appCode;

    /**
     * 交易金额
     */
    private BigDecimal tradeAmount;

    /**
     * 交易币种
     */
    private String tradeCurrency;

    /**
     * 交易方向
     */
    private String tradeDirection;

    /**
     * 支付方式（Credit Card、Google Pay、Paypal、Alipay）
     */
    private String paymentMethod;

    /**
     * 税费
     */
    private BigDecimal taxAmount;

    /**
     * 税费币种
     */
    private String taxCurrency;

    /**
     * 手续费
     */
    private BigDecimal transactionAmount;

    /**
     * 手续费币种
     */
    private String transactionCurrency;

    /**
     * 是否已发起支付
     * （指是否已请求第三方支付平台）
     */
    private Boolean hadInitiatePay;

    /**
     * 唯一流水id
     */
    private String tradeNo;
}
