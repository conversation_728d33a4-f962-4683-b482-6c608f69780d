package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 试用期信息
 *
 * <AUTHOR>
 * @version 2023/10/12
 */
@Data
@Accessors(chain = true)
public class TrialDurationDTO implements Serializable {

    /**
     * 是否享受试用期
     */
    private Boolean isTrial;

    /**
     * 剩余试用期
     */
    private Long remainTrialDay = 0L;
}
