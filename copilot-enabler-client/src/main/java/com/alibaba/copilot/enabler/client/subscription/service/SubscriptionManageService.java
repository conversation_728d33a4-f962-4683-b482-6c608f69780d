package com.alibaba.copilot.enabler.client.subscription.service;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.dto.*;
import com.alibaba.copilot.enabler.client.subscription.request.CancelSubscribedPlanRequest;

/**
 * 订阅管理服务 <hr/>
 * 原SubscriptionService类随着业务复杂度的增加，越来越膨胀 <br/>
 * 因此拆分为 {@link SubscriptionQueryService} 和 {@link SubscriptionManageService}，分别提供订阅查询和订阅管理的能力
 *
 * <AUTHOR>
 * @version 2024/1/16
 */
public interface SubscriptionManageService {

    /**
     * 订阅计划
     *
     * @param subscribePlanDTO 订阅请求信息
     * @return 订阅结果
     */
    SingleResult<SubscribePlanResultDTO> subscribePlan(SubscribePlanDTO subscribePlanDTO);

    /**
     * 取消已订阅的计划
     *
     * @param request 请求参数
     * @param userId  登录userId
     * @return 是否订阅成功
     */
    Boolean cancelSubscribedPlan(CancelSubscribedPlanRequest request, Long userId);

    /**
     * 更新订单
     *
     * @param subscriptionOrderDTO
     * @return
     */
    Boolean switchSubscriptionOrder(SubscriptionOrderDTO subscriptionOrderDTO);

    /**
     * 处理订阅回调
     *
     * @param webSubscriptionEventDTO
     * @return
     */
    Boolean handleSubscriptionsUpdate(WebSubscriptionEventDTO webSubscriptionEventDTO);

    SingleResult<SubscriptionPreviewResultDTO> previewSubscribe(SubscribePlanDTO subscribePlanDTO);
}
