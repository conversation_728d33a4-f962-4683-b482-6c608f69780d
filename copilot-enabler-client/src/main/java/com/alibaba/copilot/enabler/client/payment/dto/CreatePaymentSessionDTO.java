package com.alibaba.copilot.enabler.client.payment.dto;

import com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum;
import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2023/9/26
 */
@Data
@Accessors(chain = true)
public class CreatePaymentSessionDTO implements Serializable {

    /**
     * 业务身份
     */
    private AppEnum appEnum;

    /**
     * 支付方式 (必传)
     */
    private PaymentMethodEnum paymentMethod;

    /**
     * 支付类型 (必传)
     */
    private PaymentTypeEnum paymentType;

    /***
     * 支付请求唯一id (必传)
     */
    private String paymentRequestId;

    /***
     * 实际支付金额 (必传)
     */
    private BigDecimal actualPayAmount;

    /***
     * 订单信息 (必传)
     */
    private OrderDTO order;

    /***
     * 重定向地址URI (必传)
     */
    private String redirectUrl;

    /***
     * 客户端Ip (必传)
     */
    private String clientIp;

    /**
     * 卡Token (仅用于信用卡支付)
     */
    private String cardToken;

    /**
     * 卡组交易ID (仅用于信用卡支付)
     */
    private String networkTransactionId;

    /**
     * Alipay 授权标识 (仅用于Alipay支付, 未授权时传递)
     */
    private String authState;

    /**
     * Alipay Token (仅用于Alipay支付, 已授权时传递)
     */
    private String alipayToken;
}
