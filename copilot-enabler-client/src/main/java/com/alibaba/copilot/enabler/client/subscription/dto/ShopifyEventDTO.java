package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/10/30
 */
@Data
@Accessors(chain = true)
public class ShopifyEventDTO implements Serializable {

    /**
     * 应用标识
     */
    private String appCode;

    /**
     * app name
     */
    private String appName;
    /**
     * 店铺域名
     */
    private String shopDomain;

    /**
     * access token
     */
    private String accessToken;
}
