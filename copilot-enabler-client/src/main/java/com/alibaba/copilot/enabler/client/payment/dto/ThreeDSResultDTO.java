package com.alibaba.copilot.enabler.client.payment.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/10/7
 */
@Data
@Accessors(chain = true)
public class ThreeDSResultDTO implements Serializable {
    /**
     * The version of 3D Secure protocol. Valid values are:
     *
     * 1.0.2
     * 2.1.0
     * 2.2.0
     * More information about this field:
     *
     * Maximum length: 16 characters
     */
    String threeDSVersion;

    /**
     * Electronic Commerce Indicator (ECI) that is returned by the card scheme. This parameter is used to indicate the type of cardholder identity authentication. Valid values are:
     *
     * 02 or 05: indicates fully authenticated transaction.
     * 01 or 06: indicates attempted authentication transaction.
     * 00 or 07: indicates non-3D Secure transaction.
     * More information about this field:
     *
     * Maximum length: 2 characters
     */
    String eci;

    /**
     * The cardholder authentication value. The value of this parameter can be Cardholder Authentication Verification Value (CAVV) or Authentication Verification Value (AVV).
     *
     * This parameter is returned when the cardholder passes the 3D Secure authentication.
     *
     * More information about this field:
     *
     * Maximum length: 64 characters
     */
    String cavv;

    /**
     * The unique transaction identifier assigned by the Directory Server (DS) for 3D Secure authentication.
     *
     * This parameter is returned for 3D Secure 2.0.
     *
     * More information about this field:
     *
     * Maximum length: 64 characters
     */
    String dsTransactionId;

    /**
     * The unique transaction identifier assigned by the Directory Server (DS) for 3D Secure authentication.
     *
     * This parameter is returned for 3D Secure 1.0.
     *
     * More information about this field:
     *
     * Maximum length: 64 characters
     */
    String xid;
}
