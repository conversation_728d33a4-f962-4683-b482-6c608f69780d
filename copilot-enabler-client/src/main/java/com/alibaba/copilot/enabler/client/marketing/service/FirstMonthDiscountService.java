package com.alibaba.copilot.enabler.client.marketing.service;

import com.alibaba.copilot.enabler.client.marketing.dto.FirstMonthDiscountDTO;
import com.alibaba.copilot.enabler.client.marketing.dto.QueryFirstMonthDiscountDTO;
import com.alibaba.copilot.enabler.client.marketing.dto.RestoreFirstMonthDiscountDTO;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 2024/2/27
 */
public interface FirstMonthDiscountService {

    /**
     * 查询首月优惠
     *
     * @param discountDTO 参数信息
     * @return 查询结果
     */
    Map<Long, FirstMonthDiscountDTO> queryFirstMonthDiscount(QueryFirstMonthDiscountDTO discountDTO);

    /**
     * 恢复并校验折扣码的首月折扣信息
     */
    FirstMonthDiscountDTO restoreAndValidateFirstMonthDiscount(RestoreFirstMonthDiscountDTO dto);
}
