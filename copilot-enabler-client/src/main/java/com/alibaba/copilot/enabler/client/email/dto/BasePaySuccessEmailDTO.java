package com.alibaba.copilot.enabler.client.email.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/10/25
 */
@Data
@Accessors(chain = true)
public class BasePaySuccessEmailDTO implements Serializable {

    /**
     * 下单时间
     */
    private String orderTime;

    /**
     * 实际支付金额
     */
    private String realPay;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 用户名字
     */
    private String userName;
}
