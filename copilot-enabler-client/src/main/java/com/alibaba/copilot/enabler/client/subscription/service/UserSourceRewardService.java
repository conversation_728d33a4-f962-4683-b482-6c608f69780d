package com.alibaba.copilot.enabler.client.subscription.service;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.enums.UserInvitationSourceType;

/**
 * 用户来源福利服务接口
 * 用于管理基于特定来源（如Discord）的福利记录
 *
 * <AUTHOR>
 * @date 2025/3/19
 */
public interface UserSourceRewardService {

    /**
     * 检查用户是否已经领取过某个来源的福利
     *
     * @param userId 用户ID
     * @param sourceType 来源类型
     * @param rewardType 福利类型（可选，null表示检查所有类型）
     * @return true表示已领取过，false表示未领取过
     */
    SingleResult<Boolean> hasReceivedSourceReward(Long userId, UserInvitationSourceType sourceType, String rewardType);

    /**
     * 创建来源福利记录
     *
     * @param userId 用户ID
     * @param sourceType 来源类型
     * @param rewardType 福利类型
     * @param attributes 福利详情（JSON字符串）
     * @return 创建结果
     */
    SingleResult<Boolean> createSourceReward(Long userId, UserInvitationSourceType sourceType, 
                                           String rewardType, String attributes);
} 