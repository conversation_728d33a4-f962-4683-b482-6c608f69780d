package com.alibaba.copilot.enabler.client.email.dto;

import com.alibaba.copilot.enabler.client.email.annotation.EmailTemplate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 扣费失败的通知邮件: https://gmc.alibaba-inc.com/#/template/create?templateName=PicCopilot_buyer_Unpaid
 *
 * <AUTHOR>
 * @version 2024/04/15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@EmailTemplate(id = "144", name = "AlphaRank_Subscription_Successful")
public class SeoDeductSuccessEmailDTO extends BasePaySuccessEmailDTO {


}
