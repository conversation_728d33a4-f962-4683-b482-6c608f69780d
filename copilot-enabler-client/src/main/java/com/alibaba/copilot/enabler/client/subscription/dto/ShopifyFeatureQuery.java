package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * ShopifyFeatureQuery
 */
@Data
public class ShopifyFeatureQuery implements Serializable {

    /**
     * 用户唯一id，可以是userId，也可能是shopifyShopId
     */
    private Long id;
    /**
     * appCode
     */
    private String appCode;
    /**
     * 特性type,同模型subscription_feature中的 type，每个业务需要枚举，一个type可以对应多条subscription_feature记录
     */
    private String featureType;

}
