package com.alibaba.copilot.enabler.client.subscription.dto;

import com.alibaba.copilot.enabler.client.subscription.enums.FeatureUseType;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2024/1/31
 */
@Data
public class UseSubscriptionFeatureDTO implements Serializable {
    private Long userId;
    private String appCode;
    private Long planId;
    private Long featureId;
    private String featureType;
    private String featureName;
    private FeatureUseType useType;
}
