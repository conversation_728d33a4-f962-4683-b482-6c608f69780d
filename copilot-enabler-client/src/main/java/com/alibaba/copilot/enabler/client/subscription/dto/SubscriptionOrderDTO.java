package com.alibaba.copilot.enabler.client.subscription.dto;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2023/12/5
 */
@Data
@Accessors(chain = true)
public class SubscriptionOrderDTO implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 产品唯一code
     */
    private String appCode;

    /**
     * 用户产品关系ID
     */
    private Long userAppRelationId;

    /**
     * 计划ID
     */
    private Long subscriptionPlanId;

    /**
     * 计划名称
     */
    private String subscriptionPlanName;

    /**
     * 下次执行的计划ID
     */
    private Long nextPlanId;

    /**
     * 下次执行的计划名称
     */
    private String nextPlanName;

    /**
     * 订单状态
     */
    private SubscriptionOrderStatus status;

    /**
     * 折扣tag
     */
    private String subscriptionDiscountTag;

    /**
     * 是否续定（否取消订阅）
     */
    private Boolean autoRenew;

    /**
     * 下次自动续费的时间
     */
    private Date nextRenewalTime;

    /**
     * 是否包含试用期
     */
    private Boolean isIncludeTrial;

    /**
     * 计划价格（计划的原价）
     */
    private BigDecimal planPrice;

    /**
     * 真实付款费用
     */
    private BigDecimal actualFee;

    /**
     * 订单履约开始时间
     */
    private Date performStartTime;

    /**
     * 订单履约结束时间
     */
    private Date performEndTime;

    /**
     * 已发起下一次续订
     * (防止生成支付流水任务再起被扫描到)
     */
    private Boolean hadNextRenew;

    /**
     * Shopify订阅ID
     */
    private Long shopifySubscriptionId;

    /**
     * stripeSubscriptionId
     */
    private String stripeSubscriptionId;

    /**
     * 订阅支付类型
     */
    private SubscriptionPayType subscriptionPayType;

    /**
     * 外部订阅ID
     */
    private String outerSubscriptionId;

    /**
     * 是否为计划取消（用于Antom等不主动发送订阅结束通知的支付方式）
     */
    private Boolean scheduledCancel;
}
