package com.alibaba.copilot.enabler.client.subscription.facade;

import com.alibaba.copilot.enabler.client.subscription.dto.AuthorizeEventDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyAppUninstalledEventDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscriptionEventDTO;

/**
 * <AUTHOR>
 * @version 2023/10/30
 */
public interface ShopifyCallbackHsfApi {

    /**
     * 订阅更新   老版，不带shareCode
     *
     * @param event 事件
     */
    void onSubscriptionEvent(SubscriptionEventDTO event);

    /**
     * 订阅更新  新版，带shareCode
     *
     * @param event     事件
     * @param shareCode
     */
    void onSubscriptionEvent(SubscriptionEventDTO event, String shareCode);

    /**
     * App卸载
     *
     * @param event 事件
     */
    void onAppUninstalledEvent(ShopifyAppUninstalledEventDTO event);

    /**
     * app 安装
     *
     * @param event
     */
    void onAuthorizeEvent(AuthorizeEventDTO event);
}
