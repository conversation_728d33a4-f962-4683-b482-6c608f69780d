package com.alibaba.copilot.enabler.client.payment.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/10/7
 */
@Data
@Accessors(chain = true)
public class PaymentResultDTO implements Serializable {
    /**
     * The masked card number, which just shows part of the card number and can be used to display to the user.
     *
     * This parameter is returned when the value of paymentMethodType in the pay (Cashier Payment) API is CARD and the merchant does not have the PCI qualification.
     *
     * More information about this field:
     *
     * Maximum length: 32 characters
     */
    String cardNo;

    /**
     * The card brand, which can be used to display to the user.
     *
     * This parameter is returned when the value of paymentMethodType in the pay (Cashier Payment) API is CARD .
     */
    String cardBrand;

    /**
     * The token of the card, the value of this parameter is used by paymentMethodId in the pay (Cashier Payment) API in subsequent payments.
     *
     * This parameter is returned when the value of paymentMethodType in the pay (Cashier Payment) API is CARD and the merchant does not have the PCI qualification.
     *
     * More information about this field:
     *
     * Maximum length: 128 characters
     */
    String cardToken;

    /**
     * The issuing country of the card. The value of this parameter is a 2-letter country code that follows ISO 3166 Country Codes standard.
     *
     * This parameter is returned when the value of paymentMethodType in the pay (Cashier Payment) API is CARD .
     *
     * More information about this field:
     *
     * Maximum length: 2 characters
     */
    String issuingCountry;

    /**
     * The funding type of the card. Valid values are:
     *
     * CREDIT: indicates a credit card.
     * DEBIT: indicates a debit card.
     * This parameter is returned when the value of paymentMethodType in the pay (Cashier Payment) API is CARD .
     *
     * More information about this field:
     *
     * Maximum length: 6 characters
     */
    String funding;

    /**
     * The region code that represents the country or region of the payment method. The value of this parameter is a 2-letter ISO country code or GLOBAL.
     *
     * This parameter is returned when the value of paymentMethodType in the pay (Cashier Payment) API is CARD .
     *
     * More information about this field:
     *
     * Maximum length: 6 characters
     */
    String paymentMethodRegion;

    /**
     * The result of 3D Secure authentication.
     *
     * This parameter is returned when the value of paymentMethodType in the pay (Cashier Payment) API is CARD and the card authentication type is 3D Secure.
     */
    ThreeDSResultDTO threeDSResultDTO;

    /**
     * The raw AVS result. See AVS result codes to check the valid values.
     *
     * This parameter is returned when the issuing bank passes this information to Alipay.
     *
     * More information about this field:
     *
     * Maximum length: 128 characters
     */
    String avsResultRaw;

    /**
     * The raw Card Verification Value (CVV), Card Security Code (CSC), or Card Verification Code (CVC) result. See CVV result codes to check the valid values.
     *
     * This parameter is returned when the issuing bank passes this information to Alipay.
     *
     * More information about this field:
     *
     * Maximum length: 128 characters
     */
    String cvvResultRaw;

    /**
     * The unique ID assigned by the card scheme to identify a transaction. The value of this parameter is used by the same parameter of pay (Cashier Payment) request in subsequent payments.
     *
     * This parameter is returned when the value of paymentMethodType in the pay (Cashier Payment) API is CARD and 3D Secure or non-3D Secure authentication is successful.
     *
     * More information about this field:
     *
     * Maximum length: 128 characters
     */
    String networkTransactionId;
}
