package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Builder;

import java.util.Date;
import java.util.List;

/**
 * 订阅的单个App信息
 */
@Builder
public class SubscribedAppInfoDTO {

    /**
     * 订阅计划名称
     */
    private String planName;

    /**
     * 订阅状态
     */
    private String status;

    /**
     * 订阅计划过期时间
     */
    private Date endTime;

    /**
     * 用户订阅关系ID
     */
    private Long ordeId;

    /**
     * 计划关联特性信息
     */
    private List<Feature> features;
}
