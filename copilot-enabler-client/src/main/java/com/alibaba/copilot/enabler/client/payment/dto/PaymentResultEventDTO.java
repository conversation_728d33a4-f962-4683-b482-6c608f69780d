package com.alibaba.copilot.enabler.client.payment.dto;

import com.alibaba.copilot.boot.event.eventbus.annotation.Event;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 7/20/24
 *
 * <AUTHOR> href="mailto:<EMAIL>">ouzhencong</a>
 */
@Data
@Event(id = "paymentResultEvent")
public class PaymentResultEventDTO implements Serializable {

    /**
     * 应用标识
     */
    private String appCode;

    /**
     * 交易流水号
     */
    private String tradeNo;
    /**
     * 渠道交易流水号
     */
    private String outTradeNo;
    /**
     * 交易结果
     */
    private String tradeStatus;
    /**
     * 用户Id
     */
    private Long userId;
    /**
     * 交易时间
     */
    private Date tradeTime;
    /**
     * 订单
     */
    private OrderDTO order;

    private Long subscriptionPeriodEnd;

    private Long subscriptionPeriodStart;

    private String billingReason;
}
