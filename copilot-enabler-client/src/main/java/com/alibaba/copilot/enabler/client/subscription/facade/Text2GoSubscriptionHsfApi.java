package com.alibaba.copilot.enabler.client.subscription.facade;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.dto.DscSubscriptionInfoDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.DscSubscriptionUsageInfoDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyFeatureAllDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyFeatureQuery;
import com.alibaba.copilot.enabler.client.subscription.dto.TrialDurationDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.UseSubscriptionFeatureDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.UserFeatureRewardsDTO;
import com.alibaba.copilot.enabler.client.subscription.enums.UserInvitationSourceType;

import java.util.List;
import java.util.Map;

/**
 * @ClassName Text2GoSubscriptionHsfApi
 * <AUTHOR>
 * @Date 2025/2/21 14:08
 */
public interface Text2GoSubscriptionHsfApi {

    /**
     * 当前订阅信息
     *
     * @param userId
     * @return
     */
    SingleResult<DscSubscriptionUsageInfoDTO> currentSubscriptionInfo(Long userId);

    /**
     * 扣减权益
     *
     * @param featureLimitDTO
     * @return
     */
    SingleResult<DscSubscriptionInfoDTO> useSubscriptionFeature(UseSubscriptionFeatureDTO featureLimitDTO, Long usageCount);

    /**
     * 归还订阅特性
     *
     * @param userId
     * @param featureId
     * @param featureType
     * @return
     */
    SingleResult<Long> returnSubscriptionFeature(Long userId, Long featureId, String featureType, Long usageCount);

    /**
     * 类似getFeature接口，提供更多信息
     *
     * @param dto
     * @return
     */
    SingleResult<ShopifyFeatureAllDTO> getFeatureAll(ShopifyFeatureQuery dto);
    
    /**
     * 处理用户邀请并发放奖励
     * 
     * @param inviterId 邀请人用户ID
     * @param inviteeId 被邀请人用户ID
     * @return 处理结果，成功返回true，失败返回false并携带错误信息
     */
    SingleResult<Boolean> processInvitationAndReward(Long inviterId, Long inviteeId);
    
    /**
     * 获取用户邀请统计信息
     * 
     * @param userId 用户ID
     * @return 用户的总邀请数
     */
    SingleResult<Integer> getUserInvitationStats(Long userId);

    /**
     * 获取用户特定类型的奖励额度总数据
     * 
     * @param userId 用户ID
     * @param featureTypes 要查询的特性类型列表，可多选
     * @return 各类型奖励额度的详细信息
     */
    SingleResult<UserFeatureRewardsDTO> getUserFeatureRewards(Long userId, List<String> featureTypes);

    /**
     * 判断当前用户是否享有试用期
     * @param userId 用户ID
     */
    SingleResult<TrialDurationDTO> subscriptionIsTrial(Long userId);

}
