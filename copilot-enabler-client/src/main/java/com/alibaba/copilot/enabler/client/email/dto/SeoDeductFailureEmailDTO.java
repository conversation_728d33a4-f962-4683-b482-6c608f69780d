package com.alibaba.copilot.enabler.client.email.dto;

import com.alibaba.copilot.enabler.client.email.annotation.EmailTemplate;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 扣费失败的通知邮件: https://gmc.alibaba-inc.com/#/template/create?templateName=PicCopilot_buyer_Unpaid
 *
 * <AUTHOR>
 * @version 2024/04/15
 */
@Data
@Accessors(chain = true)
@EmailTemplate(id = "145", name = "AlphaRank_Payment_Failed")
public class SeoDeductFailureEmailDTO implements Serializable {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 计划名称
     */
    private String planName;
}
