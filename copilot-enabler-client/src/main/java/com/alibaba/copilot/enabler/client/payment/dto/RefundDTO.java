package com.alibaba.copilot.enabler.client.payment.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2023/10/8
 */
@Data
@Accessors(chain = true)
public class RefundDTO implements Serializable {

    /**
     * App code 业务身份
     */
    private String appCode;

    /**
     * 支付机构在支付时生成的唯一id，可用于这一笔支付的退款
     */
    private String paymentId;

    /**
     * 退款请求id
     */
    private String refundRequestId;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款原因，可为空
     */
    private String refundReason;

    /**
     * 退款id，由上游自己生成，可为空
     */
    private String referenceRefundId;

    /**
     * 退款通知url，可为空
     */
    private String refundNotifyUrl;
}
