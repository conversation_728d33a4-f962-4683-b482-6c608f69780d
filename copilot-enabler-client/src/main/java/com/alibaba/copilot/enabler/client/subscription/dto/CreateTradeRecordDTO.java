package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class CreateTradeRecordDTO implements Serializable {

    /**
     * 支付方式
     * 目前支持PIC_REDEEM_CODE
     * @see com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum#PIC_REDEEM_CODE
     */
    private String paymentMethod;

    /**
     * 外部交易号
     */
    private String outTradeNo;

}
