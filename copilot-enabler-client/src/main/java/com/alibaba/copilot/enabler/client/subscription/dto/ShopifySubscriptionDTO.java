package com.alibaba.copilot.enabler.client.subscription.dto;

import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ShopifySubscriptionDTO 返回
 */
@Data
public class ShopifySubscriptionDTO implements Serializable {

    /**
     * planId
     */
    private Long planId;

    /**
     * 产品唯一code
     */
    private String appCode;

    /**
     * 计划名称
     */
    private String name;

    /**
     * 计划价格（原价）
     */
    private BigDecimal price;

    /**
     * 计划周期
     */
    private Long duration;

    /**
     * 周期单位
     */
    private DurationUnit durationUnit;

}
