package com.alibaba.copilot.enabler.client.payment.request;


import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.payment.dto.OrderDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 收银台支付Request
 *
 * <AUTHOR>
 */
@Data
public class CashierPayRequest implements Serializable {

    /**
     * 业务身份
     *
     * @see AppEnum
     */
    private String appCode;

    private Long userId;

    private OrderDTO order;

    /**
     * 支付方式
     *
     * @see com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum
     */
    private String paymentMethod;
    private BigDecimal paymentAmount;
    private String paymentRedirectUrl;

    /**
     * 下面的参数在卡支付时必传
     */
    private String cardId;

    private String clientIp;

    /**
     * 调用方客户端类型：
     *
     * @see com.alibaba.copilot.enabler.client.payment.constant.ClientType
     */
    private String clientType;

    /**
     * 客户IP地址
     */
    private String userClientIp;

    /**
     * Stripe ClientReferenceId
     */
    private String clientReferenceId;

    /**
     * 回调接口透传字段
     */
    private Map<String, String> metadata = new HashMap<>();

    public void validateOrder() {
        Assertor.assertNonNull(getOrder(), "order is null");
        Assertor.assertNonNull(getOrder().getReferenceOrderId(), "order.referenceOrderId is null");
        Assertor.assertNonNull(getOrder().getOrderAmount(), "order.orderAmount is null");
        Assertor.assertNonNull(getOrder().getOrderDescription(), "order.orderDescription is null");

        Assertor.assertNonNull(getOrder().getBuyer(), "order.buyer is null");
        Assertor.assertNonNull(getOrder().getGoodsList(), "order.goodsList is null");
    }
}
