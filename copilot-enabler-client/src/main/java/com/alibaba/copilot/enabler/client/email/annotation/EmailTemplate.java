package com.alibaba.copilot.enabler.client.email.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 邮件模板, 对应 https://gmc.alibaba-inc.com/#/template 平台配置的模板信息
 *
 * <AUTHOR>
 * @version 2023/10/22
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface EmailTemplate {

    /**
     * @return 模板ID
     */
    String id();

    /**
     * @return 模板名称
     */
    String name();

    /**
     * @return 实例Id (可空, 默认与{@link #name()}相等)
     */
    String instanceId() default "";
}
