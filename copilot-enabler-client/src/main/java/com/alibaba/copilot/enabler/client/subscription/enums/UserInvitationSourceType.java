package com.alibaba.copilot.enabler.client.subscription.enums;

import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPayType;
import com.alibaba.copilot.enabler.client.user.constants.AEPayIdentityCodeEnum;

/**
 * 用户邀请来源类型枚举
 *
 * <AUTHOR>
 * @date 2025/3/19 11:23
 */
public enum UserInvitationSourceType {

    /**
     * Discord欢迎福利
     */
    DISCORD_WELCOME(900000000000001L, "Discord欢迎福利", true),

    /**
     * 正常邀请
     */
    NORMAL_INVITATION(0L, "正常用户邀请", false);

    /**
     * 伪用户ID，用于标识和区分不同来源
     */
    private final Long userId;

    /**
     * 来源描述
     */
    private final String description;
    
    /**
     * 是否为一次性福利
     * true表示同一用户只能从该来源领取一次福利
     * false表示可以多次领取
     */
    private final boolean isOneTimeReward;

    UserInvitationSourceType(Long userId, String description) {
        this(userId, description, false);
    }
    
    UserInvitationSourceType(Long userId, String description, boolean isOneTimeReward) {
        this.userId = userId;
        this.description = description;
        this.isOneTimeReward = isOneTimeReward;
    }

    public Long getUserId() {
        return userId;
    }

    public String getDescription() {
        return description;
    }
    
    public boolean isOneTimeReward() {
        return isOneTimeReward;
    }

    /**
     * 根据用户ID查找对应的邀请来源类型
     * 
     * @param userId 用户ID
     * @return 邀请来源类型，如果不匹配则返回NORMAL_INVITATION
     */
    public static UserInvitationSourceType getByUserId(Long userId) {
        if (userId == null) {
            return NORMAL_INVITATION;
        }
        
        for (UserInvitationSourceType type : values()) {
            if (type.getUserId().equals(userId)) {
                return type;
            }
        }
        
        return NORMAL_INVITATION;
    }
}
