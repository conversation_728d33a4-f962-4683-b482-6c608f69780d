package com.alibaba.copilot.enabler.client.payment.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Map;

/**
 * .
 *
 * <AUTHOR>
 * @date 2025/03/10
 */
@Data
@Accessors(chain = true)
public class UserPaymentInfoResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String appCode;

    private Long userId;

    /**
     * 是否有 Payment Token
     *
     * Map key 为
     * @link com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum#value
     */
    private Map<String, Boolean> hasPaymentTokenMap;

}