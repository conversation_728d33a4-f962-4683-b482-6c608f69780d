package com.alibaba.copilot.enabler.client.email.dto;

import com.alibaba.copilot.enabler.client.email.annotation.EmailTemplate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 续费成功的通知邮件
 *
 * <AUTHOR>
 * @version 2024/06/26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@EmailTemplate(id = "159", name = "AlphaRank-Subscription-Renew")
public class SeoRenewSuccessEmailDTO extends BasePaySuccessEmailDTO {


}
