package com.alibaba.copilot.enabler.client.payment.request;

import com.alibaba.copilot.boot.basic.request.PageQuery;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 交易流水的分页查询请求
 *
 * <AUTHOR>
 * @version 2023/10/17
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class TradeRecordPageQuery extends PageQuery {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 支付状态
     */
    private List<TradeRecordStatus> statusList;

    /**
     * 业务身份
     */
    private AppEnum appEnum;
}
