package com.alibaba.copilot.enabler.client.user.service;

import com.alibaba.copilot.enabler.client.user.constants.UserDTO;
import com.alibaba.copilot.enabler.client.user.dto.UserOverviewInfoDTO;
import com.alibaba.copilot.enabler.client.user.dto.UserSourceDTO;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/4
 */
public interface UserQueryService {
    /**
     * 用户信息查询
     *
     * @param userId
     * @param appCode
     * @return
     */
    UserOverviewInfoDTO queryUserOverviewInfo(Long userId, String appCode);

    /**
     * 查询用户信息
     *
     * @param userId  用户ID
     * @param appCode 应用标识
     * @return 用户信息
     */
    UserDTO queryUserDTO(Long userId, String appCode);

    /**
     * 查询用户来源信息
     *
     * @param userId
     * @param email
     * @return
     */
    UserSourceDTO queryUserSource(Long userId, String email);
}
