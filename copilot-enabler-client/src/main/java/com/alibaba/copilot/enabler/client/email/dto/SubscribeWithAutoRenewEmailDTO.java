package com.alibaba.copilot.enabler.client.email.dto;

import com.alibaba.copilot.enabler.client.email.annotation.EmailTemplate;

/**
 * 连续订阅的通知邮件: https://gmc.alibaba-inc.com/#/template/create?templateName=PicCopilot_buyer_Autorenew
 *
 * <AUTHOR>
 * @version 2023/10/24
 */
@EmailTemplate(id = "52", name = "PicCopilot_buyer_Autorenew")
public class SubscribeWithAutoRenewEmailDTO extends BasePaySuccessEmailDTO {

}
