package com.alibaba.copilot.enabler.client.payment.constant.dispute;

import lombok.Getter;

@Getter
public enum DisputeStatus {


    CREATED("CREATED"),
    CLOSED("CLOSED"),
    ;

    private final String value;

    DisputeStatus(String value) {
        this.value = value;
    }

    public static DisputeResult getByValue(String value) {
        for (DisputeResult disputeResult : DisputeResult.values()) {
            if (disputeResult.getValue().equals(value)) {
                return disputeResult;
            }
        }
        return null;
    }
}
