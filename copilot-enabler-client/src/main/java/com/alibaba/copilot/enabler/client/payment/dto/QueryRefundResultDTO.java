package com.alibaba.copilot.enabler.client.payment.dto;

import com.alibaba.copilot.enabler.client.payment.constant.RefundStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2023/10/7
 */
@Data
@Accessors(chain = true)
public class QueryRefundResultDTO implements Serializable {

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 调用退款后，机构返回的退款id
     */
    private String refundId;

    /**
     * 请求退款时的退款请求id
     */
    private String refundRequestId;

    /**
     * 退款状态
     * SUCCESS: Indicates the refund succeeded.
     * PROCESSING: Indicates the refund is under processing.
     * FAIL: Indicates the refund failed.
     */
    private RefundStatusEnum refundStatus;

    /**
     * 退款达到最终成功状态的日期和时间
     */
    private String refundTime;
}
