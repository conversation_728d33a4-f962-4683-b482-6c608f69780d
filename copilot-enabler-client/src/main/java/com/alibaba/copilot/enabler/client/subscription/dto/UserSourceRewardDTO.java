package com.alibaba.copilot.enabler.client.subscription.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户来源福利记录数据传输对象
 * 用于跟踪特定来源（如Discord）的福利发放情况
 *
 * <AUTHOR>
 * @date 2025/3/19
 */
public class UserSourceRewardDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 用户ID（接收福利的用户）
     */
    private Long userId;

    /**
     * 来源类型（使用枚举UserInvitationSourceType的name()方法）
     */
    private String sourceType;

    /**
     * 来源伪用户ID（使用枚举UserInvitationSourceType的userId字段）
     */
    private Long sourceUserId;
    
    /**
     * 福利类型（如DISCORD_WELCOME，可扩展）
     */
    private String rewardType;
    
    /**
     * 是否已处理
     */
    private Boolean processed;
    
    /**
     * 扩展属性（可存储福利详情的JSON）
     */
    private String attributes;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public Long getSourceUserId() {
        return sourceUserId;
    }

    public void setSourceUserId(Long sourceUserId) {
        this.sourceUserId = sourceUserId;
    }

    public String getRewardType() {
        return rewardType;
    }

    public void setRewardType(String rewardType) {
        this.rewardType = rewardType;
    }

    public Boolean getProcessed() {
        return processed;
    }

    public void setProcessed(Boolean processed) {
        this.processed = processed;
    }

    public String getAttributes() {
        return attributes;
    }

    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }
} 