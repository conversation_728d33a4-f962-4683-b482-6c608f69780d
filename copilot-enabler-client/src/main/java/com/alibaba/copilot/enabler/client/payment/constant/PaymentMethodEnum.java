package com.alibaba.copilot.enabler.client.payment.constant;

import com.alibaba.copilot.boot.basic.enums.IEnum;
import com.alibaba.copilot.boot.basic.exception.BizException;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Nonnull;

/**
 * 支付方式enum
 */
@Getter
@AllArgsConstructor
public enum PaymentMethodEnum implements IEnum<PaymentMethodEnum> {

    /**
     * 信用卡 (注: 为兼容老逻辑, 此处 name 和 value 不同)
     */
    CREDIT_CARD("CARD"),

    /**
     * 内地支付宝
     */
    ALIPAY_CN("ALIPAY_CN"),

    /**
     * 香港支付宝
     */
    ALIPAY_HK("ALIPAY_HK"),

    /**
     * SHOPIFY支付
     */
    SHOPIFY("SHOPIFY"),

    /**
     * STRIPE支付
     */
    STRIPE("STRIPE"),

    /**
     * SHOPLAZZA支付
     */
    SHOPLAZZA("SHOPLAZZA"),

    /**
     * 针对PicCopilot业务自有的兑换码方式
     */
    PIC_REDEEM_CODE("PIC_REDEEM_CODE"),
    ;

    /**
     * 注: 由于历史原因, 该字段仅用于AE金融的参数信息, 内部存储和外部传参均使用枚举的name本身
     */
    private final String value;

    /**
     * @return 是否是支付宝支付
     */
    public static boolean isAlipayPaymentMethod(PaymentMethodEnum paymentMethodType) {
        return paymentMethodType == ALIPAY_CN || paymentMethodType == ALIPAY_HK;
    }

    @Nonnull
    public static PaymentMethodEnum ofName(String name) {
        if (StringUtils.isEmpty(name)) {
            // 兼容老逻辑, 为空时默认值为信用卡
            return CREDIT_CARD;
        }
        for (PaymentMethodEnum type : values()) {
            if (type.name().equals(name)) {
                return type;
            }
        }
        throw new BizException(ErrorCode.SYS_ERROR, "unknown payment method type: " + name);
    }
}
