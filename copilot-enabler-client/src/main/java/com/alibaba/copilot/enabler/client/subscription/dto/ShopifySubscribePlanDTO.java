package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/10/29
 */
@Data
@Accessors(chain = true)
public class ShopifySubscribePlanDTO implements Serializable {

    /**
     * 店铺域名
     */
    private String shopDomain;

    /**
     * Shopify调用令牌
     */
    private String accessToken;

    /**
     * App Code
     */
    private String appCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 计划ID
     */
    private Long planId;

    /**
     * 折扣码 (用来作为推广域标识)
     */
    private String shareCode;

    /***
     * 重定向地址URI (用来支付成功后的前端页面重定向)
     */
    private String redirectUrl;

    private String clientIp;
}
