package com.alibaba.copilot.enabler.client.email.dto;

import com.alibaba.copilot.enabler.client.email.annotation.EmailTemplate;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 扣费前的通知邮件: https://gmc.alibaba-inc.com/#/template/create?templateName=PicCopilot_buyer_DeduNotice
 *
 * <AUTHOR>
 * @version 2023/10/24
 */
@Data
@Accessors(chain = true)
@EmailTemplate(id = "62", name = "PicCopilot_buyer_DeduNotice")
public class NotifyBeforeDeductEmailDTO implements Serializable {

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 计划名称
     */
    private String planName;
}
