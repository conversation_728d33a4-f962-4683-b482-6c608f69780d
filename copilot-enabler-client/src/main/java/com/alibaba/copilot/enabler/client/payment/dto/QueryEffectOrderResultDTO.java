package com.alibaba.copilot.enabler.client.payment.dto;

import lombok.Data;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2023/10/19
 */
@Data
@Accessors(chain = true)
public class QueryEffectOrderResultDTO implements Serializable {

    /**
     * id
     */
    @Setter
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 产品唯一code
     */
    private String appCode;

    /**
     * 用户产品关系ID
     */
    private Long userAppRelationId;

    /**
     * 计划ID
     */
    private Long subscriptionPlanId;

    /**
     * 计划名称
     */
    private String subscriptionPlanName;

    /**
     * 下次执行的计划ID
     */
    private Long nextPlanId;

    /**
     * 下次执行的计划名称
     */
    private String nextPlanName;

    /**
     * 折扣tag
     */
    private String subscriptionDiscountTag;

    /**
     * 是否续定（否取消订阅）
     */
    private Boolean autoRenew;

    /**
     * 下次自动续费的时间
     */
    private Date nextRenewalTime;

    /**
     * 是否包含试用期
     */
    private Boolean isIncludeTrial;

    /**
     * 计划价格（计划的原价）
     */
    private BigDecimal planPrice;

    /**
     * 真实付款费用
     */
    private BigDecimal actualFee;

    /**
     * 订单履约开始时间
     */
    private Date performStartTime;

    /**
     * 订单履约结束时间
     */
    private Date performEndTime;

    /**
     * 当前是否在试用期
     */
    private Boolean currentIsInTrial;
}
