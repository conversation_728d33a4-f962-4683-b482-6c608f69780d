package com.alibaba.copilot.enabler.client.marketing.facade;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.marketing.dto.FirstMonthDiscountDTO;
import com.alibaba.copilot.enabler.client.marketing.dto.QueryFirstMonthDiscountDTO;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 2024/2/27
 */
public interface FirstMonthDiscountHsfApi {

    /**
     * 查询首月优惠
     *
     * @param discountDTO 参数信息
     * @return 查询结果
     */
    SingleResult<Map<Long, FirstMonthDiscountDTO>> queryFirstMonthDiscount(QueryFirstMonthDiscountDTO discountDTO);
}
