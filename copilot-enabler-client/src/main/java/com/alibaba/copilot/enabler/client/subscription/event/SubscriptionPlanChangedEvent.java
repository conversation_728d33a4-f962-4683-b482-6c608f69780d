package com.alibaba.copilot.enabler.client.subscription.event;

import com.alibaba.copilot.boot.event.eventbus.annotation.Event;
import com.alibaba.copilot.enabler.client.subscription.constant.DurationUnit;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionCompleteReason;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionPlanChangedType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 套餐变更的事件
 *
 * <AUTHOR>
 * @version 2023/10/22
 */
@Data
@Accessors(chain = true)
@Event(id = "planChangedEvent")
public class SubscriptionPlanChangedEvent {

    /**
     * 应用标识
     */
    private String appCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     *
     */
    private Long currentOrderId;

    /**
     * tradeNo
     */
    private String currentTradeNo;

    /**
     * 改变类型
     */
    private SubscriptionPlanChangedType changedType;

    /**
     * 订阅完结原因
     * @see SubscriptionCompleteReason
     */
    private String subscriptionCompleteReason;

    /**
     * 上个套餐的ID (可空, 如新订场景)
     */
    private Long lastPlanId;

    /**
     * 上个套餐的周期单位 (可空, 如新订场景)
     */
    private DurationUnit lastPlanDurationUnit;

    /**
     * 上个套餐的开始时间
     */
    private Date lastPlanStartTime;

    /**
     * 上个套餐的结束时间
     */
    private Date lastPlanEndTime;

    /**
     * 当前套餐的ID (可空, 如退订场景)
     */
    private Long currentPlanId;

    /**
     * 当前套餐的周期单位 (可空, 如退订场景)
     */
    private DurationUnit currentPlanDurationUnit;

    /**
     * 当前套餐的开始时间
     */
    private Date currentPlanStartTime;

    /**
     * 当前套餐的结束时间
     */
    private Date currentPlanEndTime;
}
