package com.alibaba.copilot.enabler.client.base.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 分页DTO (用于分页查询的场景)
 *
 * <AUTHOR>
 * @version 2023/10/17
 */
@Data
@Accessors(chain = true)
public class PageDTO<T> implements Serializable {

    /**
     * 默认页数
     */
    private static final Long DEFAULT_PAGE_NUM = 1L;

    /**
     * 默认每页数据量
     */
    private static final Long DEFAULT_PAGE_SIZE = 20L;

    /**
     * 页数 (从1开始)
     */
    private Long pageNum = DEFAULT_PAGE_NUM;

    /**
     * 每页数据量
     */
    private Long pageSize = DEFAULT_PAGE_SIZE;

    /**
     * 总数据量
     */
    private Long total;

    /**
     * 分页数据集
     */
    private List<T> list;

    /**
     * 总页数
     */
    private Long totalPage = 0L;
}
