package com.alibaba.copilot.enabler.client.marketing.constant;

import com.alibaba.copilot.boot.basic.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 折扣方式
 *
 * <AUTHOR>
 * @version 2024/2/26
 */
@Getter
@AllArgsConstructor
public enum DiscountWay implements IEnum<DiscountWay> {

    /**
     * 按固定值折扣, 根据折扣的具体值进行折扣
     */
    FIXED,

    /**
     * 按比例折扣, 根据原价的比例进行折扣
     */
    RATE,
    ;

    public static DiscountWay of(String name) {
        return IEnum.of(DiscountWay.class, name);
    }
}
