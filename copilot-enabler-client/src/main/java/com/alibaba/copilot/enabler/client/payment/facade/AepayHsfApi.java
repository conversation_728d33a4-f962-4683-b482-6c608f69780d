package com.alibaba.copilot.enabler.client.payment.facade;

import com.alibaba.copilot.boot.basic.result.PageResult;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.payment.dto.*;
import com.alibaba.copilot.enabler.client.payment.request.TradeRecordPageQuery;
import com.alibaba.copilot.enabler.client.subscription.dto.QueryTradeRecordDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.QueryTradeRecordResultDTO;

/**
 * <AUTHOR>
 * @version 2023/9/27
 */
public interface AepayHsfApi {

    /**
     * 支付结果主动查询
     *
     * @param paymentDTO 支付结果查询
     * @return 支付结果
     */
    SingleResult<InquiryPaymentResultDTO> inquiryPayment(InquiryPaymentDTO paymentDTO);

    /**
     * 查询交易流水分页信息
     *
     * @param query 查询参数
     * @return 分页信息
     */
    PageResult<TradeRecordDetailDTO> queryTradeRecordsByPage(TradeRecordPageQuery query);

    /**
     * 查询交易流水分页信息
     *
     * @param query 查询参数
     * @return 分页信息
     */
    PageResult<TradeRecordDetailDTO> queryTradeRecordsByPageWithStripe(TradeRecordPageQuery query);

    /**
     * 查询交易流水分页信息
     *
     * @param query 查询参数
     * @return 分页信息
     */
    PageResult<TradeRecordForPicCopilotDTO> queryTradeRecordsPageForPicCopilot(TradeRecordPageQuery query);

    /**
     * 查询交易流水
     *
     * @param dto 参数信息
     * @return 交易流水
     */
    SingleResult<QueryTradeRecordResultDTO> queryTradeRecord(QueryTradeRecordDTO dto);

    /**
     * 查询账单票据
     *
     * @param dto 参数信息
     * @return 账单票据url
     */
    SingleResult<String> queryBillInvoice(QueryTradeRecordDTO dto);


    /**
     * 查询用户支付相关信息
     *
     * @param dto
     * @return
     */
    SingleResult<UserPaymentInfoResultDTO> queryUserPaymentInfo(UserPaymentInfoDTO dto);
}
