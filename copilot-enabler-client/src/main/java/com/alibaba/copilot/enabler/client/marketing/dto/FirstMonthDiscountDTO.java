package com.alibaba.copilot.enabler.client.marketing.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2024/2/27
 */
@Data
@Accessors(chain = true)
public class FirstMonthDiscountDTO implements Serializable {

    /**
     * 折扣规则ID
     */
    private Long discountRuleId;

    /**
     * 折扣码
     */
    private String discountCode;

    /**
     * 折后价格
     */
    private BigDecimal discountPrice;
}
