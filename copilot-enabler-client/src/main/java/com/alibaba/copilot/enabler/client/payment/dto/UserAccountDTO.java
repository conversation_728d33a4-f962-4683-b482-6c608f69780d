package com.alibaba.copilot.enabler.client.payment.dto;

import com.alibaba.copilot.enabler.client.subscription.dto.UserPaymentCardDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 用户账户基本信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAccountDTO {

    private String appCode;

    private Long userId;

    private String email;

    /**
     * 信用卡信息
     */
    private List<UserPaymentCardDTO> cardList;

    /**
     * 支付方式和支付TokenId的映射关系
     */
    private Map<String, String> paymentMethodToTokenId;

    /**
     * Stripe 一次性支付信息
     */
    private UserPaymentCardDTO stripeOneTimePaymentCard;
}
