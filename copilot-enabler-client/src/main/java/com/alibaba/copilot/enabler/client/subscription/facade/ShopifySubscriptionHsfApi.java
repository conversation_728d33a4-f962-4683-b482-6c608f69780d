package com.alibaba.copilot.enabler.client.subscription.facade;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.constant.SeoFeatureType;
import com.alibaba.copilot.enabler.client.subscription.dto.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/10/29
 */
public interface ShopifySubscriptionHsfApi {
    /**
     * 创建 / 更新订阅计划
     *
     * @param dto
     * @return
     */
    SingleResult<ShopifySubscribePlanResultDTO> subscribePlan(ShopifySubscribePlanDTO dto);

    /**
     * 查询可用的订阅计划列表
     *
     * @param dto
     * @return
     */
    SingleResult<ShopifySubscribablePlansResultDTO> getSubscribablePlans(ShopifySubscribablePlansQueryDTO dto);

    /**
     * 查询当前的订阅计划
     *
     * @param dto
     * @return
     */
    SingleResult<ShopifySubscribedPlanResultDTO> getSubscribedPlan(ShopifySubscribedPlanQueryDTO dto);

    /**
     * 提供hsf接口给各个app使用，判断当次用户对某个特性的请求是否有资格以及用量
     * @param dto
     * @return
     */
    SingleResult<ShopifyFeatureDTO> getFeature(ShopifyFeatureQuery dto);

    /**
     * 类似getFeature接口，提供更多信息
     * @param dto
     * @return
     */
    SingleResult<ShopifyFeatureAllDTO> getFeatureAll(ShopifyFeatureQuery dto);

    /**
     * 每当消耗型特性使用1次后需要写入表 feature_usage ，周期内第一次insert，后面update
     * @param dto
     * @return
     */
    SingleResult<Boolean> incFeatureUsage(ShopifyFeatureQuery dto);

    /**
     * 为了防止用户并发请求，权益修改为如果生成失败，就回退权益
     * @param dto
     * @return
     */
    SingleResult<Boolean> rollbackFeatureUsage(ShopifyFeatureQuery dto);

    /**
     * 获取当前用户的特性的权限和所剩次数
     * @param shopifyFeatureQuery
     * @return
     */
    SingleResult<ShopifyPlanFeatureDTO> getFeatureMapping(ShopifyFeatureQuery shopifyFeatureQuery);

    /**
     * 获取用户待订阅的下一个套餐的特性
     * @param shopifyFeatureQuery
     * @return
     */
    SingleResult<ShopifyPlanFeatureDTO> getFeatureMappingNextPlan(ShopifyFeatureQuery shopifyFeatureQuery);


    /**
     * 根据planId获取ShopifySubscriptionDTO信息
     * @param shopifySubscribablePlanQueryDTO
     * @return
     */
    SingleResult<ShopifySubscriptionDTO> getShopifySubscriptionDTO(ShopifySubscribablePlanQueryDTO shopifySubscribablePlanQueryDTO);

}
