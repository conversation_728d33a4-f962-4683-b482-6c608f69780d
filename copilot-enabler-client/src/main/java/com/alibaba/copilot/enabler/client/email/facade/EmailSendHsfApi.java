package com.alibaba.copilot.enabler.client.email.facade;

import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.email.dto.EmailProductFocusDTO;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2024/1/24
 */
public interface EmailSendHsfApi {
    /**
     * 发送邮件
     *
     * @param email
     * @param emailInfoObj
     * @return
     */
    SingleResult<Boolean> sendEmail(String email, EmailProductFocusDTO emailInfoObj);
}
