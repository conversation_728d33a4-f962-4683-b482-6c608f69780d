package com.alibaba.copilot.enabler.client.payment.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 2023/10/18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class TradeRecordForPicCopilotDTO extends TradeRecordDetailDTO {

    /**
     * 支付的产品码
     * CASHIER_PAYMENT: 收银台支付
     */
    private String paymentProductCode;

    private OrderDTO order;

}
