package com.alibaba.copilot.enabler.client.payment.constant;

import com.alibaba.copilot.boot.basic.enums.IEnum;

/**
 * 退款状态
 *
 * <AUTHOR>
 * @version 2023/10/8
 */
public enum RefundStatusEnum implements IEnum<RefundStatusEnum> {

    /**
     * Indicates the refund succeeded
     */
    SUCCESS,

    /**
     * Indicates the refund is under processing
     */
    PROCESSING,

    /**
     * Indicates the refund failed
     */
    FAIL,
    ;

    public static RefundStatusEnum of(String name) {
        return IEnum.of(RefundStatusEnum.class, name);
    }
}
