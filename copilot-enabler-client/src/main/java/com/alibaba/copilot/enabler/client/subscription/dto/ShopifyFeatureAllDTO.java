package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * ShopifyFeatureAllDTO 返回
 */
@Data
public class ShopifyFeatureAllDTO implements Serializable {
    /**
     * 是否有这个特性的权限（plan是否支持使用该特性） true/false
     */
    private boolean hasAuthorize;
    /**
     * 是否消耗型特性
     */
    private boolean isDepletion;
    /**
     * 剩余额度
     * - 只有是消耗型特性才会有这个字段
     * - 消耗型特性：remainQuota(剩余额度)如果是0则没有额度了不能用，大于0则可以使用
     */
    private Long remainQuota;

    /**
     * 周期内总额度
     */
    private Long allOverQuota;
    /**
     * 最高级套餐的最高额度是多少
     */
    private Long maxQuota;

}
