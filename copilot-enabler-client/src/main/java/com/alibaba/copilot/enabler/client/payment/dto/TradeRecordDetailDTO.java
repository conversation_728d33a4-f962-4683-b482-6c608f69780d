package com.alibaba.copilot.enabler.client.payment.dto;

import com.alibaba.copilot.enabler.client.payment.constant.PaymentTypeEnum;
import com.alibaba.copilot.enabler.client.payment.constant.TradeRecordStatus;
import com.alibaba.copilot.enabler.client.subscription.constant.TradeDirection;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2023/10/18
 */
@Data
@Accessors(chain = true)
public class TradeRecordDetailDTO implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 唯一流水号
     */
    private String tradeNo;

    /**
     * 外部流水号
     */
    private String outTradeNo;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 订单ID
     */
    private Long subscriptionOrderId;

    /**
     * App code 业务身份
     */
    private String appCode;

    /**
     * 支付类型（用户主动支付、系统定期代扣）
     */
    private PaymentTypeEnum paymentType;

    /**
     * 支付方式
     * @see com.alibaba.copilot.enabler.client.payment.constant.PaymentMethodEnum
     */
    private String paymentMethod;

    /**
     * 交易金额
     */
    private BigDecimal tradeAmount;

    /**
     * 交易方向
     */
    private TradeDirection tradeDirection;

    /**
     * 交易时间
     */
    private Date tradeTime;

    /**
     * 状态
     */
    private TradeRecordStatus status;

    /**
     * 套餐ID (该字段是额外拓展字段, 不在TradeRecord中)
     */
    private Long planId;

    /**
     * 套餐名称 (该字段是额外拓展字段, 不在TradeRecord中)
     */
    private String planName;

    /**
     * 是否使用首月折扣
     */
    private Boolean useFirstMonthDiscount;
}
