package com.alibaba.copilot.enabler.client.user.service;


import com.alibaba.copilot.enabler.client.user.dto.UserAppRelationDTO;
import com.alibaba.copilot.enabler.client.user.request.AppBindingRequest;
import com.alibaba.copilot.enabler.client.user.request.UserAppRelationQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/8/30
 */
public interface UserAppRelationService {
    /**
     * 条件查询用户和指定产品的关系
     *
     * @param userAppRelationQuery
     * @return
     */
    UserAppRelationDTO queryUserAppRelation(UserAppRelationQuery userAppRelationQuery);

    /**
     * 查询用户产品绑定关系
     *
     * @return
     */
    List<UserAppRelationDTO> queryUserAppRelationList(UserAppRelationQuery userAppRelationQuery);

    /**
     * 根据appCode和userId查询绑定关系
     */
    List<UserAppRelationDTO> queryListByAppCodeAndUserId(String appCode, Long userId);

    /**
     * 账号绑定 app
     *
     * @param userId
     * @param appBindingRequest
     * @return
     */
    UserAppRelationDTO createUserAppRelation(Long userId, AppBindingRequest appBindingRequest);

    /**
     * updateUserAppRelation
     *
     * @param id
     * @param userAppRelationDTO
     * @return
     */
    Boolean updateUserAppRelation(Long id, UserAppRelationDTO userAppRelationDTO);
}
