package com.alibaba.copilot.enabler.client.subscription.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/12/6
 */
@Data
@Accessors(chain = true)
public class CreateFreeOrderDTO implements Serializable {

    /**
     * 应用标识
     */
    private String appCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 套餐ID
     */
    private Long planId;

    /**
     * 套餐月数
     */
    private Integer month;

    /**
     * 账单流水
     * 如果传了就会创建0元账单流水
     */
    private CreateTradeRecordDTO tradeRecord;
}
