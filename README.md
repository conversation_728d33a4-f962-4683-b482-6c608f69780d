AI Business团队APP工厂基座

[完整文档](https://aliyuque.antfin.com/pcdveg/vqg8r3/lk9f6q5plgqmgg4z)
## 整体功能
沉淀账号、订阅、分佣、AI运维等通用服务。

## 应用分层
### module分层
- client：客户端层，对外暴露服务、传输对象（DTO、VO、Request、Response）
- service：服务层，业务流程编排，协调domain和infrastructure一起完成业务逻辑。
- domain：领域层，对应DDD的领域实体、领域服务、repository、gateway声明等。
- infrastructure：基础设施层，存储访问、消息通信、外部调用、动态配置等。
- start：应用启动层，应用启动类、全局静态配置等。
### package分层
- package按 子域 -> 功能 -> 内部子域 分层，后续一级子域可单独拆分应用。
- 子域之间通信，只能通过client中对外声明的服务。
- 具体分层：
```
|---- copilot-enabler-client    // 客户端层
    |----subscription           // 子域module
        |----event              // 领域事件声明
        |----constant           // 常量（enum、final）
        |----dto                // 服务的出参对象
        |----request            // request or query
        |----service            // 本地/HSF服务接口声明
        |----facade             // 提供给Top/MTop的HSF接口
    |----account                // 子域module
        |----event              // 领域事件声明
        |----constant           // 常量（enum、final）
        |----dto                // 服务的出参对象，value object/view object
        |----request            // request or query
        |----service            // 应用层接口声明（提供给Controller或间接提供给HSF实现的接口）
        |----facade             // 提供给Top/MTop的HSF接口

|---- copilot-enabler-domain    // 领域层（entity、domainservice等）
    |----biz1                   // 子域module
        |----factory            // 类工厂（构建器/转换器/工厂类）
        |----model              // 充血模型的实体类（实体或值对象）
        |----service            // 领域服务（非必要不新增）
        |----repository         // 对象存储接口，实现放在infrastructure
        |----gateway            // 外部调用接口，实现放在infrastructure
  |--test                       // 领域层单元测试

|---- copilot-enabler-infrastructure  // 基础设施层: mapper/config/repository impl
  |--main
    |----biz1                   // 子域module
        |----dataobject         // do: 贫血模型
        |----factory            // 类工厂（构建器/转换器/工厂类）
        |----mapper             // mybatis mapper
        |----repository         // repository impl
        |----gateway            // gateway impl
  |--test                       // 基础设施层单元测试

|---- copilot-enabler-service   // 调用外域服务，使用domain层的能力
    |----biz1                   // 子域module
        |----factory            // 类工厂（构建器/转换器/工厂类）
        |----service            // 提供给Controller或HSF的应用层接口实现
        |----facade             // 提供给Top/MTop的HSF接口实现，通常是调service的服务
        |----dto                // 视图层对象VO或数据传输对象DTO
        |----metaq
        |----controller         // HTTP服务
  |--test                       // 服务实现层单元测试

|---- copilot-enabler-starter
  |--test                       // 集成测试

```
## 代码规范
### 1、服务规范
- 请求入参：封装成请求对象，涉及写操作后缀Request，仅读操作后缀Query。
- 请求返回：
    + 外部服务（Controller）：
        * 必须使用Result包装返回结果。
        * 使用try-catch捕获异常，异常情况对外返回错误码。
    + 内部服务（service/repository/gateway）：一律抛业务异常。
### 2、监控规范
- controller、service、repository、gateway、consumer必须监控注解。
- 监控需严格定义分层，便于问题排查。
### 3、日志规范
- 流量入口必须打印日志。
- 异常和错误必须打印日志。
- 重点服务的结果需要打印日志。
- 外部服务的核心返回结果要打日志。
### 4、模型设计
- 新增模型，需要三人以上评审和review。
- 模型扩展字段使用attributes，实体类使用com.alibaba.copilot.boot.basic.data.Attributes子类，扩展属性key和value在代码层面被清晰阅读和使用。
- 不允许对数据做物理删除。
### 5、代码规范
- 校验：
    + 入参：统一使用com.alibaba.copilot.boot.tools.verify.BeanValidator
    + 断言：统一使用com.alibaba.copilot.boot.tools.verify.Assertor
- 必要注释
- 对象转换：
    + A -> B：com.alibaba.copilot.boot.basic.factory.Converter
    + A、B、C -> D：com.alibaba.copilot.boot.basic.factory.Builder
- 线程池：com.alibaba.copilot.boot.tools.thread.ThreadTracePoolExecutor
    + Runnable：com.alibaba.copilot.boot.tools.thread.ThreadTraceRunnable#of
    + Callable：com.alibaba.copilot.boot.tools.thread.ThreadTraceCallable#of
- 数据库扩展字段：
    + json字符串转map：com.alibaba.copilot.boot.tools.utils.AttributesUtils#stringConvertToMap
    + map转json字符串：com.alibaba.copilot.boot.tools.utils.AttributesUtils#mapConvertToString
- 代码可读性：[《如何写好代码》](https://ata.atatech.org/articles/11000241086)

## Git Commit 规范
> 建议尽量遵守这些规范, 提效协作
- feat: 功能 feature 的意思, 也是最常用的。当你的功能有变更的时候, 都可以采用这种类型的 type
- fix: bug 修复
- docs: 更新了文档，或者更新了注释
- style: 代码格式调整，比如执行了 code format
- refactor: 重构代码, 指的是代码结构的调整，比如使用了一些设计模式重新组织了代码
- perf: 对项目或者模块进行了性能优化。比如一些 jvm 的参数改动，把 StringBuffer 改为 StringBuilder 等
- test: 这个简单，就是增加了单元测试和自动化相关的代码
- build: 影响编译的一些更改，比如更改了 maven 插件
- ci: 持续集成方面的更改
- chore: 其他改动。比如一些注释修改或者文件清理。不影响主要代码文件的，都可以放在这里
- revert: 回滚代码

## 关于单元测试
- UT 位于各自 module 的 test 路径下
- 条件允许情况下, 建议最好写下单元测试, 好的 UT 有助于描述清楚系统边界

## 关于 CR
- 推荐完成相对独立的小模块后, 小范围阶段性 CR 下, 有助于提前发现风险, 快速调整
- 提交测试前建议进行下代码路演, 让其他研发、测试等同学对该部分业务有大概的认识
